<?xml version="1.0" encoding="utf-8"?>
    <LinearLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <!--有可能直接显示（固定的布局，非必须，按需要添加）-->
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />


        <RelativeLayout
            android:id="@+id/container"
            android:layout_width="match_parent"
            android:layout_height="match_parent">


            <!--加载失败-->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical"
                android:visibility="gone">


                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15dp"
                    android:text="加载失败,点击重试"
                    android:textSize="14sp" />
            </LinearLayout>

            <!--加载中..-->

        </RelativeLayout>
    </LinearLayout>