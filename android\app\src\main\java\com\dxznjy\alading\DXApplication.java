package com.dxznjy.alading;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.LayerDrawable;
import android.net.Uri;
import android.os.Build;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.Display;
import android.view.WindowManager;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.vectordrawable.graphics.drawable.VectorDrawableCompat;

import com.blankj.rxbus.RxBus;
import com.dxznjy.alading.activity.OpenKfActivity;
import com.dxznjy.alading.activity.JumpTempActivity;
import com.dxznjy.alading.activity.givelessons.MeetingPermissionActivity;
import com.dxznjy.alading.common.Constants;
import com.dxznjy.alading.entity.PayData;
import com.dxznjy.alading.util.GlideGifImagerLoader;
import com.dxznjy.alading.util.GlideImageLoader;
import com.dxznjy.alading.util.WxUtils;
import com.google.gson.Gson;
import com.qiyukf.nimlib.sdk.StatusBarNotificationConfig;
import com.qiyukf.nimlib.sdk.msg.constant.NotificationExtraTypeEnum;
import com.qiyukf.unicorn.api.ConsultSource;
import com.qiyukf.unicorn.api.OnBotEventListener;
import com.qiyukf.unicorn.api.OnMessageItemClickListener;
import com.qiyukf.unicorn.api.OnMixSdkReconnectClickListener;
import com.qiyukf.unicorn.api.OnVideoFloatBackIntent;
import com.qiyukf.unicorn.api.QuickEntry;
import com.qiyukf.unicorn.api.QuickEntryListener;
import com.qiyukf.unicorn.api.UICustomization;
import com.qiyukf.unicorn.api.Unicorn;
import com.qiyukf.unicorn.api.YSFOptions;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.DefaultRefreshFooterCreator;
import com.scwang.smartrefresh.layout.api.DefaultRefreshHeaderCreator;
import com.scwang.smartrefresh.layout.api.DefaultRefreshInitializer;
import com.scwang.smartrefresh.layout.api.RefreshFooter;
import com.scwang.smartrefresh.layout.api.RefreshHeader;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.constant.SpinnerStyle;
import com.scwang.smartrefresh.layout.footer.ClassicsFooter;
import com.scwang.smartrefresh.layout.header.ClassicsHeader;
import com.tencent.chat.flutter.push.tencent_cloud_chat_push.application.TencentCloudChatPushApplication;
import com.tencent.mm.opensdk.openapi.IWXAPI;
import com.tencent.mm.opensdk.openapi.WXAPIFactory;
import com.vondear.rxtool.RxTool;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Random;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSession;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;

import io.dcloud.feature.sdk.DCSDKInitConfig;
import io.dcloud.feature.sdk.DCUniMPSDK;
import io.dcloud.feature.sdk.Interface.IDCUniMPPreInitCallback;
import io.dcloud.feature.sdk.Interface.IUniMP;
import io.dcloud.feature.sdk.MenuActionSheetItem;
import io.dcloud.feature.uniapp.UniSDKEngine;
import io.dcloud.uniplugin.AppShare;
import io.dcloud.uniplugin.CustomerService;
import io.dcloud.uniplugin.PayEvent;
import io.dcloud.uniplugin.JumpToFlutter;
import io.dcloud.uniplugin.TxToFlutterParams;
import io.dcloud.uniplugin.ZXUniModule;
import io.flutter.embedding.engine.FlutterEngine;
import io.flutter.plugin.common.MethodChannel;


public  class DXApplication extends TencentCloudChatPushApplication {
    private static int foregroundActCount = 0;
    public static DXApplication app;
    private static final String APP_ID = "wx79465579ceea44ae";
    public static IWXAPI api;
    public FlutterEngine flutterEngineInstance;

    public static IUniMP iUniMP;


    @Override
    public void onCreate() {
        super.onCreate();
        app = this;
        UniSDKEngine.registerModule("ZXUniModule", ZXUniModule.class);
        setupRefreshLayout();
        initUniappSdk();
        RxTool.init(this);
        regToWx();
        RxBus.getDefault().subscribe(this, new RxBus.Callback<PayEvent>() {
            @Override
            public void onEvent(PayEvent result) {
                if (result.getPayType().equals(Constants.openUniAppWxPay)) {
                    PayData payData = new Gson().fromJson(result.getPayData(), PayData.class);
                    WxUtils.startWxUniapp(payData);
                } else if (result.getPayType().equals(Constants.openUniAppAliPay)) {
                }
            }
        });
        RxBus.getDefault().subscribe(this,  new RxBus.Callback<AppShare>() {
            @Override
            public void onEvent(AppShare result) {
                if (result.getShareType()==1){
                    WxUtils.shareMiniProgram(DXApplication.this,result);
                }else{
                    WxUtils.shareWeb(DXApplication.this,result);
                }
            }
        });
        RxBus.getDefault().subscribe(this, new RxBus.Callback<JumpToFlutter>() {
            @Override
            public void onEvent(JumpToFlutter result) {
                Intent intent = new Intent(getApplicationContext(), JumpTempActivity.class);
                intent.putExtra("jumpType", result.getJumpType());
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                getApplicationContext().startActivity(intent);
            }
        });

        RxBus.getDefault().subscribe(this, new RxBus.Callback<TxToFlutterParams>() {
            @Override
            public void onEvent(TxToFlutterParams result) {
                MethodChannel methodChannel = new MethodChannel(flutterEngineInstance.getDartExecutor().getBinaryMessenger(),"androidIosBackMeeting");
                methodChannel.invokeMethod("nativeToFlutter",new Gson().toJson(result));
                Log.i("zgj", "收到消息:" + new Gson().toJson(result));
            }
        });


        RxBus.getDefault().subscribe(this, new RxBus.Callback<CustomerService>() {
            @Override
            public void onEvent(CustomerService result) {
                startUserActivity(DXApplication.this, OpenKfActivity.class);
            }
        });

        Unicorn.config(this, "bf4b50ff1b7f469c799230a0296dc25a", ysfOptions(), new GlideImageLoader(this));
    }


    /**
     * 第三方客服相关环境配置
     * @return
     */
    private YSFOptions ysfOptions() {
        YSFOptions options = new YSFOptions();
        options.statusBarNotificationConfig = new StatusBarNotificationConfig();
        options.statusBarNotificationConfig.notificationSmallIconId = R.drawable.ic_launcher;
        options.statusBarNotificationConfig.notificationEntrance = MainActivity.class;
        options.statusBarNotificationConfig.notificationExtraType = NotificationExtraTypeEnum.MESSAGE;
        options.gifImageLoader = new GlideGifImagerLoader(this);
        options.onBotEventListener = new OnBotEventListener() {
            @Override
            public boolean onUrlClick(Context context, String url) {
                Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(url));
                context.startActivity(intent);
                return true;
            }
        };
        options.onMessageItemClickListener = new OnMessageItemClickListener() {
            @Override
            public void onURLClicked(Context context, String url) {
            }
        };
        options.quickEntryListener = new QuickEntryListener() {
            @Override
            public void onClick(Context context, String shopId, QuickEntry quickEntry) {
            }
        };
        options.uiCustomization = new UICustomization();
        options.isMixSDK = false;
        // 七鱼云信融合情况下,云信帐号被踢,点击"重新连接"响应处理
        options.onMixSdkReconnectClickListener = new OnMixSdkReconnectClickListener() {
            @Override
            public void onMixSdkReconnectClicked(Context context) {
            }
        };
        //七鱼视频客服,小窗口后要回到在线界面的回调
        options.onVideoFloatBackIntent = new OnVideoFloatBackIntent() {
            @Override
            public void onVideoFloatBackIntent(Context context) {
                // 启动聊天界面
                ConsultSource source = new ConsultSource("在线客服", "在线客服", null);
                Unicorn.openServiceActivity(context, "在线客服", source);
            }
        };
        return options;
    }


    /* *//**
     * 分享到微信
     * @param result
     *//*
    public void shareWeb(AppShare result){
        if(!AppUtils.isWxInstall(this)){//未安装微信
            Toast.makeText(this,"微信未安装",Toast.LENGTH_SHORT).show();
        }else{
            WXWebpageObject webpage = new WXWebpageObject();
            AppShareParams appShareParams=new Gson().fromJson(result.getParams(), AppShareParams.class);
            webpage.webpageUrl =Constants.SHARE_H5_URL+appShareParams.getPath();
            //用 WXWebpageObject 对象初始化一个 WXMediaMessage 对象
            WXMediaMessage msg = new WXMediaMessage(webpage);
            msg.description ="";
            String title=appShareParams.getTitle();
            if(title.length()>30){
                title=title.substring(0,30)+"...";
            }
            msg.title =title;
            if (TextUtils.isEmpty(appShareParams.getImageUrl())){
                Bitmap thumbBmp = BitmapFactory.decodeResource(getResources(), R.mipmap.ic_share_default);
                msg.thumbData =bitmapToByteArray(thumbBmp);
                SendMessageToWX.Req req = new SendMessageToWX.Req();
                req.transaction = "鼎校甄选";
                req.message =msg;
                req.scene =SendMessageToWX.Req.WXSceneSession;
                //调用api接口，发送数据到微信
                DXApplication.api.sendReq(req);
            }else{
                SimpleTarget<Bitmap> simpleTarget = new SimpleTarget<Bitmap>() {
                    @Override
                    public void onResourceReady(Bitmap resource, Transition<? super Bitmap> transition) {
                        msg.thumbData = bitmapToByteArray(resource);
                        //构造一个Req
                        SendMessageToWX.Req req = new SendMessageToWX.Req();
                        req.transaction = "鼎校甄选";
                        req.message =msg;
                        req.scene =SendMessageToWX.Req.WXSceneSession;
                        //调用api接口，发送数据到微信
                        api.sendReq(req);
                    }
                };
                Glide.with(this)
                        .asBitmap()
                        .load(appShareParams.getImageUrl())
                        .override(160,160)
                        .into(simpleTarget);
            }
        }
    }
    public  byte[] bitmapToByteArray(Bitmap bitmap) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        bitmap.compress(Bitmap.CompressFormat.PNG, 70, outputStream);
        return outputStream.toByteArray();
    }
    *//**
     * 微信小程序支付
     *
     *//*
    public  void startWxUniapp(PayEvent  result ) {
        PayData payData=new Gson().fromJson(result.getPayData(),PayData.class);
        String path="cusid="+payData.getCusid()+
                "&appid="+payData.getAppid()+
                "&orgid="+payData.getOrgid()+
                "&version="+payData.getVersion()+
                "&trxamt="+payData.getTrxamt()+
                "&reqsn="+payData.getReqsn()+
                "&innerappid="+payData.getInnerappid()+
                "&notify_url="+payData.getNotify_url()+
                "&body="+payData.getBody()+
                "&signtype="+payData.getSigntype()+
                "&validtime="+payData.getValidtime()+
                "&randomstr="+payData.getRandomstr()+
                "&paytype="+payData.getPaytype()+
                "&sign="+payData.getSign();
        WXLaunchMiniProgram.Req req = new WXLaunchMiniProgram.Req();
        req.userName = "gh_e64a1a89a0ad"; //
        req.path="pages/orderDetail/orderDetail?"+path;
        req.miniprogramType = WXLaunchMiniProgram.Req.MINIPTOGRAM_TYPE_RELEASE; // 正式版
        api.sendReq(req);
    }*/

    /**
     * 初始化小程序sdk
     */
    public void initUniappSdk() {
        MenuActionSheetItem item = new MenuActionSheetItem("关于", "gy");
        List<MenuActionSheetItem> sheetItems = new ArrayList<>();
        sheetItems.add(item);
        //小程序第三方sdk初始化
        DCSDKInitConfig config = new DCSDKInitConfig.Builder()
                .setCapsule(false)
                .setMenuDefFontSize("16px")
                .setMenuDefFontColor("#ff00ff")
                .setMenuDefFontWeight("normal")
                .setMenuActionSheetItems(sheetItems)
                .setEnableBackground(false)//开启后台运行
                .setUniMPFromRecents(false)
                .build();
        DCUniMPSDK.getInstance().initialize(this, config, new IDCUniMPPreInitCallback() {
            @Override
            public void onInitFinished(boolean isSuccess) {
                Log.e("unimp", "onInitFinished-----------" + isSuccess);
            }
        });


    }


    private void regToWx() {
        // 通过WXAPIFactory工厂，获取IWXAPI的实例
        api = WXAPIFactory.createWXAPI(this, APP_ID, true);
        api.registerApp(APP_ID);
    }

    private final List<Activity> activities = new ArrayList<>();
    public static void handleSSLHandshake() {
        try {
            TrustManager[] trustAllCerts = new TrustManager[]{new X509TrustManager() {
                public X509Certificate[] getAcceptedIssuers() {
                    return new X509Certificate[0];
                }
                @Override
                public void checkClientTrusted(X509Certificate[] certs, String authType) {

                }
                @Override
                public void checkServerTrusted(X509Certificate[] certs, String authType) {

                }
            }};
            SSLContext sc = SSLContext.getInstance("TLS");
            // trustAllCerts信任所有的证书
            sc.init(null, trustAllCerts, new SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
            HttpsURLConnection.setDefaultHostnameVerifier(new HostnameVerifier() {
                @Override
                public boolean verify(String hostname, SSLSession session) {
                    return true;
                }
            });
        } catch (Exception ignored) {

        }
    }

    private void setupRefreshLayout() {

        //设置全局默认配置（优先级最低，会被其他设置覆盖）
        SmartRefreshLayout.setDefaultRefreshInitializer(new DefaultRefreshInitializer() {
            @Override
            public void initialize(@NonNull Context context, @NonNull RefreshLayout layout) {
                //全局设置（优先级最低）
                layout.setEnableAutoLoadMore(true);
                layout.setEnableOverScrollDrag(false);
                layout.setEnableOverScrollBounce(true);
                layout.setEnableLoadMoreWhenContentNotFull(true);
                layout.setEnableScrollContentWhenRefreshed(true);
            }
        });
        SmartRefreshLayout.setDefaultRefreshHeaderCreator(new DefaultRefreshHeaderCreator() {
            @NonNull
            @Override
            public RefreshHeader createRefreshHeader(@NonNull Context context, @NonNull RefreshLayout layout) {
                //全局设置主题颜色（优先级第二低，可以覆盖 DefaultRefreshInitializer 的配置，与下面的ClassicsHeader绑定）
                layout.setPrimaryColorsId(R.color.colorPrimary, android.R.color.white);

//                layout.getLayout().setBackgroundResource(R.color.f6);
                layout.setPrimaryColors(0, 0xff666666);
                ClassicsHeader mClassicsHeader = new ClassicsHeader(context);
                mClassicsHeader.setFinishDuration(500);
                mClassicsHeader.setEnableLastTime(true);
                mClassicsHeader.setSpinnerStyle(SpinnerStyle.FixedBehind);
                mClassicsHeader.setTextSizeTitle(16);
                mClassicsHeader.setTextSizeTime(10);
                mClassicsHeader.setTextTimeMarginTop(2);
                mClassicsHeader.setDrawableArrowSize(20);
                mClassicsHeader.setDrawableProgressSize(20);
                mClassicsHeader.setDrawableMarginRight(20);
                Drawable mDrawableProgress = ((ImageView) mClassicsHeader.findViewById(ClassicsHeader.ID_IMAGE_PROGRESS)).getDrawable();
                if (mDrawableProgress instanceof LayerDrawable) {
                    mDrawableProgress = ((LayerDrawable) mDrawableProgress).getDrawable(0);
                }

                if (Build.VERSION.SDK_INT >= 21) {
                    mDrawableProgress.setTint(0xff666666);
                } else if (mDrawableProgress instanceof VectorDrawableCompat) {
                    ((VectorDrawableCompat) mDrawableProgress).setTint(0xff666666);
                }

                int delta = new Random().nextInt(7 * 24 * 60 * 60 * 1000);
                mClassicsHeader.setLastUpdateTime(new Date(System.currentTimeMillis() - delta));
                mClassicsHeader.setTimeFormat(new SimpleDateFormat("更新于 MM-dd HH:mm", Locale.CHINA));
                return mClassicsHeader;
            }
        });
        SmartRefreshLayout.setDefaultRefreshFooterCreator(new DefaultRefreshFooterCreator() {
            @NonNull
            @Override
            public RefreshFooter createRefreshFooter(@NonNull Context context, @NonNull RefreshLayout layout) {
                ClassicsFooter classicsFooter = new ClassicsFooter(context);
                classicsFooter.setFinishDuration(500);
                classicsFooter.setSpinnerStyle(SpinnerStyle.FixedBehind);
                classicsFooter.setTextSizeTitle(16);
                classicsFooter.setDrawableArrowSize(20);
                classicsFooter.setDrawableProgressSize(20);
                classicsFooter.setDrawableMarginRight(20);
                return classicsFooter;
            }
        });
    }


    public void clearActivity(Activity exclude) {
        for (int i = 0; i < activities.size(); i++) {
            if (activities.get(i) != null && activities.get(i) != exclude) {
                activities.get(i).finish();
            }
        }
    }


    @Override
    public void onTerminate() {
        super.onTerminate();
        foregroundActCount = 0;
        RxBus.getDefault().unregister(this); // 确保在应用终止时清除所有订阅者，或者在适当的时候使用remove调用移除特定订阅者。

    }

    public static int getForegroundActCount() {
        return foregroundActCount;
    }


    public static boolean isTablet(Context context) {
        boolean isPad = false;
        isPad = (context.getResources().getConfiguration().screenLayout
                & Configuration.SCREENLAYOUT_SIZE_MASK)
                >= Configuration.SCREENLAYOUT_SIZE_LARGE;
        return isPad;

    }

    /**
     * 判断是否为平板
     *
     * @return
     */
    private boolean isPad() {
        WindowManager wm = (WindowManager) getSystemService(Context.WINDOW_SERVICE);
        Display display = wm.getDefaultDisplay();
        // 屏幕宽度
        float screenWidth = display.getWidth();
        // 屏幕高度
        float screenHeight = display.getHeight();
        DisplayMetrics dm = new DisplayMetrics();
        display.getMetrics(dm);
        double x = Math.pow(dm.widthPixels / dm.xdpi, 2);
        double y = Math.pow(dm.heightPixels / dm.ydpi, 2);
        // 屏幕尺寸
        double screenInches = Math.sqrt(x + y);
        // 大于6尺寸则为Pad
        if (screenInches >= 6.0) {
            return true;
        }
        return false;
    }

    /**
     * 启动新的Activity
     *
     * @param context 当前Activity
     * @param cls     要启动的Activity的类
     */
    public static void startUserActivity(Context context, Class cls) {
        Intent intent = new Intent();
        intent.setClass(context, cls);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(intent);
    }

    /**
     * 支付宝小程序
     */
    public void startAlipayUniapp(PayEvent result) throws UnsupportedEncodingException {
        PayData payData = new Gson().fromJson(result.getPayData(), PayData.class);
        payData.setPaytype("A02");
        Log.i("jsonString", result.getPayData());
        String query = URLEncoder.encode("payinfo=" + URLEncoder.encode(new Gson().toJson(payData), "UTF-8"), "UTF-8");
        try {
            StringBuffer sb = new StringBuffer("alipays://platformapi/startapp?");
            sb.append("appId=").append("2021001104615521").append("&");
            sb.append("page=").append("pages/orderDetail/orderDetail&thirdPartSchema=");
            sb.append(URLEncoder.encode("allinpaysdk://sdk/", "UTF-8")).append("&");
            sb.append("query=").append(query);
            Log.i("zgj", sb.toString());
            Intent intent = new Intent(this, MeetingPermissionActivity.class);
            intent.putExtra("courseId", sb.toString());
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            startActivity(intent);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();

        }
    }
}
