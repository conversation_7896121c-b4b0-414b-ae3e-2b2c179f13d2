class CheckVersion {
  ExtValuesBean? extValues;
  String? id;
  String? version;
  String? upGradeLog;
  String? addTime;
  String? appType;
  String? downloadUrl;
  bool? forcedUpGrade;
  String? merchants;
  int? isEnable;

  static CheckVersion? fromMap(Map<String, dynamic> map) {
    CheckVersion aaBean = CheckVersion();
    aaBean.extValues = map['extValues'] != null?ExtValuesBean.fromMap(map['extValues'])!:null;
    aaBean.id = map['id'];
    aaBean.version = map['version'];
    aaBean.upGradeLog = map['upGradeLog'];
    aaBean.addTime = map['addTime'];
    aaBean.appType = map['appType'];
    aaBean.downloadUrl = map['downloadUrl'];
    aaBean.forcedUpGrade = map['forcedUpGrade'];
    aaBean.merchants = map['merchants'];
    aaBean.isEnable = map['isEnable'];
    return aaBean;
  }

  Map toJson() => {
    "extValues": extValues,
    "id": id,
    "version": version,
    "upGradeLog": upGradeLog,
    "addTime": addTime,
    "appType": appType,
    "downloadUrl": downloadUrl,
    "forcedUpGrade": forcedUpGrade,
    "merchants": merchants,
    "isEnable": isEnable,
  };
}

class ExtValuesBean {
  static ExtValuesBean? fromMap(Map<String, dynamic> map) {
    ExtValuesBean extValuesBean = ExtValuesBean();
    return extValuesBean;
  }

  Map toJson() => {};
}