//
//  AppDelegate + Push.swift
//  Runner
//
//  Created by 许江泉 on 2025/5/22.
//
import TIMPush
import tencent_cloud_chat_push

extension AppDelegate: TIMPushDelegate {
   @objc func offlinePushCertificateID() -> Int32 {
       return TencentCloudChatPushFlutterModal.shared.offlinePushCertificateID()
   }
    
   @objc func businessID() -> Int32 {
       return TencentCloudChatPushFlutterModal.shared.businessID()
   }


   @objc func applicationGroupID() -> String {
       return TencentCloudChatPushFlutterModal.shared.applicationGroupID()
   }
   
   @objc func onRemoteNotificationReceived(_ notice: String?) -> Bool {
       TencentCloudChatPushPlugin.shared.tryNotifyDartOnNotificationClickEvent(notice)
       return true
   }
}
