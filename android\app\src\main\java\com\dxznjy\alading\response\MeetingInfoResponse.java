package com.dxznjy.alading.response;


import com.dxznjy.alading.http.BaseResponse;

public class MeetingInfoResponse extends BaseResponse {
    private MeetingInfo data;

    public MeetingInfo getData() {
        return data;
    }

    public void setData(MeetingInfo data) {
        this.data = data;
    }

    public class MeetingInfo{
        private  String courseId;
        private  String courseName;
        private  String courseType;
        private  String courseTime;
        private  String status;
        private  String teacherId;
        private  String teacherName;
        private  String teacherPhoto;
        private  String meetingId;
        private  String meetingNum;
        private  String feedback;


        public String getCourseId() {
            return courseId;
        }

        public void setCourseId(String courseId) {
            this.courseId = courseId;
        }

        public String getCourseName() {
            return courseName;
        }

        public void setCourseName(String courseName) {
            this.courseName = courseName;
        }

        public String getCourseType() {
            return courseType;
        }

        public void setCourseType(String courseType) {
            this.courseType = courseType;
        }

        public String getCourseTime() {
            return courseTime;
        }

        public void setCourseTime(String courseTime) {
            this.courseTime = courseTime;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getTeacherId() {
            return teacherId;
        }

        public void setTeacherId(String teacherId) {
            this.teacherId = teacherId;
        }

        public String getTeacherName() {
            return teacherName;
        }

        public void setTeacherName(String teacherName) {
            this.teacherName = teacherName;
        }

        public String getTeacherPhoto() {
            return teacherPhoto;
        }

        public void setTeacherPhoto(String teacherPhoto) {
            this.teacherPhoto = teacherPhoto;
        }

        public String getMeetingId() {
            return meetingId;
        }

        public void setMeetingId(String meetingId) {
            this.meetingId = meetingId;
        }

        public String getMeetingNum() {
            return meetingNum;
        }

        public void setMeetingNum(String meetingNum) {
            this.meetingNum = meetingNum;
        }

        public String getFeedback() {
            return feedback;
        }

        public void setFeedback(String feedback) {
            this.feedback = feedback;
        }
    }
}
