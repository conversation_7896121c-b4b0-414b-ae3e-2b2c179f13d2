import 'package:student_end_flutter/generated/json/base/json_field.dart';
import 'package:student_end_flutter/generated/json/review_course_list_bean_entity.g.dart';
import 'dart:convert';
export 'package:student_end_flutter/generated/json/review_course_list_bean_entity.g.dart';

@JsonSerializable()
class ReviewCourseListBeanEntity {
  late int currentPage;
  late int totalPage;
  late int size;
  late List<ReviewCourseListBeanData> data;

  ReviewCourseListBeanEntity();

  factory ReviewCourseListBeanEntity.fromJson(Map<String, dynamic> json) =>
      $ReviewCourseListBeanEntityFromJson(json);

  Map<String, dynamic> toJson() => $ReviewCourseListBeanEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class ReviewCourseListBeanData {
  late int id = 0;
  late int meetingId = 0;
  late String meetingNum = '';
  late String subject = '';
  late int startTime = 0;
  late int endTime = 0;
  late String destroyTime = '';
  late String password = '';
  late int state = 0;
  late String meetingShortNum = '';
  late String roomArchiveId = '';
  late String userNickName = '';
  late String meetingStartBegin = '';
  late String meetingEndBegin = '';
  late String sharedScreenStart = '';
  late String sharedScreenEnd = '';
  late String studentCode = '';
  late String studentUuid = '';
  late String studentToken = '';
  late String inviteUrl = '';
  late String courseId = '';
  late String courseName = '';
  late String orderId = '';
  late String courseTimeStr = '';
  late String createTime = '';
  dynamic updateTime;
  dynamic scheduledMembers;
  late String studentName;

  ReviewCourseListBeanData();

  factory ReviewCourseListBeanData.fromJson(Map<String, dynamic> json) =>
      $ReviewCourseListBeanDataFromJson(json);

  Map<String, dynamic> toJson() => $ReviewCourseListBeanDataToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
