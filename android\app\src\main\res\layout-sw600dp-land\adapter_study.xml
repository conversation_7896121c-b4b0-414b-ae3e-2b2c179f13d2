<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingTop="14dp"
    android:paddingLeft="14dp"
    android:paddingRight="14dp"
    >
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="16sp"
            android:textColor="@color/_333333"
            android:singleLine="true"
            android:textStyle="bold"
            android:text="注意力学习课"/>
        <TextView
            android:id="@+id/tv_gostudy"
            android:layout_width="80dp"
            android:layout_height="28dp"
            android:textSize="14sp"
            android:layout_centerVertical="true"
            android:layout_alignParentRight="true"
            android:textColor="#DADEE3"
            android:gravity="center"
            android:background="@drawable/bg_radius4_line_e1dede"
            android:singleLine="true"
            android:layout_marginLeft="14dp"
            android:text="开始练习"/>
    </RelativeLayout>
    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginTop="10dp"
        android:background="#f2f2f2"/>


</LinearLayout>