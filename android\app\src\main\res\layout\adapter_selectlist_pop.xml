<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:id="@+id/rl_bg"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:layout_marginBottom="10dp"
    android:background="@drawable/bg_radius4_f2faf7"
    >

    <TextView
        android:id="@+id/tv_title_vertical"
        android:layout_width="wrap_content"
        android:layout_height="50dp"
        android:layout_centerInParent="true"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:singleLine="true"
        android:text="这是一个课程名称"
        android:textColor="@color/_555555"
        android:textSize="16sp" />
    <TextView
        android:id="@+id/tv_title_horizontal"
        android:layout_width="wrap_content"
        android:layout_centerInParent="true"
        android:layout_height="40dp"
        android:gravity="center"
        android:singleLine="true"
        android:ellipsize="end"
        android:visibility="gone"
        android:textSize="16sp"
        android:textColor="@color/_555555"
        android:text="这是一个课程名称"/>
    <ImageView
        android:id="@+id/img_select"
        android:layout_width="26dp"
        android:layout_height="22dp"
        android:visibility="gone"
        android:layout_alignParentRight="true"
        android:src="@mipmap/icon_xuanzhong_d"/>
</RelativeLayout>