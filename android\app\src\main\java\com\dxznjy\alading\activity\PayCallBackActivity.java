package com.dxznjy.alading.activity;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.util.Log;
import android.view.View;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.blankj.rxbus.RxBus;
import com.dxznjy.alading.DXApplication;
import com.dxznjy.alading.activity.givelessons.MeetingPermissionActivity;
import com.dxznjy.alading.common.Constants;
import com.dxznjy.alading.entity.PayData;
import com.dxznjy.alading.event.PayCallBackEvent;
import com.dxznjy.alading.util.AppUtils;
import com.dxznjy.alading.util.WxUtils;
import com.google.gson.Gson;
import com.tencent.mm.opensdk.modelbiz.WXLaunchMiniProgram;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.concurrent.TimeUnit;

import io.dcloud.uniplugin.PayEvent;
import io.dcloud.uniplugin.module.R;
import io.flutter.plugin.common.MethodChannel;
import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Action;
import io.reactivex.functions.Consumer;

public class PayCallBackActivity extends AppCompatActivity {
    String payData = "";
    boolean isPay = false;
    Disposable mDisposable;

    String payType = "";

    @SuppressLint("MissingInflatedId")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_pay_call_back);
        getWindow().setLayout(1,1);
        payData = getIntent().getStringExtra("payData");
        payType = getIntent().getStringExtra("payType");
//        if(payData == null || payData.isEmpty() || payType == null || payType.isEmpty()){
//            Log.e("PayCallBackActivity", "支付数据或支付类型为空");
//            Intent intent = new Intent();
//            intent.putExtra("errorCode", "");
//            setResult(1000, intent);
//            finish();
//            return;
//        }
        PayData result = new Gson().fromJson(payData,PayData.class);
        if (payType.equals("wxpay")){
            //微信支付
            if(!AppUtils.isWxInstall(this)){//未安装微信
                MethodChannel methodChannel = new MethodChannel(DXApplication.app.flutterEngineInstance.getDartExecutor().getBinaryMessenger(), Constants.androidIosBackMeeting);
                methodChannel.invokeMethod(Constants.initWeChatUnInstall,"");
                finish();
            }else {
                WxUtils.startWxUniapp(result);
            }
        }else{
            //支付宝支付
            if(!AppUtils.isAlipayInstall(this)){//未安装支付宝
//                MethodChannel methodChannel = new MethodChannel(DXApplication.app.flutterEngineInstance.getDartExecutor().getBinaryMessenger(), Constants.androidIosBackMeeting);
//                methodChannel.invokeMethod(Constants.initAlipayInstall,"");
                Toast.makeText(this, "请先安装支付宝客户端", Toast.LENGTH_SHORT).show();
                finish();
            }else{
                try {
                    startAlipayUniapp(new PayEvent(payType, payData));
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                }
            }
        }
        RxBus.getDefault().subscribe(this, new RxBus.Callback<PayCallBackEvent>() {
            @Override
            public void onEvent(PayCallBackEvent result) {
                isPay = false;
                Intent intent = new Intent();
                intent.putExtra("errorCode", result.getErrorCode());
                setResult(1000, intent);
                finish();
            }
        });
    }
/*    public  void startWxUniapp(PayData payData ) {
        String path = "cusid="+payData.getCusid()+
                "&appid="+payData.getAppid()+
                "&orgid="+payData.getOrgid()+
                "&version="+payData.getVersion()+
                "&trxamt="+payData.getTrxamt()+
                "&reqsn="+payData.getReqsn()+
                "&innerappid="+payData.getInnerappid()+
                "&notify_url="+payData.getNotify_url()+
                "&body="+payData.getBody()+
                "&signtype="+payData.getSigntype()+
//                            "&remark="+""+
                "&validtime="+payData.getValidtime()+
//                            "&limit_pay=no_credit"+
                "&randomstr="+payData.getRandomstr()+
                "&paytype="+payData.getPaytype()+
                "&sign="+payData.getSign();
        WXLaunchMiniProgram.Req req = new WXLaunchMiniProgram.Req();
        req.userName = "gh_e64a1a89a0ad"; //
        req.path = "pages/orderDetail/orderDetail?"+path;
        req.miniprogramType = WXLaunchMiniProgram.Req.MINIPTOGRAM_TYPE_RELEASE; // 正式版
        DXApplication.api.sendReq(req);
        Log.i("zgj",req.path);
    }*/


    @Override
    protected void onPostResume() {
        super.onPostResume();
        if (isPay ) {//从微信支付 支付宝小程序支付 登录状态取消登录返回关闭当前界面
            mDisposable = Observable.intervalRange(0, 1, 0, 1, TimeUnit.SECONDS)
                    .observeOn(AndroidSchedulers.mainThread())
                    .doOnNext(new Consumer<Long>() {
                        @Override
                        public void accept(Long aLong) {
                            Log.i("zgj", aLong + "");
                        }
                    })
                    .doOnComplete(new Action() {
                        @Override
                        public void run() throws Exception {
                            if (isPay) {
                                finish();
                            }
                        }
                    })
                    .subscribe();
        }
    }
    /**
     * 支付宝小程序
     *
     */
    public  void startAlipayUniapp(PayEvent  result) throws UnsupportedEncodingException {
        PayData payData = new Gson().fromJson(result.getPayData(), PayData.class);
        payData.setPaytype("A02");
        Log.i("jsonString", result.getPayData());
        String query = URLEncoder.encode("payinfo=" + URLEncoder.encode(new Gson().toJson(payData), "UTF-8"), "UTF-8");
        try {
            StringBuffer sb = new StringBuffer("alipays://platformapi/startapp?");
            sb.append("appId=").append("2021001104615521").append("&");
            sb.append("page=").append("pages/orderDetail/orderDetail&thirdPartSchema=");
            sb.append(URLEncoder.encode("allinpaysdk://sdk/", "UTF-8")).append("&");
            sb.append("query=").append(query);
            Log.i("zgj", sb.toString());
            Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(sb.toString()));
            startActivity(intent);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
    }

    @Override
    protected void onPause () {
        super.onPause();
        isPay = true;
    }

    @Override
    public void onDestroy () {
        super.onDestroy();
        if (mDisposable != null) {
            mDisposable.dispose();
        }
    }
}