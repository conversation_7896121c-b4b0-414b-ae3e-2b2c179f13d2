import 'package:student_end_flutter/generated/json/base/json_convert_content.dart';
import 'package:student_end_flutter/bean/aiRead_member_bean_entity_entity.dart';

AiReadMemberBeanEntityEntity $AiReadMemberBeanEntityEntityFromJson(
    Map<String, dynamic> json) {
  final AiReadMemberBeanEntityEntity aiReadMemberBeanEntityEntity = AiReadMemberBeanEntityEntity();
  final String? merchantCode = jsonConvert.convert<String>(
      json['merchantCode']);
  if (merchantCode != null) {
    aiReadMemberBeanEntityEntity.merchantCode = merchantCode;
  }
  final String? merchantName = jsonConvert.convert<String>(
      json['merchantName']);
  if (merchantName != null) {
    aiReadMemberBeanEntityEntity.merchantName = merchantName;
  }
  return aiReadMemberBeanEntityEntity;
}

Map<String, dynamic> $AiReadMemberBeanEntityEntityToJson(
    AiReadMemberBeanEntityEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['merchantCode'] = entity.merchantCode;
  data['merchantName'] = entity.merchantName;
  return data;
}

extension AiReadMemberBeanEntityEntityExtension on AiReadMemberBeanEntityEntity {
  AiReadMemberBeanEntityEntity copyWith({
    String? merchantCode,
    String? merchantName,
  }) {
    return AiReadMemberBeanEntityEntity()
      ..merchantCode = merchantCode ?? this.merchantCode
      ..merchantName = merchantName ?? this.merchantName;
  }
}