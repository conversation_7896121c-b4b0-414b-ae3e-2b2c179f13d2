// TCICMessageHandler.h
#import <Foundation/Foundation.h>
#import <TCICSDK/TCICCustomDef.h>

NS_ASSUME_NONNULL_BEGIN

@protocol TCICMessageHandlerDelegate;

/**
 * 消息处理器，用于处理H5发送的自定义消息
 */
@interface TCICMessageHandler : NSObject <TCICCustomMessageReceiver>

/**
 * 设置委托对象，用于接收sendMessage事件
 * @param delegate 委托对象（弱引用）
 */
@property (nonatomic, weak) id<TCICMessageHandlerDelegate> delegate;

@end

/**
 * 消息处理器委托协议
 */
@protocol TCICMessageHandlerDelegate <NSObject>

/**
 * 接收到来自H5的sendMessage事件
 * @param payload 消息内容（字典形式）
 */
- (void)didReceiveSendMessageEvent:(NSString *)payload;

@end

NS_ASSUME_NONNULL_END
