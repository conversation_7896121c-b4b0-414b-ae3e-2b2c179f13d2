//
// This is a generated file, do not edit!
// Generated by <PERSON><PERSON>swift, see https://github.com/mac-cain13/R.swift
//

import Foundation
import RswiftResources
import UIKit

private class BundleFinder {}
let R = _R(bundle: Bundle(for: BundleFinder.self))

struct _R {
  let bundle: Foundation.Bundle
  var string: string { .init(bundle: bundle, preferredLanguages: nil, locale: nil) }
  var image: image { .init(bundle: bundle) }
  var file: file { .init(bundle: bundle) }
  var nib: nib { .init(bundle: bundle) }
  var storyboard: storyboard { .init(bundle: bundle) }

  func string(bundle: Foundation.Bundle) -> string {
    .init(bundle: bundle, preferredLanguages: nil, locale: nil)
  }
  func string(locale: Foundation.Locale) -> string {
    .init(bundle: bundle, preferredLanguages: nil, locale: locale)
  }
  func string(preferredLanguages: [String], locale: Locale? = nil) -> string {
    .init(bundle: bundle, preferredLanguages: preferredLanguages, locale: locale)
  }
  func image(bundle: Foundation.Bundle) -> image {
    .init(bundle: bundle)
  }
  func file(bundle: Foundation.Bundle) -> file {
    .init(bundle: bundle)
  }
  func nib(bundle: Foundation.Bundle) -> nib {
    .init(bundle: bundle)
  }
  func storyboard(bundle: Foundation.Bundle) -> storyboard {
    .init(bundle: bundle)
  }
  func validate() throws {
    try self.nib.validate()
    try self.storyboard.validate()
  }

  struct project {
    let developmentRegion = "en"
  }

  /// This `_R.string` struct is generated, and contains static references to 1 localization tables.
  struct string {
    let bundle: Foundation.Bundle
    let preferredLanguages: [String]?
    let locale: Locale?
    var localizable: localizable { .init(source: .init(bundle: bundle, tableName: "Localizable", preferredLanguages: preferredLanguages, locale: locale)) }

    func localizable(preferredLanguages: [String]) -> localizable {
      .init(source: .init(bundle: bundle, tableName: "Localizable", preferredLanguages: preferredLanguages, locale: locale))
    }


    /// This `_R.string.localizable` struct is generated, and contains static references to 27 localization keys.
    struct localizable {
      let source: RswiftResources.StringResource.Source

      /// en translation: DingXiaozhenxuan
      ///
      /// Key: CFBundleDisplayName
      ///
      /// Locales: en, zh-Hans
      var cfBundleDisplayName: RswiftResources.StringResource { .init(key: "CFBundleDisplayName", tableName: "Localizable", source: source, developmentValue: "DingXiaozhenxuan", comment: nil) }

      /// en translation: Cancel
      ///
      /// Key: Cancel
      ///
      /// Locales: en, zh-Hans
      var cancel: RswiftResources.StringResource { .init(key: "Cancel", tableName: "Localizable", source: source, developmentValue: "Cancel", comment: nil) }

      /// en translation: cellphonenumberdoesntexist
      ///
      /// Key: Cellphonenumberdoesntexist
      ///
      /// Locales: en, zh-Hans
      var cellphonenumberdoesntexist: RswiftResources.StringResource { .init(key: "Cellphonenumberdoesntexist", tableName: "Localizable", source: source, developmentValue: "cellphonenumberdoesntexist", comment: nil) }

      /// en translation: clickRefresh
      ///
      /// Key: ClickRefresh
      ///
      /// Locales: en, zh-Hans
      var clickRefresh: RswiftResources.StringResource { .init(key: "ClickRefresh", tableName: "Localizable", source: source, developmentValue: "clickRefresh", comment: nil) }

      /// en translation: Confirm
      ///
      /// Key: Confirm
      ///
      /// Locales: en, zh-Hans
      var confirm: RswiftResources.StringResource { .init(key: "Confirm", tableName: "Localizable", source: source, developmentValue: "Confirm", comment: nil) }

      /// en translation: Confirm exit Login?
      ///
      /// Key: ConfirmExitLogin
      ///
      /// Locales: en, zh-Hans
      var confirmExitLogin: RswiftResources.StringResource { .init(key: "ConfirmExitLogin", tableName: "Localizable", source: source, developmentValue: "Confirm exit Login?", comment: nil) }

      /// en translation: login
      ///
      /// Key: Login
      ///
      /// Locales: en, zh-Hans
      var login: RswiftResources.StringResource { .init(key: "Login", tableName: "Localizable", source: source, developmentValue: "login", comment: nil) }

      /// en translation: loginPassword
      ///
      /// Key: LoginPassword
      ///
      /// Locales: en, zh-Hans
      var loginPassword: RswiftResources.StringResource { .init(key: "LoginPassword", tableName: "Localizable", source: source, developmentValue: "loginPassword", comment: nil) }

      /// en translation: logout
      ///
      /// Key: Logout
      ///
      /// Locales: en, zh-Hans
      var logout: RswiftResources.StringResource { .init(key: "Logout", tableName: "Localizable", source: source, developmentValue: "logout", comment: nil) }

      /// en translation: mobilePhoneNumberEnteredWrongPleasereenter
      ///
      /// Key: MobilePhoneNumberEnteredWrongPleasereenter
      ///
      /// Locales: en, zh-Hans
      var mobilePhoneNumberEnteredWrongPleasereenter: RswiftResources.StringResource { .init(key: "MobilePhoneNumberEnteredWrongPleasereenter", tableName: "Localizable", source: source, developmentValue: "mobilePhoneNumberEnteredWrongPleasereenter", comment: nil) }

      /// en translation: opensoonpleasewait
      ///
      /// Key: Opensoonpleasewait
      ///
      /// Locales: en, zh-Hans
      var opensoonpleasewait: RswiftResources.StringResource { .init(key: "Opensoonpleasewait", tableName: "Localizable", source: source, developmentValue: "opensoonpleasewait", comment: nil) }

      /// en translation: passwordisemptyPleaseenter
      ///
      /// Key: PasswordisemptyPleaseenter
      ///
      /// Locales: en, zh-Hans
      var passwordisemptyPleaseenter: RswiftResources.StringResource { .init(key: "PasswordisemptyPleaseenter", tableName: "Localizable", source: source, developmentValue: "passwordisemptyPleaseenter", comment: nil) }

      /// en translation: PhoneNumber
      ///
      /// Key: PhoneNumber
      ///
      /// Locales: en, zh-Hans
      var phoneNumber: RswiftResources.StringResource { .init(key: "PhoneNumber", tableName: "Localizable", source: source, developmentValue: "PhoneNumber", comment: nil) }

      /// en translation: PhoneNumberVerificatCodeLogin
      ///
      /// Key: PhoneNumberVerificatCodeLogin
      ///
      /// Locales: en, zh-Hans
      var phoneNumberVerificatCodeLogin: RswiftResources.StringResource { .init(key: "PhoneNumberVerificatCodeLogin", tableName: "Localizable", source: source, developmentValue: "PhoneNumberVerificatCodeLogin", comment: nil) }

      /// en translation: pleaseEnterLoginPassword
      ///
      /// Key: PleaseEnterLoginPassword
      ///
      /// Locales: en, zh-Hans
      var pleaseEnterLoginPassword: RswiftResources.StringResource { .init(key: "PleaseEnterLoginPassword", tableName: "Localizable", source: source, developmentValue: "pleaseEnterLoginPassword", comment: nil) }

      /// en translation: pleaseEnterPhoneNumber
      ///
      /// Key: PleaseEnterPhoneNumber
      ///
      /// Locales: en, zh-Hans
      var pleaseEnterPhoneNumber: RswiftResources.StringResource { .init(key: "PleaseEnterPhoneNumber", tableName: "Localizable", source: source, developmentValue: "pleaseEnterPhoneNumber", comment: nil) }

      /// en translation: pleaseEnterVerificationCode
      ///
      /// Key: PleaseEnterVerificationCode
      ///
      /// Locales: en, zh-Hans
      var pleaseEnterVerificationCode: RswiftResources.StringResource { .init(key: "PleaseEnterVerificationCode", tableName: "Localizable", source: source, developmentValue: "pleaseEnterVerificationCode", comment: nil) }

      /// en translation: pleaseFilmobilePhonenumber
      ///
      /// Key: PleaseFilmobilePhonenumber
      ///
      /// Locales: en, zh-Hans
      var pleaseFilmobilePhonenumber: RswiftResources.StringResource { .init(key: "PleaseFilmobilePhonenumber", tableName: "Localizable", source: source, developmentValue: "pleaseFilmobilePhonenumber", comment: nil) }

      /// en translation: pleaseObtaintheverificationCode
      ///
      /// Key: PleaseObtaintheverificationCode
      ///
      /// Locales: en, zh-Hans
      var pleaseObtaintheverificationCode: RswiftResources.StringResource { .init(key: "PleaseObtaintheverificationCode", tableName: "Localizable", source: source, developmentValue: "pleaseObtaintheverificationCode", comment: nil) }

      /// en translation: school
      ///
      /// Key: School
      ///
      /// Locales: en, zh-Hans
      var school: RswiftResources.StringResource { .init(key: "School", tableName: "Localizable", source: source, developmentValue: "school", comment: nil) }

      /// en translation: Sent
      ///
      /// Key: Sent
      ///
      /// Locales: en, zh-Hans
      var sent: RswiftResources.StringResource { .init(key: "Sent", tableName: "Localizable", source: source, developmentValue: "Sent", comment: nil) }

      /// en translation: startClassNow
      ///
      /// Key: StartClassNow
      ///
      /// Locales: en, zh-Hans
      var startClassNow: RswiftResources.StringResource { .init(key: "StartClassNow", tableName: "Localizable", source: source, developmentValue: "startClassNow", comment: nil) }

      /// en translation: userPasswordLogin
      ///
      /// Key: UserPasswordLogin
      ///
      /// Locales: en, zh-Hans
      var userPasswordLogin: RswiftResources.StringResource { .init(key: "UserPasswordLogin", tableName: "Localizable", source: source, developmentValue: "userPasswordLogin", comment: nil) }

      /// en translation: verificatCode
      ///
      /// Key: VerificatCode
      ///
      /// Locales: en, zh-Hans
      var verificatCode: RswiftResources.StringResource { .init(key: "VerificatCode", tableName: "Localizable", source: source, developmentValue: "verificatCode", comment: nil) }

      /// en translation: VerificationCode
      ///
      /// Key: VerificationCode
      ///
      /// Locales: en, zh-Hans
      var verificationCode: RswiftResources.StringResource { .init(key: "VerificationCode", tableName: "Localizable", source: source, developmentValue: "VerificationCode", comment: nil) }

      /// en translation: verificationCodeCannotbeempty
      ///
      /// Key: VerificationCodeCannotbeempty
      ///
      /// Locales: en, zh-Hans
      var verificationCodeCannotbeempty: RswiftResources.StringResource { .init(key: "VerificationCodeCannotbeempty", tableName: "Localizable", source: source, developmentValue: "verificationCodeCannotbeempty", comment: nil) }

      /// en translation: Viewfeedback
      ///
      /// Key: Viewfeedback
      ///
      /// Locales: en, zh-Hans
      var viewfeedback: RswiftResources.StringResource { .init(key: "Viewfeedback", tableName: "Localizable", source: source, developmentValue: "Viewfeedback", comment: nil) }
    }
  }

  /// This `_R.image` struct is generated, and contains static references to 58 images.
  struct image {
    let bundle: Foundation.Bundle

    /// Image `curriculum_nomal`.
    var curriculum_nomal: RswiftResources.ImageResource { .init(name: "curriculum_nomal", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `curriculum_select`.
    var curriculum_select: RswiftResources.ImageResource { .init(name: "curriculum_select", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `home_normal`.
    var home_normal: RswiftResources.ImageResource { .init(name: "home_normal", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `home_select`.
    var home_select: RswiftResources.ImageResource { .init(name: "home_select", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `icon_21tian`.
    var icon_21tian: RswiftResources.ImageResource { .init(name: "icon_21tian", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `icon_back`.
    var icon_back: RswiftResources.ImageResource { .init(name: "icon_back", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `icon_biaoqian`.
    var icon_biaoqian: RswiftResources.ImageResource { .init(name: "icon_biaoqian", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `icon_button`.
    var icon_button: RswiftResources.ImageResource { .init(name: "icon_button", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `icon_cha`.
    var icon_cha: RswiftResources.ImageResource { .init(name: "icon_cha", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `icon_cihuijiance`.
    var icon_cihuijiance: RswiftResources.ImageResource { .init(name: "icon_cihuijiance", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `icon_class`.
    var icon_class: RswiftResources.ImageResource { .init(name: "icon_class", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `icon_close`.
    var icon_close: RswiftResources.ImageResource { .init(name: "icon_close", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `icon_close_player`.
    var icon_close_player: RswiftResources.ImageResource { .init(name: "icon_close_player", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `icon_cuotiben`.
    var icon_cuotiben: RswiftResources.ImageResource { .init(name: "icon_cuotiben", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `icon_cuowu`.
    var icon_cuowu: RswiftResources.ImageResource { .init(name: "icon_cuowu", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `icon_cutticulumbg`.
    var icon_cutticulumbg: RswiftResources.ImageResource { .init(name: "icon_cutticulumbg", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `icon_cutticulumsubbg`.
    var icon_cutticulumsubbg: RswiftResources.ImageResource { .init(name: "icon_cutticulumsubbg", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `icon_dayin`.
    var icon_dayin: RswiftResources.ImageResource { .init(name: "icon_dayin", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `icon_down`.
    var icon_down: RswiftResources.ImageResource { .init(name: "icon_down", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `icon_download`.
    var icon_download: RswiftResources.ImageResource { .init(name: "icon_download", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `icon_downloading`.
    var icon_downloading: RswiftResources.ImageResource { .init(name: "icon_downloading", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `icon_fangda_44`.
    var icon_fangda_44: RswiftResources.ImageResource { .init(name: "icon_fangda_44", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `icon_fuxi`.
    var icon_fuxi: RswiftResources.ImageResource { .init(name: "icon_fuxi", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `icon_home_backgroud`.
    var icon_home_backgroud: RswiftResources.ImageResource { .init(name: "icon_home_backgroud", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `icon_home_header_bg`.
    var icon_home_header_bg: RswiftResources.ImageResource { .init(name: "icon_home_header_bg", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `icon_home_tablebg`.
    var icon_home_tablebg: RswiftResources.ImageResource { .init(name: "icon_home_tablebg", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `icon_huadong`.
    var icon_huadong: RswiftResources.ImageResource { .init(name: "icon_huadong", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `icon_huadongqian`.
    var icon_huadongqian: RswiftResources.ImageResource { .init(name: "icon_huadongqian", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `icon_lambert`.
    var icon_lambert: RswiftResources.ImageResource { .init(name: "icon_lambert", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `icon_pinyinfakangyiwang`.
    var icon_pinyinfakangyiwang: RswiftResources.ImageResource { .init(name: "icon_pinyinfakangyiwang", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `icon_placeholderImage`.
    var icon_placeholderImage: RswiftResources.ImageResource { .init(name: "icon_placeholderImage", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `icon_select`.
    var icon_select: RswiftResources.ImageResource { .init(name: "icon_select", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `icon_shangke_n`.
    var icon_shangke_n: RswiftResources.ImageResource { .init(name: "icon_shangke_n", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `icon_skbaogao`.
    var icon_skbaogao: RswiftResources.ImageResource { .init(name: "icon_skbaogao", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `icon_suoxiao_88`.
    var icon_suoxiao_88: RswiftResources.ImageResource { .init(name: "icon_suoxiao_88", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `icon_tingxie`.
    var icon_tingxie: RswiftResources.ImageResource { .init(name: "icon_tingxie", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `icon_wancheng`.
    var icon_wancheng: RswiftResources.ImageResource { .init(name: "icon_wancheng", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `icon_xiala`.
    var icon_xiala: RswiftResources.ImageResource { .init(name: "icon_xiala", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `icon_zhengque`.
    var icon_zhengque: RswiftResources.ImageResource { .init(name: "icon_zhengque", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `login_backgroud`.
    var login_backgroud: RswiftResources.ImageResource { .init(name: "login_backgroud", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `login_backgroud_ipad_landsca`.
    var login_backgroud_ipad_landsca: RswiftResources.ImageResource { .init(name: "login_backgroud_ipad_landsca", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `login_backgroud_ipad_portrait`.
    var login_backgroud_ipad_portrait: RswiftResources.ImageResource { .init(name: "login_backgroud_ipad_portrait", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `login_code_bg`.
    var login_code_bg: RswiftResources.ImageResource { .init(name: "login_code_bg", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `login_password_bg`.
    var login_password_bg: RswiftResources.ImageResource { .init(name: "login_password_bg", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `luchImage`.
    var luchImage: RswiftResources.ImageResource { .init(name: "luchImage", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `me_backgroud`.
    var me_backgroud: RswiftResources.ImageResource { .init(name: "me_backgroud", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `me_backgroud_ipad_landsca`.
    var me_backgroud_ipad_landsca: RswiftResources.ImageResource { .init(name: "me_backgroud_ipad_landsca", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `me_backgroud_ipad_portrait`.
    var me_backgroud_ipad_portrait: RswiftResources.ImageResource { .init(name: "me_backgroud_ipad_portrait", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `menotData`.
    var menotData: RswiftResources.ImageResource { .init(name: "menotData", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `mine_normal`.
    var mine_normal: RswiftResources.ImageResource { .init(name: "mine_normal", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `mine_select`.
    var mine_select: RswiftResources.ImageResource { .init(name: "mine_select", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `notData`.
    var notData: RswiftResources.ImageResource { .init(name: "notData", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `pic_beijing`.
    var pic_beijing: RswiftResources.ImageResource { .init(name: "pic_beijing", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `pic_beijing_ipad`.
    var pic_beijing_ipad: RswiftResources.ImageResource { .init(name: "pic_beijing_ipad", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `pic_jiance`.
    var pic_jiance: RswiftResources.ImageResource { .init(name: "pic_jiance", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `pic_kapian`.
    var pic_kapian: RswiftResources.ImageResource { .init(name: "pic_kapian", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `sliding_close_icon`.
    var sliding_close_icon: RswiftResources.ImageResource { .init(name: "sliding_close_icon", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }

    /// Image `update_sliding_icon`.
    var update_sliding_icon: RswiftResources.ImageResource { .init(name: "update_sliding_icon", path: [], bundle: bundle, locale: nil, onDemandResourceTags: nil) }
  }

  /// This `_R.file` struct is generated, and contains static references to 2 resource files.
  struct file {
    let bundle: Foundation.Bundle

    /// Resource file `BaseTabBarItem.json`.
    var baseTabBarItemJson: RswiftResources.FileResource { .init(name: "BaseTabBarItem", pathExtension: "json", bundle: bundle, locale: LocaleReference.none) }

    /// Resource file `__UNI__7F44210.wgt`.
    var __UNI__7F44210Wgt: RswiftResources.FileResource { .init(name: "__UNI__7F44210", pathExtension: "wgt", bundle: bundle, locale: LocaleReference.none) }
  }

  /// This `_R.nib` struct is generated, and contains static references to 3 nibs.
  struct nib {
    let bundle: Foundation.Bundle

    /// Nib `SplashView`.
    var splashView: RswiftResources.NibReference<SplashView> { .init(name: "SplashView", bundle: bundle) }

    func validate() throws {
      if UIKit.UIImage(named: "icon-64", in: bundle, compatibleWith: nil) == nil { throw RswiftResources.ValidationError("[R.swift] Image named 'icon-64' is used in nib 'SplashView', but couldn't be loaded.") }
    }
  }

  /// This `_R.storyboard` struct is generated, and contains static references to 1 storyboards.
  struct storyboard {
    let bundle: Foundation.Bundle
    var launchScreen: launchScreen { .init(bundle: bundle) }

    func launchScreen(bundle: Foundation.Bundle) -> launchScreen {
      .init(bundle: bundle)
    }
    func validate() throws {
      try self.launchScreen.validate()
    }


    /// Storyboard `LaunchScreen`.
    struct launchScreen: RswiftResources.StoryboardReference, RswiftResources.InitialControllerContainer {
      typealias InitialController = UIKit.UIViewController

      let bundle: Foundation.Bundle

      let name = "LaunchScreen"
      func validate() throws {
        if UIKit.UIImage(named: "luchImage", in: bundle, compatibleWith: nil) == nil { throw RswiftResources.ValidationError("[R.swift] Image named 'luchImage' is used in storyboard 'LaunchScreen', but couldn't be loaded.") }
      }
    }
  }
}
