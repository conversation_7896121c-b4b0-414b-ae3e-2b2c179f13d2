package com.dxznjy.alading.methodchannel;

import android.content.Context;
import android.content.Intent;
import android.provider.Settings;

import androidx.annotation.NonNull;

import com.blankj.rxbus.RxBus;
import com.dxznjy.alading.DXApplication;
import com.dxznjy.alading.activity.HomeActivity;
import com.dxznjy.alading.activity.givelessons.MeetingPermissionActivity;
import com.dxznjy.alading.common.Constants;
import com.dxznjy.alading.entity.StudentInfo;
import com.google.gson.Gson;
import com.tencent.bugly.crashreport.CrashReport;
import com.vondear.rxtool.RxSPTool;

import io.dcloud.feature.sdk.DCUniMPSDK;
import io.dcloud.feature.sdk.Interface.IUniMP;
import io.dcloud.feature.unimp.config.UniMPOpenConfiguration;
import io.flutter.Log;
import io.flutter.embedding.engine.plugins.FlutterPlugin;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;

public class OpenMeetMethodchannel implements FlutterPlugin {

    String methodChannelName=Constants.openAndroidIosMeet;
    Context mContext;
    @Override
    public void onAttachedToEngine(@NonNull FlutterPluginBinding binding) {
        mContext=binding.getApplicationContext();
        MethodChannel methodChannel = new MethodChannel(binding.getBinaryMessenger(),methodChannelName);
        methodChannel.setMethodCallHandler(new MethodChannel.MethodCallHandler() {
            @Override
            public void onMethodCall(@NonNull MethodCall call, @NonNull MethodChannel.Result result) {
                Log.i("zgj",call.arguments.toString());
                String methodCall = call.method;
                if(methodCall.equals(Constants.setUserIdForBugly)){
                    CrashReport.initCrashReport(mContext, "c6f390203a", true);
                    CrashReport.setUserId(mContext,call.arguments.toString());
                }else{
                    StudentInfo studentInfo = new Gson().fromJson(call.arguments.toString(), StudentInfo.class);
                    RxSPTool.putString(mContext,Constants.STUDENT_TOKEN,studentInfo.getStudentToken());
                    RxSPTool.putString(mContext,Constants.PARENT_TOKEN,studentInfo.getParentToken());
                    RxSPTool.putString(mContext,Constants.STUDENT_CODE,studentInfo.getStudentCode());
                    RxSPTool.putString(mContext,Constants.MERCHANT_CODE,studentInfo.getMerchantCode());
                    RxSPTool.putString(mContext,Constants.SELECTED,studentInfo.getSelected()+"");
                    if (methodCall.equals(Constants.openLessonsList)){//打开课程列表
                        startUserActivity(mContext, HomeActivity.class);
                    }else if (methodCall.equals(Constants.openLessonsDetail)){//打开课程上课权限界面
                        Intent intent = new Intent();
                        intent.putExtra("meetingParams",call.arguments.toString());
//                        intent.putExtra("identutyID",studentInfo.getIdentutyID());
                        intent.setClass(mContext, MeetingPermissionActivity.class);
                        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK );
                        mContext.startActivity(intent);
                    }else if (methodCall.equals(Constants.openVideoLessonsLearn)){//打开录播课详情界面
                        toUniappPage(studentInfo);
                    }
                }
            }
        });
    }

    @Override
    public void onDetachedFromEngine(@NonNull FlutterPluginBinding binding) {

    }
    /**
     * 启动新的Activity
     * @param context 当前Activity
     * @param cls     要启动的Activity的类
     */
    public void startUserActivity(Context context , Class cls){
        Intent intent = new Intent();
        intent.setClass(context, cls);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK );
        context.startActivity(intent);
    }
    /**
     * 打开录播课微信小程序页面
     */
    public void toUniappPage(StudentInfo studentInfo){
//        startActivity(new Intent(getActivity(),MeetingPermissionActivity.class));
        UniMPOpenConfiguration uniMPOpenConfiguration=new UniMPOpenConfiguration();
        try {
                uniMPOpenConfiguration.path = Constants.COURSE_VIDEO_DEATAIL
                        +"&token="+studentInfo.getStudentToken()
                        +"&studentCode="+studentInfo.getStudentCode()
                        +"&memberCode="+studentInfo.getMerchantCode()
                        +"&courseId="+studentInfo.getCourseId()
                        +"&userId="+studentInfo.getUserId();
            android.util.Log.i("zgj", uniMPOpenConfiguration.path);
//            uniMPOpenConfiguration.splashClass = MySplashView.class;
            DXApplication.iUniMP = DCUniMPSDK.getInstance().openUniMP(mContext, Constants.XCX_PKG_NAME,uniMPOpenConfiguration);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
