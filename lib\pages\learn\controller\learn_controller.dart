import 'dart:convert';
import 'dart:io';
import 'package:bot_toast/bot_toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:student_end_flutter/bean/base_bean/open_dxn_uniapp.dart';
import 'package:student_end_flutter/bean/curriculum_entity.dart';
import 'package:student_end_flutter/bean/learn_tool_entity.dart';
import 'package:student_end_flutter/common/ne_user_option.dart';
import 'package:student_end_flutter/components/toast_utils.dart';
import 'package:student_end_flutter/pages/learn/lesson_list_page.dart';
import 'package:student_end_flutter/utils/android_ios_plugin.dart';
import 'package:student_end_flutter/utils/httpRequest.dart';
import '../../../bean/aiRead_member_bean_entity_entity.dart';
import '../../../bean/course_record_list_bean_entity.dart';
import '../../../bean/deliver_bean_entity.dart';
import '../../../bean/last_record_course_bean_entity.dart';
import '../../../bean/last_student_info_bean_entity.dart';
import '../../../bean/pyf_memberCode_bean_entity_entity.dart';
import '../../../bean/review_course_list_bean_entity.dart';
import '../../../bean/student_app_bean_entity.dart';
import '../../../bean/student_info_bean_entity.dart';
import '../../../bean/student_token_bean_entity.dart';
import '../../../bean/user_info_bean_entity.dart';
import '../../../common/Config.dart';
import '../../../common/user_option.dart';
import '../../../common/sensors_analytics_option.dart';
import '../../../dialog/dialog_learn_select_identity.dart';
import '../../../dialog/dialog_learn_select_school.dart';
import '../../../dialog/dialog_learn_select_student.dart';
import '../../../dialog/dialog_course_progress_school.dart';
import '../../../generated/json/base/json_convert_content.dart';
import 'package:get/get.dart';
import '../../../navigator/learn_navigator.dart';
import '../../../utils/event_bus_utils.dart';
import '../../../utils/native_channel_utils.dart';
import '../../../utils/ne_metting_utils.dart';
import '../../base/controller/base_controller.dart';

class LearnController extends GetxController
    with GetSingleTickerProviderStateMixin, WidgetsBindingObserver {
  RxString learnTitle = "学习服务".obs;
  RxList<Map> learnClickList = RxList<Map>([]);
  int clickType = 0; //0 跳转原生会议列表 1 拼音法趣味复习 2:
  String meetingNum = "";
  BuildContext? context;
  TabController? tabController;
  var currentTabIndex = 0.obs;
  double bottomPosW = 30.w;
  // Rx<DeliverBeanEntity?> deliverBeanEntity = DeliverBeanEntity().obs;
  Rx<LastRecordCourseBeanEntity?> recordBeanEntity =
      LastRecordCourseBeanEntity().obs;

  RxInt choseIndex = 0.obs;
  RxList<StudentInfoBeanEntity> studentsList =
      RxList<StudentInfoBeanEntity>([]);
  var percentageList = [];
  Rx<UserInfoBeanEntity> userInfoBeanEntity = UserInfoBeanEntity().obs;
  RxString studentName = "".obs;
  RxString studentCode = "".obs;
  RxString memberCode = "".obs;
  RxString memberName = "".obs;
  // String useHours = '';
  // String haveHours = '';
  RxString useHours = ''.obs;
  RxString haveHours = ''.obs;
  int grade = 1;
  double? percentage = 0;
  String studentToken = "";
  var refreshTime;
  bool isDisable = false;
  String PYFcurriculumId = "1283343602584145920"; //正式
  // 测试 1223288510123233280   正式 1283284867302313984
  String CurriculumId = "1283284867302313984"; //正式

  String identutyID = "0";
  bool initFinish = false;
  bool hasEnteredMyStudents = false;
  bool _isShowingStudentDialog = false;
  bool _isHandlingEmptyRecord = false;

  RxList<LearnToolEntity> generalToolsList = RxList<LearnToolEntity>([]);

  RxList<CurriculumEntity> curriculumList = RxList<CurriculumEntity>([]);

  RxList<CurriculumCurriculumTool> recentlyToolList =
      RxList<CurriculumCurriculumTool>([]);
  List<AiReadMemberBeanEntityEntity> resList = [];
  List<dynamic> latestCourseList = []; // 使用dynamic类型
  Map? lastClickItem; // 记录最后点击的项目
  bool _isFormalStudent = false;


  var courseType = 0.obs;
  Rx<CourseRecordListBeanData> courseRecordListBeanData = CourseRecordListBeanData().obs;
  Rx<ReviewCourseListBeanData> reviewCourseListBeanData = ReviewCourseListBeanData().obs;

  Rx<DeliverBeanEntity> deliverBeanEntity = DeliverBeanEntity().obs;




  @override
  Future<void> onInit() async {
    UserOption.getUserInfoRequest(false);
    WidgetsBinding.instance.addObserver(this);
    EventBusUtils.eventBus.on<EventUtils>().listen((event) {
      if (event.message == "refreshUserInfo") {
        var now = DateTime.now();
        if (refreshTime == null) {
          refreshTime = now;
          refreshUserInfo();
        }
        if (now.difference(refreshTime) > Duration(seconds: 2)) {
          refreshUserInfo();
          refreshTime = now;
        }
        print('refreshUserInfo');
      }
    });
    super.onInit();
    // 首先初始化 TabController
    tabController = TabController(vsync: this, length: 2);
    tabController?.addListener(() {
      if (!tabController!.indexIsChanging) {
        currentTabIndex.value = tabController!.index;
        changeTab(currentTabIndex.value);
      }
    });

    learnTitle.value = "学习服务";
    // await updateLearnCenterVisibility();

    registerHandler();
    await queryMerchantAndStudentRecord();

    // 如果有学员信息但没有学时数据，主动获取
    if (studentCode.value.isNotEmpty && (useHours.value.isEmpty || haveHours.value.isEmpty)) {
      await getMemberStudentList();
    }
    initFinish = true;

    // 监听 eventBus 事件：刷新最近一节交付课
    EventBusUtils.eventBus.on<EventUtils>().listen(_onRefreshLastClass);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    //页面恢复时 刷新交付课 录播课
    if (state == AppLifecycleState.resumed) {
      // 重置标题为"学习服务"
      learnTitle.value = "学习服务";
      // learnClickList.value = getLearnParentList();

      if (Get.isDialogOpen == true) {
        return;
      }
      getStudentsList().then((_) {
        if (studentCode.value.isNotEmpty) {
          getLatestDeliverCourse();
          getLatestRecordCourse();
        } else if (studentsList.isNotEmpty) {
          //无学员 添加学员恢复页面刷新学员和门店数据
          var response = HttpUtil().get(Config.queryMerchantAndStudentRecord);
          response.then((value) async {
            if (value == null) {
              if (!hasEnteredMyStudents) {
                showSelectStudentDialog(false);
              }
            } else {
              LastStudentInfoBeanEntity beanEntity =
                  JsonConvert.fromJsonAsT<LastStudentInfoBeanEntity>(value)!;
              studentName.value = beanEntity.studentName;
              studentCode.value = beanEntity.studentCode;
              if (beanEntity.merchantVos != null &&
                  beanEntity.merchantVos.isNotEmpty) {
                memberCode.value = beanEntity.merchantVos[0].merchantCode;
              }
              if (percentageList.isNotEmpty) {
                showSelectPercentage(percentageList);
              } else {
                getMemberStudentList();
              }
            }
          });
        }
      });
    } else if (state == AppLifecycleState.paused) {
      refreshTime = null;
    }
  }

  ///清空数据
  cleanStudentData() {
    learnTitle.value = "学习服务";
    // learnClickList.value = getLearnParentList();
    studentName.value = "";
    studentCode.value = "";
    memberCode.value = "";
    currentTabIndex.value = 0;
    deliverBeanEntity.value = DeliverBeanEntity();
    recordBeanEntity.value = LastRecordCourseBeanEntity();
    userInfoBeanEntity.value = UserInfoBeanEntity();
    percentageList.clear();
    // useHours = '';
    // haveHours = '';
    useHours.value = '';
    haveHours.value = '';
    grade = 1;
    percentage = 0;
    studentToken = "";
  }

  /// gotoMeeting
  registerHandler() {
    /// 注册方法给原生调用 加入会议
    // NativeBridge.registerHandler(NativeMethods.onEnterMeeting, (params) async {
    //   String identutyID = "";
    //   if (Platform.isIOS) {
    //     meetingNum = params["meetingNum"];
    //     identutyID = params["identutyID"];
    //   } else {
    //     Map<String, dynamic> data = json.decode(params);
    //     meetingNum = data["meetingNum"];
    //     identutyID = data["identutyID"];
    //   }
    //   UserType type = identutyID == "0" ? UserType.student : UserType.patriarch;
    //   ENUserOption.yunXinUserRecord(studentCode.value, type);
    //   String name =
    //       identutyID == "0" ? studentName.value : "${studentName.value}家长";
    //   if (!ENUserOption.iSLogin) {
    //     ENUserOption.getYunXinToken(studentCode.value, type, (succeed) async {
    //       if (succeed) {
    //         NEMeetingUtil.onEnterMeeting(
    //             meetingNum: meetingNum, realName: name);
    //       }
    //     });
    //   } else {
    //     NEMeetingUtil.onEnterMeeting(meetingNum: meetingNum, realName: name);
    //   }
    // });
  }

  refreshUserInfo() async {
    if (userInfoBeanEntity.value.userId !=
        UserOption.userInfoBeanEntity?.userId) {
      await getStudentsList();
      queryMerchantAndStudentRecord();
    } else {
      Future.delayed(Duration(seconds: 1), () {
        queryRecentlyToolList();
        queryCourseToolList();
        queryGeneralToolList();
      });
    }
    userInfoBeanEntity.value = UserOption.userInfoBeanEntity!;
    // await updateLearnCenterVisibility();
  }

  getHead() {
    if (userInfoBeanEntity.value.headPortrait == "") {
      return AssetImage("assets/mine/home_avaUrl.png");
    }
    return NetworkImage(userInfoBeanEntity.value.headPortrait);
  }

  @override
  void onClose() {
    _isShowingStudentDialog = false;
    _isHandlingEmptyRecord = false;
    if (tabController != null) {
      tabController!.removeListener(() {});
      tabController!.dispose();
      tabController = null;
    }
    WidgetsBinding.instance.removeObserver(this);
    print('vvvvvvvvvvvvvvvvvvvvvvvvvvvvvlearn');
    super.onClose();
  }

  changeTab(index) {
    if (tabController == null) return;
    currentTabIndex.value = index;
    if (tabController!.index != index) {
      tabController!.animateTo(index);
    }
  }

  /// 查询记录的门店和学生信息
  queryMerchantAndStudentRecord() {
    if (studentsList.isEmpty) {
      queryGeneralToolList();
      recentlyToolList.clear();
      curriculumList.clear();
      return;
    }
    var response = HttpUtil().get(Config.queryMerchantAndStudentRecord);
    response.then((value) async {
      if (value == null) {
        showSelectStudentDialog(false);
      } else {
        LastStudentInfoBeanEntity beanEntity =
            JsonConvert.fromJsonAsT<LastStudentInfoBeanEntity>(value)!;
        studentName.value = beanEntity.studentName;
        studentCode.value = beanEntity.studentCode;
        memberCode.value = beanEntity.merchantVos[0].merchantCode;
        // 先获取学时数据，再显示
        await getMemberStudentList();

        if (beanEntity.merchantVos != null &&
            beanEntity.merchantVos.isNotEmpty) {
          memberCode.value = beanEntity.merchantVos[0].merchantCode;
        }
        // 从学生列表中查找当前学生的 isFormal 状态
        var currentStudent = studentsList.firstWhere(
          (student) => student.studentCode == beanEntity.studentCode,
          orElse: () => StudentInfoBeanEntity(),
        );
        _isFormalStudent = currentStudent.isFormal;
        // learnClickList.value = getLearnParentList();

        if (percentageList.isNotEmpty) {
          showSelectPercentage(percentageList);
        } else {
          await getMemberStudentList();
        }
        getLatestDeliverCourse();
        getLatestRecordCourse();

        queryGeneralToolList();
        queryRecentlyToolList();
        queryCourseToolList();

        getStudentLatestCourse();
      }
    });
  }

  showSelectStudentDialog(isClick) async {
    print('444444444444444444444444444444');
    if (Get.isDialogOpen == true) {
      return;
    }
    if (isClick) {
      ToastUtil.showLoadingDialog();
    }

    await getStudentsList();
    if (isClick) {
      ToastUtil.closeLoadingDialog();
    }

    /// 当只有一个学员时 直接显示
    print(studentsList.length);
    print('111111111111111111111111111111');

    if (studentsList.length == 1) {
      selectStudentFunc(0);
    } else if (studentsList.isEmpty) {
      ToastUtil.showShortErrorToast("未获取到学员信息");
    } else {
      BaseController controller = Get.find<BaseController>();
      if (controller.currentIndex.value == 1) {
        Get.dialog(
          LearnSelectStudentDialog(studentsList, selectStudentFunc),
          barrierDismissible: false,
        );
        // navigateToPermissionPage();
      }
    }
  }

  /// 处理学员选择
  selectStudentFunc(index) async {
    // 立即更新学练中心入口的显示状态
    _isFormalStudent = studentsList[index].isFormal;
    // learnClickList.value = getLearnParentList();

    // 更新其他状态
    studentName.value = studentsList[index].studentName;
    studentCode.value = studentsList[index].studentCode;

    //记录选择学员埋点
    TrackingUtils.trackEvent("click_study_service_student",
        {"student_name": studentName.value, "student_code": studentCode.value});

    choseIndex.value = index;
    // memberCode.value = studentsList[index].merchantVos[0].merchantCode;

    if (studentsList[index].merchantVos.length > 1) {
      // 多个门店，显示选择门店弹窗
      showSelectSchoolDialog(studentsList[index].merchantVos);
    } else if (studentsList[index].merchantVos.isNotEmpty) {
      // 只有一个门店，直接使用
      memberCode.value = studentsList[index].merchantVos[0].merchantCode;
      saveMerchantAndStudentList();
      getLatestDeliverCourse();
      if (percentageList.isNotEmpty) {
        showSelectPercentage(percentageList);
      } else {
        getMemberStudentList();
      }
      // 检查学习记录
      checkStudentLearningRecords();
    } else {
      // 没有门店信息，直接检查学习记录
      memberCode.value = "";
      if (percentageList.isNotEmpty) {
        showSelectPercentage(percentageList);
      } else {
        getMemberStudentList();
      }
      checkStudentLearningRecords();
    }

    /// 判断如果是鼎英语课程进度的点击，则进行特殊处理
    // if (lastClickItem != null && lastClickItem!["title"] == "鼎英语课程进度") {
    //   // 处理门店选择
    //   if (studentsList[index].merchantVos.length > 1) {
    //     // 多个门店，显示选择门店弹窗
    //     showSelectSchoolDialog(studentsList[index].merchantVos);
    //   } else if (studentsList[index].merchantVos.isNotEmpty) {
    //     // 只有一个门店，直接使用
    //     memberCode.value = studentsList[index].merchantVos[0].merchantCode;
    //     saveMerchantAndStudentList();
    //     getLatestDeliverCourse();
    //     if (percentageList.isNotEmpty) {
    //       showSelectPercentage(percentageList);
    //     } else {
    //       getMemberStudentList();
    //     }
    //     // 检查学习记录
    //     checkStudentLearningRecords();
    //   } else {
    //     // 没有门店信息，直接检查学习记录
    //     memberCode.value = "";
    //     checkStudentLearningRecords();
    //   }
    //   return;
    // }

    // 其他功能的正常处理流程
    saveMerchantAndStudentList();
    getLatestDeliverCourse();
    getLatestRecordCourse();
    if (percentageList.isNotEmpty) {
      showSelectPercentage(percentageList);
    } else {
      if (percentageList.isNotEmpty) {
        showSelectPercentage(percentageList);
      } else {
        getMemberStudentList();
      }
      memberCode.value = "";
      deliverBeanEntity.value = DeliverBeanEntity();
      saveMerchantAndStudentList();
      getLatestDeliverCourse();
      getLatestRecordCourse();
      getStudentLatestCourse();

      getMemberStudentList();
    }
  }

  showSelectPercentage(percentageList) {
    // useHours = '';
    // haveHours = '';
    useHours.value = '';
    haveHours.value = '';
    percentage = 0;
    bool found = false;
    for (int i = 0; i < percentageList.length; i++) {
      if (memberCode.value.isEmpty) {
        if (percentageList[i]['studentCode'] == studentCode.value) {
          // useHours = percentageList[i]['useHours'];
          // haveHours = percentageList[i]['haveHours'];
          useHours.value = percentageList[i]['useHours'];
          haveHours.value = percentageList[i]['haveHours'];
          grade = percentageList[i]['grade'];
          percentage = double.tryParse(percentageList[i]['rate']);
          found = true;
          break;
        }
      } else {
        if (percentageList[i]['merchantCode'] == memberCode.value &&
            percentageList[i]['studentCode'] == studentCode.value) {
          // useHours = percentageList[i]['useHours'];
          // haveHours = percentageList[i]['haveHours'];
          useHours.value = percentageList[i]['useHours'];
          haveHours.value = percentageList[i]['haveHours'];
          grade = percentageList[i]['grade'];
          percentage = double.tryParse(percentageList[i]['rate']);
          found = true;
          break;
        }
      //   else {
        //           grade = percentageList[0]['grade'];
        //           // useHours = percentageList[0]['useHours'] ?? '';
        //           // haveHours = percentageList[0]['haveHours'] ?? '';
        //           useHours.value = percentageList[0]['useHours'] ?? '';
        //           haveHours.value = percentageList[0]['haveHours'] ?? '';
        //           percentage = double.tryParse(percentageList[0]['rate']) ?? 0;
        //
        //           return;
        //         }
      }
    }
    // 如果没有找到匹配的数据，使用第一条数据作为默认值
    if (!found && percentageList.isNotEmpty) {
      grade = percentageList[0]['grade'];
      useHours.value = percentageList[0]['useHours'] ?? '';
      haveHours.value = percentageList[0]['haveHours'] ?? '';
      percentage = double.tryParse(percentageList[0]['rate']) ?? 0;
    }
  }

  /// 显示选择门店弹窗
  showSelectSchoolDialog(schoolData) {
    Get.dialog(
      LearnSelectSchoolDialog(
          schoolData, (index) => selectSchoolFunc(index, schoolData)),
      barrierDismissible: false,
    );
  }

  selectSchoolFunc(int index, List<StudentInfoBeanMerchantVos> merchantData) {
    memberName.value = merchantData[index].merchantName;
    memberCode.value = merchantData[index].merchantCode;
    // getLatestDeliverCourse();
    // getLatestRecordCourse();
    if (percentageList.isNotEmpty) {
      showSelectPercentage(percentageList);
    } else {
      getMemberStudentList();
    }
    if (index < 0 || index >= merchantData.length) {
      return;
    }
    //记录选择学员埋点
    TrackingUtils.trackEvent("click_study_service_student", {
      "student_name": studentName.value,
      "student_code": studentCode.value,
      "merchant_name": merchantData[index].merchantName,
      "merchant_code": merchantData[index].merchantCode
    });

    memberName.value = merchantData[index].merchantName;
    memberCode.value = merchantData[index].merchantCode;
  }

  /// 获取学员列表
  getStudentsList() async {
    var response = await HttpUtil().get(Config.getMerchantAndStudentList);
    if (response != null) {
      studentsList.value =
          jsonConvert.convertListNotNull<StudentInfoBeanEntity>(response)!;
    }
    // await updateLearnCenterVisibility();
  }

  /// 获取学生最近课程
  getStudentLatestCourse() async {
    var response = await HttpUtil().get(
        '${Config.getStudentLatestCourse}?studentCode=${studentCode.value}');
    if (response != null) {
      if(response is Map) {
        if (response.containsKey('courseType')) {
          courseType.value = response['courseType'];
          if (response['courseType'] == 3) {
            courseRecordListBeanData.value = jsonConvert.convert(response['piUserCourseVo'])!;
          }
          else if (response['courseType'] == 2) {
            reviewCourseListBeanData.value = jsonConvert.convert(response['meetingInfoOneToManyVo'])!;
          }
          else if (response['courseType'] == 1) {
            deliverBeanEntity.value =  jsonConvert.convert(response['deliverCourseVO'])!;
          }
        }
      }
    }
    // await updateLearnCenterVisibility();
  }




  getMemberStudentList() async {
    var response = await HttpUtil().get(
        '${Config.getMemberStudentList}?memberId=${UserOption.userInfoBeanEntity?.userCode}');
    percentageList = response;
    if (percentageList.isNotEmpty && studentName.value.isNotEmpty) {
      showSelectPercentage(percentageList);

    }
  }

  queryMyCourseInfo() async {
    var response = await HttpUtil().get(
        '${Config.getMemberStudentList}?memberId=${UserOption.userInfoBeanEntity?.userCode}');
    percentageList = response;
    if (percentageList.isNotEmpty && studentName.value.isNotEmpty) {
      showSelectPercentage(percentageList);

    }
  }

  /// 保存用户选择
  saveMerchantAndStudentList() {
    var response = HttpUtil().get(
        "${Config.saveMerchantAndStudentList}?merchantCode=${memberCode.value}&studentCode=${studentCode.value}");
  }

  ///获取学生token
  getStudentToken(String? routePath, {member = ""}) {
    if (studentCode.value.isEmpty) {
      ToastUtil.showShortErrorToast("请先选择学员");
      return;
    }
    if (clickType == 5) {
      //录播课 继续学习-视频界面
      StudentAppBeanEntity studentAppBeanEntity = StudentAppBeanEntity();
      studentAppBeanEntity.studentToken = UserOption.token;
      studentAppBeanEntity.parentToken = UserOption.token;
      studentAppBeanEntity.studentCode = studentCode.value;
      studentAppBeanEntity.merchantCode = memberCode.value;
      studentAppBeanEntity.courseId = recordBeanEntity.value!.courseId;
      studentAppBeanEntity.userId = recordBeanEntity.value!.userId;
      studentAppBeanEntity.baseUrl = Config.URL;
      AndroidIosPlugin.openVideoLessonsLearn(jsonEncode(studentAppBeanEntity));
      return;
    }
    if (member == "") {
      member = memberCode.value;
    }
    BotToast.showLoading();
    String request =
        "${Config.getStudentToken}?memberToken=${UserOption.token}&studentCode=${studentCode.value}&merchantCode=$member";
    var response = HttpUtil().get(request);
    response.then((value) async {
      isDisable = false;
      StudentTokenBeanEntity studentTokenBeanEntity =
          JsonConvert.fromJsonAsT<StudentTokenBeanEntity>(value)!;
      studentToken = studentTokenBeanEntity.token;
      ENUserOption.studentToken = studentTokenBeanEntity.token;
      UserOption.studentToken = studentToken;
      // //学生token
      switch (clickType) {
        case 0: //学习 -更多
          Get.to(() => LessonListPage(), arguments: {
            "currentTabIndex": currentTabIndex.value,
            "studentCode": studentCode.value,
            "merchantCode": memberCode.value,
            "studentName": studentName.value,
            "studentToken": studentToken
          });
          break;
        case 1: //AI智阅
          String uniApp = LearnNavigator.aiIntelligentWords(
              merchantCode: memberCode.value,
              studentCode: studentCode.value,
              logintokenReview: studentToken)
              .path;
          AndroidIosPlugin.openUniApp(uniApp);
          break;
        case 2: //学习 鼎英语趣味复习
          String uniApp = LearnNavigator.funReview(
                  studentCode: studentCode.value,
                  merchantCode: memberCode.value,
                  logintokenReview: studentToken)
              .path;
          AndroidIosPlugin.openUniApp(uniApp);
          break;
        case 3: //学能一课一练
          OpenDxnUniapp openDxnUniapp = OpenDxnUniapp();
          openDxnUniapp.studentName = studentName.value;
          openDxnUniapp.token = studentToken;
          openDxnUniapp.code = studentCode.value;
          openDxnUniapp.grade = grade.toString();
          AndroidIosPlugin.openDxnUniApp(jsonEncode(openDxnUniapp));
          break;
        case 4: //交付课-开始上课 加入会议
          {

            String phone = UserOption.mobile;
            if (identutyID == '6') { // 如果是学生
              phone = studentCode.value;
            }

            var response = HttpUtil().get(
                '${Config.getAccountByType}?phone=$phone&userType=$identutyID');
            response.then((value) async {
              String userUuid = '';
              String userToken = '';
              String schoolId = '';
              String userType = '';
              String scene = '';
              if (value is Map) {
                if (value.containsKey('userUuid')) {
                  userUuid = value["userUuid"];
                }
                if (value.containsKey('userToken')) {
                  userToken = value["userToken"];
                }
                if (value.containsKey('appId')) {
                  schoolId = value["appId"];
                }
                if (value.containsKey('userType')) {
                  userType = value["userType"].toString();
                }
                if (value.containsKey('scene')) {
                  scene = value["scene"];
                }
                var saveTokenResponse =  HttpUtil().post('${Config.saveToken}?token=${UserOption.token}');
                saveTokenResponse.then((response) async {
                  if (response!.data is Map) {
                    Map dataMap = response.data as Map;
                    if (dataMap.containsKey('data')) {
                      String tokenKey = dataMap['data'];
                      Map params = {
                        'schoolId': schoolId,
                        'classId': meetingNum,
                        'userId': userUuid,
                        'token': userToken,
                        'role': userType,
                        'token_key': tokenKey,
                        'studentCode':studentCode.value,
                        'scene' : scene,
                      };
                      AndroidIosPlugin.openLessonsDetail(jsonEncode(params));
                    }
                  }
                });
              }
            });
          }
      }
    });
  }

  /// 获取距当前时间最近的交付课
  getLatestDeliverCourse() {
    // var response = HttpUtil().get(
    //     '${Config.getLatestDeliverCourse}?studentCode=${studentCode.value}&merchantCode=${memberCode.value}');
    // response.then((value) async {
    //   if (value != null) {
    //     deliverBeanEntity.value = JsonConvert.fromJsonAsT<DeliverBeanEntity>(value);
    //   } else {
    //     deliverBeanEntity.value = DeliverBeanEntity();
    //   }
    // });
  }

  /// 查询上次学习的录播课
  getLatestRecordCourse() {
    var response = HttpUtil().get(
        '${Config.getLatestRecordCourse}?studentCode=${studentCode.value}');
    response.then((value) async {
      if (value != null) {
        recordBeanEntity.value =
            JsonConvert.fromJsonAsT<LastRecordCourseBeanEntity>(value);
      } else {
        recordBeanEntity.value = LastRecordCourseBeanEntity();
      }
    });
  }

  getCourseMeetingInfo() {
    BotToast.showLoading(); // 家长巡课 增加loading

    // if (response['courseType'] == 3) {
    //   courseRecordListBeanData.value = jsonConvert.convert(response['piUserCourseVo'])!;
    // }
    // else if (response['courseType'] == 2) {
    //   reviewCourseListBeanData.value = jsonConvert.convert(response['meetingInfoOneToManyVo'])!;
    // }
    // else if (response['courseType'] == 1) {
    //   deliverBeanEntity.value =  jsonConvert.convert(response['deliverCourseVO'])!;
    // }
    if (courseType.value == 3) {
      ENUserOption.studyId = courseRecordListBeanData.value.courseId ?? "";
      ENUserOption.curriculum =
          courseRecordListBeanData.value.courseType.toString() ?? "";
    } else if (courseType.value == 1) {
      ENUserOption.studyId = deliverBeanEntity.value.courseId ?? "";
      ENUserOption.curriculum = deliverBeanEntity.value.courseType ?? "";
      if (deliverBeanEntity.value.oneToManyType == "1") {
        ENUserOption.studyId = deliverBeanEntity.value.classPlanStudyId ?? "";
      }
    } else if (courseType.value == 2) {
      ENUserOption.studyId = reviewCourseListBeanData.value.courseId ?? "";
      // ENUserOption.curriculum = reviewCourseListBeanData.value.courseType ?? "";
    }

    var response = HttpUtil()
        .get('${Config.getCourseMeetingInfo}?courseId=${ENUserOption.studyId}');
    response.then((value) async {
      if (value != null) {
        String meetingNum = value["meetingNum"];
        goToAndroidIosStartLessons(meetingNum);
      } else {
        // ToastUtil.showShortErrorToast("获取上课数据失败~");
      }
    });
  }

  /// 学习服务模块点击
  learnClick(dynamic item) async {
    String title = "";
    if (item is CurriculumCurriculumTool) {
      title = item.toolName;
    } else if (item is LearnToolEntity) {
      title = item.toolName;
    }
    switch (title) {
      case "我的学员":
        hasEnteredMyStudents = true;
        if (item is CurriculumCurriculumTool || item is LearnToolEntity) {
          String uniApp = LearnNavigator.jumpLearnTool(
                  routePath: item.appJumpAddress,
                  merchantCode: memberCode.value,
                  studentCode: studentCode.value,
                  trialname: studentName.value,
                  logintokenReview: studentToken)
              .path;
          AndroidIosPlugin.openUniApp(uniApp);
        }
        break;
      case "拼音法趣味复习":
        if (item is CurriculumCurriculumTool) {
          goToPYFFunReview(item.appJumpAddress);
        }
        break;
      case "学能一课一练":
        if (item is CurriculumCurriculumTool) {
          var result = await getStudentCourseSummary();
          if (result == null) {
            ToastUtil.showShortErrorToast("您还没学习过学能正式课程");
            return;
          }
          clickType = 3;
          getStudentToken(item.appJumpAddress);
        }
        break;
      case "AI智阅":
        clickType = 1;
        goToAIRead();
        break;
      case "新阅读理解抗遗忘":
        if (item is CurriculumCurriculumTool) {
          goToReadForget(item.appJumpAddress);
        }
        break;
      case "趣味复习":
        clickType = 2;
        getStudentToken(item.appJumpAddress);
        break;
      case "鼎英语课程进度":
        goToCourseProgress();
        break;
      default:
        {
          if (item is CurriculumCurriculumTool || item is LearnToolEntity) {
            String uniApp = LearnNavigator.jumpLearnTool(
                    routePath: item.appJumpAddress,
                    merchantCode: memberCode.value,
                    studentCode: studentCode.value,
                    trialname: studentName.value,
                    logintokenReview: studentToken)
                .path;
            AndroidIosPlugin.openUniApp(uniApp);
          }
        }
        break;
    }
  }

  learnBackClick() {
    learnTitle.value = "学习服务";
    // learnClickList.value = getLearnParentList();
  }

  /// 学习-录播课交付课更多跳转
  goToAndroidIosList() {
    clickType = 0;
    getStudentToken(null);
  }

  /// 学习-交付课开始上课
  goToAndroidIosStartLessons(String meetNum) {
    meetingNum = meetNum;
    clickType = 4;
    getStudentToken(null);
  }

  /// 学习-录播课开始上课
  goToAndroidIosVideoLessons() {
    clickType = 5;
    getStudentToken(null);
  }

  goToPYFFunReview(String routePath) async {
    var response =
        await HttpUtil().get("${Config.getPYFId}?enCode=PYF&enType=CURRICULUM");
    if (response != null) {
      PYFcurriculumId = response["id"];
    }
    var getPYFMemberCode = await HttpUtil().get(
        '${Config.getPYFMemberCode}?studentCode=${studentCode.value}&curriculumId=$PYFcurriculumId');
    if (getPYFMemberCode == null || getPYFMemberCode.length == 0) {
      ToastUtil.showShortErrorToast("您还没有学习过课程");
      return;
    }
    List<PyfMemberCodeBeanEntityEntity> memberList = jsonConvert
        .convertListNotNull<PyfMemberCodeBeanEntityEntity>(getPYFMemberCode)!;
    String memberCodePYF = memberList[0].merchantCode;
    // String uniApp = LearnNavigator.pYFFunReview(
    //         merchantCode: memberCodePYF, studentCode: studentCode.value)
    //     .path;
    String uniApp = LearnNavigator.jumpLearnTool(
      routePath: routePath,
      studentCode: studentCode.value,
      merchantCode: memberCodePYF,
    ).path;
    AndroidIosPlugin.openUniApp(uniApp);
  }

  goToAIRead() async {
    var getAIReadMemberCode = await HttpUtil()
        .get('${Config.getAIReadMemberCode}?studentCode=${studentCode.value}');
    if (getAIReadMemberCode == null || getAIReadMemberCode.length == 0) {
      ToastUtil.showShortErrorToast("您还没有学习过课程");
      return;
    }
    List<AiReadMemberBeanEntityEntity> memberList = jsonConvert
        .convertListNotNull<AiReadMemberBeanEntityEntity>(getAIReadMemberCode)!;
    getStudentToken(null, member: memberList[0].merchantCode);
  }

  goToReadForget(String routePath) async {
    var getAIReadMemberCode = await HttpUtil()
        .get('${Config.getAIReadMemberCode}?studentCode=${studentCode.value}');
    if (getAIReadMemberCode == null || getAIReadMemberCode.length == 0) {
      ToastUtil.showShortErrorToast("您还没有学习过课程");
      return;
    }
    List<AiReadMemberBeanEntityEntity> memberList = jsonConvert
        .convertListNotNull<AiReadMemberBeanEntityEntity>(getAIReadMemberCode)!;

    if (memberList.length == 1) {
      String uniApp = LearnNavigator.jumpLearnTool(
        routePath: routePath,
        studentCode: studentCode.value,
        merchantCode: memberList[0].merchantCode,
      ).path;
      AndroidIosPlugin.openUniApp(uniApp);
    } else {
      List<StudentInfoBeanMerchantVos> schoolData = [];
      for (int i = 0; i < memberList.length; i++) {
        StudentInfoBeanMerchantVos merchant = StudentInfoBeanMerchantVos();
        merchant.merchantName = memberList[i].merchantName;
        merchant.merchantCode = memberList[i].merchantCode;
        schoolData.add(merchant);
      }
      Get.dialog(
        LearnSelectSchoolDialog(schoolData, (index) {
          String uniApp = LearnNavigator.jumpLearnTool(
            routePath: routePath,
            studentCode: studentCode.value,
            merchantCode: memberList[index].merchantCode,
          ).path;
          AndroidIosPlugin.openUniApp(uniApp);
        }),
        barrierDismissible: false,
      );
    }
  }

  ///获取通用工具
  queryGeneralToolList() async {
    Map<String, dynamic> params = {
      'showChannel': '0',
    };
    var response = await HttpUtil().get(Config.queryGeneralTool, data: params);

    if (response != null) {
      generalToolsList.value =
          jsonConvert.convertListNotNull<LearnToolEntity>(response)!;
    }
  }

  ///获取课程大类工具
  queryCourseToolList() async {
    if (studentCode.value.isEmpty) {
      return;
    }
    Map<String, dynamic> params = {
      'showChannel': '0',
      'studentCode': studentCode.value,
    };
    var response = await HttpUtil().get(Config.queryCourseTool, data: params);
    if (response != null) {
      if (response is Map) {
        if (response.containsKey('data')) {
          curriculumList.value = jsonConvert
              .convertListNotNull<CurriculumEntity>(response['data'])!;
        }
      }
    }
  }

  ///获取最近工具
  queryRecentlyToolList() async {
    if (studentCode.value.isEmpty) {
      return;
    }
    Map<String, dynamic> params = {
      'channel': '0',
      'studentCode': studentCode.value,
    };
    var response =
        await HttpUtil().get(Config.queryRecentlyToolList, data: params);
    if (response != null) {
      recentlyToolList.value =
          jsonConvert.convertListNotNull<CurriculumCurriculumTool>(response)!;
    }
  }

  ///工具点击上传
  postToolClick(String toolId) {
    if (studentCode.value.isEmpty) {
      return;
    }
    Map<String, dynamic> params = {
      'channel': '0',
      'studentCode': studentCode.value,
      'toolId': toolId,
    };
    HttpUtil().postBaseBean(Config.postToolClick, data: params);
  }

  /// 前往鼎英语课程进度
  goToCourseProgress() async {
    // 显示加载状态
    ToastUtil.showLoadingDialog();

    // 埋点：点击鼎英语课程进度按钮
    TrackingUtils.trackEvent('DingEnglishCourseProgress', {
      'page': '学习服务',
      'button_name': '鼎英语课程进度',
      'student_code': studentCode.value,
      'merchant_code': memberCode.value
    });

    try {
      // 检查是否有学员编码
      if (studentCode.value.isEmpty) {
        ToastUtil.showShortErrorToast("请先选择学员");
        return;
      }

      // 检查是否有门店编码
      if (memberCode.value.isEmpty) {
        ToastUtil.showShortErrorToast("未找到该学员的门店信息");
        return;
      }

      // 获取学习记录
      var response = await HttpUtil().get(
          '${Config.queryMerchantAndStudentRecord}?studentCode=${studentCode.value}');

      if (response == null || response.isEmpty) {
        // 埋点：没有学习记录
        TrackingUtils.trackEvent('no_learning_records', {
          'student_code': studentCode.value,
          'merchant_code': memberCode.value
        });

        // 显示提示信息
        ToastUtil.showShortErrorToast("请开通或学习课程后再来查看详情");
        return;
      }

      // 埋点：进入课程进度页面
      TrackingUtils.trackEvent('enter_course_progress_page', {
        'student_code': studentCode.value,
        'merchant_code': memberCode.value
      });

      // 直接跳转到鼎英语课程进度页面
      String uniApp = LearnNavigator.courseProgress(
              studentCode: studentCode.value, merchantCode: memberCode.value)
          .path;
      AndroidIosPlugin.openUniApp(uniApp);
    } catch (e) {
      print('进入课程进度失败: $e');
      ToastUtil.showShortErrorToast("获取学习记录失败，请稍后再试");
    } finally {
      // 关闭加载状态
      ToastUtil.closeLoadingDialog();
    }
  }

  /// 检查学员学习记录
  checkStudentLearningRecords() async {
    try {
      // 获取学习记录
      var response = await HttpUtil().get(
          '${Config.queryMerchantAndStudentRecord}?studentCode=${studentCode.value}');

      if (response == null || response.isEmpty) {
        // 埋点：没有学习记录
        TrackingUtils.trackEvent('no_learning_records', {
          'student_code': studentCode.value,
          'merchant_code': memberCode.value
        });

        // 显示提示信息
        ToastUtil.showShortErrorToast("请开通或学习课程后再来查看详情");
        return;
      }

      LastStudentInfoBeanEntity beanEntity =
          JsonConvert.fromJsonAsT<LastStudentInfoBeanEntity>(response)!;

      if (beanEntity.merchantVos != null && beanEntity.merchantVos.isNotEmpty) {
        if (beanEntity.merchantVos.length > 1) {
          // 转换门店列表类型
          List<StudentInfoBeanMerchantVos> merchantList =
              beanEntity.merchantVos.map((merchant) {
            StudentInfoBeanMerchantVos newMerchant =
                StudentInfoBeanMerchantVos();
            newMerchant.merchantCode = merchant.merchantCode;
            newMerchant.merchantName = merchant.merchantName;
            return newMerchant;
          }).toList();

          // 多个门店，显示选择门店弹窗
          Get.dialog(
            CourseProgressSchoolDialog(
              merchantList: merchantList,
              onConfirm: (index) {
                memberCode.value = beanEntity.merchantVos[index].merchantCode;
                // // 埋点：进入课程进度页面
                // TrackingUtils.trackEvent('enter_course_progress_page', {
                //   'student_code': studentCode.value,
                //   'merchant_code': memberCode.value
                // });
                // // 跳转到鼎英语课程进度页面
                // String uniApp = LearnNavigator.courseProgress(
                //         studentCode: studentCode.value,
                //         merchantCode: memberCode.value)
                //     .path;
                // AndroidIosPlugin.openUniApp(uniApp);
              },
              onCancel: () {
                // 处理取消按钮点击
              },
            ),
            barrierDismissible: false,
          );
        } else {
          // 只有一个门店，直接使用
          memberCode.value = beanEntity.merchantVos[0].merchantCode;
          // // 埋点：进入课程进度页面
          // TrackingUtils.trackEvent('enter_course_progress_page', {
          //   'student_code': studentCode.value,
          //   'merchant_code': memberCode.value
          // });
          // // 跳转到鼎英语课程进度页面
          // String uniApp = LearnNavigator.courseProgress(
          //         studentCode: studentCode.value,
          //         merchantCode: memberCode.value)
          //     .path;
          // AndroidIosPlugin.openUniApp(uniApp);
        }
      } else {
        ToastUtil.showShortErrorToast("未找到该学员的门店信息");
      }
    } catch (e) {
      ToastUtil.showShortErrorToast("获取学习记录失败，请稍后再试");
    }
  }

  /// 获取学生课程总结
  Future<dynamic> getStudentCourseSummary() async {
    if (studentCode.value.isEmpty) {
      ToastUtil.showShortErrorToast("请先选择学员");
      return null;
    }
    Map<String, dynamic> params = {
      'studentCode': studentCode.value,
      'curriculumId': CurriculumId
    };
    var response =
        await HttpUtil().get(Config.getStudentCourseSummary, data: params);
    return response;
  }

  void _onRefreshLastClass(EventUtils event) {
    if(event.message == 'refreshLastClass') {
      getLatestDeliverCourse();
    }
  }
}
