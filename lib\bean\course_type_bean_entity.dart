import 'package:student_end_flutter/generated/json/base/json_field.dart';
import 'package:student_end_flutter/generated/json/course_type_bean_entity.g.dart';
import 'dart:convert';
export 'package:student_end_flutter/generated/json/course_type_bean_entity.g.dart';

@JsonSerializable()
class CourseTypeBeanEntity {
	late String id;
	late String enName;

	CourseTypeBeanEntity();

	factory CourseTypeBeanEntity.fromJson(Map<String, dynamic> json) => $CourseTypeBeanEntityFromJson(json);

	Map<String, dynamic> toJson() => $CourseTypeBeanEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}