package com.dxznjy.alading.methodchannel;

import android.content.Context;
import android.content.Intent;

import androidx.annotation.NonNull;

import com.dxznjy.alading.DXApplication;
import com.dxznjy.alading.common.Constants;
import com.dxznjy.alading.widget.MySplashView;
import com.google.gson.Gson;
import com.tencent.mm.opensdk.modelbiz.WXLaunchMiniProgram;

import io.dcloud.feature.sdk.DCUniMPSDK;
import io.dcloud.feature.sdk.Interface.IUniMP;
import io.dcloud.feature.unimp.config.UniMPOpenConfiguration;
import io.flutter.Log;
import io.flutter.embedding.engine.plugins.FlutterPlugin;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;

public class UserMethodchannel implements FlutterPlugin {
    String methodChannelName = "appUseCoupon";
    Context mContext;
    public static MethodChannel methodChannel;
    @Override
    public void onAttachedToEngine(@NonNull FlutterPluginBinding binding) {
        mContext = binding.getApplicationContext();
        methodChannel = new MethodChannel(binding.getBinaryMessenger(),methodChannelName);
        methodChannel.setMethodCallHandler(new MethodChannel.MethodCallHandler() {
            @Override
            public void onMethodCall(MethodCall call, MethodChannel.Result result) {
            }
        });
    }
    @Override
    public void onDetachedFromEngine(@NonNull FlutterPluginBinding binding) {

    }
}
