import 'package:student_end_flutter/generated/json/base/json_field.dart';
import 'package:student_end_flutter/generated/json/home_type_bean_entity.g.dart';
import 'dart:convert';
export 'package:student_end_flutter/generated/json/home_type_bean_entity.g.dart';

@JsonSerializable()
class HomeTypeBeanEntity {
	late int code = 0;
	late bool success = false;
	late String message = '';
	late List<HomeTypeBeanData> data = [];
	late int total = 0;

	HomeTypeBeanEntity();

	factory HomeTypeBeanEntity.fromJson(Map<String, dynamic> json) => $HomeTypeBeanEntityFromJson(json);

	Map<String, dynamic> toJson() => $HomeTypeBeanEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class HomeTypeBeanData {
	late String id = '';
	late String categoryName = '';
	late int weight = 0;
	late int goodsQuantity = 0;

	HomeTypeBeanData();

	factory HomeTypeBeanData.fromJson(Map<String, dynamic> json) => $HomeTypeBeanDataFromJson(json);

	Map<String, dynamic> toJson() => $HomeTypeBeanDataToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}