import 'package:student_end_flutter/generated/json/base/json_convert_content.dart';
import 'package:student_end_flutter/bean/user_login_list_bean_entity.dart';

UserLoginListBeanEntity $UserLoginListBeanEntityFromJson(
    Map<String, dynamic> json) {
  final UserLoginListBeanEntity userLoginListBeanEntity = UserLoginListBeanEntity();
  final bool? isCur = jsonConvert.convert<bool>(json['isCur']);
  if (isCur != null) {
    userLoginListBeanEntity.isCur = isCur;
  }
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    userLoginListBeanEntity.id = id;
  }
  final String? userCode = jsonConvert.convert<String>(json['userCode']);
  if (userCode != null) {
    userLoginListBeanEntity.userCode = userCode;
  }
  final String? userType = jsonConvert.convert<String>(json['userType']);
  if (userType != null) {
    userLoginListBeanEntity.userType = userType;
  }
  final String? waitAllocateFunds = jsonConvert.convert<String>(
      json['waitAllocateFunds']);
  if (waitAllocateFunds != null) {
    userLoginListBeanEntity.waitAllocateFunds = waitAllocateFunds;
  }
  final String? totalMoney = jsonConvert.convert<String>(json['totalMoney']);
  if (totalMoney != null) {
    userLoginListBeanEntity.totalMoney = totalMoney;
  }
  final String? availableCashAmount = jsonConvert.convert<String>(
      json['availableCashAmount']);
  if (availableCashAmount != null) {
    userLoginListBeanEntity.availableCashAmount = availableCashAmount;
  }
  final double? status = jsonConvert.convert<double>(json['status']);
  if (status != null) {
    userLoginListBeanEntity.status = status;
  }
  final String? unifiedId = jsonConvert.convert<String>(json['unifiedId']);
  if (unifiedId != null) {
    userLoginListBeanEntity.unifiedId = unifiedId;
  }
  final String? paymentIn = jsonConvert.convert<String>(json['paymentIn']);
  if (paymentIn != null) {
    userLoginListBeanEntity.paymentIn = paymentIn;
  }
  final String? paymentOut = jsonConvert.convert<String>(json['paymentOut']);
  if (paymentOut != null) {
    userLoginListBeanEntity.paymentOut = paymentOut;
  }
  return userLoginListBeanEntity;
}

Map<String, dynamic> $UserLoginListBeanEntityToJson(
    UserLoginListBeanEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['isCur'] = entity.isCur;
  data['id'] = entity.id;
  data['userCode'] = entity.userCode;
  data['userType'] = entity.userType;
  data['waitAllocateFunds'] = entity.waitAllocateFunds;
  data['totalMoney'] = entity.totalMoney;
  data['availableCashAmount'] = entity.availableCashAmount;
  data['status'] = entity.status;
  data['unifiedId'] = entity.unifiedId;
  data['paymentIn'] = entity.paymentIn;
  data['paymentOut'] = entity.paymentOut;
  return data;
}

extension UserLoginListBeanEntityExtension on UserLoginListBeanEntity {
  UserLoginListBeanEntity copyWith({
    bool? isCur,
    String? id,
    String? userCode,
    String? userType,
    String? waitAllocateFunds,
    String? totalMoney,
    String? availableCashAmount,
    double? status,
    String? unifiedId,
    String? paymentIn,
    String? paymentOut,
  }) {
    return UserLoginListBeanEntity()
      ..isCur = isCur ?? this.isCur
      ..id = id ?? this.id
      ..userCode = userCode ?? this.userCode
      ..userType = userType ?? this.userType
      ..waitAllocateFunds = waitAllocateFunds ?? this.waitAllocateFunds
      ..totalMoney = totalMoney ?? this.totalMoney
      ..availableCashAmount = availableCashAmount ?? this.availableCashAmount
      ..status = status ?? this.status
      ..unifiedId = unifiedId ?? this.unifiedId
      ..paymentIn = paymentIn ?? this.paymentIn
      ..paymentOut = paymentOut ?? this.paymentOut;
  }
}