package com.dxznjy.alading.response;

import com.dxznjy.alading.http.BaseResponse;

public class SlideRespose extends BaseResponse {
  private   Slide data;

    public Slide getData() {
        return data;
    }

    public void setData(Slide data) {
        this.data = data;
    }
    public  class  Slide{
        public  int cutImageWidth;
        public int  cutImageHeight;
        public  String cutImage;

        public String oriImage;

        public String image;

        public  int checkXpos;

        public int ypos;

        public int getYpos() {
            return ypos;
        }

        public void setYpos(int ypos) {
            this.ypos = ypos;
        }

        public String getImage() {
            return image;
        }

        public void setImage(String image) {
            this.image = image;
        }

        public String getOriImage() {
            return oriImage;
        }

        public void setOriImage(String oriImage) {
            this.oriImage = oriImage;
        }

        public int getCutImageWidth() {
            return cutImageWidth;
        }

        public void setCutImageWidth(int cutImageWidth) {
            this.cutImageWidth = cutImageWidth;
        }

        public int getCutImageHeight() {
            return cutImageHeight;
        }

        public void setCutImageHeight(int cutImageHeight) {
            this.cutImageHeight = cutImageHeight;
        }

        public int getCheckXpos() {
            return checkXpos;
        }

        public void setCheckXpos(int checkXpos) {
            this.checkXpos = checkXpos;
        }

        public String getCutImage() {
            return cutImage;
        }

        public void setCutImage(String cutImage) {
            this.cutImage = cutImage;
        }
    }
}
