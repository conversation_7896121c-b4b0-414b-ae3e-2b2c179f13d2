import 'package:student_end_flutter/bean/course_list_bean_entity.dart';
import 'package:student_end_flutter/common/enums/lesson_leave_status.dart';

class LessonUtil {
  LessonUtil._();

  /// 获取当前学生指定课程的请假状态
  static LessonLeaveStatus getStuLeaveStatus4Lesson(
    int leaveStatus
  ) {
    switch(leaveStatus) {
      case 0:
        return LessonLeaveStatus.withoutLeave; // 未请假
      case 1:
        return LessonLeaveStatus.leaveWithoutMakeup; // 请假没有设置补课时间
      case 2:
        return LessonLeaveStatus.leaveWithMakeUp; // 请假设置了补课时间
      default:
        throw ArgumentError('请假状态入参与枚举不匹配！');
    }
  }

  /// 是否应该展示请假与补课时间设置按钮
  static bool shouldShowLeaveBtn(
    CourseListBeanData courseData,
    int courseTypeTabIndex,
    int lessonStatusTabIndex
  ) {
    return courseTypeTabIndex == 0 // 伴学课
      && lessonStatusTabIndex == 0 // 未上课
      && courseData.oneToManyType == 1 // 一对多课程
      && courseData.leaveStatus != 2 // 非已请假状态
      && courseData.lessonsFlag != 1 // 非补课
      && !(courseData.experience == true && courseData.leaveStatus == 1); // 非试课且非请过假
  }

  /// 获取请假按钮文本
  /// 根据当前用户当前课程的请假状态决定请假按钮标题
  /// 展示 ['请假'] 还是 ['期望补课时间']
  static String getLeaveBtnTitle(CourseListBeanData courseData) {
    return courseData.leaveStatus == 0
      ? '   请 假   '
      : '期望补课时间';
  }

  /// 获取请假按钮的操作类型
  /// 根据当前用户当前课程的请假状态决定请假按钮点击后
  /// 是请假还是设置补课时间
  static int getLeaveOperationType(CourseListBeanData courseData) {
    return courseData.leaveStatus == 0 ? 1 : 2;
  }
}