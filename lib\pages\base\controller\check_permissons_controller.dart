import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:netease_meeting_kit/meeting_ui.dart';
import 'package:student_end_flutter/bean/base_bean/open_dxn_uniapp.dart';
import 'package:student_end_flutter/common/ne_user_option.dart';
import 'package:student_end_flutter/components/toast_utils.dart';
import 'package:student_end_flutter/pages/learn/lesson_list_page.dart';
import 'package:student_end_flutter/utils/android_ios_plugin.dart';
import 'package:student_end_flutter/utils/httpRequest.dart';
import '../../../bean/aiRead_member_bean_entity_entity.dart';
import '../../../bean/deliver_bean_entity.dart';
import '../../../bean/last_record_course_bean_entity.dart';
import '../../../bean/last_student_info_bean_entity.dart';
import '../../../bean/popup_model_entity.dart';
import '../../../bean/pyf_memberCode_bean_entity_entity.dart';
import '../../../bean/student_app_bean_entity.dart';
import '../../../bean/student_info_bean_entity.dart';
import '../../../bean/student_token_bean_entity.dart';
import '../../../bean/user_info_bean_entity.dart';
import '../../../common/Config.dart';
import '../../../common/pop_option.dart';
import '../../../common/user_option.dart';
import '../../../dialog/dialog_learn_select_identity.dart';
import '../../../dialog/dialog_home_activity.dart';
import '../../../dialog/dialog_learn_select_school.dart';
import '../../../dialog/dialog_learn_select_student.dart';
import '../../../generated/json/base/json_convert_content.dart';
import 'package:get/get.dart';

import '../../../navigator/learn_navigator.dart';
import '../../../utils/android_ios_plugin.dart';
import '../../../utils/event_bus_utils.dart';
import '../../../utils/native_channel_utils.dart';
import '../../../utils/ne_metting_utils.dart';
import '../../base/controller/base_controller.dart';
import '../base_page.dart';
import '../check_notification_permissons_page.dart';

class CheckPermissonsController extends GetxController
    with GetSingleTickerProviderStateMixin, WidgetsBindingObserver {
  // 添加标志位，防止重复导航
  bool _hasNavigated = false;
  @override
  Future<void> onInit() async {
    print('cccccccccccccccccccccccccccccccccccccccccccc');
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      // 延迟执行检查，避免在页面构建过程中进行导航
      Future.delayed(Duration(milliseconds: 500), () {
        if (!_hasNavigated && Get.currentRoute == '/CheckNotificationPermissonsPage') {
          isOpenNotificationSettings();
        }
      });
    } else if (state == AppLifecycleState.paused) {}
  }
  isOpenNotificationSettings() {
    print('检查通知权限状态');
    if (_hasNavigated) return;

    AndroidIosPlugin.isOpenNotificationSettings("").then((value){
      if(value == "true" && !_hasNavigated){
        _hasNavigated = true;
        print('通知权限已开启，跳转到BasePage');
        // 使用offAll确保完全替换当前页面
        Get.offAll(() => BasePage());
      }
    }).catchError((error) {
      debugPrint("检查通知权限状态失败: $error");
    });
  }
}
