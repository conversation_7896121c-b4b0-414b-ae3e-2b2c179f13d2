import 'package:student_end_flutter/generated/json/base/json_convert_content.dart';
import 'package:student_end_flutter/bean/banner_bean_entity.dart';

BannerBeanEntity $BannerBeanEntityFromJson(Map<String, dynamic> json) {
  final BannerBeanEntity bannerBeanEntity = BannerBeanEntity();
  final int? code = jsonConvert.convert<int>(json['code']);
  if (code != null) {
    bannerBeanEntity.code = code;
  }
  final bool? success = jsonConvert.convert<bool>(json['success']);
  if (success != null) {
    bannerBeanEntity.success = success;
  }
  final String? message = jsonConvert.convert<String>(json['message']);
  if (message != null) {
    bannerBeanEntity.message = message;
  }
  final List<BannerBeanData>? data = (json['data'] as List<dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<BannerBeanData>(e) as BannerBeanData)
      .toList();
  if (data != null) {
    bannerBeanEntity.data = data;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    bannerBeanEntity.total = total;
  }
  return bannerBeanEntity;
}

Map<String, dynamic> $BannerBeanEntityToJson(BannerBeanEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['code'] = entity.code;
  data['success'] = entity.success;
  data['message'] = entity.message;
  data['data'] = entity.data.map((v) => v.toJson()).toList();
  data['total'] = entity.total;
  return data;
}

extension BannerBeanEntityExtension on BannerBeanEntity {
  BannerBeanEntity copyWith({
    int? code,
    bool? success,
    String? message,
    List<BannerBeanData>? data,
    int? total,
  }) {
    return BannerBeanEntity()
      ..code = code ?? this.code
      ..success = success ?? this.success
      ..message = message ?? this.message
      ..data = data ?? this.data
      ..total = total ?? this.total;
  }
}

BannerBeanData $BannerBeanDataFromJson(Map<String, dynamic> json) {
  final BannerBeanData bannerBeanData = BannerBeanData();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    bannerBeanData.id = id;
  }
  final String? bannerName = jsonConvert.convert<String>(json['bannerName']);
  if (bannerName != null) {
    bannerBeanData.bannerName = bannerName;
  }
  final String? bannerPicUrl = jsonConvert.convert<String>(
      json['bannerPicUrl']);
  if (bannerPicUrl != null) {
    bannerBeanData.bannerPicUrl = bannerPicUrl;
  }
  final String? bannerPosition = jsonConvert.convert<String>(
      json['bannerPosition']);
  if (bannerPosition != null) {
    bannerBeanData.bannerPosition = bannerPosition;
  }
  final int? bannerType = jsonConvert.convert<int>(json['bannerType']);
  if (bannerType != null) {
    bannerBeanData.bannerType = bannerType;
  }
  final String? goodsId = jsonConvert.convert<String>(json['goodsId']);
  if (goodsId != null) {
    bannerBeanData.goodsId = goodsId;
  }
  final String? activityId = jsonConvert.convert<String>(json['activityId']);
  if (activityId != null) {
    bannerBeanData.activityId = activityId;
  }
  final String? goodsName = jsonConvert.convert<String>(json['goodsName']);
  if (goodsName != null) {
    bannerBeanData.goodsName = goodsName;
  }
  final String? bannerLinkUrl = jsonConvert.convert<String>(
      json['bannerLinkUrl']);
  if (bannerLinkUrl != null) {
    bannerBeanData.bannerLinkUrl = bannerLinkUrl;
  }
  final int? weight = jsonConvert.convert<int>(json['weight']);
  if (weight != null) {
    bannerBeanData.weight = weight;
  }
  final int? bannerStatus = jsonConvert.convert<int>(json['bannerStatus']);
  if (bannerStatus != null) {
    bannerBeanData.bannerStatus = bannerStatus;
  }
  final String? effectiveStartTime = jsonConvert.convert<String>(
      json['effectiveStartTime']);
  if (effectiveStartTime != null) {
    bannerBeanData.effectiveStartTime = effectiveStartTime;
  }
  final String? effectiveEndTime = jsonConvert.convert<String>(
      json['effectiveEndTime']);
  if (effectiveEndTime != null) {
    bannerBeanData.effectiveEndTime = effectiveEndTime;
  }
  final int? needLogin = jsonConvert.convert<int>(json['needLogin']);
  if (needLogin != null) {
    bannerBeanData.needLogin = needLogin;
  }
  final String? createdTime = jsonConvert.convert<String>(json['createdTime']);
  if (createdTime != null) {
    bannerBeanData.createdTime = createdTime;
  }
  final String? updatedTime = jsonConvert.convert<String>(json['updatedTime']);
  if (updatedTime != null) {
    bannerBeanData.updatedTime = updatedTime;
  }
  final String? goodsSharePoster = jsonConvert.convert<String>(
      json['goodsSharePoster']);
  if (goodsSharePoster != null) {
    bannerBeanData.goodsSharePoster = goodsSharePoster;
  }
  final List<dynamic>? goodsShareTextList = (json['goodsShareTextList'] as List<
      dynamic>?)?.map(
          (e) => e).toList();
  if (goodsShareTextList != null) {
    bannerBeanData.goodsShareTextList = goodsShareTextList;
  }
  return bannerBeanData;
}

Map<String, dynamic> $BannerBeanDataToJson(BannerBeanData entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['bannerName'] = entity.bannerName;
  data['bannerPicUrl'] = entity.bannerPicUrl;
  data['bannerPosition'] = entity.bannerPosition;
  data['bannerType'] = entity.bannerType;
  data['goodsId'] = entity.goodsId;
  data['activityId'] = entity.activityId;
  data['goodsName'] = entity.goodsName;
  data['bannerLinkUrl'] = entity.bannerLinkUrl;
  data['weight'] = entity.weight;
  data['bannerStatus'] = entity.bannerStatus;
  data['effectiveStartTime'] = entity.effectiveStartTime;
  data['effectiveEndTime'] = entity.effectiveEndTime;
  data['needLogin'] = entity.needLogin;
  data['createdTime'] = entity.createdTime;
  data['updatedTime'] = entity.updatedTime;
  data['goodsSharePoster'] = entity.goodsSharePoster;
  data['goodsShareTextList'] = entity.goodsShareTextList;
  return data;
}

extension BannerBeanDataExtension on BannerBeanData {
  BannerBeanData copyWith({
    String? id,
    String? bannerName,
    String? bannerPicUrl,
    String? bannerPosition,
    int? bannerType,
    String? goodsId,
    String? activityId,
    String? goodsName,
    String? bannerLinkUrl,
    int? weight,
    int? bannerStatus,
    String? effectiveStartTime,
    String? effectiveEndTime,
    int? needLogin,
    String? createdTime,
    String? updatedTime,
    String? goodsSharePoster,
    List<dynamic>? goodsShareTextList,
  }) {
    return BannerBeanData()
      ..id = id ?? this.id
      ..bannerName = bannerName ?? this.bannerName
      ..bannerPicUrl = bannerPicUrl ?? this.bannerPicUrl
      ..bannerPosition = bannerPosition ?? this.bannerPosition
      ..bannerType = bannerType ?? this.bannerType
      ..goodsId = goodsId ?? this.goodsId
      ..activityId = activityId ?? this.activityId
      ..goodsName = goodsName ?? this.goodsName
      ..bannerLinkUrl = bannerLinkUrl ?? this.bannerLinkUrl
      ..weight = weight ?? this.weight
      ..bannerStatus = bannerStatus ?? this.bannerStatus
      ..effectiveStartTime = effectiveStartTime ?? this.effectiveStartTime
      ..effectiveEndTime = effectiveEndTime ?? this.effectiveEndTime
      ..needLogin = needLogin ?? this.needLogin
      ..createdTime = createdTime ?? this.createdTime
      ..updatedTime = updatedTime ?? this.updatedTime
      ..goodsSharePoster = goodsSharePoster ?? this.goodsSharePoster
      ..goodsShareTextList = goodsShareTextList ?? this.goodsShareTextList;
  }
}