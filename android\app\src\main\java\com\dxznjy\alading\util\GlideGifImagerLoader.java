package com.dxznjy.alading.util;

import android.content.Context;
import android.widget.ImageView;

import com.bumptech.glide.Glide;
import com.dxznjy.alading.R;
import com.qiyukf.unicorn.api.UnicornGifImageLoader;

import java.io.Serializable;

/**
 * Created by andya on 2019/1/17
 * Describe: 加载 gif 图片 ImageLoader demo
 */
public class GlideGifImagerLoader implements UnicornGifImageLoader, Serializable {

    Context context;

    public GlideGifImagerLoader(Context context) {
        this.context = context.getApplicationContext();
    }


    @Override
    public void loadGifImage(String url, ImageView imageView,String imgName) {
        if (url == null || imgName == null) {
            return;
        }
            Glide.with(context).load(url).placeholder(R.drawable.ic_launcher).error(R.drawable.ic_launcher).into(imageView);
//        }

    }
}
