import 'package:student_end_flutter/generated/json/base/json_field.dart';
import 'package:student_end_flutter/generated/json/user_info_bean_entity.g.dart';
import 'dart:convert';
export 'package:student_end_flutter/generated/json/user_info_bean_entity.g.dart';

@JsonSerializable()
class UserInfoBeanEntity {
	late String userId = '';
	late String nickName = '';
	late String mobile = '';
	late String headPortrait = '';
	late int currencyNumber = 0;
	late int gradeLevel = 0;
	late int identityType = 0;
	late String identityTypeName = '';
	late int checkUser = 0;
	late String userCode = '';
	late String bizUserId = '';
	late int certStatus = 0;
	late int isBindCard = 0;
	late int isBindPayPhone = 0;
	late int signContractStatus = 0;
	late String userPhone = '';
	late double totalCommissionAmount = 0.0;
	late double freezeAmount =0.0;
	late double withdrawAmount =0.0;
	late String shareUserName = '';
	late String shareUserPhone = '';
	late String shareId = '';
	late int expireStatus = 0;
	late String memberDay = '';
	late String partnerDay = '';
	late String expireTime = '';
	late String memberStartTime = '';
	late int parentMemberType = 0;
	late String parentMemberStartTime = '';
	late String parentMemberEndTime = '';
	late String parentMemberDay = '';
	late int parentMemberStatus = 0;
	late String parentShareId = '';
	late String parentShareUserName = '';
	late String parentShareUserPhone = '';
	late String partnerExpireTime = '';
	late String openId = '';
	late String merchantCode = '';
	late String julebuCode = '';
	late String brandCode = '';
	late String secMerchantPhone = '';
	late String parentAffiliationPartnerCode = '';
	late String parentAffiliationPartnerName = '';
	late String affiliationPartnerCode = '';
	late String affiliationPartnerName = '';

	UserInfoBeanEntity();

	factory UserInfoBeanEntity.fromJson(Map<String, dynamic> json) => $UserInfoBeanEntityFromJson(json);

	Map<String, dynamic> toJson() => $UserInfoBeanEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}