//
//  Enum.swift
//  StuMetApp
//
//  Created by 许江泉 on 2024/11/11.
//

import UIKit
/**
 *  开发模式
 */
enum DeveloperMode {
    /** 调试 */
    case debug
    /** 线上 注册设备可用 */
    case adHoc
    /** 线上 */
    case release
}

/// 网络请求没数据的类型
enum ResponseNoDataType {
    case normal // 正常情况的没数据
    case serverError // 请求失败导致的没数据
    case networkDisable // 没网导致的没数据
    case none
}

enum SheetEventType: Equatable {
    case openPhotoLibrary // 打开相册 多选
    case openCamera // 打开相机
    case openPhotoLibrarySingle // 单选相册
    case openCameraSingle // 单选相机
    case savePicture
    case copy // 复制
    case delete // 删除
}


/// 网络请求没数据的类型
enum CurriculumScreenType: Int {
    case notAttending = 0
    case alreadyInclass = 1
}

/// 网络请求没数据的类型
enum ResponseMoreArrDataType {
    case hasMoreArrData
    case notMoreData
    case nullData
}

// 定义一个枚举来表示弹出窗口的方向
enum PopupDirection {
     case center
     case top
     case bottom
 }

enum PermissionType: Int {
    case speaker // 扬声器
    case microphone // 麦克风
    case camera // 摄像头
}


// 分享
enum SocialPlatformType: Int {
    case wechatSession // 微信聊天
    case wechatTimeLine // 微信朋友圈
}



enum SegmentIndex: Int {
    case deliver = 0
    case recorded = 1
}

enum ChannelNames: String {
    case openAndroidIos = "openAndroidIos"
    case appUseCoupon = "appUseCoupon"
    case openAndroidIosMeet = "openAndroidIosMeet"
    case androidIosBackMeeting = "androidIosBackMeeting"
}

enum MethodNames: String {
    case openUniApp = "openUniApp" // 打开小程序
    case openDxnUniApp = "openDxnUniApp" // 打开鼎校微信小程序
    case openTLPayFunc = "openTLPayFunc" // 打开支付
    case switchToHomeTab = "switchToHomeTab" // 回到首页
    case appPayCallBack = "appPayCallBack" // 支付回调
    case openLessonsList = "openLessonsList" // 打开交付课列表 录播课列表
    case openVideoLessonsLearn = "openVideoLessonsLearn" // 打开录播课详情页面
    case openLessonsDetail = "openLessonsDetail" // 打开权限页面
    case onEnterMeeting = "onEnterMeeting" // 加入会议
    case navigateToFlutterPage = "navigateToFlutterPage" // 分享回到flutter
    case opneCustomerService = "opneCustomerService" // 打开客服
    case openShareApp = "openShareApp" // 会员分享
    case initWeChatUnInstall = "initWeChatUnInstall" // 微信未安装提示
    case openNetwork = "openNetwork"
    case getAppSharePath = "getAppSharePath"
    case exitApp = "exitApp"
    case openShareAppPic = "openShareAppPic"
    case setUserIdForBugly = "setUserIdForBugly"
    case creatCalendar = "creatCalendar"
    case openShareMiniProgram = "openShareMiniProgram"
    case joinMeeting = "joinMeeting"
    case nativeToFlutter = "nativeToFlutter"
    case releaseWgtToRunPath = "releaseWgtToRunPath" // 释放wgt资源包
    case openUniMP = "openUniMP" // 释放wgt资源包

}


enum LoginType: Int {
    case passwordLogin
    case codeLogin
}


/// 环境配置
enum EnvironmentType: String, CaseIterable {
    case sit
    case dev
    case uat
    case prd
    
    var introduced: String {
        return "\(self.rawValue.uppercased())环境"
    }
    
    var baseUrl: String {
        switch self {
        case .sit, .dev, .uat:
            return "https://test179.ngrok.dxznjy.com/"
        case .prd:
            return "https://test179.ngrok.dxznjy.com/"
        }
    }
   
    var baseWebUrl: String {
       return ""
    }
    
}

class EnviromentService {
    static let manager = EnviromentService()
    var currentEnv: EnvironmentType {
        didSet {
            #if DEBUG
                LDebugDefaults.currentEnv = currentEnv.rawValue
            #endif
        }
    }

    init() {
        #if DEBUG
            let cachedCurrentEnv = LDebugDefaults.currentEnv
            if let env = EnvironmentType(rawValue: cachedCurrentEnv) {
                currentEnv = env
            } else {
                currentEnv = .sit
            }
        #else
            currentEnv = .prd
        #endif
    }

    var baseUrl: String {
        return currentEnv.baseUrl
    }

    var baseWebUrl: String {
        return currentEnv.baseWebUrl
    }

    let wildcardHost = "*.dingxiao.com"
}

enum ApiManager {
    
    // MARK: - ============登录================/
    static let addYXUser = "https://meeting.yunxinroom.com/scene/meeting/api/v2/add-user" /// 云信登录
    static let batchgetUser = "https://meeting.yunxinroom.com/scene/meeting/api/v2/batch-get-user" /// 查询
    static let login = "new/security/v2/login/student/app" /// 学生登录
    static let smsCode = "new/security/v2/sms/" /// 获取验证码
    static let captcha = "new/security/captcha/image" /// 图形验证
    static let slideImage = "new/security/captcha/image/slide" // 图形验证码
    static let checkslide = "new/security/captcha/check/slide" // 滑块验证
    

    // MARK: - ============首页================/
    
    // MARK: - ============上课================/
    static let getDeliverCoursePage = "zx/student/course/getDeliverCoursePage" /// 交付课列表
    static let getRecordCoursePage = "zx/student/course/getRecordCoursePage" /// 录播课列表
    static let getCourseType = "zx/student/course/getCourseType" /// 查询课程分类
    
    
    // MARK: - ============会议================/
    static let getCourseMeetingInfo = "zx/student/course/getCourseMeetingInfo" /// 交付课上课获取会议id
    static let getPlayVideo = "zx/student/course/getPlayVideo" /// 获取播放视频信息
    static let getCourseById = "deliver/app/parent/getCourseById" /// 获取播放视频信息

    
    // MARK: - ============我的================/
    static let getUserInfo = "zx/student/getUserInfo" /// 获取学生信息
    static let getStudentMerchantList = "v2/mall/getStudentMerchantList" /// 获取门店列表
 
    
    static func phoneNumberSmsCodeUrl(_ phoneNumber: String, code: String, uuid: String) -> String {
        return smsCode + phoneNumber + "?&code=" + code  + "&uuid=" + uuid
    }

}

/// 小程序跳转
enum UniMPSDK {
    
    case trialreport(trialname: String, token: String) /// 试课报告
    case vocabulary(studentCode: String, studentName: String, token: String) /// 检测报告
    case errorBook(studentCode: String, studentName: String, token: String) /// 错题本
    case antiAmnesia(studentCode: String, merchantCode: String, token: String, logintokenReview: String) /// 趣味复习 学生toekn其他都是家长token
    case personalcenter(studentCode: String, studentName: String, token: String) /// 学习打印内容
    case pYFforget(studentCode: String, token: String)                           /// 评英法
    case antiAmnesia21(Buttonclick: String, buttonclickName: String, token: String) /// 21天抗遗忘
    case dictationReport(studentCode: String, studentName: String, token: String) /// 听写报告
    case coursedetails(token: String, studentCode: String) /// 查看反馈
    case newcoursedetails(token: String, data: String) /// 查看反馈
    case growthReport(token: String, courseId: String, studentCode: String, memberCode: String, userId: String) // 成长报告
    var path: String {
        switch self {
        case let .trialreport(trialname, token):
            return getFullPath(path: "Trialclass/trialreport?type=1&trialname=\(trialname)&token=\(token)&app=2")
        case let .vocabulary(studentCode, studentName, token):
            return getFullPath(path: "parentEnd/vocabulary/vocabulary?studentCode=\(studentCode)&studentName=\(studentName)&token=\(token)&app=2")
        case let .errorBook(studentCode, studentName, token):
            return getFullPath(path: "errorBook/index?studentCode=\(studentCode)&studentName=\(studentName)&token=\(token)&app=2")
        case let .antiAmnesia(studentCode, merchantCode, token, logintokenReview):
            return getFullPath(path: "antiAmnesia/review/funReview?studentCode=\(studentCode)&logintokenReview=\(logintokenReview)&merchantCode=\(merchantCode)&token=\(token)&app=2")
        case let .personalcenter(studentCode, studentName, token):
            return getFullPath(path: "Personalcenter/studyPrint/studyPrint?studentCode=\(studentCode)&studentName=\(studentName)&token=\(token)&app=2")
        case let .pYFforget(studentCode, token):
            return getFullPath(path: "PYFforget/forgetReview?studentCode=\(studentCode)&token=\(token)&app=2")
        case let .antiAmnesia21(Buttonclick, buttonclickName, token):
            return getFullPath(path: "antiAmnesia/review/index?Buttonclick=\(Buttonclick)&buttonclickName=\(buttonclickName)&token=\(token)&app=2")
        case let .dictationReport(studentCode, studentName, token):
            return getFullPath(path: "parentEnd/dictation/dictationReport?studentName=\(studentName)&studentCode=\(studentCode)&token=\(token)&app=2")
        case let .coursedetails(token, studentCode):
            return getFullPath(path: "Coursedetails/feedback/index?studentCode=\(studentCode)&token=\(token)&app=2")
        case let .newcoursedetails(token, data):
            return getFullPath(path: "Coursedetails/feedback/newIndex?data=\(data)&token=\(token)&app=2")
        case let .growthReport(token, courseId, studentCode, memberCode, userId):
            return getFullPath(path: "Coursedetails/study/courseDetail?app=2&token=\(token)&courseId=\(courseId)&studentCode=\(studentCode)&memberCode=\(memberCode)&userId=\(userId)")
        }
        
    }
    
    private func getFullPath(path: String) -> String {
        return path
    }
    
}
