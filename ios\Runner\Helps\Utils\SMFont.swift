//
//  SMFont.swift
//  StuMetApp
//
//  Created by 许江泉 on 2024/11/11.
//

import Foundation
import UIKit
public protocol FontRepresentable: RawRepresentable {}

extension FontRepresentable where Self.RawValue == String {
    public func of(size: CGFloat) -> UIFont {
        return UIFont(name: rawValue, size: size) ?? UIFont.systemFont(ofSize: size)
    }

    public func of(size: Double) -> UIFont {
        return UIFont(name: rawValue, size: CGFloat(size)) ?? UIFont.systemFont(ofSize: CGFloat(size))
    }

    public func of(size: SMFont.Size) -> UIFont {
        return UIFont(name: rawValue, size: size.rawValue) ?? UIFont.systemFont(ofSize: size.rawValue)
    }
}

public struct SMFont {

    public enum PingFangSC: String, FontRepresentable {
        case light = "PingFangSC-Light",
             regular = "PingFangSC-Regular",
             medium = "PingFangSC-Medium",
             bold = "PingFangSC-Bold"
    }
    public enum Size: CGFloat {
        case xSmall = 10,
             small = 12,
             f13 = 13,
             normal = 14,
             f15 = 15,
             large = 16,
             xLarge = 18,
             xxLarge = 20,
             xxxLarge = 24,
             maxLarge = 29
    }
    public static func h() -> UIFont {
        return PingFangSC.medium.of(size: .maxLarge)
    }
    public static func h_1() -> UIFont {
        return PingFangSC.regular.of(size: .maxLarge)
    }
    public static func h1() -> UIFont {
        return PingFangSC.medium.of(size: .xxxLarge)
    }

    public static func h2() -> UIFont {
        return PingFangSC.medium.of(size: .xxLarge)
    }

    public static func h3() -> UIFont {
        return PingFangSC.medium.of(size: .xLarge)
    }

    public static func h3_1() -> UIFont {
        return PingFangSC.regular.of(size: .xLarge)
    }

    public static func body1() -> UIFont {
        return PingFangSC.medium.of(size: .large)
    }

    public static func body1_1() -> UIFont {
        return PingFangSC.regular.of(size: .large)
    }

    public static func body1_2() -> UIFont {
        return PingFangSC.medium.of(size: .large)
    }
    
    public static func body1_3() -> UIFont {
        return PingFangSC.bold.of(size: .large)
    }

    public static func body2() -> UIFont {
        return PingFangSC.medium.of(size: .normal)
    }

    public static func body2_1() -> UIFont {
        return PingFangSC.regular.of(size: .normal)
    }
    
    public static func body2_2() -> UIFont {
        return PingFangSC.bold.of(size: .normal)
    }

    public static func f13() -> UIFont {
        return PingFangSC.medium.of(size: .f13)
    }

    public static func f13_1() -> UIFont {
        return PingFangSC.regular.of(size: .f13)
    }

    
    public static func c1() -> UIFont {
        return PingFangSC.medium.of(size: .small)
    }

    public static func c1_1() -> UIFont {
        return PingFangSC.regular.of(size: .small)
    }

    public static func c3() -> UIFont {
        return PingFangSC.medium.of(size: .xSmall)
    }
    public static func c4() -> UIFont {
        return PingFangSC.regular.of(size: .xSmall)
    }
    
    public static func f15_B() -> UIFont {
        return PingFangSC.medium.of(size: .f15)
    }
    
    public static func f15() -> UIFont {
        return PingFangSC.regular.of(size: .f15)
    }

}
