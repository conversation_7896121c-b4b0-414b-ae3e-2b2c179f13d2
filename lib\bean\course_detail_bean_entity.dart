import 'package:student_end_flutter/generated/json/base/json_field.dart';
import 'package:student_end_flutter/generated/json/course_detail_bean_entity.g.dart';
import 'dart:convert';
export 'package:student_end_flutter/generated/json/course_detail_bean_entity.g.dart';

@JsonSerializable()
class CourseDetailBeanEntity {
	late String studentName;
	late String id;
	late String planId;
	late int type;
	late String date;
	late String startTime;
	late String endTime;
	late String courseType;
	late String teachingType;
	late String teacher;
	late bool isFeedback;
	late bool experience;
	late String dateTime;
	late String curriculumName;

	CourseDetailBeanEntity();

	factory CourseDetailBeanEntity.fromJson(Map<String, dynamic> json) => $CourseDetailBeanEntityFromJson(json);

	Map<String, dynamic> toJson() => $CourseDetailBeanEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}