//
//  UIKit+Extension.swift
//  News
//
//  Created by yj9527 on 2023/2/12.
//  Copyright © 2023 CocoaPods. All rights reserved.
//
//

import CoreText
import Foundation
import QMUIKit
import UIKit

protocol StoryboardLoadable {}

extension StoryboardLoadable where Self: UIViewController {
    /// 提供 加载方法
    static func loadStoryboard() -> Self {
        return UIStoryboard(name: "\(self)", bundle: nil).instantiateViewController(withIdentifier: "\(self)") as! Self
    }
}

protocol NibLoadable { }

extension NibLoadable {
    static func loadViewFromNib() -> Self {
        return Bundle.main.loadNibNamed("\(self)", owner: nil, options: nil)?.last as! Self
    }
}

extension Dictionary {
    func dicValueString(_ dic: [String: Any]) -> String? {
        let data = try? JSONSerialization.data(withJSONObject: dic, options: [])
        let str = String(data: data!, encoding: String.Encoding.utf8)
        return str
    }
}

func currentViewController() -> (UIViewController?) {
    var window = UIApplication.shared.keyWindow
    if window?.windowLevel != UIWindow.Level.normal {
        let windows = UIApplication.shared.windows
        for windowTemp in windows {
            if windowTemp.windowLevel == UIWindow.Level.normal {
                window = windowTemp
                break
            }
        }
    }
    let vc = window?.rootViewController
    return currentViewController(vc)
}

func currentViewController(_ vc: UIViewController?) -> UIViewController? {
    if vc == nil {
        return nil
    }
    if let presentVC = vc?.presentedViewController {
        return currentViewController(presentVC)
    } else if let tabVC = vc as? UITabBarController {
        if let selectVC = tabVC.selectedViewController {
            return currentViewController(selectVC)
        }
        return nil
    } else if let naiVC = vc as? UINavigationController {
        return currentViewController(naiVC.visibleViewController)
    } else {
        return vc
    }
}



extension UIViewController {
    @objc func dismissKeyboard() {
        view.endEditing(true) // 让view中的所有textfield失去焦点--即关闭小键盘
    }
    
    func hideKeyboardWhenTappedAround() {
        let tap = UITapGestureRecognizer(target: self, action: #selector(dismissKeyboard))
        // 保证tap手势不会影响到其他touch类控件的手势
        // 若不设，则本页面有tableview时，点击cell不会触发didSelectRowAtIndex（除非长按）
        tap.cancelsTouchesInView = false
        view.addGestureRecognizer(tap)
    }
}

extension Array {
    subscript(safe index: Int) -> Element? {
        guard index >= 0 && index < count else {
            return nil
        }
        return self[index]
    }
}

extension UIWindow {
    var currentFirstResponder: UIResponder? {
        return findFirstResponder(in: self)
    }

    private func findFirstResponder(in view: UIView) -> UIResponder? {
        if view.isFirstResponder {
            return view
        }

        for subview in view.subviews {
            if let firstResponder = findFirstResponder(in: subview) {
                return firstResponder
            }
        }

        return nil
    }
}

extension String {
    /// 截取指定子字符串后的所有内容
    /// - Parameter substring: 要查找的子字符串
    /// - Returns: 如果找到子字符串，返回其后的内容；否则返回 nil
    func substringAfter(_ substring: String) -> String? {
        guard let range = self.range(of: substring) else {
            return nil
        }
        let startIndex = range.upperBound
        return String(self[startIndex...])
    }

    /// 截取最后一次出现的子字符串后的所有内容
    /// - Parameter substring: 要查找的子字符串
    /// - Returns: 如果找到子字符串，返回其后的内容；否则返回 nil
    func substringAfterLast(_ substring: String) -> String? {
        guard let range = self.range(of: substring, options: .backwards) else {
            return nil
        }
        let startIndex = range.upperBound
        return String(self[startIndex...])
    }
}

extension String {
    func toDictionary() -> [String: Any]? {
        guard let jsonData = self.data(using: .utf8) else {
            print("无法将字符串转换为 Data")
            return nil
        }
        
        do {
            if let dictionary = try JSONSerialization.jsonObject(with: jsonData, options: []) as? [String: Any] {
                return dictionary
            } else {
                print("JSON 数据不是一个有效的字典")
                return nil
            }
        } catch {
            print("解析 JSON 数据时出错：\(error.localizedDescription)")
            return nil
        }
    }
}
extension String {
    public var length: Int {
        /// 更改成其他的影响含有emoji协议的签名
        return utf16.count
    }

    public var doubleValue: Double {
        return (self as NSString).doubleValue
    }

    public var intValue: Int32 {
        return (self as NSString).intValue
    }

    public var floatValue: Float {
        return (self as NSString).floatValue
    }

    public var integerValue: Int {
        return (self as NSString).integerValue
    }

    public var longLongValue: Int64 {
        return (self as NSString).longLongValue
    }

    public var boolValue: Bool {
        return (self as NSString).boolValue
    }
}

extension String {
    /// 正则表达匹配
    private func matchsForRegx(_ regx: String) -> Bool {
        let regxpd = NSPredicate(format: "SELF MATCHES %@", regx)
        return regxpd.evaluate(with: self)
    }
    
    /// 是否为1开头11位的手机号不带+86等国家编码
    func isValidPhoneNumber() -> Bool {
        let px = "^1[0-9]{10}$"
        return matchsForRegx(px)
    }
}

extension UILabel {

    static func createLabel(text: String = "",
                            fontSize: UIFont,
                           color: UIColor,
                           alignment: NSTextAlignment = NSTextAlignment.left,
                           numberOfLines: Int = 1

        ) -> UILabel {
        let label = UILabel()
        label.font = fontSize
        label.textColor = color
        label.text = text
        label.textAlignment = alignment
        label.numberOfLines = numberOfLines
        return label
    }
    
    class func createLabelWithName(text: String,
                           name: String,
                           fontSize: UIFont,
                           color: UIColor,
                           bgColor: UIColor = UIColor.clear,
                           alignment: NSTextAlignment = NSTextAlignment.left,
                           isWeight: Bool = false,
                           cornerRadius: CGFloat = 0.0,
                           borderColor: UIColor = UIColor.clear,
                           borderWidth: CGFloat = 1,
                           numberOfLines: Int = 1

        ) -> UILabel {
        let label = UILabel()
        label.font = fontSize

        label.textColor = color
        label.text = text
        label.backgroundColor = bgColor
        label.layer.cornerRadius = cornerRadius
        label.layer.masksToBounds = true
        label.layer.borderColor = borderColor.cgColor
        label.layer.borderWidth = borderWidth
        label.textAlignment = alignment
        label.numberOfLines = numberOfLines

        return label
    }
    // MARK: 设置某个圆角
    func configSideRadius(rectCorners: UIRectCorner, size: CGSize, rect: CGRect) {

        let maskPath = UIBezierPath.init(roundedRect: rect, byRoundingCorners: rectCorners, cornerRadii: size)
        let maskLayer = CAShapeLayer.init()
        maskLayer.path = maskPath.cgPath
        self.layer.mask = maskLayer
    }

}

extension QMUIButton {

    class func createCustomButton (style: QMUIButton.ButtonType = QMUIButton.ButtonType.custom,
                                   title: String ,
                                   bgColor: UIColor = UIColor.clear,
                                   titleColor: UIColor ,
                                   titleSize: UIFont ,
                                   corner: CGFloat = 0,
                                   borderWidth: CGFloat = 0.0,
                                   borderColor: UIColor = .clear

        ) -> QMUIButton {
        let btn = QMUIButton.init(type: style)
        btn.setTitle(title, for: .normal)
        btn.backgroundColor = bgColor

        btn.titleLabel?.font = titleSize
        btn.setTitleColor(titleColor, for: .normal)

        btn.layer.cornerRadius = CGFloat(corner)
        btn.layer.masksToBounds = true
        btn.layer.borderWidth = borderWidth
        btn.layer.borderColor = borderColor.cgColor

        return btn
    }
    class func createCustomButton(normarlImg: String) -> QMUIButton {
        let btn = QMUIButton.init(type: UIButton.ButtonType.custom)
        btn.setImage(UIImage.imgWithName(normarlImg), for: UIControl.State.normal)
        return btn
    }
    
    class func createCustomImageButton(normarlImg: UIImage?) -> QMUIButton {
        let btn = QMUIButton.init(type: UIButton.ButtonType.custom)
        btn.setImage(normarlImg, for: UIControl.State.normal)
        return btn
    }

    func addTargetTo(target: Any?, action: Selector) {
        self.addTarget(target, action: action, for: UIControl.Event.touchUpInside)
    }

}


extension CGSize {
    /// 根据给定的尺寸和宽高比计算保持宽高比的最大尺寸
    /// - Parameters:
    ///   - targetSize: 目标尺寸
    ///   - aspectRatio: 宽高比（宽度/高度）
    /// - Returns: 保持宽高比的最大尺寸
    func aspectFittedSize(within targetSize: CGSize, aspectRatio: CGFloat) -> CGSize {
        let targetWidth = targetSize.width
        let targetHeight = targetSize.height
        
        // 根据宽高比计算实际宽度和高度
        let width = targetWidth
        let height = width / aspectRatio
        
        // 如果计算出的高度大于目标高度，则以目标高度为准，反算宽度
        if height > targetHeight {
            let adjustedHeight = targetHeight
            let adjustedWidth = adjustedHeight * aspectRatio
            return CGSize(width: adjustedWidth, height: adjustedHeight)
        }
        
        // 否则，使用计算出的宽度和高度
        return CGSize(width: width, height: height)
    }
}

extension String {
    func substringFrom(char: Character) -> String {
        guard let index = self.firstIndex(of: char) else {
            return "" // 未找到字符时返回空字符串
        }
        return String(self[index...]) // 包含起始字符
    }
}
