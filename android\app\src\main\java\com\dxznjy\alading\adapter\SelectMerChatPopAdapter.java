package com.dxznjy.alading.adapter;

import android.content.Context;
import android.widget.LinearLayout;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dxznjy.alading.R;
import com.dxznjy.alading.common.Constants;
import com.dxznjy.alading.response.StudentMerchantResponse;

import java.util.List;


public class SelectMerChatPopAdapter extends BaseQuickAdapter<StudentMerchantResponse.StudentMerchant, BaseViewHolder> {
    private Integer  mOrientation = Constants.VERTICAL;
    public SelectMerChatPopAdapter(Context context, List<StudentMerchantResponse.StudentMerchant> data, Integer orientation) {
        super(R.layout.adapter_login_selectuser_pop, data);
        this.mContext = context;
        this.mOrientation = orientation;
    }

    @Override
    protected void convert(final BaseViewHolder helper, StudentMerchantResponse.StudentMerchant item) {
        LinearLayout ll_bg = helper.getView(R.id.ll_bg);
        if (item.getType() != 0){
            if (item.isCheck()){
                ll_bg.setBackgroundResource(R.drawable.bg_radius24_line_428a6f);
                helper.setTextColor(R.id.tv_title,mContext.getResources().getColor(R.color._428a6f));
            }else{
                ll_bg.setBackgroundResource(R.drawable.bg_radius24_line_e1dede);
                helper.setTextColor(R.id.tv_title,mContext.getResources().getColor(R.color._555555));
            }
        }else{
            ll_bg.setBackgroundResource(R.drawable.bg_radius4_f7f7f7);
        }
        if (mOrientation == Constants.VERTICAL){
            helper.setText(R.id.tv_title,item.getMerchantName());
            helper.setGone(R.id.tv_title,true);
        }else{
            helper.setText(R.id.tv_title,item.getMerchantName());
            helper.setGone(R.id.tv_title,false);
        }
    }
}
