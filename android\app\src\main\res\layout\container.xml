<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/container_backgroud"
    android:orientation="vertical"
    android:padding="10dp">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="200dp"
        android:paddingLeft="10dp"
        android:paddingRight="10dp">

        <com.dxznjy.alading.widget.captcha.PictureVertifyView
            android:id="@+id/vertifyView"
            android:layout_width="match_parent"
            android:layout_height="200dp"
            android:scaleType="fitXY" />

        <LinearLayout
            android:id="@+id/accessRight"
            android:layout_width="match_parent"
            android:layout_height="28dp"
            android:layout_gravity="bottom"
            android:background="#7F000000"
            android:orientation="horizontal"
            android:visibility="gone">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_gravity="center"
                android:layout_marginLeft="10dp"
                android:src="@drawable/right" />

            <TextView
                android:id="@+id/accessText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginLeft="10dp"
                android:textColor="#FFFFFF"
                android:textSize="14sp" />


        </LinearLayout>

        <LinearLayout
            android:id="@+id/accessFailed"
            android:layout_width="match_parent"
            android:layout_height="28dp"
            android:layout_gravity="bottom"
            android:background="#7F000000"
            android:orientation="horizontal"
            android:visibility="gone">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_gravity="center"
                android:layout_marginLeft="10dp"
                android:src="@drawable/wrong" />

            <TextView
                android:id="@+id/accessFailedText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginLeft="10dp"
                android:textColor="#FFFFFF"
                android:textSize="14sp" />

        </LinearLayout>

        <ImageView
            android:id="@+id/refresh"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_gravity="right|top"
            android:layout_margin="5dp"
            android:src="@drawable/refresh" />


    </FrameLayout>

   <RelativeLayout
       android:layout_width="match_parent"
       android:layout_height="wrap_content"
       android:layout_marginTop="16dp">
       <com.dxznjy.alading.widget.captcha.TextSeekbar
           android:id="@+id/seekbar"
           style="@style/MySeekbarSytle"
           android:layout_width="match_parent"
           android:layout_height="wrap_content"
           android:layout_gravity="center"
           android:splitTrack="false"
           android:thumbOffset="0dp" />

       <TextView
           android:id="@+id/tv_tips"
           android:layout_width="wrap_content"
           android:layout_height="wrap_content"
           android:layout_centerInParent="true"
           android:text="向右滑动滑块填充拼图"
           android:textColor="@color/_333333"
           android:textSize="15sp" />
   </RelativeLayout>

</LinearLayout>