apply plugin: 'com.android.library'


android {
    compileSdkVersion 33
    buildToolsVersion "30.0.3"
    namespace = "io.dcloud.uniplugin.module"

    defaultConfig {
        minSdk 28
        targetSdk 34
        versionCode 1
        versionName "1.0"
        testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"
        ndk{
            abiFilters "armeabi-v7a","arm64-v8a"
        }
    }

    sourceSets {
        main {
            res.srcDirs=[
                     'src/main/res'
            ]
            aidl.srcDirs = [
                    'src/main/java'
            ]

            java.srcDirs = [
                    'src/main/java'
            ]
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

//导入aar需要的配置
repositories {
    flatDir {
        dirs 'libs'
    }
}

dependencies {
    implementation 'com.google.android.material:material:1.8.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    //必须添加的依赖
    compileOnly 'androidx.recyclerview:recyclerview:1.1.0'
    compileOnly "androidx.appcompat:appcompat:1.1.0"
    compileOnly "androidx.legacy:legacy-support-v4:1.0.0"
    compileOnly 'com.alibaba:fastjson:1.2.66'
    implementation "com.blankj:rxbus:1.1"
    implementation 'com.tencent.edu:TCICSDK:1.8.17'
//    implementation 'com.writingminds:FFmpegAndroid:0.3.2'
    implementation 'com.arthenica:mobile-ffmpeg-full:4.4.LTS'
    compileOnly fileTree(include: ['uniapp-v8-release.aar'], dir: '../app/libs')
}
