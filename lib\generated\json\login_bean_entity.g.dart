import 'package:student_end_flutter/generated/json/base/json_convert_content.dart';
import 'package:student_end_flutter/bean/login_bean_entity.dart';

LoginBeanEntity $LoginBeanEntityFromJson(Map<String, dynamic> json) {
  final LoginBeanEntity loginBeanEntity = LoginBeanEntity();
  final String? tokenType = jsonConvert.convert<String>(json['tokenType']);
  if (tokenType != null) {
    loginBeanEntity.tokenType = tokenType;
  }
  final String? tokenHeader = jsonConvert.convert<String>(json['tokenHeader']);
  if (tokenHeader != null) {
    loginBeanEntity.tokenHeader = tokenHeader;
  }
  final String? token = jsonConvert.convert<String>(json['token']);
  if (token != null) {
    loginBeanEntity.token = token;
  }
  final String? issuedAt = jsonConvert.convert<String>(json['issuedAt']);
  if (issuedAt != null) {
    loginBeanEntity.issuedAt = issuedAt;
  }
  final String? expiresAt = jsonConvert.convert<String>(json['expiresAt']);
  if (expiresAt != null) {
    loginBeanEntity.expiresAt = expiresAt;
  }
  final String? principalName = jsonConvert.convert<String>(
      json['principalName']);
  if (principalName != null) {
    loginBeanEntity.principalName = principalName;
  }
  final String? refreshToken = jsonConvert.convert<String>(
      json['refreshToken']);
  if (refreshToken != null) {
    loginBeanEntity.refreshToken = refreshToken;
  }
  final String? refreshIssuedAt = jsonConvert.convert<String>(
      json['refreshIssuedAt']);
  if (refreshIssuedAt != null) {
    loginBeanEntity.refreshIssuedAt = refreshIssuedAt;
  }
  final dynamic callbackData = json['callbackData'];
  if (callbackData != null) {
    loginBeanEntity.callbackData = callbackData;
  }
  final dynamic deviceInfo = json['deviceInfo'];
  if (deviceInfo != null) {
    loginBeanEntity.deviceInfo = deviceInfo;
  }
  final LoginBeanAdditionalParameters? additionalParameters = jsonConvert
      .convert<LoginBeanAdditionalParameters>(json['additionalParameters']);
  if (additionalParameters != null) {
    loginBeanEntity.additionalParameters = additionalParameters;
  }
  return loginBeanEntity;
}

Map<String, dynamic> $LoginBeanEntityToJson(LoginBeanEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['tokenType'] = entity.tokenType;
  data['tokenHeader'] = entity.tokenHeader;
  data['token'] = entity.token;
  data['issuedAt'] = entity.issuedAt;
  data['expiresAt'] = entity.expiresAt;
  data['principalName'] = entity.principalName;
  data['refreshToken'] = entity.refreshToken;
  data['refreshIssuedAt'] = entity.refreshIssuedAt;
  data['callbackData'] = entity.callbackData;
  data['deviceInfo'] = entity.deviceInfo;
  data['additionalParameters'] = entity.additionalParameters.toJson();
  return data;
}

extension LoginBeanEntityExtension on LoginBeanEntity {
  LoginBeanEntity copyWith({
    String? tokenType,
    String? tokenHeader,
    String? token,
    String? issuedAt,
    String? expiresAt,
    String? principalName,
    String? refreshToken,
    String? refreshIssuedAt,
    dynamic callbackData,
    dynamic deviceInfo,
    LoginBeanAdditionalParameters? additionalParameters,
  }) {
    return LoginBeanEntity()
      ..tokenType = tokenType ?? this.tokenType
      ..tokenHeader = tokenHeader ?? this.tokenHeader
      ..token = token ?? this.token
      ..issuedAt = issuedAt ?? this.issuedAt
      ..expiresAt = expiresAt ?? this.expiresAt
      ..principalName = principalName ?? this.principalName
      ..refreshToken = refreshToken ?? this.refreshToken
      ..refreshIssuedAt = refreshIssuedAt ?? this.refreshIssuedAt
      ..callbackData = callbackData ?? this.callbackData
      ..deviceInfo = deviceInfo ?? this.deviceInfo
      ..additionalParameters = additionalParameters ??
          this.additionalParameters;
  }
}

LoginBeanAdditionalParameters $LoginBeanAdditionalParametersFromJson(
    Map<String, dynamic> json) {
  final LoginBeanAdditionalParameters loginBeanAdditionalParameters = LoginBeanAdditionalParameters();
  return loginBeanAdditionalParameters;
}

Map<String, dynamic> $LoginBeanAdditionalParametersToJson(
    LoginBeanAdditionalParameters entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  return data;
}

extension LoginBeanAdditionalParametersExtension on LoginBeanAdditionalParameters {
}