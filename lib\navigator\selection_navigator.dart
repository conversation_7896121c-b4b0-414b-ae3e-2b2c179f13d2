import '../common/user_option.dart';
import 'mini_program_path.dart';
import 'path_generator.dart';
class CourseSelectionNavigator {
  const CourseSelectionNavigator._();
  /// 签到
  /// 示例：Personalcenter/my/myIntegral?
  static MiniProgramPath checkin() {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
        pathComponents: ['Personalcenter', 'my', 'myIntegral'],
        queryParams: {
          'userId': UserOption.userId,
        }
    );
    return MiniProgramPath(path);
  }
}