<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.dxznjy.alading" >

    <queries>
        <package android:name="com.tencent.mm" />
    </queries>
    <!--
 Required to query activities that can process text, see:
         https://developer.android.com/training/package-visibility and
         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.

         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
    -->
    <uses-permission android:name="android.permission.ACCESS_NOTIFICATION_POLICY" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE"
        />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES"/>
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-feature android:name="android.hardware.camera" />
    <uses-feature android:name="android.hardware.camera.autofocus" />
    <uses-permission android:name="android.permission.READ_CALENDAR" />
    <uses-permission android:name="android.permission.WRITE_CALENDAR" />
    <uses-permission android:name="android.permission.ACCESS_NOTIFICATION_POLICY"/>
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>


    <queries>
        <intent>
            <action android:name="android.intent.action.PROCESS_TEXT" />
            <data android:mimeType="text/plain" />
        </intent>
    </queries>

    <application
        android:label="@string/app_name"
        android:name=".DXApplication"
        android:icon="@mipmap/ic_launcher"
        android:requestLegacyExternalStorage="true"
        android:extractNativeLibs="true"
        android:hardwareAccelerated="true"
        android:forceDarkAllowed="false"
        android:theme="@style/AppTheme"
        tools:replace="android:label,android:icon,android:allowBackup"
        android:allowBackup="true"
        tools:targetApi="q">
        <meta-data
            android:name="ScopedStorage"
            android:value="true" />
        <meta-data
            android:name="android.max_aspect"
            android:value="2.4"
            tools:replace="android:value" />
        <activity
            android:name=".activity.AliPayTempActivity"
            android:exported="true"
            >
            <intent-filter>
                <data android:scheme="allinpaysdk" />
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>
        </activity>
        <activity
            android:name=".activity.PayCallBackActivity"
            android:theme="@style/AppTheme"
            android:exported="true" />
        <activity
            android:name=".activity.OpenKfActivity"
            android:theme="@style/AppTheme" />
        <activity
            android:name=".activity.JumpTempActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/AppTheme" />
        <activity
            android:name=".MainActivity"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:exported="true"
            android:launchMode="singleTop"
            android:taskAffinity="com.dxznjy.alading"
            android:theme="@style/AppTheme"
            android:windowSoftInputMode="adjustResize" >

            <!--
                 Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI.
            -->
            <meta-data
                android:name="io.flutter.embedding.android.NormalTheme"
                android:resource="@style/NormalTheme" />

            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <!--
 Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
        -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
        <activity
            android:name=".activity.givelessons.MeetingPermissionActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/AppTheme" />
        <activity
            android:name=".activity.HomeActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/AppTheme" />
        <activity
            android:name=".activity.LoginActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/AppTheme" />
        <activity
            android:name=".wxapi.WXEntryActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:taskAffinity="com.dxznjy.alading"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <provider
            android:name="io.dcloud.common.util.DCloud_FileProvider"
            android:authorities="${apk.applicationId}.dc.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/dcloud_file_provider" />
        </provider>
        <provider
            android:name="com.pichillilorenzo.flutter_inappwebview_android.InAppWebViewFileProvider"
            android:authorities="${applicationId}.flutter_inappwebview_android.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths" />
        </provider>
        <activity
            android:name=".activity.ShareActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/AppTheme" >
            <intent-filter>
                <data android:scheme="openapp" />
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>
        </activity>
    </application>

</manifest>