package com.dxznjy.alading.response;

import java.util.List;

public class YXUserInfoResponse {

    private Integer code;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    private  UserInfoData data;

    public UserInfoData getData() {
        return data;
    }

    public void setData(UserInfoData data) {
        this.data = data;
    }

    public class UserInfoData{
       private List<UserInfo> userInfos;

        public List<UserInfo> getUserInfos() {
            return userInfos;
        }

        public void setUserInfos(List<UserInfo> userInfos) {
            this.userInfos = userInfos;
        }
    }


    public class UserInfo{
        private String   name;
        private String userUuid;
        private String      privateMeetingNum;
        private String      state;
        private String      userToken;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getUserUuid() {
            return userUuid;
        }

        public void setUserUuid(String userUuid) {
            this.userUuid = userUuid;
        }

        public String getPrivateMeetingNum() {
            return privateMeetingNum;
        }

        public void setPrivateMeetingNum(String privateMeetingNum) {
            this.privateMeetingNum = privateMeetingNum;
        }

        public String getState() {
            return state;
        }

        public void setState(String state) {
            this.state = state;
        }

        public String getUserToken() {
            return userToken;
        }

        public void setUserToken(String userToken) {
            this.userToken = userToken;
        }
    }

}

