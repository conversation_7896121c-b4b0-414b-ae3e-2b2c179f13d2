package com.dxznjy.alading;

import android.os.Bundle;
import android.util.Log;
import android.view.KeyEvent;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatDelegate;

import com.dxznjy.alading.common.Constants;
import com.dxznjy.alading.methodchannel.OpenMeetMethodchannel;
import com.dxznjy.alading.methodchannel.PayMethodchannel;
import com.dxznjy.alading.methodchannel.UniappMethodchannel;
import com.google.gson.Gson;
import com.qiyukf.unicorn.api.Unicorn;
import com.vondear.rxtool.RxSPTool;


import android.content.Intent;
import android.provider.Settings;
import io.flutter.plugin.common.MethodChannel;

import io.flutter.embedding.android.FlutterActivity;
import io.flutter.embedding.engine.FlutterEngine;
import io.flutter.plugins.GeneratedPluginRegistrant;

public class MainActivity extends FlutterActivity {
    @Override
    public void configureFlutterEngine(@NonNull FlutterEngine flutterEngine) {
        super.configureFlutterEngine(flutterEngine);
        AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO);
        flutterEngine.getPlugins().add(new UniappMethodchannel());
        flutterEngine.getPlugins().add(new PayMethodchannel());
        flutterEngine.getPlugins().add(new OpenMeetMethodchannel());
        DXApplication.app.flutterEngineInstance=flutterEngine;
        GeneratedPluginRegistrant.registerWith(flutterEngine);
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Unicorn.initSdk();
    }

    /**·
     * 菜单、返回键响应
     */
   /* //记录用户首次点击返回键的时间
    private long firstTime = 0;

    *//**
     * 第一种解决办法 通过监听keyUp
     * @param keyCode
     * @param event
     * @return
     *//*
    @Override
    public boolean onKeyUp(int keyCode, KeyEvent event) {
        String  canBackFinish= RxSPTool.getString(this,Constants.canBackFinish);
        if (canBackFinish.equals("true")){
            if (keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_UP) {
                long secondTime = System.currentTimeMillis();
                if (secondTime - firstTime > 2000) {
                    firstTime = secondTime;
                    Toast.makeText(MainActivity.this,"再按一次关闭程序~",Toast.LENGTH_SHORT).show();
                    return true;
                } else {
                    finish();
                }
            }
        }
        return super.onKeyUp(keyCode, event);
    }*/

}