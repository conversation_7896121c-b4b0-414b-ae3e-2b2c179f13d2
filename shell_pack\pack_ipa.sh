#!/bin/bash
###
# @Descripttion:
# @version:
# @Author: Cullen
# @Date: 2024-08-11 09:48:31
# @LastEditors: Cullen
# @LastEditTime: 2024-08-14 16:21:56
###

# shellcheck disable=SC1091
# shellcheck disable=SC2154
source ./pack_tool.sh
source ./pgyer_upload.sh

chmod a+x pgyer_upload.sh

# 上传到商店
uploadApp() {
    echoMsg "开始上传app到商店 "
    uploadapp=$(xcrun altool \
        --upload-app \
        -f "$export_ipa_path/$ipa_name.ipa" \
        -u "$xcrun_u" \
        -p "$xcrun_p" \
        -t ios)
    echoMsg "上传商店结果:$uploadapp"
    if [ -z "$uploadapp" ]; then
        echoMsg "app上传到商店失败 "
        bd="${shell_pack_path}build/"
        if [ -d "$bd" ]; then
            rm -rf "$bd"
        fi
    else
        echoMsg "app已上传到商店 "
    fi
}

# 验证App
validateApp() {
    echoMsg "开始验证app"
    validate=$(xcrun altool \
        --validate-app \
        -f "$export_ipa_path/$ipa_name.ipa" \
        -u "$xcrun_u" \
        -p "$xcrun_p" \
        -t ios)
    echoMsg "验证结果:$validate"
    if [[ -z "$validate" ]]; then
        echoMsg "app验证失败"
    else
        uploadApp
    fi
}

# 开始导出ipa
configExportAcrhive() {
    echoMsg "开始导出ipa"
    # 导出上传商店的ipa
    xcodebuild \
        -exportArchive -archivePath "$export_xcarchive_path$xcarchive_name" \
        -exportPath "${export_ipa_path}" \
        -exportOptionsPlist "${export_options_plist}" \
        -destination "$generic" \
        -allowProvisioningUpdates -quiet || exit
    if [ -e "${export_ipa_path}" ]; then
        echoMsg "ipa导出成功"
        if [ "$upload_type" == 1 ]; then
            validateApp
        else
            echoMsg "开始上传ipa到蒲公英"
            uploadPgyer "$api_key" "$export_ipa_path/$ipa_name".ipa
        fi
    else
        echoMsg "ipa包导出失败"
    fi
}

# 开始归档
configIpaArchive() {
    echoMsg "开始archive"
    xcodebuild archive \
        -workspace "${project_workspace_path}" \
        -scheme "$project_scheme" \
        -configuration "$configuration" \
        -archivePath "${export_xcarchive_path}${xcarchive_name}" \
        -destination "$generic" -quiet || exit
    echoMsg "archive成功"
    echoMsg "读取APP信息"
    info_plist="$export_xcarchive_path$xcarchive_name/Products/Applications/Runner.app/Info.plist"
    ipa_name=$(/usr/libexec/PlistBuddy -c "Print CFBundleName" "$info_plist")
    run_plist="$export_xcarchive_path$xcarchive_name/Info.plist"
    ipa_teamID=$(/usr/libexec/PlistBuddy -c "Print ApplicationProperties:Team" "$run_plist")
    echoMsg "app名字:$ipa_name"
    echoMsg "teamID:$ipa_teamID"
    # 通过 plutil 工具进行对 plist文件修改
    plutil -replace teamID -string "$ipa_teamID" "$export_options_plist"
    echoMsg "plist文件修改完成"
}

# 开始构建版本
configIpaBuild() {
    echoMsg "编译IOS:${build_type}"
    if [ -e "${project_path}"/pubspec.yaml ]; then
        echoMsg "编译FLUTTER"
        flutter build ios --"$build_type" --no-tree-shake-icons
    else
        echoMsg "正在编译XCODE工程"
        cd "$project_path/ios" || quiet
        pod install
        xcodebuild build \
            -workspace "${project_workspace_path}" \
            -scheme "$project_scheme" \
            -configuration "$configuration" \
            -destination "$generic" -quiet || exit
    fi
}

# 配置iOS打包环境
configPackagingIosIpaEnvironment() {
    #CPU架构
    #cpu_architecture=$(uname -m)
    # 为了解决打包hoc,在导出的时候,提示The operation couldn’t be completed.这个问题.
    echo "$computer_password" | sudo -s gem install sqlite3
    # 获取对应版本的export_options_plist文件
    if [[ $upload_type == 1 && $project_build_type == 1 ]]; then
        export_options_plist=$export_options_plist_store
        build_type="release"
        configuration="Release"
    elif [[ $upload_type == 2 && $project_build_type == 1 ]]; then
        export_options_plist=$export_options_plist_hoc
        build_type="release"
        configuration="Release"
    else
        build_type="debug"
        configuration="Debug"
        export_options_plist=$export_options_plist_dev
    fi
    echoMsg "选择plist文件路径${export_options_plist}"

}

buidIos() {
    configPackagingIosIpaEnvironment
    configIpaBuild
    configIpaArchive
    configExportAcrhive
}
