import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_state_manager/src/simple/get_view.dart';
import 'package:student_end_flutter/common/CommonUtil.dart';
import 'package:student_end_flutter/common/user_option.dart';
import 'package:student_end_flutter/pages/base/controller/base_controller.dart';

import '../../res/colors.dart';
import '../../utils/app_version_util.dart';
import 'controller/mine_controller.dart';

class SettingsPage extends GetView<MineController> {
  @override
  // final BaseController baseController = Get.find<BaseController>();
  Widget build(BuildContext context) {
    Get.put(MineController());
    return Scaffold(
      appBar: AppBar(
          title: Text(
            '设置',
            style: TextStyle(
              color: HexColor('333333'),
              fontSize: 16.sp,
              fontFamily: "R",
            ),
          ),
          centerTitle: true,
          shadowColor: Colors.white,
          backgroundColor: HexColor("f2f3f5"),
          leading: IconButton(
            padding: const EdgeInsets.only(left: 16),
            icon: Icon(
              Icons.arrow_back_ios,
              color: HexColor("2a2e35"),
              size: 20,
            ),
            onPressed: () {
              Get.back();
            },
          )),
      body: RefreshIndicator(
        color: AppColors.f2E896F,
        backgroundColor: Colors.white,
        notificationPredicate: (ScrollNotification notifation) {
          ScrollMetrics scrollMetrics = notifation.metrics;
          if (scrollMetrics.minScrollExtent == 0) {
            return true;
          } else {
            return false;
          }
        },
        onRefresh: controller.onRefresh,
        child: _buildContent(context),
      ),
    );
  }

  _buildContent(BuildContext context) {
    return SingleChildScrollView(
      child:Column(
        children: [
          // Container(
          //   alignment: Alignment.center,
          //   // padding: EdgeInsets.only(left: 10.w, right: 10.w, bottom: 0.h),
          //   width: double.infinity,
          //   decoration: BoxDecoration(
          //
          //   ),
          //   child:,
          // ),
          Column(
            mainAxisAlignment:MainAxisAlignment.start,
            children: [
              GestureDetector(
                onTap: () {
                  // UserOption.logoutFunc(false);
                  controller.utilsClick({'name':'合同管理'},context);
                },
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border(
                      bottom: BorderSide( // 只设置右边框
                        color:HexColor('efefef'), // 边框颜色
                        width: 1,
                        // 边框宽度
                      ),
                    ),
                  ),
                  padding:EdgeInsets.only(left: 20.w, right: 10.w, bottom: 20.h,top: 20.h) ,
                  width: double.infinity,
                  child: Text('合同管理'),
                ),
              ),
              GestureDetector(
                onTap: () {
                  // UserOption.logoutFunc(false);
                  controller.utilsClick({'name':'营销小组'},context);
                },

                child: Container(
                  color:Colors.white,
                  padding:EdgeInsets.only(left: 20.w, right: 10.w, bottom: 20.h,top: 20.h) ,
                  width: double.infinity,
                  child: Text('营销小组'),
                ),
              ),
            ],),
          SizedBox(
            height: 8.h,
          ),
          Column(
            mainAxisAlignment:MainAxisAlignment.start,
            children: [
              GestureDetector(
                onTap: () {
                  // UserOption.logoutFunc(false);
                //   联系客服
                  controller.utilsClick({'name':'联系客服'},context);
                },
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border(
                      bottom: BorderSide( // 只设置右边框
                        color:HexColor('efefef'), // 边框颜色
                        width: 1,
                        // 边框宽度
                      ),
                    ),
                  ),
                  padding:EdgeInsets.only(left: 20.w, right: 10.w, bottom: 20.h,top: 20.h) ,
                  width: double.infinity,
                  child: Text('联系客服'),
                ),
              ),
              GestureDetector(
                onTap: () {
                  // UserOption.logoutFunc(false);
                  controller.utilsClick({'name':'意见反馈'},context);
                },
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border(
                      bottom: BorderSide( // 只设置右边框
                        color:HexColor('efefef'), // 边框颜色
                        width: 1,
                        // 边框宽度
                      ),
                    ),
                  ),
                  padding:EdgeInsets.only(left: 20.w, right: 10.w, bottom: 20.h,top: 20.h) ,
                  width: double.infinity,
                  child: Text('意见反馈'),
                ),
              ),
              GestureDetector(
                onTap: () {
                  controller.complaintsHotline();
                },
                child: Container(
                  color:Colors.white,
                  padding:EdgeInsets.only(left: 20.w, right: 10.w, bottom: 20.h,top: 20.h) ,
                  width: double.infinity,
                  child: Text('投诉电话'),
                ),
              )
            ],),
          SizedBox(
            height: 8.h,
          ),
          Column(
            mainAxisAlignment:MainAxisAlignment.start,
            children: [
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border(
                    bottom: BorderSide( // 只设置右边框
                      color:HexColor('efefef'), // 边框颜色
                      width: 1,
                      // 边框宽度
                    ),
                  ),
                ),
                padding:EdgeInsets.only(left: 20.w, right: 10.w, bottom: 20.h,top: 20.h) ,
                width: double.infinity,
                child: InkWell(
                  onTap: (){
                    controller.onClickCheckVer(isShowTips: true);
                  },
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    // crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('软件版本'),
                      Text("${AppVersionUtil.appVersion}",style: TextStyle(fontSize: 12,color: HexColor("666666")),),
                    ],
                  ),
                ),
              ),
             // Container(
             //   height: 40.h,
             //   child:  Row(
             //     children: [
             //       GestureDetector(
             //         onTap: () {
             //           // UserOption.logoutFunc(false);
             //           controller.onClickCheckVer(isShowTips: true);
             //         },
             //         child: Container(
             //
             //           child: Text('软件版本'),
             //         ),
             //       ),
             //     ],
             //   ),
             // ),
              GestureDetector(
                onTap: () {
                  controller.utilsClick({'name':'协议及政策'},context);
                },
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border(
                      bottom: BorderSide( // 只设置右边框
                        color:HexColor('efefef'), // 边框颜色
                        width: 1,
                        // 边框宽度
                      ),
                    ),
                  ),

                  padding:EdgeInsets.only(left: 20.w, right: 10.w, bottom: 20.h,top: 20.h) ,
                  width: double.infinity,
                  child: Text('协议与政策'),
                ),
              )
            ],),
          SizedBox(
            height: 8.h,
          ),
          Column(
            mainAxisAlignment:MainAxisAlignment.start,
            children: [

              GestureDetector(
                onTap: () {
                  UserOption.logoutFunc(false);
                  Get.back();
                },
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border(
                      bottom: BorderSide( // 只设置右边框
                        color:HexColor('efefef'), // 边框颜色
                        width: 1,
                        // 边框宽度
                      ),
                    ),
                  ),

                  padding:EdgeInsets.only(left: 20.w, right: 10.w, bottom: 20.h,top: 20.h) ,
                  width: double.infinity,
                  child: Text('注销账号'),
                ),
              )
            ],),
          SizedBox(
            height: 15.h,
          ),

          SizedBox(
            height: 20.h,
          ),
          Visibility(
              visible: true,
              child: GestureDetector(
                onTap: () {
                  UserOption.logoutFunc(false);
                  Get.back();
                },
                child: Container(
                  alignment: Alignment.center,
                  width: double.infinity,
                  margin: EdgeInsets.only(
                      left: 10.w, right: 10.w, bottom: 10.h),
                  padding: EdgeInsets.all(10.w),
                  decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.all(Radius.circular(5))),
                  child: Text(
                    "退出登录",
                    style: TextStyle(
                        fontSize: 14.sp,
                        fontFamily: "M",
                        color: HexColor("666666")),
                  ),
                ),
              )),

        ],
      ),
    );
  }
}
