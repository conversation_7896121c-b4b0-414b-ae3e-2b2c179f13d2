name: student_end_flutter
description: "鼎校甄选"
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# TODO 当前版本2.1.0后续迭代要变更版本号(2.0.3+5)
version: 2.3.2+33 # 0701  版本 2.1.0+11

environment:
  sdk: ^3.5.0

dependencies:
  flutter:
    sdk: flutter
  get: ^4.6.1
  json_annotation: ^4.8.1
  dio: ^5.0.3
  pretty_dio_logger: ^1.4.0
  hex: ^0.2.0
  bot_toast: ^4.0.3
  flutter_screenutil: ^5.6.0
  cupertino_icons: ^1.0.8
  flutter_carousel_widget: ^3.1.0
  flutter_staggered_grid_view: ^0.7.0
  cached_network_image: ^3.2.0
#  contained_tab_bar_view: ^0.8.0
  sensors_analytics_flutter_plugin: ^3.0.1
  image: ^4.1.7
  shared_preferences: ^2.3.0
  connectivity_plus: ^6.1.3
  event_bus: ^2.0.1
  url_launcher: ^6.3.1
  modal_bottom_sheet: ^3.0.0
  crypto: ^3.0.1
  webview_flutter: ^4.9.0
  #  image_gallery_saver: ^2.0.3
  permission_handler: ^10.4.5
  flutter_native_splash: ^2.3.3
  # 打开系统设置页面
  app_settings: ^6.1.1
  # 日期选择器
  calendar_date_picker2: ^1.1.9
  flutter_inappwebview: ^6.0.0

  scan: ^1.6.0
  image_picker: ^0.8.4
  device_info_plus: ^10.1.2
  # 上拉刷新/下拉加载
  pull_to_refresh: ^2.0.0
  qr_flutter: ^4.0.0
#  netease_meeting_kit:
#    path: ./meeting_kit
  waterfall_flow: any
  tx_im:
    path: ./tx-im
  tencent_cloud_chat_sdk: 8.6.7019+2
  tencent_cloud_chat_push: 8.6.7019
  flutter_linkify: ^6.0.0
  intl: ^0.19.0 # 请检查最新版本
  easy_debounce: ^2.0.3
  package_info_plus: ^8.0.0
  path_provider: ^2.1.1
dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^5.0.0

flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  fonts:
    - family: R
      fonts:
        - asset: assets/fonts/regular.ttf
    - family: M
      fonts:
        - asset: assets/fonts/medium.ttf
  assets:
    - tx-im/assets/images/
    - assets/
    - assets/login/
    - assets/tab/
    - assets/zx/
    - assets/home/
    - assets/learn/
    - assets/mine/
    - android/app/src/main/
    - android/app/src/main/res/
    - android/app/src/main/res/anim/
    - android/app/src/main/res/layout/
    - android/app/src/main/res/values/
    - android/app/src/main/res/drawable/
    - android/app/src/main/res/values-zh/
    - android/app/src/main/res/mipmap-hdpi/
    - android/app/src/main/res/mipmap-mdpi/
    - android/app/src/main/res/values-land/
    - android/app/src/main/res/drawable-v21/
    - android/app/src/main/res/mipmap-xhdpi/
    - android/app/src/main/res/values-night/
    - android/app/src/main/res/mipmap-xxhdpi/
    - android/app/src/main/res/layout-sw600dp/
    - android/app/src/main/res/mipmap-xxxhdpi/
    - android/app/src/main/res/layout-sw600dp-land/
    - android/app/src/main/java/
    - android/app/src/main/java/io/
    - android/app/src/main/java/io/flutter/
    - android/app/src/main/java/io/flutter/plugins/
    - android/app/src/main/java/com/
    - android/app/src/main/java/com/dxznjy/
    - android/app/src/main/java/com/dxznjy/alading/
    - android/app/src/main/java/com/dxznjy/alading/api/
    - android/app/src/main/java/com/dxznjy/alading/api/service/
    - android/app/src/main/java/com/dxznjy/alading/http/
    - android/app/src/main/java/com/dxznjy/alading/util/
    - android/app/src/main/java/com/dxznjy/alading/event/
    - android/app/src/main/java/com/dxznjy/alading/wxapi/
    - android/app/src/main/java/com/dxznjy/alading/common/
    - android/app/src/main/java/com/dxznjy/alading/entity/
    - android/app/src/main/java/com/dxznjy/alading/widget/
    - android/app/src/main/java/com/dxznjy/alading/widget/captcha/
    - android/app/src/main/java/com/dxznjy/alading/adapter/
    - android/app/src/main/java/com/dxznjy/alading/adapter/study/
    - android/app/src/main/java/com/dxznjy/alading/adapter/homepage/
    - android/app/src/main/java/com/dxznjy/alading/adapter/givelessons/
    - android/app/src/main/java/com/dxznjy/alading/activity/
    - android/app/src/main/java/com/dxznjy/alading/activity/givelessons/
    - android/app/src/main/java/com/dxznjy/alading/fragment/
    - android/app/src/main/java/com/dxznjy/alading/response/
    - android/app/src/main/java/com/dxznjy/alading/methodchannel/
    - android/app/src/main/assets/
    - android/app/src/main/assets/apps/
    - android/app/src/main/assets/data/

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
#flutter_native_splash:
#  color: "#ffffff"           # 背景颜色
#  image: assets/launchImage.png   # 启动页图片路径
#  android: true              # 启用 Android 配置
#  ios: false                  # 启用 iOS 配置

