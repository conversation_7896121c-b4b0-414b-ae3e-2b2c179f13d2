import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:student_end_flutter/common/CommonUtil.dart';
import 'package:student_end_flutter/pages/base/base_page.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter/foundation.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:app_settings/app_settings.dart';
import '../../utils/android_ios_plugin.dart';
import 'controller/check_permissons_controller.dart';
import '../advertisement/open_screen_advertisement.dart';
import '../../utils/sharedPreferences_util.dart';
class CheckNotificationPermissonsPage extends StatelessWidget {

  const CheckNotificationPermissonsPage({super.key});
  @override
  Widget build(BuildContext context) {
    final topPadding = MediaQuery.of(context).padding.top;
    Get.put(CheckPermissonsController());
    return Material(
      child: Container(
        alignment: Alignment.center,
        child: Stack(
          alignment: Alignment.center,
          children: [
            Image(
              image: AssetImage("assets/image_notice_access.png"),
            ),
            Positioned(
                left: 20.w,
                top: topPadding + 10.w,
                child: InkWell(
                  onTap: () {
                    // Navigator.pop(context);
                    getagreeTips();

                  },
                  child: Image(
                    width: 20.w,
                    height: 20.h,
                    image: AssetImage("assets/icon_home.png"),
                  ),
                )),
            Positioned(
                bottom: 100.w,
                child: InkWell(
                  onTap: () {
                    _openNotificationSettings();
                  },
                  child: Container(
                    alignment: Alignment.center,
                    width: 200.w,
                    height: 40.w,
                    decoration: BoxDecoration(
                      color: HexColor('14806C'),
                      borderRadius: BorderRadius.all(Radius.circular(15)),
                    ),
                    child: Text(
                      '去开启',
                      style: TextStyle(
                        decoration: TextDecoration.none,
                        fontWeight: FontWeight.w500,
                        fontSize: 17.sp,
                        color: Colors.white,
                      ),
                    ),
                  ),
                )),
            Positioned(
              bottom: 50,
              child: Container(
                alignment: Alignment.center,
                child: Text(
                  '点击按钮后,按上图指导开启消息通知权限',
                  style: TextStyle(
                    decoration: TextDecoration.none,
                    fontWeight: FontWeight.w500,
                    fontSize: 16,
                    color: HexColor('969696'),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  static const platform = MethodChannel('com.dxznjy.alading/notifications');
  getagreeTips () async{
    print('************************************************');
    Get.off(BasePage());
  }
  _openNotificationSettings() {
    try {
      AndroidIosPlugin.openNotificationSettings()
          .then((value) {})
          .catchError((error) {
        print("打开通知设置页面失败: $error");
      });
    } catch (e) {
      print("打开通知设置页面异常: $e");
    }
  }
}
