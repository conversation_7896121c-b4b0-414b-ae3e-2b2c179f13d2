<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="160dp"
    android:orientation="vertical"
    android:paddingBottom="14dp"
    android:paddingTop="14dp"
    android:paddingLeft="14dp"
    android:paddingRight="14dp"
    android:layout_marginBottom="14dp"
    android:background="@mipmap/pic_kapian">
    <TextView
        android:id="@+id/tv_coursename"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="14sp"
        android:textColor="@color/_333333"
        android:singleLine="true"
        android:textStyle="bold"
        android:text="注意力学习课"/>
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp">
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="14sp"
                android:textColor="@color/_555555"
                android:gravity="center"
                android:singleLine="true"
                android:text="学生姓名："/>
            <TextView
                android:id="@+id/tv_studentname"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="14sp"
                android:textColor="@color/_2c9e7c"
                android:gravity="center"
                android:textStyle="bold"
                android:singleLine="true"
                android:text="鼎学能"/>
        </LinearLayout>
        <LinearLayout
            android:id="@+id/tv_classstatus"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:orientation="horizontal">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="14sp"
                android:textColor="@color/_555555"
                android:gravity="center"
                android:singleLine="true"
                android:text="课程类型："/>
            <TextView
                android:id="@+id/tv_coursetype"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="14sp"
                android:textColor="@color/_2c9e7c"
                android:gravity="center"
                android:textStyle="bold"
                android:singleLine="true"
                android:text="鼎学能"/>
        </LinearLayout>
    </RelativeLayout>
    <TextView
        android:id="@+id/tv_coursetime"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="14sp"
        android:layout_marginTop="10dp"
        android:textColor="@color/_555555"
        android:singleLine="true"
        android:text="上课时间：2024-11-07 19:30:00"/>
    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="14dp"
        android:background="@color/_f1eeee"/>
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <TextView
            android:id="@+id/tv_fristname"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:text=""
            android:background="@drawable/bg_circle_12c287"
            android:textSize="14sp"
            android:gravity="center"
            android:textColor="@color/white"
            />
        <TextView
            android:id="@+id/tv_teachername"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="12sp"
            android:layout_toRightOf="@+id/tv_fristname"
            android:layout_centerVertical="true"
            android:textColor="@color/_555555"
            android:singleLine="true"
            android:layout_marginLeft="10dp"
            android:text="主讲师:张老师"/>

        <TextView
            android:id="@+id/tv_goonlearn"
            android:layout_width="70dp"
            android:layout_height="24dp"
            android:textSize="12sp"
            android:layout_centerVertical="true"
            android:textColor="@color/_339378"
            android:gravity="center"
            android:layout_toLeftOf="@+id/tv_feedback"
            android:background="@drawable/bg_radius20_428a6f_line"
            android:singleLine="true"
            android:visibility="gone"
            android:layout_marginLeft="14dp"
            android:text="继续学习"/>
        <TextView
            android:id="@+id/tv_feedback"
            android:layout_width="70dp"
            android:layout_height="24dp"
            android:textSize="12sp"
            android:layout_centerVertical="true"
            android:layout_alignParentRight="true"
            android:textColor="@color/_339378"
            android:gravity="center"
            android:background="@drawable/bg_radius20_428a6f_line"
            android:singleLine="true"
            android:layout_marginLeft="14dp"
            android:text="查看反馈"/>
    </RelativeLayout>


</LinearLayout>