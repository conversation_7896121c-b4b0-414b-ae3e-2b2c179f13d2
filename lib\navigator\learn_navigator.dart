import 'package:flutter/cupertino.dart';
import '../common/user_option.dart';
import 'mini_program_path.dart';
import 'path_generator.dart';

class LearnNavigator {
  const LearnNavigator._();

  static MiniProgramPath jumpLearnTool({
    required String routePath,
    String? userId,
    String? studentCode,
    String? merchantCode,
    String? trialname,
    String? logintokenReview,
    String? data,
    bool showDetails = false,
  }) {
    List<String> pathComponents = routePath.split('/');
    final path = MiniProgramPathBuilder.buildMiniProgramPath(pathComponents: pathComponents, queryParams: {
      'userId': userId,
      'app': '2',
      'studentCode': studentCode ?? '',
      'merchantCode': merchantCode ?? '',
      'trialname': trialname ?? '',
      'studentName': trialname??'',
      'logintokenReview': logintokenReview ?? '',
      'data': data ?? '',
      'pageNum': 1, // 固定参数
      'pageSize': 20,
      'memberId': UserOption.userCode ?? '',
      'token': UserOption.token ?? '',
      // 'showDetails': showDetails ? '1' : '0',
    });
    return MiniProgramPath(path);
  }

  /// 积分中心
  /// 示例：Personalcenter/my/myIntegral?
  static MiniProgramPath pointsCenter({
    @required required String userId,
  }) {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(pathComponents: [
      'Personalcenter',
      'my',
      'myIntegral'
    ], queryParams: {
      'userId': userId,
      'app': '2',
      'token': UserOption.token, // 家长token
      'pageNum': 1,
    });
    return MiniProgramPath(path);
  }

  /// 我的学员
  /// 示例：Personalcenter/my/mystudent
  static MiniProgramPath myStudent() {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(pathComponents: [
      'Personalcenter',
      'my',
      'mystudent'
    ], queryParams: {
      'memberId': UserOption.userCode,
      'app': '2',
      'token': UserOption.token, // 家长token
    });
    return MiniProgramPath(path);
  }

  /// 资料学习记录
  /// 示例：Personalcenter/my/mystudent
  static MiniProgramPath learningRecords() {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(pathComponents: [
      'Coursedetails',
      'xktStudy',
      'index'
    ], queryParams: {
      'memberId': UserOption.userCode,
      'app': '2',
      'token': UserOption.token, // 家长token
    });
    return MiniProgramPath(path);
  }

  /// 学时详情
  /// 示例：parentEnd/recharge/lessonDetails/
  static MiniProgramPath studyHoursDetails(
      {@required required String studentCode,
      @required required String merchantCode}) {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(pathComponents: [
      'parentEnd',
      'recharge',
      'lessonDetails'
    ], queryParams: {
      'app': '2',
      'token': UserOption.token, // 家长token
      'studentCode': studentCode,
      'merchantCode': merchantCode
    });
    return MiniProgramPath(path);
  }

  /// 发音设置
  /// 示例：Personalcenter/my/pronunciationList
  static MiniProgramPath pronunciationList(
      {@required required String studentCode}) {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(pathComponents: [
      'Personalcenter',
      'my',
      'pronunciationList'
    ], queryParams: {
      'studentCode': studentCode,
      'memberId': UserOption.userCode,
      'app': '2',
      'token': UserOption.token, // 家长token
    });
    return MiniProgramPath(path);
  }

  /// 鼎英语课程表
  /// 示例：Personalcenter/my/myCourseList
  static MiniProgramPath myCourseList({
    @required required String studentCode,
  }) {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(pathComponents: [
      'Personalcenter',
      'my',
      'myCourseList'
    ], queryParams: {
      'pageNum': 1, // 固定参数
      'pageSize': 20,
      'app': '2',
      'token': UserOption.token, // 家长token
      'studentCode': studentCode
    });
    return MiniProgramPath(path);
  }

  /// 学习记录
  /// 示例： Listen/index
  static MiniProgramPath listen({
    @required required String studentCode,
  }) {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(pathComponents: [
      'Listen',
      'index'
    ], queryParams: {
      'studentCode': studentCode,
      'app': '2',
      'token': UserOption.token, // 家长token
    });
    return MiniProgramPath(path);
  }

  /// 知识图谱
  static MiniProgramPath knowledgeGraph({
    @required required String studentCode,
    @required required String studentName,
  }) {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(pathComponents: [
      'knowledgeGraph',
      'index'
    ], queryParams: {
      'memberId': UserOption.userCode,
      'app': '2',
      'token': UserOption.token, // 家长token
      'studentName':studentName,
      'studentCode':studentCode,
    });
    return MiniProgramPath(path);
  }

  /// 数学测试卷
  static MiniProgramPath testEnter({
    @required required String studentCode,
  }) {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(pathComponents: [
      'knowledgeGraph',
      'testEnter'
    ], queryParams: {
      'memberId': UserOption.userCode,
      'studentCode': studentCode,
      'app': '2',
      'token': UserOption.token, // 家长token
    });
    return MiniProgramPath(path);
  }

  /// 试课报告页面
  /// 示例：Trialclass/trialreport?type=1&trialname=数学&token=abc123
  static MiniProgramPath trialReport({
    @required required String studentCode,
    @required required String trialname,
  }) {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
      pathComponents: ['Trialclass', 'trialreport'],
      queryParams: {
        'token': UserOption.token,
        'app': '2',
        'studentCode': studentCode,
        'trialname': trialname
      },
    );
    return MiniProgramPath(path);
  }

  /// 词汇检测报告
  /// 示例：parentEnd/vocabulary?studentCode=S001&name=张三
  static MiniProgramPath vocabularyReport({
    @required required String studentCode,
    @required required String studentName,
    bool showDetails = false, // 可选参数
  }) {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
      pathComponents: ['parentEnd', 'vocabulary'],
      queryParams: {
        'studentCode': studentCode,
        'studentName': studentName,
        'token': UserOption.token,
        'showDetails': showDetails ? '1' : '0',
        'app': '2',
      },
    );
    return MiniProgramPath(path);
  }

  /// 错题本
  /// 示例：errorBook/index?studentCode=S001&name=张三
  static MiniProgramPath errorBook({@required required String studentCode}) {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
      pathComponents: ['errorBook', 'index'],
      queryParams: {
        'app': '2',
        'memberId': UserOption.userInfoBeanEntity?.userCode,
        'token': UserOption.token,
        'studentCode': studentCode
      },
    );
    return MiniProgramPath(path);
  }

  /// 学习打印内容
  /// 示例：Personalcenter/studyPrint/studyPrint?studentCode=S001&name=张三
  static MiniProgramPath studyPrint({@required required String studentCode}) {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
      pathComponents: ['Personalcenter', 'studyPrint', 'studyPrint'],
      queryParams: {
        'token': UserOption.token,
        'studentCode': studentCode,
        'app': '2',
      },
    );
    return MiniProgramPath(path);
  }

  /// 21天抗遗忘
  /// 示例：antiAmnesia/review/index?studentCode=S001&name=张三
  static MiniProgramPath dayMemoryRetentionProgram({
    @required required String studentCode,
    @required required String merchantCode,
  }) {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
      pathComponents: ['antiAmnesia', 'review', 'index'],
      queryParams: {
        'studentCode': studentCode,
        'app': '2',
        'merchantCode': merchantCode,
        'token': UserOption.token
      },
    );
    return MiniProgramPath(path);
  }

  /// 拼音法抗遗忘
  /// 示例：PYFforget/forgetReview
  static MiniProgramPath forgetReview({
    @required required String studentCode,
    @required required String merchantCode,
  }) {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
      pathComponents: ['PYFforget', 'forgetReview'],
      queryParams: {
        'studentCode': studentCode,
        'app': '2',
        'merchantCode': merchantCode,
        'token': UserOption.token
      },
    );
    return MiniProgramPath(path);
  }

  /// 新阅读理解抗遗忘
  /// 示例：  ReadForget/index
  static MiniProgramPath readForget({
    @required required String studentCode,
    @required required String merchantCode,
  }) {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
      pathComponents: ['ReadForget', 'index'],
      queryParams: {
        'studentCode': studentCode,
        'app': '2',
        'merchantCode': merchantCode,
        'token': UserOption.token
      },
    );
    return MiniProgramPath(path);
  }

  /// 鼎英语趣味复习
  /// 示例：  ReadForget/index
  static MiniProgramPath funReview(
      {@required required String studentCode,
      @required required String merchantCode,
      @required required String logintokenReview}) {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
      pathComponents: ['antiAmnesia', 'review', 'funReview'],
      queryParams: {
        'studentCode': studentCode,
        'app': '2',
        'token': logintokenReview,
        'merchantCode': merchantCode,
        'logintokenReview': logintokenReview,
      },
    );
    return MiniProgramPath(path);
  }

  /// 词汇量检测报告
  /// 示例：    parentEnd/vocabulary/vocabulary
  static MiniProgramPath vocabulary({
    @required required String studentCode,
  }) {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
      pathComponents: ['parentEnd', 'vocabulary', 'vocabulary'],
      queryParams: {
        'studentCode': studentCode,
        'app': '2',
        'token': UserOption.token
      },
    );
    return MiniProgramPath(path);
  }

  /// 听写报告
  /// 示例：parentEnd/dictation/dictationReport?studentCode=S001&name=张三
  static MiniProgramPath dictationReport({
    @required required String studentCode,
  }) {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
      pathComponents: ['parentEnd', 'dictation', 'dictationReport'],
      queryParams: {
        'studentCode': studentCode,
        'app': '2',
        'token': UserOption.token
      },
    );
    return MiniProgramPath(path);
  }

  /// 查看反馈
  static MiniProgramPath feedback({
    @required required String studentCode,
  }) {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
      pathComponents: ['Coursedetails', 'feedback', 'index'],
      queryParams: {
        'studentCode': studentCode,
        'app': '2',
        'token': UserOption.token
      },
    );
    return MiniProgramPath(path);
  }

  /// 查看鼎英语反馈
  static MiniProgramPath feedbackDyx({
    @required required String data,
  }) {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
      pathComponents: ['Coursedetails', 'feedback', 'newIndex'],
      queryParams: {'data': data, 'app': '2', 'token': UserOption.token},
    );
    return MiniProgramPath(path);
  }

  // 录播课继续学习 Coursedetails/study/courseDetail?app=2
  static MiniProgramPath courseDetail({
    @required required String studentCode,
    @required required String courseId,
    @required required String userId,
    @required required String memberCode,
  }) {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
      pathComponents: ['Coursedetails', 'study', 'courseDetail'],
      queryParams: {
        'studentCode': studentCode,
        'app': '2',
        'token': UserOption.token,
        'courseId': courseId,
        'userId': userId,
        'memberCode': memberCode,
      },
    );
    return MiniProgramPath(path);
  }

  /// AI智阅
  /// 示例：aiIntelligentWords/wordsSelect/wordsSelect
  static MiniProgramPath aiIntelligentWords({
    @required required String merchantCode,
    @required required String studentCode,
    @required required String logintokenReview,
  }) {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(pathComponents: [
      'aiIntelligentWords',
      'wordsSelect',
      'wordsSelect'
    ], queryParams: {
      'merchantCode': merchantCode,
      'studentCode': studentCode,
      'logintokenReview': logintokenReview,
    });
    return MiniProgramPath(path);
  }

  /// 拼音发趣味复习
  static MiniProgramPath pYFFunReview({
    @required required String merchantCode,
    @required required String studentCode,
  }) {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(pathComponents: [
      'interestModule',
      'pyfinterest',
      'index'
    ], queryParams: {
      'merchantCode': merchantCode,
      'studentCode': studentCode
    });
    return MiniProgramPath(path);
  }
  /// 鼎英语课程进度
  /// 示例：courseProgress/index
  static MiniProgramPath courseProgress({
    @required required String studentCode,
    @required required String merchantCode,
  }) {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
      pathComponents: ['courseProgress', 'index'],
      queryParams: {
        'studentCode': studentCode,
        'merchantCode': merchantCode,
        'app': '2',
        'token': UserOption.token
      },
    );
    return MiniProgramPath(path);
  }

  /// 课后练习
  /// 示例：exercisesAfterSchool/index
  static MiniProgramPath afterClassExercises({
    @required required String studentCode,
    @required required String merchantCode,
  }) {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
      pathComponents: ['exercisesAfterSchool', 'index'],
      queryParams: {
        'studentCode': studentCode,
        'merchantCode': merchantCode,
        'app': '2',
        'token': UserOption.token,
      },
    );
    return MiniProgramPath(path);
  }
}
