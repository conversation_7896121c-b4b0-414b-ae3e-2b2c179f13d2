package com.dxznjy.alading.activity;


import android.annotation.SuppressLint;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.Toast;

import androidx.annotation.LayoutRes;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;

import com.blankj.rxbus.RxBus;
import com.dxznjy.alading.AppManager;
import com.dxznjy.alading.R;
import com.dxznjy.alading.http.BaseSubscriber;
import com.gyf.barlibrary.ImmersionBar;
import com.kaopiz.kprogresshud.KProgressHUD;

import butterknife.ButterKnife;
import butterknife.Unbinder;
import io.dcloud.feature.sdk.DCSDKInitConfig;
import io.dcloud.feature.sdk.DCUniMPSDK;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;

public abstract class BaseDxActivity extends AppCompatActivity implements Handler.Callback{
    // 布局view
    protected View contentView;
    protected View rootView;
    protected Handler mHandler;
    boolean noFits = true;
    private Unbinder binder;

    KProgressHUD hud;
    public void showDialog(){
        if(hud == null){
            hud = KProgressHUD.create(this)
                    .setStyle(KProgressHUD.Style.SPIN_INDETERMINATE)
                    .setSize(50, 50)
                    .setCancellable(true)
                    .setAnimationSpeed(2)
                    .setDimAmount(0.5f)
                    .show();
        }else{
            hud.show();
        }
    }


    public void showDialogLoadVideo(){
        if(hud == null){
            hud = KProgressHUD.create(this)
                    .setStyle(KProgressHUD.Style.SPIN_INDETERMINATE)
                    .setLabel("已接收到视频播放，正在准备中")
                    .setSize(300, 120)
                    .setCancellable(true)
                    .setAnimationSpeed(2)
                    .setDimAmount(0.5f)
                    .show();
        }else{
            hud.show();
        }
    }


    public void closeDialog(){
        if(hud != null){
            hud.dismiss();
        }
    }
    @Override
    public void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);
    }
    @Override
    protected void onRestoreInstanceState(@NonNull Bundle savedInstanceState) {
        super.onRestoreInstanceState(savedInstanceState);
    }

    @Override
    protected final void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        AppManager.getAppManager().addActivity(this);
        beforeInflateNofit();
        setContentView(getLayoutId());
        binder = ButterKnife.bind(this);
        initViewAndData();
    }

    @SuppressLint("InflateParams")
    @Override
    public void setContentView(@LayoutRes int layoutResID) {
        rootView = LayoutInflater.from(this).inflate(R.layout.activity_dx_base, null, false);
        if(noFits){
            ImmersionBar.with(this)
                    .fitsSystemWindows(noFits)//设置这个是为了防止布局和顶部的状态栏重叠
                    .statusBarColor(R.color.white)     //状态栏颜色，不写默认透明色
                    .statusBarDarkFont(true,0.2f)   //状态栏字体是深色，不写默认为亮色
                    .navigationBarDarkIcon(true) //导航栏图标是深色，不写默认为亮色
                    .init();
        }else{
            ImmersionBar.with(this)
                    .fitsSystemWindows(noFits)//设置这个是为了防止布局和顶部的状态栏重叠
                    .navigationBarDarkIcon(true) //导航栏图标是深色，不写默认为亮色
                    .statusBarDarkFont(true,0.2f)   //状态栏字体是深色，不写默认为亮色
                    .init();
        }
        contentView = LayoutInflater.from(this).inflate(layoutResID, null, false);
        RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        contentView.setLayoutParams(params);
        RelativeLayout mContainer = rootView.findViewById(R.id.container);
        mContainer.addView(contentView);
        setContentView(rootView);
        mHandler = new Handler(this);
    }


    public void inVisableDbTitleBar(){
        ((RelativeLayout)rootView.findViewById(R.id.rl_titlebar)).setVisibility(View.GONE);
    }
    public void onBack() {
        ((ImageView)rootView.findViewById(R.id.img_base_back)).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
    }
    @Override
    public boolean handleMessage(@NonNull Message message) {
        return false;
    }

    public abstract int getLayoutId();

    /**
     * 防止有一些操作要在生成界面之前
     */
    public void beforeInflateNofit() {
    }

    public void setNoFits(){
        noFits = false;
    }
    public abstract void initViewAndData();

    @Override
    public void onDestroy() {
        if (binder != null && binder != Unbinder.EMPTY)
            binder.unbind();
        this.binder = null;
        RxBus.getDefault().unregister(this);
        AppManager.getAppManager().removeActivity(this);
        super.onDestroy();
    }
    public void request(rx.Observable observable, BaseSubscriber s) {
        observable.subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(s);
    }
    public void showToast(String msg) {
//        ToastUtils.showShort(msg);
//        Toast.makeText(this, msg, Toast.LENGTH_SHORT).show();
        Toast toast = Toast.makeText(this, msg, Toast.LENGTH_SHORT);
        //设置位置    参数1:表示设置toast的位置   参数2:表示相对参数1位置的x偏移量    参数3:表示相对参数1位置的Y偏移量
        toast.setGravity(Gravity.TOP, 0, 0);
        toast.show();
    }



}
