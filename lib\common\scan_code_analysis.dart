import 'dart:convert';
import 'dart:io';

import 'package:bot_toast/bot_toast.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:scan/scan.dart'; // 导入 scan 插件
import 'package:permission_handler/permission_handler.dart'; // 导入权限请求插件
import 'package:image_picker/image_picker.dart';
import 'package:student_end_flutter/common/user_option.dart';
import '../pages/mine/mine_page.dart';
import '../utils/android_ios_plugin.dart';
import '../navigator/path_generator.dart';

// import '../addGroup/add_group.dart';  // 导入相册选择插件

class QRScannerScreenanAlysis extends StatefulWidget {
  @override
  _QRScannerScreenanAlysisState createState() =>
      _QRScannerScreenanAlysisState();
}

class _QRScannerScreenanAlysisState extends State<QRScannerScreenanAlysis> {
  ScanController controller = ScanController(); // 创建扫描控制器
  bool _isDisposed = false; // 添加页面状态标记

  @override
  void initState() {
    super.initState();
    controller.resume();
  }

  @override
  dispose() {
    _isDisposed = true;
    // 确保摄像头完全释放
    controller.pause();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        // 先关闭所有弹窗
        Navigator.of(context).popUntil((route) => route.isFirst);
        // 释放摄像头资源
        controller.pause();
        // 延迟退出，确保资源释放完成
        await Future.delayed(const Duration(milliseconds: 100));
        Navigator.pop(context);
        return false;
      },
      child: Scaffold(
        backgroundColor: Colors.black,
        appBar: AppBar(
          backgroundColor: Colors.black,
          centerTitle: true,
          leading: IconButton(
            icon: Icon(
              Icons.arrow_back_ios_new,
              color: Colors.white,
              size: 20,
            ),
            onPressed: () {
              // 手动返回按钮也使用相同的逻辑
              controller.pause();
              Navigator.pop(context);
            },
          ),
          title: Text("扫描二维码",
              style: TextStyle(
                  color: Colors.white, fontSize: 16, fontFamily: "R")),
          actions: [
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 20),
              child: InkWell(
                onTap: () async {
                  // var photoStatus = await Permission.photos.request();
                  // if(photoStatus.isGranted){
                  _selectImageFromGallery();
                  // }else{
                  // permissionUtils.showSettingDialog(context,title: "权限请求",content: "我们需要访问您的相册来选择照片。如果您愿意，请在设置中打开权限");
                  // }
                },
                child: Text(
                  "相册",
                  style: TextStyle(
                      color: Colors.white, fontSize: 16, fontFamily: "R"),
                ),
              ),
            )
          ],
        ),
        body: Stack(
          alignment: Alignment.center,
          children: [
            ScanView(
              controller: controller,
              scanAreaScale: 0.7, // 设置扫描区域占满整个屏幕
              scanLineColor: Colors.green.shade400, // 设置扫描线颜色
              onCapture: (data) {
                _dataOption(data, true);
                controller.pause();
              },
            ),
            Positioned(
                bottom: 170,
                child: Text(
                  "支持扫码查看解析",
                  style: TextStyle(
                      color: Color.fromRGBO(255, 255, 255, 0.6),
                      fontFamily: "R",
                      fontSize: 14),
                ))
          ],
        ),
      ),
    );
  }

  /// 选择相册中图片返回
  _selectImageFromGallery() async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(source: ImageSource.gallery);
    if (pickedFile != null) {
      String result = await Scan.parse(pickedFile.path) ?? '';
      _dataOption(result, false);
    } else {
      controller.pause();
      controller.resume();
    }
  }

  _dataOption(result, isCarme) {
    _jumpPage(result);
  }

  _jumpPage(data) {
    // 检查扫码数据是否为空
    if (data.isEmpty) {
      _showErrorDialog();
      return;
    }

    // 解析扫码获取的printId参数
    String printId = _parsePrintId(data);

    if (printId.isNotEmpty) {
      // 构建小程序路径并打开小程序
      _openQuestionAnalysisMiniProgram(printId);
    } else {
      _showErrorDialog();
    }
  }

  /// 解析扫码获取的printId参数
  String _parsePrintId(String scanData) {
    try {
      if (scanData.isEmpty) {
        return '';
      }

      // 先进行URL解码
      String decodedData = Uri.decodeComponent(scanData);

      // 截取 printId= 的位置
      int printIdIndex = decodedData.indexOf('printId=');
      if (printIdIndex != -1) {
        String printIdPart =
            decodedData.substring(printIdIndex + 8); // 8是 'printId=' 的长度

        // 如果后面还有其他参数，取到 & 为止
        int endIndex = printIdPart.indexOf('&');
        if (endIndex != -1) {
          printIdPart = printIdPart.substring(0, endIndex);
        }
        // 打印printId
        print('printId: $printIdPart');
        return printIdPart;
      }

      return '';
    } catch (e) {
      print('解析printId失败: $e');
      return '';
    }
  }

  /// 打开题目解析小程序
  void _openQuestionAnalysisMiniProgram(String printId) {
    String miniProgramPath =
        MiniProgramPathBuilder.buildMiniProgramPath(pathComponents: [
      'errorBook',
      'mathAnswerKey'
    ], queryParams: {
      'printId': printId,
      'token': UserOption.token,
    });
    // 调用小程序打开方法
    // AndroidIosPlugin.openUniApp(miniProgramPath).then((result) {
    //   Navigator.pop(context);
    // }).catchError((error) {
    //   _showErrorDialog();
    // });
    AndroidIosPlugin.openUniApp(miniProgramPath);
    Future.delayed(Duration(milliseconds: 500), () {
      if (mounted) {
        Navigator.pop(context);
      }
    });
    // Navigator.pop(context);
  }

  /// 重新启动扫码
  void _restartScanner() {
    if (!_isDisposed) {
      controller.pause();
      Future.delayed(Duration(milliseconds: 100), () {
        if (!_isDisposed) {
          controller.resume();
        }
      });
    }
  }

  /// 显示错误提示对话框
  void _showErrorDialog() {
    showDialog(
      context: context,
      barrierDismissible: true, // 改为true，允许点击外部关闭
      builder: (context) => AlertDialog(
        title: Text('扫码失败'),
        content: Text('二维码格式不正确，请重新扫码'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context); // 关闭弹窗
              _restartScanner(); // 重新启动扫码
            },
            child: Text('重新扫码'),
          ),
        ],
      ),
    ).then((_) {
      // 弹窗关闭后的处理
      _restartScanner();
    });
  }
}
