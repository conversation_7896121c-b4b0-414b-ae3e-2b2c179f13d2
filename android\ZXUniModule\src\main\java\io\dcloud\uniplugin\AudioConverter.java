package io.dcloud.uniplugin;

import com.arthenica.mobileffmpeg.Config;
import com.arthenica.mobileffmpeg.FFmpeg;
import com.arthenica.mobileffmpeg.LogCallback;
import com.arthenica.mobileffmpeg.LogMessage;

public class AudioConverter {
    // 转换PCM到MP3的方法
    public static int convertPcmToMp3(String inputPcmPath, String outputMp3Path,
                                      int sampleRate, int channels, int bitRate) {
        String[] command = {
                "-f", "s16le",          // PCM格式为16位小端
                "-ar", String.valueOf(sampleRate),  // 采样率
                "-ac", String.valueOf(channels),    // 声道数
                "-i", inputPcmPath,     // 输入PCM文件路径
                "-c:a", "libmp3lame",   // 编码器
                "-b:a", bitRate + "k",  // 比特率
                outputMp3Path           // 输出MP3文件路径
        };

        // 执行FFmpeg命令
        return FFmpeg.execute(command);
    }

    // 转换PCM到WAV的方法
    public static int convertPcmToWav(String inputPcmPath, String outputWavPath,
                                      int sampleRate, int channels, int bitsPerSample) {
        String[] command = {
                "-f", "s" + bitsPerSample + "le",  // PCM格式
                "-ar", String.valueOf(sampleRate),
                "-ac", String.valueOf(channels),
                "-i", inputPcmPath,
                "-c:a", "pcm_s" + bitsPerSample + "le",  // WAV编码器
                outputWavPath
        };

        return FFmpeg.execute(command);
    }

    // 转换PCM的采样率和声道数
    public static int resamplePcm(String inputPcmPath, String outputPcmPath,
                                  int inputSampleRate, int inputChannels, int inputBits,
                                  int outputSampleRate, int outputChannels) {

        // 设置日志回调，用于调试
        Config.enableLogCallback(new LogCallback() {
            @Override
            public void apply(LogMessage logMessage) {
                // 打印FFmpeg日志
                android.util.Log.d("FFmpeg", logMessage.getText());
            }
        });

        // 构建FFmpeg命令
        // 输入格式参数需与原始PCM文件匹配
        String[] command = {
                "-f", "s" + inputBits + "le",         // 输入PCM格式 (s16le表示16位小端)
                "-ar", String.valueOf(inputSampleRate), // 输入采样率
                "-ac", String.valueOf(inputChannels),   // 输入声道数
                "-i", inputPcmPath,                    // 输入文件路径
                "-f", "s" + inputBits + "le",         // 输出PCM格式（保持位深不变）
                "-ar", String.valueOf(outputSampleRate),// 输出采样率
                "-ac", String.valueOf(outputChannels),  // 输出声道数
                "-y",                                  // 覆盖输出文件
                outputPcmPath                          // 输出文件路径
        };

        // 执行FFmpeg命令
        return FFmpeg.execute(command);
    }

}
