//
//  SDKConfig.swift
//  StuMetApp
//
//  Created by 许江泉 on 2024/11/11.
//

import Foundation
struct SDKConfig {
    static let wechatAppId = "wx79465579ceea44ae"
    static let baseUniversalLink =  "https://document.dxznjy.com/zhenxuanUlink/"
    static let kServiceQYAppkey = "bf4b50ff1b7f469c799230a0296dc25a"
    static let payweChatId = "gh_e64a1a89a0ad"
    static let shareId = "gh_d43ada85807b"
    static let dxnId = "gh_32851b0f7b11"
    static let kBuglyKey = "c6f390203a"
    
    static var kNEMeetingkey: String {
        #if DEBUG
        switch EnviromentService.manager.currentEnv {
        case .sit, .uat, .dev:
            return "ee0fb7743cc7658f9d1305bb918b7e42" // /// 测试
        case .prd:
            return "e39eb7ff1a808cf969ea9f7f52aed878" /// 正式
        }
        #else
        return "9f920ca06eaf42806f7635fdb4e5e7eb" /// 正式
        #endif
    }


    static var kAppSecret: String {
        #if DEBUG
        switch EnviromentService.manager.currentEnv {
        case .sit, .uat, .dev:
            return "eb2583cf500e"  /// 测试
        case .prd:
            return "d92a29f86e6c" /// 正式
        }
        #else
        return "d92a29f86e6c" /// 正式
        #endif
    }
    
    static var kSafepay: String {
        return "safepay"
    }
    
    static var kAppScheme: String {
        return "xzScheme"
    }
    

}
