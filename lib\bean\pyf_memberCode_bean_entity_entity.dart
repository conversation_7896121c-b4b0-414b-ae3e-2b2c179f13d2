import 'package:student_end_flutter/generated/json/base/json_field.dart';
import 'package:student_end_flutter/generated/json/pyf_memberCode_bean_entity_entity.g.dart';
import 'dart:convert';
export 'package:student_end_flutter/generated/json/pyf_memberCode_bean_entity_entity.g.dart';

@JsonSerializable()
class PyfMemberCodeBeanEntityEntity {
	String merchantCode = '';
	String studentCode = '';
	String studentName = '';
	int curriculumId = 0;
	String curriculumName = '';
	int totalCourseHours = 0;
	int haveCourseHours = 0;
	int haveDeliverHours = 0;
	int relievedDeliverHours = 0;
	bool isFirstBuyDyy = false;

	PyfMemberCodeBeanEntityEntity();

	factory PyfMemberCodeBeanEntityEntity.fromJson(Map<String, dynamic> json) => $PyfMemberCodeBeanEntityEntityFromJson(json);

	Map<String, dynamic> toJson() => $PyfMemberCodeBeanEntityEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}