plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
}

//apply plugin: 'com.google.gms.google-services'
apply plugin: 'com.huawei.agconnect'
apply plugin: 'com.hihonor.mcs.asplugin'


repositories {
    // 配置HMS Core SDK的Maven仓地址。
    google()        // Google Maven 仓库
    mavenCentral()
    maven {url 'https://developer.huawei.com/repo/'}
    maven {url 'https://developer.hihonor.com/repo'}
    maven { url 'https://mirrors.tencent.com/nexus/repository/maven-public/' }
}

// 寻找签名配置文件
def keystorePropertiesFile = rootProject.file("key.properties")
def keystoreProperties = new Properties()
keystoreProperties.load(new FileInputStream(keystorePropertiesFile))


//必须配置
//def mfph = [
//        //宿主包名
//        "apk.applicationId" : "com.dxznjy.alading",
//]

android {
    namespace = "com.dxznjy.alading"
    compileSdk = 34
    ndkVersion = "25.1.8937393"
    compileOptions {
        encoding "UTF-8"
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }
    dexOptions {
        javaMaxHeapSize "4g"   // 增加内存
    }
    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8
    }
    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.dxznjy.alading"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = 28
        targetSdk = 31
        versionCode = flutter.versionCode
        versionName = flutter.versionName
        ndk {
            abiFilters "armeabi-v7a","arm64-v8a"
        }
        manifestPlaceholders = [
                "apk.applicationId" : "com.dxznjy.alading",
                "VIVO_APPKEY" : "533351f1f79e9151976d177c9bc752e1",
                "VIVO_APPID" : "105839664",
                "HONOR_APPID" : "104502573"
        ]
        multiDexEnabled false
//        manifestPlaceholders = mfph
        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    }
    // 添加 packagingOptions，否则可能会造成资源文件冲突。
    packagingOptions {
        pickFirst 'lib/x86_64/libc++_shared.so'
        pickFirst 'lib/x86/libc++_shared.so'
        pickFirst 'lib/arm64-v8a/libapp.so'
        pickFirst 'lib/armeabi-v7a/libapp.so'
        pickFirst 'lib/arm64-v8a/libflutter.so'
        pickFirst 'lib/armeabi-v7a/libflutter.so'
        pickFirst 'lib/arm64-v8a/libc++_shared.so'
        pickFirst 'lib/armeabi-v7a/libc++_shared.so'
        exclude '**/APMSPlugin/debug/*.jar' // 排除问题JAR
        exclude 'META-INF/*.version'
    }
    //小程序配置  此处配置必须添加 否则无法正确运行
    aaptOptions {
        additionalParameters '--auto-add-overlay'
        //noCompress 'foo', 'bar'
        ignoreAssetsPattern "!.svn:!.git:.*:!CVS:!thumbs.db:!picasa.ini:!*.scc:*~"
    }
    lint {
        baseline = file("lint-baseline.xml")
    }
    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile file(keystoreProperties['storeFile'])
            storePassword keystoreProperties['storePassword']
        }
        debug{
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile file(keystoreProperties['storeFile'])
            storePassword keystoreProperties['storePassword']
        }
    }
    buildTypes {
        debug {
            //签名
            minifyEnabled false
            shrinkResources false
            signingConfig signingConfigs.release
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard.cfg'
        }
        release {
            //签名
            minifyEnabled false
            shrinkResources false
            signingConfig signingConfigs.release
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard.cfg'
        }
    }

    packagingOptions {
        jniLibs {
            useLegacyPackaging = true
        }
    }
    lint {
        baseline = file("lint-baseline.xml")
    }
}
configurations {
    all*.exclude group: 'net.lingala.zip4j', module: 'zip4j'
}
configurations.all {
    resolutionStrategy.cacheChangingModulesFor(0, "seconds")
    resolutionStrategy.cacheDynamicVersionsFor(0, "seconds")
}
flutter {
    source = "../.."
}
dependencies {
//    api('com.netease.yunxin.kit.meeting:meeting-dingxiao:4.9.6') {
//        exclude module: 'flutter_embedding',group:'io.flutter'
//    }
    implementation("androidx.appcompat:appcompat:1.6.1")
    implementation("com.google.android.material:material:1.8.0")
    implementation("androidx.constraintlayout:constraintlayout:2.1.4")
    testImplementation("junit:junit:4.13.2")
    androidTestImplementation("androidx.test.ext:junit:1.1.5")
    androidTestImplementation("androidx.test.espresso:espresso-core:3.5.1")
    implementation 'com.github.bumptech.glide:glide:4.15.1'
    annotationProcessor 'com.github.bumptech.glide:compiler:4.15.1'
    implementation 'com.squareup.okhttp3:logging-interceptor:3.4.1'
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:adapter-rxjava:2.9.0'
    implementation 'com.gyf.immersionbar:immersionbar:2.3.3-beta15'
    implementation 'com.github.vondear.RxTool:RxKit:v2.3.9'
    implementation "com.blankj:rxbus:1.1"
    implementation 'com.blankj:utilcode:1.23.7'
    // ButterKnife的sdk
    implementation 'com.jakewharton:butterknife:10.2.1'
    annotationProcessor 'com.jakewharton:butterknife-compiler:10.2.1'
    //recycleview
    api 'com.github.CymChad:BaseRecyclerViewAdapterHelper:2.9.28'
    implementation 'com.flyco.tablayout:FlycoTabLayout_Lib:2.1.2@aar'
    implementation 'com.github.getActivity:XXPermissions:18.5'
    implementation 'com.kaopiz:kprogresshud:1.2.0'
    api 'com.squareup.retrofit2:converter-gson:2.9.0'
    api 'com.scwang.smartrefresh:SmartRefreshLayout:1.1.0-alpha-18'
    api 'com.scwang.smartrefresh:SmartRefreshHeader:1.1.0-alpha-18'
//    //rxjava
    api('io.reactivex:rxandroid:1.2.1') {
        exclude module: 'rxjava'
    }
    api 'io.reactivex:rxjava:1.2.7'
    api 'com.tencent.mm.opensdk:wechat-sdk-android:6.8.30'
    api 'com.alipay.sdk:alipaysdk-android:+@aar'
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation fileTree(include: ['*.aar'], dir: 'libs')
    implementation 'androidx.recyclerview:recyclerview:1.0.0' //必须集成，android 自带recyclerview支持
    implementation 'androidx.legacy:legacy-support-v4:1.0.0' //必须集成，androidx support支持
    implementation 'androidx.appcompat:appcompat:1.0.0' //必须集成，androidx appcompat支持
    implementation 'com.alibaba:fastjson:1.2.83' //必须集成，fastjson功能需要
    implementation 'com.facebook.fresco:fresco:2.5.0'//必须集成，图片加载需要
    implementation 'com.facebook.fresco:animated-gif:2.5.0'//必须集成，图片加载需要
    implementation 'com.github.bumptech.glide:glide:4.9.0'//必须集成，图片加载需要
    implementation 'androidx.webkit:webkit:1.8.0' //3.6.15版本之后 必须集成，用来支持暗黑模式
    implementation project(':ZXUniModule')
    implementation 'com.squareup.okhttp3:okhttp:3.7.0'
    implementation 'com.squareup.okio:okio:1.8.0'
    implementation 'androidx.core:core-ktx:1.10.0'
    implementation 'androidx.core:core:1.6.0'
    implementation 'com.android.support:multidex:1.0.3'
    implementation 'com.qiyukf.unicorn:unicorn:9.11.0'
    implementation 'com.tencent.bugly:crashreport:latest.release' //其中latest.release指代最新Bugly SDK版本号，也可以指定明确的版本号，例如4.0.3
    implementation 'com.tencent.timpush:timpush:8.6.7019'
    implementation 'com.tencent.timpush:huawei:8.6.7019'
    implementation 'com.tencent.timpush:xiaomi:8.6.7019'
    implementation 'com.tencent.timpush:oppo:8.6.7019'
    implementation 'com.tencent.timpush:vivo:8.6.7019'
    implementation 'com.tencent.timpush:honor:8.6.7019'
    implementation 'com.tencent.timpush:meizu:8.6.7019'
//    implementation 'com.writingminds:FFmpegAndroid:0.3.2'
    // Google Firebase Cloud Messaging (Google FCM)
//    implementation 'com.tencent.timpush:fcm:8.6.7019'
//    implementation 'com.huawei.agconnect:agconnect-core:1.5.2.300'
}
