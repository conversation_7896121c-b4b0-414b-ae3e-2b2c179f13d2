<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="LaunchTheme" parent="@android:style/Theme.Black.NoTitleBar">
        <!-- Show a splash screen on the activity. Automatically removed when
             the Flutter engine draws its first frame -->
        <item name="android:windowBackground">@drawable/launch_background</item>
        <item name="android:windowAnimationStyle">@style/uniMPHostWindowAnimation</item>
        <item name="android:forceDarkAllowed">false</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowDrawsSystemBarBackgrounds">false</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    </style>
    <style name="AppTheme" parent="MyThemeRed"/>
    <style name="MyThemeRed" parent="Theme.AppCompat.Light.NoActionBar">
        <!--选中状态icon的颜色和字体颜色-->
        <item name="colorPrimary">@color/colorAccent</item>
        <item name="colorPrimaryDark">@color/colorAccent</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowDisablePreview">false</item>
        <item name="android:windowBackground">@drawable/launch_background</item>
        <item name="android:windowAnimationStyle">@style/uniMPHostWindowAnimation</item>
    </style>
    <!-- 默认Activity跳转动画 -->
    <style name="default_animation" mce_bogus="1" parent="@android:style/Animation.Activity">
        <item name="android:activityOpenEnterAnimation">@anim/activity_up_in</item>
        <item name="android:activityOpenExitAnimation">@anim/activity_up_out</item>
        <item name="android:activityCloseEnterAnimation">@anim/activity_down_in</item>
        <item name="android:activityCloseExitAnimation">@anim/activity_down_out</item>
    </style>
    <style name="MySeekbarSytle" parent="Base.Widget.AppCompat.SeekBar">
        <item name="android:thumb">@drawable/ic_thumb_n</item>
        <item name="android:progressDrawable">@drawable/custom_seekbar_progress_default</item>
    </style>
</resources>
