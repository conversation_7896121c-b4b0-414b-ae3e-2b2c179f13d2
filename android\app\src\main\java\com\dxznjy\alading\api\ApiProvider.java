package com.dxznjy.alading.api;


import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import com.dxznjy.alading.DXApplication;
import com.dxznjy.alading.common.Constants;
import com.dxznjy.alading.common.GlobalConfig;
import com.dxznjy.alading.http.CustomGsonConverterFactory;
import com.dxznjy.alading.response.LoginResponse;
import com.google.gson.Gson;
import com.vondear.rxtool.RxSPTool;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

import okhttp3.Interceptor;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.logging.HttpLoggingInterceptor;
import retrofit2.Retrofit;
import retrofit2.adapter.rxjava.RxJavaCallAdapterFactory;
import retrofit2.converter.gson.GsonConverterFactory;

/**
 * Created by Administrator on 2016/9/3.
 */

public class ApiProvider {
    LiveNetworkMonitor networkMonitor;
    //    Context context;
    boolean payToken=false;
    public static final String BASE_URL = GlobalConfig.getBaseUrl();
    private ApiProvider(Context context){
        HttpLoggingInterceptor loggingInterceptor = new HttpLoggingInterceptor(new HttpLoggingInterceptor.Logger() {
            @Override
            public void log(String message) {
                if (message.length() > 4000) {
                    for (int i = 0; i < message.length(); i += 4000) {
                        //当前截取的长度<总长度则继续截取最大的长度来打印
                        if (i + 4000 < message.length()) {
                            Log.i(" zgj" + i, message.substring(i, i + 4000));
                        } else {
                            //当前截取的长度已经超过了总长度，则打印出剩下的全部信息
                            Log.i(" zgj" + i, message.substring(i, message.length()));
                        }
                    }
                } else {
                    //直接打印
                    Log.i(" zgj", message);
                }
            }
        });
        loggingInterceptor.setLevel(HttpLoggingInterceptor.Level.BODY);
        networkMonitor = new LiveNetworkMonitor(DXApplication.app);
        OkHttpClient.Builder builder = new OkHttpClient().newBuilder();
        builder.retryOnConnectionFailure(true);
        builder.readTimeout(100, TimeUnit.SECONDS);
        builder.connectTimeout(100, TimeUnit.SECONDS);
        builder.addInterceptor(loggingInterceptor);
        builder.addInterceptor(new Interceptor() {
            @Override
            public Response intercept(Chain chain) throws IOException {
                boolean connected = networkMonitor.isConnected();
                String loginInfo=RxSPTool.getString(context, Constants.STUDENT_INFO);
//                String token="";
//                if (!(TextUtils.isEmpty(loginInfo))){
//                    LoginResponse.Login login=new Gson().fromJson(loginInfo, LoginResponse.Login.class);
//                    token=login.getToken();
//                }
                String token= RxSPTool.getString(context,Constants.STUDENT_TOKEN);
                if (connected) {
                    Request.Builder requestBuilder = chain
                            .request()
                            .newBuilder();
                    requestBuilder
                            .addHeader("Content-Type", "application/json")
                            .addHeader("Accept", "application/json")
                            .addHeader("dx-source","ZHEN_XUAN##STUDENT##APP")
                            .addHeader("X-Www-iap-Assertion",token)
                            .build();
                    Response response = chain.proceed(requestBuilder.build());
                    return response;
                } else {
                    throw new RuntimeException("网络出错，请检查网络连接");
                }
            }
        });
        OkHttpClient client = builder.build();
        retrofit =
                new Retrofit.Builder().baseUrl(BASE_URL)
                        .client(client)
                        .addConverterFactory(CustomGsonConverterFactory.create())
                        .addConverterFactory(GsonConverterFactory.create())
                        .addCallAdapterFactory(RxJavaCallAdapterFactory.create())
                        .build();
        DFService = retrofit.create(com.dxznjy.alading.api.service.DFService.class);
    }
    private static ApiProvider instance;
    public static ApiProvider getInstance(Context context){
        if (instance == null){
            instance = new ApiProvider(context);
        }
        return instance;
    }
    public com.dxznjy.alading.api.service.DFService DFService;
    public Retrofit retrofit;

}
