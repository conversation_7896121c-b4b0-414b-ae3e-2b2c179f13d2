import 'package:student_end_flutter/generated/json/base/json_field.dart';
import 'package:student_end_flutter/generated/json/zx_course_entity.g.dart';
import 'dart:convert';
export 'package:student_end_flutter/generated/json/zx_course_entity.g.dart';

@JsonSerializable()
class ZxCourseEntity {
	late int code = 0;
	late bool success = false;
	late String message = '';
	late ZxCourseData data;
	late int total = 0;

	ZxCourseEntity();

	factory ZxCourseEntity.fromJson(Map<String, dynamic> json) => $ZxCourseEntityFromJson(json);

	Map<String, dynamic> toJson() => $ZxCourseEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ZxCourseData {
	late String currentPage = '';
	late String totalPage = '';
	late String size = '';
	late List<ZxCourseDataData> data = [];
	late String totalItems = '';

	ZxCourseData();

	factory ZxCourseData.fromJson(Map<String, dynamic> json) => $ZxCourseDataFromJson(json);

	Map<String, dynamic> toJson() => $ZxCourseDataToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ZxCourseDataData {
	late String goodsId = '';
	late String goodsName = '';
	late String goodsDesc = '';
	late String usageInstruction = '';
	late String goodsCategoryId = '';
	late String goodsCategoryName = '';
	late int goodsType = 0;
	late String goodsPicUrl = '';
	late String goodsSpecialPicUrl = '';
	late String goodsVideoUrl = '';
	late int goodsStock = 0;
	late int goodsSales = 0;
	late double goodsCostPrice;
	late double goodsMaxCostPrice;
	late double goodsOriginalPrice;
	late double goodsVipPrice;
	late double goodsOriginalDiscountPrice;
	late double goodsVipDiscountPrice;
	late String goodsCurrencyUnit = '';
	late String goodsCurrencyDesc = '';
	late int goodsCommission = 0;
	late double goodsMaxOriginalPrice;
	late int goodsMaxOriginalDiscountPrice = 0;
	late double goodsMaxVipPrice;
	late double goodsShowPrice;
	late int goodsMaxVipDiscountPrice = 0;
	late int goodsCollect = 0;
	late int goodsWeight = 0;
	late String goodsTagOne = '';
	late String goodsTagTwo = '';
	late String goodsTagThree = '';
	late int goodsStatus = 0;
	late String onSaleTime = '';
	late String curriculumId = '';
	late String curriculumName = '';
	late ZxCourseDataDataCoupon coupon;
	late int whetherCollect = 0;
	late String goodsSharePoster = '';
	late String goodsSharePosterTwo = '';
	late String goodsSharePosterThree = '';
	late int goodsRecommendationWeight = 0;
	late int goodsRecommendationWhetherTop = 0;
	dynamic studentCourse;
	late List<dynamic> goodsSpecList = [];
	late List<dynamic> goodsSpecPriceList = [];
	late List<dynamic> goodsCarouselList = [];
	late List<ZxCourseDataDataGoodsShareTextList> goodsShareTextList = [];
	late List<dynamic> goodsCatalogueList = [];
	late List<dynamic> piGoodsExchangeCardList = [];

	ZxCourseDataData();

	factory ZxCourseDataData.fromJson(Map<String, dynamic> json) => $ZxCourseDataDataFromJson(json);

	Map<String, dynamic> toJson() => $ZxCourseDataDataToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ZxCourseDataDataCoupon {
	late String id = '';
	late String couponName = '';
	late String couponStartTime = '';
	late String couponEndTime = '';
	late int couponEffectiveDays = 0;
	late int couponHasCondition = 0;
	late int couponCondition = 0;
	late double couponDiscount;
	late int couponLimitType = 0;
	late int couponLimitQuantity = 0;
	late int couponQuantity = 0;
	late int couponReceivedQuantity = 0;
	late int couponType = 0;
	late int couponStatus = 0;
	late String createdTime = '';
	late String updatedTime = '';
	late int personLimitType = 0;
	late int couponUserType = 0;
	late int couponReceivedType = 0;
	late int couponTaskType = 0;
	late List<dynamic> couponVoList = [];
	late int showApp = 0;
	late String gainWay = '';
	late int gainPersonLimitType = 0;
	late int shareable = 0;
	late int createRedemptionCode = 0;
	late int useSharePersonLimitType = 0;

	ZxCourseDataDataCoupon();

	factory ZxCourseDataDataCoupon.fromJson(Map<String, dynamic> json) => $ZxCourseDataDataCouponFromJson(json);

	Map<String, dynamic> toJson() => $ZxCourseDataDataCouponToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ZxCourseDataDataGoodsShareTextList {
	late String id = '';
	late String goodsId = '';
	late String shareText = '';

	ZxCourseDataDataGoodsShareTextList();

	factory ZxCourseDataDataGoodsShareTextList.fromJson(Map<String, dynamic> json) => $ZxCourseDataDataGoodsShareTextListFromJson(json);

	Map<String, dynamic> toJson() => $ZxCourseDataDataGoodsShareTextListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}