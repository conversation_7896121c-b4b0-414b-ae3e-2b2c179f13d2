
import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:student_end_flutter/common/CommonUtil.dart';
import 'package:student_end_flutter/res/colors.dart';
import 'package:student_end_flutter/utils/android_ios_plugin.dart';

import '../common/ne_user_option.dart';
import '../utils/ne_metting_utils.dart';
import '../utils/sharedPreferences_util.dart';

class SdkDescriptionDialog extends StatefulWidget {
  var agreeFunc,onClickXieYi,onClickYinsi;
  SdkDescriptionDialog({this.agreeFunc,this.onClickXieYi,this.onClickYinsi});

  @override
  _SdkDescriptionDialogState createState() =>
      _SdkDescriptionDialogState();
}

class _SdkDescriptionDialogState extends State<SdkDescriptionDialog> {

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(context);
    double screenHeight = MediaQuery.of(context).size.height;
    String agreement = "欢迎来到鼎校甄选，我们非常重视您的隐私个人信息和隐私保护，请仔细阅读《用户服务协议》和《隐私正常》我们将按照您同意的条款使用您的个人信息，以便为您提供服务。"
        "为提高服务安全性、保障运营质量及效率，我们会收集您的设备信息,用户信息(SN)。"
        "为了保障App运行以及能够向您提供更全面的功能服务，应用集成了第三方SDK,需申请以下权限。"
        "\n包括但不限于："
        "\n网易会议SDK:\n(1)共享信息名称：设备信息,用户信息(SN)。\n(2)使用目的：用于检测进入视频会议环境。\n(3)使用场景：用于用户在应用内,加入会议进行视频聊天。\n(4)共享方式：Sdk自主收集。\n(5)第三方个人信息处理规则：https://meeting.163.com/privacy/agreement_mobile_ysbh_wap.shtml"
        "\n微信分享SDK:\n(1)共享信息名称：设备信息,用户信息(SN)。\n(2)使用目的：微信分享功能。\n(3)使用场景：用于用户在应用内,分享相关信息。\n(4)共享方式：Sdk自主收集。\n(5)第三方个人信息处理规则：https://www.wechat.com/zh_CN/privacy_policy.html"
        "\n同时应用申请以下权限："
        "\n申请手机SD卡读写功能存储文件,手机拍照功能;"
        "\n申请相册权限，用于选取图片;"
        "\n以上权限都是系统公开权限。如您想开启或关闭相关权限，你可以在手机设置--应用程序管理--鼎校甄选--权限管理中更改状态(各厂商机型设置路径可能存在不一致，您可参考厂商设置说明)"
        "\n\n如同意以上申请，则点击同意进入应用，否则点击退出关闭应用！";
    return PopScope(
        canPop: false,
        child:Container(
          height: screenHeight,
          alignment:Alignment.center,
          child:    SingleChildScrollView(
            child:Container(
              alignment: Alignment.center,
              width: double.infinity,
              margin: EdgeInsets.only(left: 20.w, right: 20.w),
              padding: EdgeInsets.only(left: 6.w,right: 6.w,bottom: 20.h,top: 20.h),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(15)),
                  color: Colors.white),
              child:Column(
                mainAxisSize: MainAxisSize.min, // 确保列的大小适中
                children: [
                  Text(
                    '温馨提示',
                    style: TextStyle(
                        decoration: TextDecoration.none,
                        color: AppColors.black,
                        fontWeight: FontWeight.bold,
                        fontSize: 18
                    ),
                  ),
                  SizedBox(
                    height: 300.h,
                    child:   SingleChildScrollView(
                      scrollDirection: Axis.vertical,
                      child:Padding(
                        padding:  EdgeInsets.only(left: 14.w,right: 14.w,top: 10.h),
                        child: Text(agreement,
                          style: TextStyle(
                            decoration: TextDecoration.none,
                            color:HexColor('333333'),
                            fontSize: 14,
                            fontWeight: FontWeight.normal,
                          ),
                        ) ,),
                    ),
                  ),
                  SizedBox(
                    height: 10.h,
                  ),
                  RichText(
                    text: TextSpan(
                      style: TextStyle(color: AppColors.f333333, fontSize: 15),
                      children: <TextSpan>[
                        TextSpan(text: '请您阅读'),
                        TextSpan(
                          style: TextStyle(color: AppColors.f368e5c, fontSize:15),
                          text: '《用户协议》',
                          recognizer: TapGestureRecognizer()..onTap = () {
                            if(widget.onClickXieYi != null){
                              widget.onClickXieYi();
                            }
                          },
                        ),
                        TextSpan(text: '和',
                          style: TextStyle(color: AppColors.f333333, fontSize: 15),
                        ),
                        TextSpan(
                          style: TextStyle(color: AppColors.f368e5c, fontSize: 15),
                          text: '《隐私政策》',
                          recognizer: TapGestureRecognizer()..onTap = () {
                            if(widget.onClickYinsi != null){
                              widget.onClickYinsi();
                            }
                          },
                        ),
                      ],
                    ),
                  ),
                  Column(
                    children: [
                      GestureDetector(
                        onTap: (){
                          // ENUserOption.initToken();
                          // NEMeetingUtil.commonInit();//初始化云信相关配置
                          SharedPreferencesUtil.saveData(SharedPreferencesUtil.agreeTips,1 );
                          Navigator.pop(context);
                          if(widget.agreeFunc != null){
                            widget.agreeFunc();
                          }
                        },
                        child: Container(
                            height: 40.h,
                            alignment: Alignment.center,
                            margin: EdgeInsets.only(left: 14.w,right: 14.w,top: 20.h,bottom: 10.h),
                            decoration: BoxDecoration(
                              borderRadius:BorderRadius.circular(20),
                              color:AppColors.f368e5c,
                            ),
                            child: Text(
                              '同意并进入',
                              style: TextStyle(
                                  fontWeight: FontWeight.normal,
                                  decoration: TextDecoration.none,
                                  color: AppColors.white,
                                  fontSize: 16
                              ),
                            )
                        ),
                      ),
                      GestureDetector(
                        onTap: (){
                          SharedPreferencesUtil.saveData(
                              SharedPreferencesUtil.agreeTips,0 );
                          AndroidIosPlugin.exitApp();
                        },
                        child: Container(
                            height: 30.h,
                            alignment: Alignment.center,
                            child: Text(
                              '退出',
                              style: TextStyle(
                                  fontWeight: FontWeight.normal,
                                  decoration: TextDecoration.none,
                                  color: AppColors.f999999,
                                  fontSize: 16
                              ),
                            )
                        ),
                      ),
                    ],
                  )
                ],
              ),
            ),
          ),
        )
    );
  }
}