//
//  BaseViewController.swift
//  StuMetApp
//
//  Created by 许江泉 on 2024/11/12.
//

import QMUIKit
import UIKit

class BaseViewController: QMUICommonViewController {
    override func viewDidLoad() {
        super.viewDidLoad()
        // 设置ui
        setupUI()
        // 数据请求
        fetchData()
    }

    func setupUI() {
        view.backgroundColor = .white
        navigationController?.navigationBar.isTranslucent = false
        extendedLayoutIncludesOpaqueBars = false
        setupNavBar()
    }

    func fetchData() {
        
    }

    /// 设置导航的背景颜色
    private func setupNavBar(_ bgColor: UIColor = .white) {
        if #available(iOS 13.0, *) {
            let barAppearance = UINavigationBarAppearance()
            barAppearance.backgroundColor = bgColor
            barAppearance.shadowImage = UIImage()
            barAppearance.shadowColor = .clear
            barAppearance.backgroundImage = nil
            barAppearance.titleTextAttributes = [NSAttributedString.Key.font: SMFont.body1() as Any, NSAttributedString.Key.foregroundColor: UIColor.hex333333]
            navigationController?.navigationBar.tintColor = UIColor.hex333333
            navigationController?.navigationBar.standardAppearance = barAppearance
            navigationController?.navigationBar.scrollEdgeAppearance = barAppearance
        } else {
            let barImg = UIImage.imageWithColor(bgColor)
            navigationController?.navigationBar.setBackgroundImage(barImg, for: .any, barMetrics: .default)
        }
    }

    func setNavBarHidden(ishidden: Bool = false) {
        navigationController?.navigationBar.isHidden = ishidden
    }

    override var preferredStatusBarStyle: UIStatusBarStyle {
        .default
    }

    // MARK: - 右边按钮事件
    /// 右边按钮事件
    @objc public func rightButtonAction(_ btn: UIButton) {}

    override func shouldCustomizeNavigationBarTransitionIfHideable() -> Bool {
        return true
    }

    override func forceEnableInteractivePopGestureRecognizer() -> Bool {
        return true
    }

    /// 影藏导航条
    override func preferredNavigationBarHidden() -> Bool {
        return false
    }
 
}
