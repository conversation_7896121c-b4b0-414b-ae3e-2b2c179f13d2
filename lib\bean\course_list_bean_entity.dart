import 'package:student_end_flutter/generated/json/base/json_field.dart';
import 'package:student_end_flutter/generated/json/course_list_bean_entity.g.dart';
import 'dart:convert';
export 'package:student_end_flutter/generated/json/course_list_bean_entity.g.dart';

@JsonSerializable()
class CourseListBeanEntity {
	late int currentPage;
	late int totalPage;
	late int size;
	late List<CourseListBeanData> data;
	late int totalItems;

	CourseListBeanEntity();

	factory CourseListBeanEntity.fromJson(Map<String, dynamic> json) => $CourseListBeanEntityFromJson(json);

	Map<String, dynamic> toJson() => $CourseListBeanEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class CourseListBeanData {
	late String courseId;
	late String planId;
	late String courseName;
	late String courseType;
	late String courseTime;
	late int status;
	late String teacherId;
	late String teacherName;
	late String teacherPhoto;
	late String meetingId;
	late String meetingNum;
	late String feedback;
	late String studentCode;
	late String studentName;
	late String classPlanStudyId;
	int? oneToManyType;
	int? leaveStatus;
	bool? experience;
	int? lessonsFlag;
	String? planStudyId;

	CourseListBeanData();

	factory CourseListBeanData.fromJson(Map<String, dynamic> json) => $CourseListBeanDataFromJson(json);

	Map<String, dynamic> toJson() => $CourseListBeanDataToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}