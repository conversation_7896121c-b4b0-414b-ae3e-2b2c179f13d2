
import 'package:dio/dio.dart';
// import 'package:netease_meeting_kit/meeting_ui.dart';
import 'package:student_end_flutter/common/user_option.dart';
import '../bean/yunxin_userinfo_bean_entity.dart';
import '../dialog/dialog_learn_select_identity.dart';
import '../generated/json/base/json_convert_content.dart';
import '../utils/httpRequest.dart';
import '../utils/ne_metting_utils.dart';
import '../utils/sharedPreferences_util.dart';
import '../utils/utils.dart';
import 'Config.dart';
import 'sdk_config.dart';

typedef MeetingHandler = void Function(bool succeed);

enum UserType { student, patriarch }
class ENUserOption {
  static String userUuid = "";
  static String yxUserToken = "";
  static bool get iSLogin {
    if (userUuid.isNotEmpty && yxUserToken.isNotEmpty){
      return true;
    }
    return false;
  }

  static cleanData() {
    userUuid = "";
    yxUserToken = "";
    SharedPreferencesUtil.deleteData(SharedPreferencesUtil.userUuid);
    SharedPreferencesUtil.deleteData(SharedPreferencesUtil.yxUserToken);
  }

  static saveData(String userUuid, String accountToken) {
    userUuid = userUuid;
    yxUserToken = accountToken;
    SharedPreferencesUtil.saveData(SharedPreferencesUtil.userUuid, userUuid);
    SharedPreferencesUtil.saveData(SharedPreferencesUtil.yxUserToken, accountToken);
  }
  //
  // static  initToken() async {
  //   String uid = await SharedPreferencesUtil.getData<String>(SharedPreferencesUtil.userUuid);
  //   if (uid != null && uid != "" && uid != SharedPreferencesUtil.userUuid) {
  //     userUuid = uid;
  //   }
  //   String t = await SharedPreferencesUtil.getData<String>(
  //       SharedPreferencesUtil.yxUserToken);
  //   if (t != null && t != "" && t != SharedPreferencesUtil.yxUserToken) {
  //     yxUserToken = t;
  //   }
  // }

  // /// 云信信息获取
  // static  getYunXinToken(studentCode, UserType userType, handler) async {
  //   String uuid = Utils.getUuid();
  //   int timestampInMilliseconds = DateTime.now().millisecondsSinceEpoch;
  //   String curTime = timestampInMilliseconds.toString();
  //   String userUuids = userType == UserType.student ? "$studentCode-6" : "${UserOption.userInfoBeanEntity?.mobile}-1";
  //   HttpUtil.heads.putIfAbsent("Content-Type", () => "application/json");
  //   HttpUtil.heads.putIfAbsent("Accept", () => "application/json");
  //   HttpUtil.heads.putIfAbsent("AppKey", () => KSDKConfig.kNEMeetingkey);
  //   HttpUtil.heads.putIfAbsent("Nonce", () => uuid);
  //   HttpUtil.heads.putIfAbsent("CurTime", () => curTime);
  //   HttpUtil.heads.putIfAbsent("checksum", () => Utils.getCheckSum(uuid,curTime, KSDKConfig.kAppSecret));
  //   var response = await HttpUtil().postYx(Config.regYunXin,
  //       data: {
  //         'userUuid':userUuids,
  //         'name':'昵称',
  //       },  options: Options(
  //     headers: {'Content-Type': 'application/json'}, // 明确指定 JSON 类型
  //   ),);
  //   if(response != null){
  //     var response = await HttpUtil().getNoBaseBean("${Config.getYunXinToken}?userUuids=$userUuids");
  //     if(response != null){
  //       YunxinUserinfoBeanEntity? data = JsonConvert.fromJsonAsT<YunxinUserinfoBeanEntity>(response?.data);
  //       if(data!.data.userInfos.isNotEmpty){
  //         String accountToken = data.data.userInfos[0].userToken;
  //         String userUuid = data.data.userInfos[0].userUuid;
  //         bool loginSuccess = await NEMeetingUtil.yxLogin(userUuid, accountToken, userType);
  //         handler(loginSuccess); // 根据登录结果触发回调
  //       }else{
  //         handler(false); // 请求失败处理
  //       }
  //     }else {
  //       handler(false); // 请求失败处理
  //     }
  //   }else {
  //     handler(false); // 请求失败处理
  //   }
  //   HttpUtil.heads.remove("Content-Type");
  //   HttpUtil.heads.remove("Accept");
  //   HttpUtil.heads.remove("AppKey");
  //   HttpUtil.heads.remove("Nonce");
  //   HttpUtil.heads.remove("CurTime");
  //   HttpUtil.heads.remove("checksum");
  // }

  ///记录会议中需要的数据
  static String _curriculum = "";
  static String get curriculum => _curriculum;
  static set curriculum(String value) => _curriculum = value;

  static String _studyId = "";
  static String get studyId => _studyId;
  static set studyId(String value) => _studyId = value;

  static String _studentToken = "";
  static String get studentToken => _studentToken;
  static set studentToken(String value) => _studentToken = value;

  // static yunXinUserRecord(studentCode, UserType userType){
  //   UserInfoRecord.studentCode = studentCode;
  //   UserInfoRecord.studentToken = studentToken;
  //   UserInfoRecord.curriculum = curriculum;
  //   UserInfoRecord.studyId = studyId;
  //   UserInfoRecord.userRole = (userType == UserType.student) ?  0 : 1;
  //
  //   if(Config.URL.contains("uat-gateway") || Config.URL.contains("179")){ //公测
  //     UserInfoRecord.runEnv = "beta";
  //   }else if(Config.URL.contains("gateway")){ // 正式
  //     UserInfoRecord.runEnv = "production";
  //   }else{
  //     UserInfoRecord.runEnv = "dev";
  //   }
  // }
}

