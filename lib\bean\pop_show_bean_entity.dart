import 'package:student_end_flutter/generated/json/base/json_field.dart';
import 'package:student_end_flutter/generated/json/pop_show_bean_entity.g.dart';
import 'dart:convert';
export 'package:student_end_flutter/generated/json/pop_show_bean_entity.g.dart';

@JsonSerializable()
class PopShowBeanEntity {
	late String todayShowData;
	late List<String> popNoShowListId;

	PopShowBeanEntity();

	factory PopShowBeanEntity.fromJson(Map<String, dynamic> json) => $PopShowBeanEntityFromJson(json);

	Map<String, dynamic> toJson() => $PopShowBeanEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}