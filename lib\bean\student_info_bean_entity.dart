import 'package:student_end_flutter/generated/json/base/json_field.dart';
import 'package:student_end_flutter/generated/json/student_info_bean_entity.g.dart';
import 'dart:convert';
export 'package:student_end_flutter/generated/json/student_info_bean_entity.g.dart';

@JsonSerializable()
class StudentInfoBeanEntity {
	late String studentCode = '';
	late String studentName = '';
	late bool isFormal = false;
	late List<StudentInfoBeanMerchantVos> merchantVos = [];

	StudentInfoBeanEntity();

	factory StudentInfoBeanEntity.fromJson(Map<String, dynamic> json) =>
			$StudentInfoBeanEntityFromJson(json);

	Map<String, dynamic> toJson() => $StudentInfoBeanEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class StudentInfoBeanMerchantVos {
	late String name = '';
	late String realName = '';
	late String mobile = '';
	late int isEnable = 0;
	late String merchantCode = '';
	late String province = '';
	late String city = '';
	late String area = '';
	late String address = '';
	late String regTime = '';
	late String merchantName = '';
	late int isCheck = 0;
	late String longitude = '';
	late String latitude = '';
	late String expireDate = '';
	late String marketChargePerson = '';
	late String consultingChargePerson = '';
	late String teachingChargePerson = '';
	late String marketChargePersonPhone = '';
	late String consultingChargePersonPhone = '';
	late String teachingChargePersonPhone = '';
	late String roleTag = '';
	late String channelManagerCode = '';
	late String refereeCode = '';

	StudentInfoBeanMerchantVos();

	factory StudentInfoBeanMerchantVos.fromJson(Map<String, dynamic> json) =>
			$StudentInfoBeanMerchantVosFromJson(json);

	Map<String, dynamic> toJson() => $StudentInfoBeanMerchantVosToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}
