#!/usr/bin/env bash

# Copyright (c) 2022 NetEase, Inc. All rights reserved.
# Use of this source code is governed by a MIT license that can be
# found in the LICENSE file.

#test
serverUrl="https://roomkit-dev.netease.im/scene/meeting"
appkey="bc0993298a60bdd85723cf865609b78b"

#online
#serverUrl="https://roomkit.netease.im/scene/meeting"
#appkey="91d597b20132e6fa131615aa2d229388"

#private
#serverUrl="https://solution-demo.netease.im:28084/scene/meeting"
#appkey="ca08cc8fd90ccb3caf76ac1e4f5a20a6"

username=$1
password=$(md5 -s "$<EMAIL>" | awk -F' = ' '{print $2}')

echo "$username"
echo "$password"
data="{\"password\": \"${password}\",\"nickname\": \"${username}\"}"
echo "$data"

curl --location --request PUT "$serverUrl/$appkey/v1/register/$username" \
--header 'Content-Type: application/json' \
--data "$data"