import 'package:student_end_flutter/generated/json/base/json_field.dart';
import 'package:student_end_flutter/generated/json/course_record_list_bean_entity.g.dart';
import 'dart:convert';
export 'package:student_end_flutter/generated/json/course_record_list_bean_entity.g.dart';

@JsonSerializable()
class CourseRecordListBeanEntity {
	late int currentPage;
	late int totalPage;
	late int size;
	late List<CourseRecordListBeanData> data;
	late int totalItems;

	CourseRecordListBeanEntity();

	factory CourseRecordListBeanEntity.fromJson(Map<String, dynamic> json) => $CourseRecordListBeanEntityFromJson(json);

	Map<String, dynamic> toJson() => $CourseRecordListBeanEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class CourseRecordListBeanData {
	late String id;
	late String userId  = '';
	late String studentCode  = '';
	late String studentName  = '';
	late String courseId = '';
	late String courseName  = '';
	late int courseType = 0;
	late String lastStudyTime = '';
	late String createdTime  = '';
	late String updatedTime  = '';
	late int refundStatus  = 0;

	CourseRecordListBeanData();

	factory CourseRecordListBeanData.fromJson(Map<String, dynamic> json) => $CourseRecordListBeanDataFromJson(json);

	Map<String, dynamic> toJson() => $CourseRecordListBeanDataToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}