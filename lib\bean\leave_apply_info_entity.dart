import 'package:student_end_flutter/generated/json/base/json_field.dart';
import 'package:student_end_flutter/generated/json/leave_apply_info_entity.g.dart';
import 'dart:convert';
export 'package:student_end_flutter/generated/json/leave_apply_info_entity.g.dart';

@JsonSerializable()
class LeaveApplyInfoEntity {
	int? surplusLeaveNum;
	String? className;
	String? courseTime;
	int? studentLeaveId;
	int? configLeaveNum;
	int? courseType;
	int? grade;
	int? curriculumId;

	LeaveApplyInfoEntity();

	factory LeaveApplyInfoEntity.fromJson(Map<String, dynamic> json) => $LeaveApplyInfoEntityFromJson(json);

	Map<String, dynamic> toJson() => $LeaveApplyInfoEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}