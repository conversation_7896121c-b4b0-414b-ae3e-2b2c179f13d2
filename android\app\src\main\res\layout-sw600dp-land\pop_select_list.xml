<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    >
    <LinearLayout
        android:layout_width="280dp"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:orientation="vertical"
        android:paddingTop="20dp"
        android:paddingLeft="20dp"
        android:paddingRight="10dp"
        android:background="@mipmap/pic_beijing"
        >
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_above="@+id/recyclerView"
            >
            <RelativeLayout
                android:layout_width="match_parent"
                android:orientation="vertical"
                android:layout_centerVertical="true"
                android:layout_marginRight="10dp"
                android:layout_height="wrap_content">
                <View
                    android:layout_width="36dp"
                    android:layout_height="4dp"
                    android:layout_marginTop="-6dp"
                    android:layout_below="@+id/tv_title"
                    android:visibility="invisible"
                    android:background="@drawable/bg_green_line"/>

                <TextView
                    android:id="@+id/tv_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="下载列表"
                    android:textColor="@color/_333333"
                    android:textSize="12sp"
                    android:textStyle="bold" />
            </RelativeLayout>
            <LinearLayout
                android:id="@+id/ll_close"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:orientation="horizontal">
                <ImageView
                    android:layout_width="14dp"
                    android:layout_height="14dp"
                    android:layout_marginLeft="14dp"
                    android:src="@mipmap/ic_close"/>
            </LinearLayout>
        </RelativeLayout>
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_marginTop="20dp"
            android:layout_height="wrap_content"/>
        <LinearLayout
            android:id="@+id/ll_bottom"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginRight="10dp"
            android:layout_marginTop="14dp"
            android:layout_marginBottom="14dp"
            >
            <TextView
                android:id="@+id/tv_cancle"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:layout_weight="1"
                android:background="@drawable/bg_radius20_428a6f_line"
                android:gravity="center"
                android:textSize="10sp"
                android:textColor="@color/_428a6f"
                android:text="取消"/>
            <TextView
                android:id="@+id/tv_sure"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:layout_weight="1"
                android:layout_marginLeft="20dp"
                android:gravity="center"
                android:textColor="@color/white"
                android:background="@drawable/bg_radius20_428a6f"
                android:textSize="10sp"
                android:text="下载"/>
        </LinearLayout>
    </LinearLayout>
</RelativeLayout>