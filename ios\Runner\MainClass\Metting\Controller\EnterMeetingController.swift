//
//  PreMemShipTestingController.swift
//  StuMetApp
//
//  Created by yj9527 on 2024/11/16.
//
import QMUIKit
import SnapKit
import SwiftyJSON
import HandyJSON
import AVFoundation
import Foundation
import TencentMeetingSDK


class EnterMettingController: BaseCommonController {
    private var txData: [String: Any] = [:]   // 腾讯会议参数
    private var meetingNum = ""
    private var identutyID = ""
    private var isPresent = false
    private let audioSession = AVAudioSession.sharedInstance()
    private let iconImageView = UIImageView(image: R.image.pic_jiance())
    private let titleLabel = UILabel.createLabel(text: "权限检测", fontSize: SMFont.body1_2(), color: UIColor.hex333333, alignment: .center)
    private let subtitleLabel = UILabel.createLabel(text: "请先检测设备权限是否已开启", fontSize: SMFont.body2_1(), color: UIColor.hex555555, alignment: .center)
    private lazy var permissionDetectionView: PermissionDetectionView = {
        let permissionDetectionView = PermissionDetectionView()
        return permissionDetectionView
    }()

    private lazy var joinMeetingBtn: QMUIButton = {
        let joinMeetingBtn = QMUIButton()
        joinMeetingBtn.setTitle("加入会议", for: .normal)
        joinMeetingBtn.adjustsButtonWhenHighlighted = false
        joinMeetingBtn.setTitleColor(.white, for: .normal)
        joinMeetingBtn.titleLabel?.font = SMFont.body1_1()
        joinMeetingBtn.backgroundColor = UIColor.primary33
        joinMeetingBtn.addCorner(LSize.large)
        joinMeetingBtn.addTarget(self, action: #selector(joinMeetingBtnTapped), for: .touchUpInside)
        return joinMeetingBtn
    }()


    func setMeetingNum(meetingNum: String,
                       identutyID: String,
                       isPresent: Bool = false,
                       txData: [String: Any] = [:]) {
        self.isPresent = isPresent
        self.meetingNum = meetingNum
        self.identutyID = identutyID
        self.txData = txData        // 保存外部传入的字典
    }
    
    override func setupUI() {
        super.setupUI()
        view.addSubview(iconImageView)
        iconImageView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview().offset(LSize.xxLarge)
            make.width.height.equalTo(ICONIMAGE_HEIGHT)
        }

        view.addSubview(titleLabel)
        titleLabel.snp.makeConstraints { make in
            make.centerX.equalTo(iconImageView)
            make.top.equalTo(iconImageView.snp.bottom).offset(PADDING_SMALL)
        }

        view.addSubview(subtitleLabel)
        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(PADDING_OUTER)
            make.centerX.equalTo(titleLabel)
        }
        
        view.addSubview(permissionDetectionView)
        permissionDetectionView.snp.makeConstraints { make in
            make.top.equalTo(subtitleLabel.snp.bottom).offset(LSize.xxLarge)
            make.centerX.equalTo(subtitleLabel)
            make.width.equalTo(subtitleLabel)
            make.height.equalTo(88)
        }
       
        view.addSubview(joinMeetingBtn)
        joinMeetingBtn.snp.makeConstraints { make in
            make.top.equalTo(permissionDetectionView.snp.bottom).offset(52)
            make.centerX.equalToSuperview()
            make.width.equalTo(CELLHEIGHT_MIN)
            make.height.equalTo(LSize.xxxxLarge)
        }
    }
    
    
    override func fetchData() {
        super.fetchData()
    }
    

    override func viewDidLoad() {
        super.viewDidLoad()
        setupAudioSession()
        if isPresent {
            navigationItem.leftBarButtonItem = UIBarButtonItem(image: R.image.icon_back(), style: .plain, target: self, action: #selector(close))
        }
    }

    // MARK: - 返回事件处理
    @objc private func close() {
        dismiss(animated: true)
    }
    

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        permissionDetectionView.permission()
    }
    
    
    private func setupAudioSession()  {
        do {
            try AVAudioSession.sharedInstance().setCategory(.playback)
            try AVAudioSession.sharedInstance().setActive(true)
        } catch {
        }
    }
 

    @objc private func joinMeetingBtnTapped() {
        guard permissionDetectionView.isMeeing() else { return }
        
        // 1. 提前解析并校验
        let schoolId = txData["schoolId"] as? NSNumber
        ?? (Int("\(txData["schoolId"] ?? "")") as NSNumber?)
        let classId  = txData["classId"]  as? NSNumber
        ?? (Int("\(txData["classId"]  ?? "")") as NSNumber?)
        
        guard let schoolId = schoolId, let classId = classId else {
            view.l_showToastMessage(with: "缺少 schoolId 或 classId")
            return
        }
        
        dismissAllPresentedControllers { [weak self] in
            guard let self = self else { return }
            DXMeeting.shared().needAudioRecording = true
            DXMeeting.shared().join(fromArgs: self.txData)
            DXMeeting.shared().callback = {jsonStr in
                let message = jsonStr ?? ""
                appDelegate.flutterChannelManager?.notifyFlutter(message: message)
            }
            
            // 通知 Flutter
//            appDelegate.flutterChannelManager?
//                .callMethod(MethodNames.onEnterMeeting.rawValue,
//                            args: ["meetingNum": self.meetingNum,
//                                   "identutyID": self.identutyID])
        }
    }
}
