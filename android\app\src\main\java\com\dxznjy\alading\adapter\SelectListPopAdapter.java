package com.dxznjy.alading.adapter;

import android.content.Context;
import android.graphics.Typeface;
import android.widget.RelativeLayout;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dxznjy.alading.R;
import com.dxznjy.alading.common.Constants;
import com.dxznjy.alading.entity.SelectListPopInfo;

import java.util.List;


public class SelectListPopAdapter extends BaseQuickAdapter<SelectListPopInfo, BaseViewHolder> {
    private Integer  mOrientation = Constants.VERTICAL;
    public SelectListPopAdapter(Context context, List<SelectListPopInfo> data, Integer orientation) {
        super(R.layout.adapter_selectlist_pop, data);
        this.mContext = context;
        this.mOrientation = orientation;
    }

    @Override
    protected void convert(final BaseViewHolder helper, SelectListPopInfo item) {
        RelativeLayout rl_bg = helper.getView(R.id.rl_bg);
        if (item.getType() != 0){
            if (item.isCheck()){
                rl_bg.setBackgroundResource(R.drawable.bg_radius4_f2faf7);
                helper.setTextColor(R.id.tv_title_vertical,mContext.getResources().getColor(R.color._287E66));
                helper.setTextColor(R.id.tv_title_horizontal,mContext.getResources().getColor(R.color._287E66));
                helper.setVisible(R.id.img_select,false);
                helper.setTypeface(R.id.tv_title_vertical, Typeface.DEFAULT_BOLD);
            }else{
                rl_bg.setBackgroundResource(R.drawable.bg_radius4_line_e1dede);
                helper.setTextColor(R.id.tv_title_vertical,mContext.getResources().getColor(R.color._636363));
                helper.setTextColor(R.id.tv_title_horizontal,mContext.getResources().getColor(R.color._636363));
                helper.setVisible(R.id.img_select,false);
                helper.setTypeface(R.id.tv_title_vertical, Typeface.DEFAULT);
            }
        }else{
            rl_bg.setBackgroundResource(R.drawable.bg_radius4_f7f7f7);
            helper.setTextColor(R.id.tv_title_vertical,mContext.getResources().getColor(R.color._b0aeae));
            helper.setTextColor(R.id.tv_title_horizontal,mContext.getResources().getColor(R.color._b0aeae));
            helper.setVisible(R.id.img_select,false);
            helper.setTypeface(R.id.tv_title_vertical, Typeface.DEFAULT);
        }
        if (mOrientation == Constants.VERTICAL){
            helper.setText(R.id.tv_title_vertical,item.getTitle());
            helper.setGone(R.id.tv_title_vertical,true);
            helper.setGone(R.id.tv_title_horizontal,false);
        }else{
            helper.setText(R.id.tv_title_horizontal,item.getTitle());
            helper.setGone(R.id.tv_title_vertical,false);
            helper.setGone(R.id.tv_title_horizontal,true);
        }
    }
}
