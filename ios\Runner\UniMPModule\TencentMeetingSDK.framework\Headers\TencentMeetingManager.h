// TencentMeetingManager.h
#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import "TCICMessageHandler.h"

// 前置声明
@class TencentMeetingManager;

// 会议状态枚举
typedef NS_ENUM(NSInteger, MeetingStatus) {
    MeetingStatusIdle,
    MeetingStatusConnecting,
    MeetingStatusInProgress,
    MeetingStatusDisconnected
};

// 错误类型枚举
typedef NS_ENUM(NSInteger, MeetingError) {
    MeetingErrorInvalidParam,
    MeetingErrorInitializeFailed,
    MeetingErrorJoinFailed,
    MeetingErrorNetworkIssue
};

// 代理协议
@protocol TencentMeetingDelegate <NSObject>
@optional
- (void)meetingStatusChanged:(MeetingStatus)status;
//- (void)meetingErrorOccurred:(MeetingError)error message:(NSString *)message;
//- (void)meetingJoinedSuccessfully;
//- (void)meetingEnded;

// 新增方法：接收来自H5的sendMessage事件
- (void)meetingManager:(TencentMeetingManager *)manager didReceiveSendMessage:(NSString *)payload;

@end

@interface TencentMeetingManager : NSObject <TCICMessageHandlerDelegate>

@property (nonatomic, weak) id<TencentMeetingDelegate> delegate;
@property (nonatomic, strong) TCICMessageHandler *messageHandler;
@property (nonatomic, assign, readonly) MeetingStatus currentStatus;
@property (nonatomic, copy) NSString *scene;
@property (nonatomic, strong) TCICClassController *vc;

// 单例
+ (instancetype)sharedManager;

/**
 初始化SDK
 @param sdkAppId 腾讯云SDKAppId
 @param secretId 腾讯云secretId
 @param secretKey 腾讯云secretKey
 @return 是否初始化成功
 */
//- (BOOL)initializeWithSDKAppId:(NSString *)sdkAppId
//                     secretId:(NSString *)secretId
//                   secretKey:(NSString *)secretKey;

/**
 加入会议
 @param schoolid SDKAppId
 @param classid 课堂id
 @param userid 用户id 
 @param token 用户token (腾讯token)
 */
- (void)joinMeetingWithSchoolId:(NSNumber *)schoolid ClassId:(NSNumber *)classid UserId:(NSString *)userid Token:(NSString *)token;

- (void)sendCustomMessage:(NSString *)msg;

@end
