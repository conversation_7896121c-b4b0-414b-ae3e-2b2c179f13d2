package com.dxznjy.alading.response;


import com.dxznjy.alading.http.BaseResponse;

import java.util.List;

public class VideoCourseDataResponse extends BaseResponse {

    private VideoCourseDataList data;

    public VideoCourseDataList getData() {
        return data;
    }

    public void setData(VideoCourseDataList data) {
        this.data = data;
    }

    public class VideoCourseDataList{
        private List<VideoCourseData> data;

        public List<VideoCourseData> getData() {
            return data;
        }

        public void setData(List<VideoCourseData> data) {
            this.data = data;
        }

    }

    public class VideoCourseData{
      private  String id;
        private  String userId;
        private  String studentCode;
        private  String studentName;
        private  String courseId;
        private  String courseName;
        private  String courseType;
        private  String lastStudyTime;
        private  String createdTime;
        private  String updatedTime;
        private  String refundStatus;
        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getStudentCode() {
            return studentCode;
        }

        public void setStudentCode(String studentCode) {
            this.studentCode = studentCode;
        }

        public String getStudentName() {
            return studentName;
        }

        public void setStudentName(String studentName) {
            this.studentName = studentName;
        }

        public String getCourseId() {
            return courseId;
        }

        public void setCourseId(String courseId) {
            this.courseId = courseId;
        }

        public String getCourseName() {
            return courseName;
        }

        public void setCourseName(String courseName) {
            this.courseName = courseName;
        }

        public String getCourseType() {
            return courseType;
        }

        public void setCourseType(String courseType) {
            this.courseType = courseType;
        }

        public String getLastStudyTime() {
            return lastStudyTime;
        }

        public void setLastStudyTime(String lastStudyTime) {
            this.lastStudyTime = lastStudyTime;
        }

        public String getCreatedTime() {
            return createdTime;
        }

        public void setCreatedTime(String createdTime) {
            this.createdTime = createdTime;
        }

        public String getUpdatedTime() {
            return updatedTime;
        }

        public void setUpdatedTime(String updatedTime) {
            this.updatedTime = updatedTime;
        }

        public String getRefundStatus() {
            return refundStatus;
        }

        public void setRefundStatus(String refundStatus) {
            this.refundStatus = refundStatus;
        }
    }

}
