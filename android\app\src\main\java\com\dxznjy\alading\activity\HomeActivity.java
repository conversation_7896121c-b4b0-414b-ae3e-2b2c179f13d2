package com.dxznjy.alading.activity;

import android.content.Intent;
import android.util.Log;
import android.view.KeyEvent;
import android.view.View;

import androidx.viewpager.widget.ViewPager;

import com.dxznjy.alading.AppManager;
import com.dxznjy.alading.R;
import com.dxznjy.alading.adapter.HomePageAdapter;
import com.dxznjy.alading.common.Constants;
import com.dxznjy.alading.entity.TabEntity;
import com.dxznjy.alading.fragment.BaseFragment;
import com.dxznjy.alading.fragment.FragmentGiveLessons;
import com.dxznjy.alading.fragment.FragmentMine;
import com.flyco.tablayout.CommonTabLayout;
import com.flyco.tablayout.listener.CustomTabEntity;
import com.flyco.tablayout.listener.OnTabSelectListener;
import com.vondear.rxtool.RxSPTool;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import io.dcloud.feature.sdk.DCSDKInitConfig;
import io.dcloud.feature.sdk.DCUniMPSDK;
import io.dcloud.feature.sdk.Interface.IDCUniMPPreInitCallback;
import io.dcloud.feature.sdk.MenuActionSheetItem;

public class HomeActivity extends BaseDxActivity {

/*    private String[] mTitles = {"首页", "上课", "我的"};
    private int[] mIconSelectIds = {
            R.mipmap.icon_shouye_h, R.mipmap.icon_shangke_h,
            R.mipmap.icon_wode_h};
    private int[] mIconUnselectIds =  {
            R.mipmap.icon_shouye_n, R.mipmap.icon_shangke_n,
            R.mipmap.icon_wode_n};*/
    private String[] mTitles = {"上课"};
    private int[] mIconSelectIds = {
             R.mipmap.icon_shangke_h,
            R.mipmap.icon_wode_h};
    private int[] mIconUnselectIds =  {
            R.mipmap.icon_shangke_n,
            R.mipmap.icon_wode_n};


    List<BaseFragment> mFragmentList = new ArrayList<BaseFragment>();
    @BindView(R.id.tl_2)
    CommonTabLayout tabLayout;
    @BindView(R.id.vp_content)
    ViewPager mViewPager;
    private ArrayList<CustomTabEntity> mTabEntities = new ArrayList<>();
    @Override
    public void beforeInflateNofit() {
        super.beforeInflateNofit();
        setNoFits();
    }

    @Override
    public void initViewAndData() {
        inVisableDbTitleBar();
        initData();
    }
    @Override
    public int getLayoutId() {
        return R.layout.activity_home;
    }
    public void initData(){
        tabLayout.setVisibility(View.GONE);
        initContentFragment();
        for (int i = 0; i < mTitles.length; i++) {
            mTabEntities.add(new TabEntity(mTitles[i], mIconSelectIds[i], mIconUnselectIds[i]));
        }
        mViewPager.setOffscreenPageLimit(1);
        mViewPager.setAdapter(new HomePageAdapter(getSupportFragmentManager(), mFragmentList, new String[]{"上课"}));
        tabLayout.setTabData(mTabEntities);
        tabLayout.setOnTabSelectListener(new OnTabSelectListener() {
            @Override
            public void onTabSelect(int position) {
                mViewPager.setCurrentItem(position);
            }

            @Override
            public void onTabReselect(int position) {
            }
        });
        mViewPager.setCurrentItem(0);
    }
     private FragmentGiveLessons fragmentGiveLessons;
    private void initContentFragment() {
        if (fragmentGiveLessons!=null)
            fragmentGiveLessons = null;
        fragmentGiveLessons = new FragmentGiveLessons();
        //初始化各个模块
        mFragmentList.add(fragmentGiveLessons);
    }
    /**
     * @param keyCode
     * @param event
     * @return
     */
    @Override
    public boolean onKeyUp(int keyCode, KeyEvent event) {
        finish();
        return super.onKeyUp(keyCode, event);
    }

}