import 'package:student_end_flutter/generated/json/base/json_convert_content.dart';
import 'package:student_end_flutter/bean/leave_apply_info_entity.dart';

LeaveApplyInfoEntity $LeaveApplyInfoEntityFromJson(Map<String, dynamic> json) {
  final LeaveApplyInfoEntity leaveApplyInfoEntity = LeaveApplyInfoEntity();
  final int? surplusLeaveNum = jsonConvert.convert<int>(
      json['surplusLeaveNum']);
  if (surplusLeaveNum != null) {
    leaveApplyInfoEntity.surplusLeaveNum = surplusLeaveNum;
  }
  final String? className = jsonConvert.convert<String>(json['className']);
  if (className != null) {
    leaveApplyInfoEntity.className = className;
  }
  final String? courseTime = jsonConvert.convert<String>(json['courseTime']);
  if (courseTime != null) {
    leaveApplyInfoEntity.courseTime = courseTime;
  }
  final int? studentLeaveId = jsonConvert.convert<int>(json['studentLeaveId']);
  if (studentLeaveId != null) {
    leaveApplyInfoEntity.studentLeaveId = studentLeaveId;
  }
  final int? configLeaveNum = jsonConvert.convert<int>(json['configLeaveNum']);
  if (configLeaveNum != null) {
    leaveApplyInfoEntity.configLeaveNum = configLeaveNum;
  }
  final int? courseType = jsonConvert.convert<int>(json['courseType']);
  if (courseType != null) {
    leaveApplyInfoEntity.courseType = courseType;
  }
  final int? grade = jsonConvert.convert<int>(json['grade']);
  if (grade != null) {
    leaveApplyInfoEntity.grade = grade;
  }
  final int? curriculumId = jsonConvert.convert<int>(json['curriculumId']);
  if (curriculumId != null) {
    leaveApplyInfoEntity.curriculumId = curriculumId;
  }
  return leaveApplyInfoEntity;
}

Map<String, dynamic> $LeaveApplyInfoEntityToJson(LeaveApplyInfoEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['surplusLeaveNum'] = entity.surplusLeaveNum;
  data['className'] = entity.className;
  data['courseTime'] = entity.courseTime;
  data['studentLeaveId'] = entity.studentLeaveId;
  data['configLeaveNum'] = entity.configLeaveNum;
  data['courseType'] = entity.courseType;
  data['grade'] = entity.grade;
  data['curriculumId'] = entity.curriculumId;
  return data;
}

extension LeaveApplyInfoEntityExtension on LeaveApplyInfoEntity {
  LeaveApplyInfoEntity copyWith({
    int? surplusLeaveNum,
    String? className,
    String? courseTime,
    int? studentLeaveId,
    int? configLeaveNum,
    int? courseType,
    int? grade,
    int? curriculumId,
  }) {
    return LeaveApplyInfoEntity()
      ..surplusLeaveNum = surplusLeaveNum ?? this.surplusLeaveNum
      ..className = className ?? this.className
      ..courseTime = courseTime ?? this.courseTime
      ..studentLeaveId = studentLeaveId ?? this.studentLeaveId
      ..configLeaveNum = configLeaveNum ?? this.configLeaveNum
      ..courseType = courseType ?? this.courseType
      ..grade = grade ?? this.grade
      ..curriculumId = curriculumId ?? this.curriculumId;
  }
}