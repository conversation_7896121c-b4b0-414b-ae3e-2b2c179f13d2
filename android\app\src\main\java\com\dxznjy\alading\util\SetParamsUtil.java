package com.dxznjy.alading.util;

import com.google.gson.Gson;

import java.util.Map;

import okhttp3.MediaType;
import okhttp3.RequestBody;

public class SetParamsUtil {


    public static RequestBody getRequestBodyfromParam( Map<String,String> entries) {
        String json = (new Gson()).toJson(entries);
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(json);
        RequestBody body = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), stringBuilder.toString());
        return body;
    }
}
