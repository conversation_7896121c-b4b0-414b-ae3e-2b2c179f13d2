package com.dxznjy.alading.widget;

import static com.vondear.rxtool.view.RxToast.showToast;

import android.app.Activity;
import android.content.Context;
import android.graphics.Color;
import android.graphics.Rect;
import android.graphics.drawable.ColorDrawable;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.TextView;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.ConvertUtils;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.dxznjy.alading.R;
import com.dxznjy.alading.adapter.LoginSelectUserPopAdapter;
import com.dxznjy.alading.common.Constants;
import com.dxznjy.alading.response.LoginResponse;

import java.util.ArrayList;
import java.util.List;

public class LoginSelectUserPopWindow {
    private List<LoginResponse.CallbackData> data=new ArrayList<>();
    private Integer  orientation= Constants.VERTICAL;
    private String title;
    private Context mContext;
    private LoginSelectUserPopAdapter selectListPopAdapter;
    private Integer horizontalCount;
    private  View locationView;
    private String sureBtnStr;
    private boolean isMoreSelect=false;

    OnSelectListener onSelectListener;
    public LoginSelectUserPopWindow(Context context, String title, String sureBtnStr, View locationView, List<LoginResponse.CallbackData> data, int orientation, int horizontalCount, boolean isMoreSelect, OnSelectListener onSelectListener) {
        this.mContext=context;
        this.title=title;
        this.sureBtnStr=sureBtnStr;
        this.locationView=locationView;
        this.data=data;
        this.orientation=orientation;
        this.horizontalCount=horizontalCount;
        this.isMoreSelect=isMoreSelect;
        this.onSelectListener=onSelectListener;
    }



    public void showPop(){
        View view = LayoutInflater.from(mContext).inflate(R.layout.pop_login_select_user, null);
        LinearLayout ll_close=view.findViewById(R.id.ll_close);
        TextView tv_title=view.findViewById(R.id.tv_title);
        TextView tv_sure=view.findViewById(R.id.tv_sure);
        RecyclerView recyclerView=view.findViewById(R.id.recyclerView);
        tv_title.setText(title);
        selectListPopAdapter=new LoginSelectUserPopAdapter(mContext,data,orientation);
        if (orientation==0){
            recyclerView.setLayoutManager(new LinearLayoutManager(mContext));
        }else{
            GridLayoutManager gridLayoutManager = new GridLayoutManager(mContext, horizontalCount);
// 设置RecyclerView的ItemDecoration，为item添加间距
            recyclerView.addItemDecoration(new RecyclerView.ItemDecoration() {
                @Override
                public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
                    // 设置间距的左右值，这里设置为10dp
                    int spacing = ConvertUtils.dp2px(10);
                    if (parent.getChildAdapterPosition(view) %horizontalCount == 0) {
                        outRect.left = 0; //第一列左边贴边
                    } else {
                        outRect.left = spacing ;
                    }
                }
            });
            recyclerView.setLayoutManager(gridLayoutManager);
        }
        recyclerView.setAdapter(selectListPopAdapter);
        selectListPopAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                if (data.get(position).getType()!=0){
                    if (isMoreSelect){
                        data.get(position).setCheck(!data.get(position).isCheck());
                        selectListPopAdapter.notifyItemChanged(position);
                    }else{
                        for (int i=0;i<data.size();i++){
                            data.get(i).setCheck(false);
                        }
                        data.get(position).setCheck(true);
                        selectListPopAdapter.notifyDataSetChanged();
                    }
                }
            }
        });
        PopupWindow popupWindow = new PopupWindow(view, ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT, true);
        popupWindow.setOutsideTouchable(true);
        // 设置PopupWindow的背景
        popupWindow.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
// 设置PopupWindow是否能响应点击事件
        popupWindow.setTouchable(true);
        popupWindow.setOnDismissListener(new PopupWindow.OnDismissListener() {
            @Override
            public void onDismiss() {
                backgroundAlpha(1f);
            }
        });
        tv_sure.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                boolean isSelect=false;
                for (int i=0;i<data.size();i++){
                    if (data.get(i).isCheck()){
                        isSelect=true;
                        break;
                    }
                }
                if (isSelect){
                    onSelectListener.onSelect(data);
                    popupWindow.dismiss();
                }else{
                    showToast("请选择学员");
                }
            }
        });
        ll_close.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                popupWindow.dismiss();
            }
        });
        backgroundAlpha(0.5f);
        // 获取控件的位置，安卓系统>7.0
        popupWindow.showAtLocation(locationView, Gravity.CENTER, 0, 0);
    }
    public void backgroundAlpha(float f) {
        WindowManager.LayoutParams lp =((Activity)mContext).getWindow().getAttributes();
        lp.alpha = f;
        ((Activity)mContext).getWindow().setAttributes(lp);
    }


    public interface OnSelectListener {//选择任意项 回调所有数据和当前选中状态
        void onSelect(List<LoginResponse.CallbackData> data);
    }
}
