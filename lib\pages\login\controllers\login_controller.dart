import 'dart:async';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:student_end_flutter/common/user_option.dart';
import 'package:student_end_flutter/dialog/dialog_sdk_description.dart';
import '../../../common/Config.dart';
import '../../../common/pop_option.dart';
import '../../../components/sms_verify_widget.dart';
import '../../../components/toast_utils.dart';
import '../../../navigator/login_navigator.dart';
import '../../../utils/android_ios_plugin.dart';
import '../../../utils/event_bus_utils.dart';
import '../../../utils/httpRequest.dart';
import '../../../utils/utils.dart';
import '../webView_page.dart';

class LoginController extends GetxController {
  // 获取当前的 BuildContext
  BuildContext get context => Get.context!;

  final TextEditingController phoneController = TextEditingController();
  final TextEditingController pwdController = TextEditingController();
  final TextEditingController codeController = TextEditingController();
  RxInt showType = 0.obs; ///0按钮  1验证码  2密码
  RxBool isChecked = false.obs;
  bool isDisable = false;

  Timer? timer;
  RxString timerText = "发送验证码".obs;
  RxInt codeTime = 60.obs;
  FocusNode focusNode = FocusNode();

  // 设置焦点
  void setFocus() {
    FocusScope.of(context).requestFocus(focusNode);
  }
  // 清除焦点
  void removeFocus() {
    focusNode.unfocus();
  }

  @override
  void onInit() {
    super.onInit();
    AndroidIosPlugin.twoBackFinish('false');
  }

  @override
  void onClose() {
    PopOption.currentTabIndex = 1;
    EventBusUtils.sendMsg("showPop");
    cancelTimer();
    focusNode.dispose();
    AndroidIosPlugin.twoBackFinish('true');
    super.onClose();
  }

  cancelTimer() {
    timer?.cancel();
  }

  gotoChangePwd(){
    String path = LoginNavigator.passwordChange().path;
    AndroidIosPlugin.openUniApp(path);
  }

  changeCheck() {
    isChecked.value = !isChecked.value;
  }

  clickUserAgree(){
    Get.to(() => WebViewPage(), arguments: {"titleName":"用户服务协议","url":"https://document.dxznjy.com/applet/agreeon/useragreement.html"});
  }

  clickPrivacy(){
    Get.to(() => WebViewPage(), arguments: {"titleName":"隐私政策","url":"https://document.dxznjy.com/applet/agreeon/app_privacypolicy.html"});
    // Get.to(() => WebViewPage(), arguments: {"titleName":"隐私政策","url":"http://192.168.200.253:9000/"});

  }
  //////////////////////密码登录////////////////////
  pwdLoginFun() {
    if(isDisable){
      ToastUtil.showToastText("请勿重复点击");
      return;
    }
    isDisable = true;
    UserOption().loginFun(
        phone: phoneController.text,
        pwd: pwdController.text,
        isPwd: true,
        isCheck: isChecked.value,
        successFunc:(){
          Get.back();
          isDisable = false;
        },
        failFunc: (){
          isDisable = false;
        });
  }


  /// 切换时清除数据
  cleanData(){
    phoneController.text = "";
    pwdController.text = "";
    codeController.text = "";
    isChecked.value = false;
    isDisable = false;
    cancelTimer();
    timerText.value = "发送验证码";
    codeTime.value = 60;
  }

  //////////////////////验证码登录////////////////////
  checkSlideOption() {
    if (phoneController.text.length != 11 || !Utils.checkPhone(phoneController.text)) {
      ToastUtil.showShortErrorToast("请输入正确的手机号");
      return;
    }
    if (timerText.value != "发送验证码") {
      ToastUtil.showShortErrorToast("验证码已发送，请注意查收");
      return;
    }
    setFocus();
    Get.dialog(
      BlockPuzzleCaptchaPage(sendSms),
      barrierDismissible: false,
    );
  }

  sendSms(code, uuid) {
    if (phoneController.text.length != 11 ||
        !Utils.checkPhone(phoneController.text)) {
      ToastUtil.showShortErrorToast("请输入正确的手机号");
      return;
    }
    String params = "?mobile=${phoneController.text}";
    var response = HttpUtil().post('${Config.checkRegister}$params');
    response.then((value) async {
      if (value?.data["status"] == 1) {
        var response2 = HttpUtil().post(
            '${Config.sendSms}/${phoneController.text}?code=$code&uuid=$uuid');
        response2.then((data) async {
          if(data?.data["success"]){
            ToastUtil.showShortSuccessToast("发送验证码成功");
            timerFunc();
          }else{
            ToastUtil.showShortErrorToast("发送验证码失败");
          }
        });
      } else{
        ToastUtil.showShortErrorToast(value?.data["message"]??"发送验证码失败");
      }
    });
  }

  timerFunc() {
    timer = Timer.periodic(Duration(seconds: 1), (timer) {
      codeTime.value--;
      timerText.value = "${codeTime.value}s秒后重发";
      if (codeTime.value == 0) {
        cancelTimer();
        codeTime.value = 60;
        timerText.value = "发送验证码";
      }
    });
  }

  smsLoginFun() {
    if(isDisable){
      ToastUtil.showToastText("请勿重复点击");
      return;
    }
    isDisable = true;
    UserOption().loginFun(phone: phoneController.text,
        sms: codeController.text,
        isPwd: false,
        isCheck: isChecked.value,
        successFunc:(){
          Get.back();
          isDisable = false;
        },failFunc: (){
          isDisable = false;
        });
  }


}