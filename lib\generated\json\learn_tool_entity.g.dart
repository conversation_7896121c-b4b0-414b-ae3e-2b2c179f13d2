import 'package:student_end_flutter/generated/json/base/json_convert_content.dart';
import 'package:student_end_flutter/bean/learn_tool_entity.dart';

LearnToolEntity $LearnToolEntityFromJson(Map<String, dynamic> json) {
  final LearnToolEntity learnToolEntity = LearnToolEntity();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    learnToolEntity.id = id;
  }
  final String? toolName = jsonConvert.convert<String>(json['toolName']);
  if (toolName != null) {
    learnToolEntity.toolName = toolName;
  }
  final int? status = jsonConvert.convert<int>(json['status']);
  if (status != null) {
    learnToolEntity.status = status;
  }
  final String? generalFlag = jsonConvert.convert<String>(json['generalFlag']);
  if (generalFlag != null) {
    learnToolEntity.generalFlag = generalFlag;
  }
  final int? sort = jsonConvert.convert<int>(json['sort']);
  if (sort != null) {
    learnToolEntity.sort = sort;
  }
  final String? iconAddress = jsonConvert.convert<String>(json['iconAddress']);
  if (iconAddress != null) {
    learnToolEntity.iconAddress = iconAddress;
  }
  final String? ashIconAddress = jsonConvert.convert<String>(
      json['ashIconAddress']);
  if (ashIconAddress != null) {
    learnToolEntity.ashIconAddress = ashIconAddress;
  }
  final String? jumpAddress = jsonConvert.convert<String>(json['jumpAddress']);
  if (jumpAddress != null) {
    learnToolEntity.jumpAddress = jumpAddress;
  }
  final String? appJumpAddress = jsonConvert.convert<String>(
      json['appJumpAddress']);
  if (appJumpAddress != null) {
    learnToolEntity.appJumpAddress = appJumpAddress;
  }
  final String? updateTime = jsonConvert.convert<String>(json['updateTime']);
  if (updateTime != null) {
    learnToolEntity.updateTime = updateTime;
  }
  final String? createTime = jsonConvert.convert<String>(json['createTime']);
  if (createTime != null) {
    learnToolEntity.createTime = createTime;
  }
  final String? bindId = jsonConvert.convert<String>(json['bindId']);
  if (bindId != null) {
    learnToolEntity.bindId = bindId;
  }
  final String? remark = jsonConvert.convert<String>(json['remark']);
  if (remark != null) {
    learnToolEntity.remark = remark;
  }
  final String? showChannel = jsonConvert.convert<String>(json['showChannel']);
  if (showChannel != null) {
    learnToolEntity.showChannel = showChannel;
  }
  final int? deleted = jsonConvert.convert<int>(json['deleted']);
  if (deleted != null) {
    learnToolEntity.deleted = deleted;
  }
  final int? showStatus = jsonConvert.convert<int>(json['showStatus']);
  if (showStatus != null) {
    learnToolEntity.showStatus = showStatus;
  }
  return learnToolEntity;
}

Map<String, dynamic> $LearnToolEntityToJson(LearnToolEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['toolName'] = entity.toolName;
  data['status'] = entity.status;
  data['generalFlag'] = entity.generalFlag;
  data['sort'] = entity.sort;
  data['iconAddress'] = entity.iconAddress;
  data['ashIconAddress'] = entity.ashIconAddress;
  data['jumpAddress'] = entity.jumpAddress;
  data['appJumpAddress'] = entity.appJumpAddress;
  data['updateTime'] = entity.updateTime;
  data['createTime'] = entity.createTime;
  data['bindId'] = entity.bindId;
  data['remark'] = entity.remark;
  data['showChannel'] = entity.showChannel;
  data['deleted'] = entity.deleted;
  data['showStatus'] = entity.showStatus;
  return data;
}

extension LearnToolEntityExtension on LearnToolEntity {
  LearnToolEntity copyWith({
    String? id,
    String? toolName,
    int? status,
    String? generalFlag,
    int? sort,
    String? iconAddress,
    String? ashIconAddress,
    String? jumpAddress,
    String? appJumpAddress,
    String? updateTime,
    String? createTime,
    String? bindId,
    String? remark,
    String? showChannel,
    int? deleted,
    int? showStatus,
  }) {
    return LearnToolEntity()
      ..id = id ?? this.id
      ..toolName = toolName ?? this.toolName
      ..status = status ?? this.status
      ..generalFlag = generalFlag ?? this.generalFlag
      ..sort = sort ?? this.sort
      ..iconAddress = iconAddress ?? this.iconAddress
      ..ashIconAddress = ashIconAddress ?? this.ashIconAddress
      ..jumpAddress = jumpAddress ?? this.jumpAddress
      ..appJumpAddress = appJumpAddress ?? this.appJumpAddress
      ..updateTime = updateTime ?? this.updateTime
      ..createTime = createTime ?? this.createTime
      ..bindId = bindId ?? this.bindId
      ..remark = remark ?? this.remark
      ..showChannel = showChannel ?? this.showChannel
      ..deleted = deleted ?? this.deleted
      ..showStatus = showStatus ?? this.showStatus;
  }
}