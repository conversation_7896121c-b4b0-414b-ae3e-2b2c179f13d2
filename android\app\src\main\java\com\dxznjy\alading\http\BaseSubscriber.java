package com.dxznjy.alading.http;

import android.content.Context;
import android.content.Intent;
import android.util.Log;
import android.view.Gravity;
import android.widget.Toast;

import com.dxznjy.alading.AppManager;
import com.dxznjy.alading.activity.LoginActivity;
import com.dxznjy.alading.common.Constants;
import com.vondear.rxtool.RxSPTool;

import java.io.IOException;

import retrofit2.adapter.rxjava.HttpException;
import rx.Subscriber;

/**
 * Created by houzheng on 2016/12/2.
 */

public class BaseSubscriber<T> extends Subscriber<T> {
    protected Context mContext;
    public BaseSubscriber(Context context) {
        this.mContext = context;
    }

    @Override
    public void onCompleted() {
    }

    @Override
    public void onError(final Throwable e) {
        Log.w("Subscriber onError", e);
        if (e instanceof HttpException) {
            // We had non-2XX http error
//            Toast.makeText(mContext, "服务器错误", Toast.LENGTH_SHORT).show();
        } else if (e instanceof IOException) {
            // A network or conversion error happened
//            Toast.makeText(mContext, "找不到服务器", Toast.LENGTH_SHORT).show();
        } else if (e instanceof ApiException) {
            ApiException exception = (ApiException) e;
            if (exception.getmErrorCode()==50004){
            }
            else if (e.getMessage().contains("@eid")){
               int index= e.getMessage().indexOf("@eid");
               String subString=e.getMessage().substring(0,index);
                Toast toast = Toast.makeText(mContext, subString, Toast.LENGTH_SHORT);
                //设置位置    参数1:表示设置toast的位置   参数2:表示相对参数1位置的x偏移量    参数3:表示相对参数1位置的Y偏移量
                toast.setGravity(Gravity.TOP, 0, 0);
                toast.show();
            } else {
                Toast toast = Toast.makeText(mContext, e.getMessage(), Toast.LENGTH_SHORT);
                //设置位置    参数1:表示设置toast的位置   参数2:表示相对参数1位置的x偏移量    参数3:表示相对参数1位置的Y偏移量
                toast.setGravity(Gravity.TOP, 0, 0);
                toast.show();
            }
        }
    }

    @Override
    public void onNext(T t) {
    }

}
