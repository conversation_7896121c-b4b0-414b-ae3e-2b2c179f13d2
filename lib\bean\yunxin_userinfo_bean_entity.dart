import 'package:student_end_flutter/generated/json/base/json_field.dart';
import 'package:student_end_flutter/generated/json/yunxin_userinfo_bean_entity.g.dart';
import 'dart:convert';
export 'package:student_end_flutter/generated/json/yunxin_userinfo_bean_entity.g.dart';

@JsonSerializable()
class YunxinUserinfoBeanEntity {
	late int code = 0;
	late String msg = '';
	late int ts = 0;
	late String cost = '';
	late String requestId = '';
	late YunxinUserinfoBeanData data;

	YunxinUserinfoBeanEntity();

	factory YunxinUserinfoBeanEntity.fromJson(Map<String, dynamic> json) => $YunxinUserinfoBeanEntityFromJson(json);

	Map<String, dynamic> toJson() => $YunxinUserinfoBeanEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class YunxinUserinfoBeanData {
	late List<dynamic> notFoundUserUuids = [];
	late List<YunxinUserinfoBeanDataUserInfos> userInfos = [];

	YunxinUserinfoBeanData();

	factory YunxinUserinfoBeanData.fromJson(Map<String, dynamic> json) => $YunxinUserinfoBeanDataFromJson(json);

	Map<String, dynamic> toJson() => $YunxinUserinfoBeanDataToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class YunxinUserinfoBeanDataUserInfos {
	late String name = '';
	late String userUuid = '';
	late String privateMeetingNum = '';
	late int state = 0;
	late String userToken = '';
	late List<dynamic> departments = [];

	YunxinUserinfoBeanDataUserInfos();

	factory YunxinUserinfoBeanDataUserInfos.fromJson(Map<String, dynamic> json) => $YunxinUserinfoBeanDataUserInfosFromJson(json);

	Map<String, dynamic> toJson() => $YunxinUserinfoBeanDataUserInfosToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}