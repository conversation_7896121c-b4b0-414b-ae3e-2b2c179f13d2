import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:student_end_flutter/pages/base/controller/base_controller.dart';
import 'package:student_end_flutter/pages/learn/controller/learn_controller.dart';
import 'package:student_end_flutter/utils/android_ios_plugin.dart';
import 'package:student_end_flutter/utils/event_bus_utils.dart';
import 'package:tencent_cloud_chat_push/common/tim_push_listener.dart';
import 'package:tencent_cloud_chat_push/common/tim_push_message.dart';
import 'package:tencent_cloud_chat_push/tencent_cloud_chat_push.dart';
import 'package:tx_im/dx_utils/IMHttpRequest.dart';
import 'package:tx_im/im_module_service.dart';
import 'package:tx_im/user.dart';

import '../bean/login_bean_entity.dart';
import '../bean/user_info_bean_entity.dart';
import '../components/toast_utils.dart';
import '../generated/json/base/json_convert_content.dart';
import '../pages/login/login.dart';
import '../utils/httpRequest.dart';
import '../utils/ne_metting_utils.dart';
import '../utils/sharedPreferences_util.dart';
import '../utils/utils.dart';
import 'Config.dart';


class UserOption {
  static String tokenKey = "";
  static String token = "";
  static String expiresAt = "";
  static String userId = "";
  static UserInfoBeanEntity? userInfoBeanEntity;
  static String userCode = "";
  static String mobile = "";
  static int parentMemberType = -1;
  static String studentToken = "";
  static bool hasAgreedToPrivacyPolicy = false; //用户同意隐私政策
  static String payToken = "";

  static bool showParentVipBuyMember = false;
  static bool showBuyMember =true;

  static setVipRenew() {
    print('xxxxxxxxxxxxxxxxx1111111111111111111111111111111111111111111');
    showBuyMember = false;
    showParentVipBuyMember = false;
    DateTime now = DateTime.now(); // 获取当前日期和时间
    if (userInfoBeanEntity?.parentMemberEndTime != "") {
      var vipDate2 = DateTime.parse(userInfoBeanEntity!.parentMemberEndTime);
      var vipDiffDays = vipDate2.difference(now).inDays;

      if (vipDiffDays <= 30) {
        showParentVipBuyMember = true;
      } else {
        showParentVipBuyMember = false;
      }
    }
    if(userInfoBeanEntity?.expireTime!=''){
      DateTime date = DateTime.now();
      DateTime date2 = DateTime.parse(userInfoBeanEntity!.expireTime); // 请根据实际数据结构调整
      Duration difference = date2.difference(date);
      print(difference);


      double diffDays = difference.inDays.toDouble();
      print(diffDays);
      print('fffffffffff333333333333333333333333333333333333333333');

      if (diffDays <= 30) {
        showBuyMember = true;
      } else {
        showBuyMember = false;
      }
      print(showBuyMember);
    }

  }

  static cleanData() {
    final BaseController baseController = Get.find<BaseController>();
    studentToken = '';
    tokenKey = "";
    token = "";
    expiresAt = "";
    userId = "";
    userCode = "";
    mobile = "";
    parentMemberType = -1;
    userInfoBeanEntity = null;
    baseController.showMessageRedDot.value = false;
  }

  static cleanLocalData() {
    SharedPreferencesUtil.deleteData(SharedPreferencesUtil.tokenKey);
    SharedPreferencesUtil.deleteData(SharedPreferencesUtil.token);
    SharedPreferencesUtil.deleteData(SharedPreferencesUtil.expiresAt);
    SharedPreferencesUtil.deleteData(SharedPreferencesUtil.userId);
    SharedPreferencesUtil.deleteData(SharedPreferencesUtil.userCode);
    SharedPreferencesUtil.deleteData(SharedPreferencesUtil.mobile);
    SharedPreferencesUtil.deleteData(SharedPreferencesUtil.parentMemberType);
  }

  /// 从本地储存中去token
  static initToken() async {
    String key = await SharedPreferencesUtil.getData<String>(
        SharedPreferencesUtil.tokenKey);
    if (key != null && key != "" && key != SharedPreferencesUtil.tokenKey) {
      tokenKey = key;
    }
    String t = await SharedPreferencesUtil.getData<String>(
        SharedPreferencesUtil.token);
    if (t != null && t != "" && t != SharedPreferencesUtil.token) {
      token = t;
    }
    String date = await SharedPreferencesUtil.getData<String>(
        SharedPreferencesUtil.expiresAt);
    if (date != null && date != "" && date != SharedPreferencesUtil.expiresAt) {
      expiresAt = date;
    }
    String u = await SharedPreferencesUtil.getData<String>(
        SharedPreferencesUtil.userId);
    if (u != null && u != "") {
      userId = u;
    }

    String code = await SharedPreferencesUtil.getData<String>(
        SharedPreferencesUtil.userCode);
    if (code != null && code != "") {
      userCode = code;
    }

    String m = await SharedPreferencesUtil.getData<String>(
        SharedPreferencesUtil.mobile);
    if (m != null && m != "") {
      mobile = m;
    }
    int parentMembertype = await SharedPreferencesUtil.getData<int>(
        SharedPreferencesUtil.parentMemberType);
    if (parentMembertype != null && parentMembertype != -1) {
      parentMemberType = parentMembertype;
    }
  }

  /// 判断token是否过期 true已过期 ,false未过期
  static bool checkTokenTimeOut() {
    if (token.isEmpty) {
      return true;
    }
    if (expiresAt.isEmpty) {
      return true;
    }
    DateTime expirationDate = DateTime.parse(expiresAt);
    // 获取当前的时间
    DateTime currentDate = DateTime.now();
    // 比较当前时间和到期时间
    bool expired = currentDate.isAfter(expirationDate);
    return expired;
  }

  /// 登录
  loginFun({phone, sms = "", pwd = "", isCheck, isPwd, successFunc, failFunc}) {
    if (phone.length != 11 || !Utils.checkPhone(phone)) {
      ToastUtil.showShortErrorToast("请输入正确的手机号");
      if (failFunc != null) {
        failFunc();
      }
      return;
    }
    if (isPwd) {
      if (pwd.isEmpty) {
        ToastUtil.showShortErrorToast("请输入密码");
        if (failFunc != null) {
          failFunc();
        }
        return;
      }
    } else {
      if (sms.isEmpty) {
        ToastUtil.showShortErrorToast("请输入验证码");
        if (failFunc != null) {
          failFunc();
        }
        return;
      }
      if (sms.length != 6) {
        ToastUtil.showShortErrorToast("验证码必须输入6位");
        if (failFunc != null) {
          failFunc();
        }
        return;
      }
    }
    if (!isCheck) {
      ToastUtil.showShortErrorToast("请阅读并同意用户协议与隐私政策");
      if (failFunc != null) {
        failFunc();
      }
      return;
    }
    ToastUtil.showLoadingDialog();
    var responseCheck =
        HttpUtil().post('${Config.checkZxAppRegister}?mobile=$phone');
    responseCheck.then((value) {
      if (value?.data["status"] == 1) {
        String params =
            "?username=$phone&password=$pwd&role=Member&smsCode=$sms";
        var response = HttpUtil().get('${Config.loginApi}$params');
        response.then((value) async {
          LoginBeanEntity? data =
              JsonConvert.fromJsonAsT<LoginBeanEntity>(value);
          ToastUtil.closeLoadingDialog();
          if (data != null) {
            ToastUtil.showShortSuccessToast("登录成功");
            SharedPreferencesUtil.saveData(
                SharedPreferencesUtil.tokenKey, data.tokenType);
            SharedPreferencesUtil.saveData(
                SharedPreferencesUtil.token, data.token);
            SharedPreferencesUtil.saveData(
                SharedPreferencesUtil.expiresAt, data.expiresAt);
            tokenKey = data.tokenType;
            token = data.token;
            print(data.token);
            print('ffffffffffffffffffffffffffffffffffffffffffffffffff');
            expiresAt = data.expiresAt;
            IMHttpUtil.heads['x-www-iap-assertion'] = token;
            getUserInfoRequest(true);
            if (successFunc != null) {
              successFunc();
            }
          } else {
            if (failFunc != null) {
              failFunc();
            }
            ToastUtil.showShortErrorToast("登录失败");
          }
        });
      } else {
        if (failFunc != null) {
          failFunc();
        }
        ToastUtil.closeLoadingDialog();
        ToastUtil.showShortErrorToast("登录失败");
      }
    });
  }

  /// 获取个人信息
  static getUserInfoRequest(bool isFirstEnter) {
    if (token == "") {
      return;
    }
    var response = HttpUtil().get(Config.getUserInfo);
    response.then((value) async {
      UserInfoBeanEntity? data =
          JsonConvert.fromJsonAsT<UserInfoBeanEntity>(value);
      if (data != null) {
        userInfoBeanEntity = data;
        print("获取用户信息成功${userInfoBeanEntity?.toJson()}");
        userId = data.userId;
        userCode = data.userCode;
        if(isFirstEnter) {
          // 检查用户上次切换的角色
          final prefs = await SharedPreferences.getInstance();
          final savedTencentId = prefs.getString('currentTencentId');

          if (savedTencentId != null && savedTencentId.isNotEmpty) {
            // 使用记录的tencentId登录
            IMModuleService().mobile = userInfoBeanEntity?.mobile ?? '';
            IMModuleService().TIMLoginByTencId(tencentId: savedTencentId);
          } else {
            // 没有保存的tencentId，使用常规登录方式
            IMModuleService().mobile = userInfoBeanEntity?.mobile ?? '';
            IMModuleService().TIMLogin();
          }
        }
        // 更新头像
        IMModuleService().updateUserAvatar(data.headPortrait);
        parentMemberType = data.parentMemberType;
        mobile = data.mobile;
        parentMemberType = data.parentMemberType;
        setVipRenew();
        SharedPreferencesUtil.saveData(
            SharedPreferencesUtil.userId, data.userId);
        SharedPreferencesUtil.saveData(
            SharedPreferencesUtil.userCode, data.userCode);
        SharedPreferencesUtil.saveData(
            SharedPreferencesUtil.mobile, data.mobile);
        SharedPreferencesUtil.saveData(
            SharedPreferencesUtil.parentMemberType, data.parentMemberType);
        EventBusUtils.sendMsg("refreshUserInfo");
        AndroidIosPlugin.setUserIdForBugly(data.userId);
      }
    });
  }

  static bool get iSLogin {
    if (token.isNotEmpty) {
      return true;
    }
    return false;
  }

  static checkNoTokenJump() {
    if (token.isEmpty) {
      Get.to(LoginPage());
      return true;
    }
    return false;
  }
  static checkNoToken() {
    if (token.isEmpty) {
      return true;
    }
    return false;
  }
  /// 跳转登录页面
  static toLoginPage({bool isTime = false}) {
    try{
      UserOption.token = '';
      SharedPreferencesUtil.saveData(SharedPreferencesUtil.token, '');
      HttpUtil.heads.remove(UserOption.tokenKey);
    }catch(e){
    }
    Get.to(LoginPage());
  }

  //退出登录
  static logoutFunc(logoff) async {
    try {
      HttpUtil.heads.remove(UserOption.tokenKey);
      cleanData();
      cleanLocalData();
      // 删除保存的用户角色记录
      try {
        final prefs = await SharedPreferences.getInstance();
        await prefs.remove('currentTencentId');
      } catch (e) {
        print("删除tencentId失败: $e");
      }

      BaseController controller = Get.find<BaseController>();
      controller.changeTab(0);
      if (Get.isRegistered<LearnController>()) {
        LearnController learnController = Get.find<LearnController>();
        learnController.cleanStudentData();
      }
      // NEMeetingUtil.logOut();
      IMModuleService().TIMLogout();
      if (!logoff) {
        //logoff 是否注销操作
        // NEMeetingUtil.yxLogOut();
        if (!logoff) {
          //logoff 是否注销操作
          ToastUtil.showShortSuccessToast("退出登录成功");
        } else {
          ToastUtil.showShortSuccessToast("注销账号成功");
        }
      }
    } catch (e) {
      if (!logoff) {
        ToastUtil.showShortSuccessToast("退出登录失败");
      } else {
        ToastUtil.showShortSuccessToast("注销账号失败");
      }
    }
  }
}
