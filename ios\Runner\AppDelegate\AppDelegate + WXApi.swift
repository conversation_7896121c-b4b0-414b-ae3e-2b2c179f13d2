//
//  AppDelegate + Pay.swift
//  Runner
//
//  Created by 许江泉 on 2025/2/24.
//

import UIKit
import WebKit

extension AppDelegate {
    override func application(_ application: UIApplication, handleOpen url: URL) -> Bool {
        return WXApi.handleOpen(url, delegate: self)
    }
    
    override func application(_ application: UIApplication, open url: URL, sourceApplication: String?, annotation: Any) -> Bool {
        return WXApi.handleOpen(url, delegate: self)
    }
    
    override func application(_ app: UIApplication, open url: URL, options: [UIApplication.OpenURLOptionsKey: Any] = [:]) -> <PERSON>ol {
        // 1. 处理支付宝回调
        if url.scheme == "allinpaysdk" {
            var errorCode = ""
            if let query = url.query, query.contains("code=cancel") {
                errorCode = "cancel"
                let dic = ["errorCode": errorCode]
                NotificationCenter.default.post(name: APP_PAY_CALLBACK, object: nil, userInfo: dic)
            } else {
                let dic = ["errorCode": errorCode,"payType":"alipay"]
                NotificationCenter.default.post(name: APP_PAY_CALLBACK, object: nil, userInfo: dic)
            }
            return true
        }
        if url.scheme?.lowercased() == schemeStr.lowercased() {
            if let query = url.query, let fullPath = decodeDoubleEncoded(query) {
                extractParams(from: fullPath)
                return true
            }
        }
        DCUniMPSDKEngine.application(app, open: url, options: options)
        return  WXApi.handleOpen(url, delegate: self)
    }
    
    override func application(_ application: UIApplication, continue userActivity: NSUserActivity, restorationHandler: @escaping ([any UIUserActivityRestoring]?) -> Void) -> Bool {
        DCUniMPSDKEngine.application(application, continue: userActivity)
        return WXApi.handleOpenUniversalLink(userActivity, delegate: self)
    }
}

extension AppDelegate {
    private func extractParams(from path: String) {
        UrlProtocolResolver.closeUniMp()
        if !path.isEmpty {
            if path.contains(schemeUrlPath) {
                if let url = path.substringAfter(schemeUrlPath) {
                    paths = url
                }
            }else{
                paths = path
            }
        }
        
        if let url = URL(string: openURL) {
          if UIApplication.shared.canOpenURL(url) {
              UIApplication.shared.open(url);
          }
        }
    }
    
    private func decodeDoubleEncoded(_ string: String) -> String? {
        let temp = string
            .replacingOccurrences(of: "+", with: "%2B")
            .replacingOccurrences(of: " ", with: "%20")
        guard let onceDecoded = temp.removingPercentEncoding,
              let twiceDecoded = onceDecoded.removingPercentEncoding else {
            return nil
        }
        return twiceDecoded
    }
 
}


extension AppDelegate: WXApiDelegate {
    func onResp(_ resp: BaseResp) {
        if let payResp = resp as? WXLaunchMiniProgramResp, let errCode = payResp.extMsg {
            NotificationCenter.default.post(name: APP_PAY_CALLBACK, object: nil, userInfo: ["errorCode": errCode])
        }
    }
    
    
    func onReq(_ req: BaseReq) {
        // 设置方法通道
    }
}

