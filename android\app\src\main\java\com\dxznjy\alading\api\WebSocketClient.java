package com.dxznjy.alading.api;

import android.content.Context;

import androidx.annotation.Nullable;

import com.dxznjy.alading.common.GlobalConfig;

import dc.squareup.okhttp3.OkHttpClient;
import dc.squareup.okhttp3.Request;
import dc.squareup.okhttp3.Response;
import dc.squareup.okhttp3.WebSocket;
import dc.squareup.okhttp3.WebSocketListener;
import dc.squareup.okio.ByteString;

public class WebSocketClient {
    private static WebSocketClient mInstance;
    Context mContext;
    private WebSocket webSocket;
    private OkHttpClient client;

    public static WebSocketClient getInstance(Context context){
        mInstance = new WebSocketClient(context);
        return mInstance;
    }

    private WebSocketClient(Context context){
        client = new OkHttpClient();
        Request request = new Request.Builder().url(GlobalConfig.getWebSocketUrl()).build();
        webSocket = client.newWebSocket(request, new WebSocketListener() {
            @Override
            public void onOpen(WebSocket webSocket, Response response) {
                super.onOpen(webSocket, response);
            }

            @Override
            public void onMessage(WebSocket webSocket, String s) {
                super.onMessage(webSocket, s);
            }

            @Override
            public void onMessage(WebSocket webSocket, ByteString byteString) {
                super.onMessage(webSocket, byteString);
            }

            @Override
            public void onClosing(WebSocket webSocket, int i, String s) {
                super.onClosing(webSocket, i, s);
            }

            @Override
            public void onClosed(WebSocket webSocket, int i, String s) {
                super.onClosed(webSocket, i, s);
            }

            @Override
            public void onFailure(WebSocket webSocket, Throwable throwable, @Nullable Response response) {
                super.onFailure(webSocket, throwable, response);
            }
        });
    }


    public void send(String message) {
        webSocket.send(message);
    }

    public void close() {
        webSocket.close(1000, "close");
    }
}
