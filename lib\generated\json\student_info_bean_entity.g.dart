import 'package:student_end_flutter/generated/json/base/json_convert_content.dart';
import 'package:student_end_flutter/bean/student_info_bean_entity.dart';

StudentInfoBeanEntity $StudentInfoBeanEntityFromJson(
    Map<String, dynamic> json) {
  final StudentInfoBeanEntity studentInfoBeanEntity = StudentInfoBeanEntity();
  final String? studentCode = jsonConvert.convert<String>(json['studentCode']);
  if (studentCode != null) {
    studentInfoBeanEntity.studentCode = studentCode;
  }
  final String? studentName = jsonConvert.convert<String>(json['studentName']);
  if (studentName != null) {
    studentInfoBeanEntity.studentName = studentName;
  }
  final bool? isFormal = jsonConvert.convert<bool>(json['isFormal']);
  if (isFormal != null) {
    studentInfoBeanEntity.isFormal = isFormal;
  }
  final List<
      StudentInfoBeanMerchantVos>? merchantVos = (json['merchantVos'] as List<
      dynamic>?)?.map(
          (e) =>
      jsonConvert.convert<StudentInfoBeanMerchantVos>(
          e) as StudentInfoBeanMerchantVos).toList();
  if (merchantVos != null) {
    studentInfoBeanEntity.merchantVos = merchantVos;
  }
  return studentInfoBeanEntity;
}

Map<String, dynamic> $StudentInfoBeanEntityToJson(
    StudentInfoBeanEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['studentCode'] = entity.studentCode;
  data['studentName'] = entity.studentName;
  data['isFormal'] = entity.isFormal;
  data['merchantVos'] = entity.merchantVos.map((v) => v.toJson()).toList();
  return data;
}

extension StudentInfoBeanEntityExtension on StudentInfoBeanEntity {
  StudentInfoBeanEntity copyWith({
    String? studentCode,
    String? studentName,
    bool? isFormal,
    List<StudentInfoBeanMerchantVos>? merchantVos,
  }) {
    return StudentInfoBeanEntity()
      ..studentCode = studentCode ?? this.studentCode
      ..studentName = studentName ?? this.studentName
      ..isFormal = isFormal ?? this.isFormal
      ..merchantVos = merchantVos ?? this.merchantVos;
  }
}

StudentInfoBeanMerchantVos $StudentInfoBeanMerchantVosFromJson(
    Map<String, dynamic> json) {
  final StudentInfoBeanMerchantVos studentInfoBeanMerchantVos = StudentInfoBeanMerchantVos();
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    studentInfoBeanMerchantVos.name = name;
  }
  final String? realName = jsonConvert.convert<String>(json['realName']);
  if (realName != null) {
    studentInfoBeanMerchantVos.realName = realName;
  }
  final String? mobile = jsonConvert.convert<String>(json['mobile']);
  if (mobile != null) {
    studentInfoBeanMerchantVos.mobile = mobile;
  }
  final int? isEnable = jsonConvert.convert<int>(json['isEnable']);
  if (isEnable != null) {
    studentInfoBeanMerchantVos.isEnable = isEnable;
  }
  final String? merchantCode = jsonConvert.convert<String>(
      json['merchantCode']);
  if (merchantCode != null) {
    studentInfoBeanMerchantVos.merchantCode = merchantCode;
  }
  final String? province = jsonConvert.convert<String>(json['province']);
  if (province != null) {
    studentInfoBeanMerchantVos.province = province;
  }
  final String? city = jsonConvert.convert<String>(json['city']);
  if (city != null) {
    studentInfoBeanMerchantVos.city = city;
  }
  final String? area = jsonConvert.convert<String>(json['area']);
  if (area != null) {
    studentInfoBeanMerchantVos.area = area;
  }
  final String? address = jsonConvert.convert<String>(json['address']);
  if (address != null) {
    studentInfoBeanMerchantVos.address = address;
  }
  final String? regTime = jsonConvert.convert<String>(json['regTime']);
  if (regTime != null) {
    studentInfoBeanMerchantVos.regTime = regTime;
  }
  final String? merchantName = jsonConvert.convert<String>(
      json['merchantName']);
  if (merchantName != null) {
    studentInfoBeanMerchantVos.merchantName = merchantName;
  }
  final int? isCheck = jsonConvert.convert<int>(json['isCheck']);
  if (isCheck != null) {
    studentInfoBeanMerchantVos.isCheck = isCheck;
  }
  final String? longitude = jsonConvert.convert<String>(json['longitude']);
  if (longitude != null) {
    studentInfoBeanMerchantVos.longitude = longitude;
  }
  final String? latitude = jsonConvert.convert<String>(json['latitude']);
  if (latitude != null) {
    studentInfoBeanMerchantVos.latitude = latitude;
  }
  final String? expireDate = jsonConvert.convert<String>(json['expireDate']);
  if (expireDate != null) {
    studentInfoBeanMerchantVos.expireDate = expireDate;
  }
  final String? marketChargePerson = jsonConvert.convert<String>(
      json['marketChargePerson']);
  if (marketChargePerson != null) {
    studentInfoBeanMerchantVos.marketChargePerson = marketChargePerson;
  }
  final String? consultingChargePerson = jsonConvert.convert<String>(
      json['consultingChargePerson']);
  if (consultingChargePerson != null) {
    studentInfoBeanMerchantVos.consultingChargePerson = consultingChargePerson;
  }
  final String? teachingChargePerson = jsonConvert.convert<String>(
      json['teachingChargePerson']);
  if (teachingChargePerson != null) {
    studentInfoBeanMerchantVos.teachingChargePerson = teachingChargePerson;
  }
  final String? marketChargePersonPhone = jsonConvert.convert<String>(
      json['marketChargePersonPhone']);
  if (marketChargePersonPhone != null) {
    studentInfoBeanMerchantVos.marketChargePersonPhone =
        marketChargePersonPhone;
  }
  final String? consultingChargePersonPhone = jsonConvert.convert<String>(
      json['consultingChargePersonPhone']);
  if (consultingChargePersonPhone != null) {
    studentInfoBeanMerchantVos.consultingChargePersonPhone =
        consultingChargePersonPhone;
  }
  final String? teachingChargePersonPhone = jsonConvert.convert<String>(
      json['teachingChargePersonPhone']);
  if (teachingChargePersonPhone != null) {
    studentInfoBeanMerchantVos.teachingChargePersonPhone =
        teachingChargePersonPhone;
  }
  final String? roleTag = jsonConvert.convert<String>(json['roleTag']);
  if (roleTag != null) {
    studentInfoBeanMerchantVos.roleTag = roleTag;
  }
  final String? channelManagerCode = jsonConvert.convert<String>(
      json['channelManagerCode']);
  if (channelManagerCode != null) {
    studentInfoBeanMerchantVos.channelManagerCode = channelManagerCode;
  }
  final String? refereeCode = jsonConvert.convert<String>(json['refereeCode']);
  if (refereeCode != null) {
    studentInfoBeanMerchantVos.refereeCode = refereeCode;
  }
  return studentInfoBeanMerchantVos;
}

Map<String, dynamic> $StudentInfoBeanMerchantVosToJson(
    StudentInfoBeanMerchantVos entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['name'] = entity.name;
  data['realName'] = entity.realName;
  data['mobile'] = entity.mobile;
  data['isEnable'] = entity.isEnable;
  data['merchantCode'] = entity.merchantCode;
  data['province'] = entity.province;
  data['city'] = entity.city;
  data['area'] = entity.area;
  data['address'] = entity.address;
  data['regTime'] = entity.regTime;
  data['merchantName'] = entity.merchantName;
  data['isCheck'] = entity.isCheck;
  data['longitude'] = entity.longitude;
  data['latitude'] = entity.latitude;
  data['expireDate'] = entity.expireDate;
  data['marketChargePerson'] = entity.marketChargePerson;
  data['consultingChargePerson'] = entity.consultingChargePerson;
  data['teachingChargePerson'] = entity.teachingChargePerson;
  data['marketChargePersonPhone'] = entity.marketChargePersonPhone;
  data['consultingChargePersonPhone'] = entity.consultingChargePersonPhone;
  data['teachingChargePersonPhone'] = entity.teachingChargePersonPhone;
  data['roleTag'] = entity.roleTag;
  data['channelManagerCode'] = entity.channelManagerCode;
  data['refereeCode'] = entity.refereeCode;
  return data;
}

extension StudentInfoBeanMerchantVosExtension on StudentInfoBeanMerchantVos {
  StudentInfoBeanMerchantVos copyWith({
    String? name,
    String? realName,
    String? mobile,
    int? isEnable,
    String? merchantCode,
    String? province,
    String? city,
    String? area,
    String? address,
    String? regTime,
    String? merchantName,
    int? isCheck,
    String? longitude,
    String? latitude,
    String? expireDate,
    String? marketChargePerson,
    String? consultingChargePerson,
    String? teachingChargePerson,
    String? marketChargePersonPhone,
    String? consultingChargePersonPhone,
    String? teachingChargePersonPhone,
    String? roleTag,
    String? channelManagerCode,
    String? refereeCode,
  }) {
    return StudentInfoBeanMerchantVos()
      ..name = name ?? this.name
      ..realName = realName ?? this.realName
      ..mobile = mobile ?? this.mobile
      ..isEnable = isEnable ?? this.isEnable
      ..merchantCode = merchantCode ?? this.merchantCode
      ..province = province ?? this.province
      ..city = city ?? this.city
      ..area = area ?? this.area
      ..address = address ?? this.address
      ..regTime = regTime ?? this.regTime
      ..merchantName = merchantName ?? this.merchantName
      ..isCheck = isCheck ?? this.isCheck
      ..longitude = longitude ?? this.longitude
      ..latitude = latitude ?? this.latitude
      ..expireDate = expireDate ?? this.expireDate
      ..marketChargePerson = marketChargePerson ?? this.marketChargePerson
      ..consultingChargePerson = consultingChargePerson ??
          this.consultingChargePerson
      ..teachingChargePerson = teachingChargePerson ?? this.teachingChargePerson
      ..marketChargePersonPhone = marketChargePersonPhone ??
          this.marketChargePersonPhone
      ..consultingChargePersonPhone = consultingChargePersonPhone ??
          this.consultingChargePersonPhone
      ..teachingChargePersonPhone = teachingChargePersonPhone ??
          this.teachingChargePersonPhone
      ..roleTag = roleTag ?? this.roleTag
      ..channelManagerCode = channelManagerCode ?? this.channelManagerCode
      ..refereeCode = refereeCode ?? this.refereeCode;
  }
}