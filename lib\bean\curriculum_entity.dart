import 'package:student_end_flutter/generated/json/base/json_field.dart';
import 'package:student_end_flutter/generated/json/curriculum_entity.g.dart';
import 'dart:convert';
export 'package:student_end_flutter/generated/json/curriculum_entity.g.dart';

@JsonSerializable()
class CurriculumEntity {
	late String id;
	late String curriculumName;
	late String curriculumTypeName;
	late String curriculumType;
	late int status;
	late int sort;
	late String curriculumToolName;
	late String createTime;
	late String updateTime;
	late List<CurriculumCurriculumTool> curriculumTool;
	late bool isPurchase;
	dynamic curriculumIdList;

	CurriculumEntity();

	factory CurriculumEntity.fromJson(Map<String, dynamic> json) => $CurriculumEntityFromJson(json);

	Map<String, dynamic> toJson() => $CurriculumEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class CurriculumCurriculumTool {
	late String id;
	late String toolName;
	late int status;
	late String generalFlag;
	late int sort;
	late String iconAddress;
	late String ashIconAddress;
	late String jumpAddress;
	late String appJumpAddress;
	late String updateTime;
	late String createTime;
	late String bindId;
	late String remark;
	late String showChannel;
	late int deleted;
	late int showStatus;

	CurriculumCurriculumTool();

	factory CurriculumCurriculumTool.fromJson(Map<String, dynamic> json) => $CurriculumCurriculumToolFromJson(json);

	Map<String, dynamic> toJson() => $CurriculumCurriculumToolToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}