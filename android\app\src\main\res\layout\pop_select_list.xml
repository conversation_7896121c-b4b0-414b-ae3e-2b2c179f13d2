<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    >
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingTop="20dp"
        android:paddingLeft="14dp"
        android:paddingRight="14dp"
        android:layout_marginLeft="14dp"
        android:layout_marginRight="14dp"
        android:background="@drawable/bg_radius20_white"
        android:layout_centerInParent="true"
        >
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_above="@+id/recyclerView"
            >

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="选择上课身份"
                android:textColor="@color/_333333"
                android:textSize="16sp"
                android:textStyle="bold" />
        </RelativeLayout>
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_marginTop="14dp"
            android:layout_height="wrap_content"/>
        <LinearLayout
            android:id="@+id/ll_bottom"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="14dp"
            android:layout_marginBottom="14dp"
            >
            <TextView
                android:id="@+id/tv_cancle"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:background="@drawable/bg_radius20_428a6f_line"
                android:gravity="center"
                android:textSize="16sp"
                android:textColor="@color/_428a6f"
                android:text="取消"/>
            <TextView
                android:id="@+id/tv_sure"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:layout_marginLeft="20dp"
                android:gravity="center"
                android:textColor="@color/white"
                android:background="@drawable/bg_radius20_428a6f"
                android:textSize="16sp"
                android:text="确定"/>
        </LinearLayout>
    </LinearLayout>
</RelativeLayout>