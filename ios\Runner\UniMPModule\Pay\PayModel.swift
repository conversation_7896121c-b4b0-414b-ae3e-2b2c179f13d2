//
//  PayModel.swift
//  Runner
//
//  Created by 许江泉 on 2025/2/17.
//

import UIKit
import HandyJSON

class PayModel {
    struct WechatPayModel: HandyJSON {
        var cusid: String = ""
        var appid: String = ""
        var orgid: String = ""
        var version: String = ""
        var trxamt: String = ""
        var reqsn: String = ""
        var notify_url: String = ""
        var body: String = ""
        var remark: String = ""
        var validtime: String = ""
        var randomstr: String = ""
        var paytype: String = ""
        var sign: String = ""
        var innerappid: String = ""
        var signtype: String = ""

    }

}

