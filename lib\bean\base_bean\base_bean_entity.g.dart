import 'package:student_end_flutter/generated/json/base/json_convert_content.dart';
import 'package:student_end_flutter/bean/base_bean/base_bean_entity.dart';

BaseBeanEntity<T> $BaseBeanEntityFromJson<T>(Map<String, dynamic> json) {
  final BaseBeanEntity<T> baseBeanEntity = BaseBeanEntity<T>();
  final int? code = jsonConvert.convert<int>(json['code']);
  if (code != null) {
    baseBeanEntity.code = code;
  }
  final bool? success = jsonConvert.convert<bool>(json['success']);
  if (success != null) {
    baseBeanEntity.success = success;
  }
  final String? message = jsonConvert.convert<String>(json['message']);
  if (message != null) {
    baseBeanEntity.message = message;
  }
  final T? data = jsonConvert.convert<T>(json['data']);
  baseBeanEntity.data = data;

  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    baseBeanEntity.total = total;
  }
  final int? status = jsonConvert.convert<int>(json['status']);
  if (status != null) {
    baseBeanEntity.status = status;
  }
  return baseBeanEntity;
}

Map<String, dynamic> $BaseBeanEntityToJson(BaseBeanEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['code'] = entity.code;
  data['success'] = entity.success;
  data['message'] = entity.message;
  data['data'] = entity.data.toJson();
  data['total'] = entity.total;
  data['status'] = entity.status;
  return data;
}

extension BaseBeanEntityExtension<T> on BaseBeanEntity<T?> {
  BaseBeanEntity<T?> copyWith({
    int? code,
    bool? success,
    String? message,
    T? data,
    int? total,
    int? status,
  }) {
    return BaseBeanEntity()
      ..code = code ?? this.code
      ..success = success ?? this.success
      ..message = message ?? this.message
      ..data = data ?? this.data
      ..total = total ?? this.total
      ..status = status ?? this.status;
  }

}