package com.dxznjy.alading.fragment;


import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Typeface;
import android.net.Uri;
import android.net.wifi.WifiManager;
import android.util.Log;
import android.view.View;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.dxznjy.alading.DXApplication;
import com.dxznjy.alading.R;
import com.dxznjy.alading.activity.givelessons.MeetingPermissionActivity;
import com.dxznjy.alading.adapter.givelessons.PayGiveFinishLessonsAdapter;
import com.dxznjy.alading.adapter.givelessons.PayGiveLessonsAdapter;
import com.dxznjy.alading.adapter.givelessons.VideoGiveLessonsAdapter;
import com.dxznjy.alading.api.ApiProvider;
import com.dxznjy.alading.common.Constants;
import com.dxznjy.alading.entity.FeedBack;
import com.dxznjy.alading.entity.SelectListPopInfo;
import com.dxznjy.alading.entity.ToFlutterMeetingNum;
import com.dxznjy.alading.http.BaseSubscriber;
import com.dxznjy.alading.response.CourseInfoResponse;
import com.dxznjy.alading.response.CourseTypeResponse;
import com.dxznjy.alading.response.MeetingInfoResponse;
import com.dxznjy.alading.response.PayCourseDataResponse;
import com.dxznjy.alading.response.VideoCourseDataResponse;
import com.dxznjy.alading.widget.MySplashView;
import com.dxznjy.alading.widget.SelectCoursePopWindow;
import com.dxznjy.alading.widget.SelectCoursePopWindowPad;
import com.dxznjy.alading.widget.SelectListPopWindow;
import com.google.gson.Gson;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.tencent.mm.opensdk.modelmsg.SendMessageToWX;
import com.tencent.mm.opensdk.modelmsg.WXMediaMessage;
import com.tencent.mm.opensdk.modelmsg.WXWebpageObject;
import com.vondear.rxtool.RxSPTool;

import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import io.dcloud.feature.sdk.DCUniMPSDK;
import io.dcloud.feature.sdk.Interface.IUniMP;
import io.dcloud.feature.unimp.config.UniMPOpenConfiguration;
import io.flutter.plugin.common.MethodChannel;

public class FragmentGiveLessons extends BaseFragment implements View.OnClickListener {
    @BindView(R.id.recyclerView)
    RecyclerView recyclerView;
    @BindView(R.id.rl_onclass)
    RelativeLayout rl_onclass;
    @BindView(R.id.rl_unclass)
    RelativeLayout rl_unclass;
    @BindView(R.id.tv_payclass)
    TextView tv_payclass;
    @BindView(R.id.tv_videoclass)
    TextView tv_videoclass;
    @BindView(R.id.view_unclass)
    View view_unclass;
    @BindView(R.id.view_onclass)
    View view_onclass;
    @BindView(R.id.view_line)
    View view_line;
    @BindView(R.id.tv_select_type)
    TextView tv_select_type;
    @BindView(R.id.refreshLayout)
    SmartRefreshLayout refreshLayout;

    int pageNo = 1;
    boolean refresh = true;
    private  String curriculumTypeId = "";
    /**
     * classType 0  交付课  1 录播课   classStatusType 0未上课 1 已上课
     */
    Integer classType=0,classStatusType=0;
    List<PayCourseDataResponse.PayCourseData> payCourseData = new ArrayList<>();

    List<VideoCourseDataResponse.VideoCourseData> videoCourseData = new ArrayList<>();
    List<CourseTypeResponse.CourseType> selectCourseTypePopInfos = new ArrayList<>();//课程类型选择
    PayGiveLessonsAdapter unGiveLessonsAdapter;//未上课
    PayGiveFinishLessonsAdapter onGiveLessonsAdapter;//已上课
    VideoGiveLessonsAdapter videoGiveLessonsAdapter;//录播课
    String pageRoute = "",courseId = "",userId = "";
    @Override
    public void initViewAndData() {
        if (DXApplication.isTablet(getActivity())){
            recyclerView.setLayoutManager(new GridLayoutManager(getActivity(),2));
        }else{
            recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
        }
        classType = Integer.parseInt(RxSPTool.getString(getActivity(),Constants.SELECTED));
        if (classType == 0){
            unGiveLessonsAdapter=new PayGiveLessonsAdapter(getActivity(),payCourseData);
            recyclerView.setAdapter(unGiveLessonsAdapter);
            unGiveLessonsAdapter.setOnItemChildClickListener(new BaseQuickAdapter.OnItemChildClickListener() {
                @Override
                public void onItemChildClick(BaseQuickAdapter adapter, View view, int position) {
                    switch (view.getId()){//立即上课
                        case R.id.tv_golessons:
                            gotoStudy(payCourseData.get(position).getCourseId());
                            break;
                    }
                }
            });
            unGiveLessonsAdapter.setEmptyView(R.layout.empty_data,recyclerView);
        }else{
            if (videoGiveLessonsAdapter == null){
                videoGiveLessonsAdapter = new VideoGiveLessonsAdapter(getActivity(),videoCourseData);
            }
            recyclerView.setAdapter(videoGiveLessonsAdapter);
            videoGiveLessonsAdapter.setOnItemChildClickListener(new BaseQuickAdapter.OnItemChildClickListener() {
                @Override
                public void onItemChildClick(BaseQuickAdapter adapter, View view, int position) {
                    switch (view.getId()){
                        case R.id.tv_gotostudy:
                            pageRoute = Constants.COURSE_VIDEO_DEATAIL;
                            courseId = videoCourseData.get(position).getCourseId();
                            userId = videoCourseData.get(position).getUserId();
                            toUniappPage();
                            break;
                    }
                }
            });
            videoGiveLessonsAdapter.setEmptyView(R.layout.empty_data,recyclerView);
        }
        changeClassType();



//        getDeliverCoursePage();

        refreshLayout.setEnableLoadMore( true );
        refreshLayout.setEnableNestedScroll( false );
        refreshLayout.setOnRefreshListener(refreshLayout -> {
            refresh = true;
            if (classType == 0){
                getDeliverCoursePage();
            }else if (classType == 1){
                getRecordCoursePage();
            }
        });
        refreshLayout.setOnLoadMoreListener(refreshLayout -> {
            refresh = false;
            if (classType == 0){
                getDeliverCoursePage();
            }else if (classType == 1){
                getRecordCoursePage();
            }
        });
    }
    /**
     * 获取课程下的会议号  并加入会议
     */
    public void gotoStudy(String courseId){
        showDialog();
        Map<String,String> map = new HashMap<>();
        map.put("courseId",courseId);
        request(ApiProvider.getInstance(getActivity()).DFService.getCourseMeetingInfo(map), new BaseSubscriber<MeetingInfoResponse>(getActivity()){
            @Override
            public void onCompleted() {
                super.onCompleted();
            }

            @Override
            public void onError(Throwable e) {
                super.onError(e);
                closeDialog();
            }


            @Override
            public void onNext(MeetingInfoResponse response) {
                super.onNext(response);
                closeDialog();
                Intent intent = new Intent(getActivity(), MeetingPermissionActivity.class);
                intent.putExtra("courseId",courseId);
                getActivity().startActivity(intent);
                getActivity().finish();
            }
        });
    }


    @Override
    public int setContent() {
        return R.layout.fragment_give_lessons;
    }
    @OnClick({R.id.ll_select_type,R.id.tv_payclass,R.id.tv_videoclass,R.id.rl_unclass,R.id.rl_onclass,R.id.rl_titlebar})
    public void onClick(View view) {
        switch (view.getId()){
            case R.id.rl_titlebar:
                getActivity().finish();
                break;
            case R.id.ll_select_type:
//                if (selectCourseTypePopInfos != null&&selectCourseTypePopInfos.size()>0){
//                    showPopSelectCourseType();
//                }else{
//                    getCourseType();
//                }
                List<SelectListPopInfo> selectListPopInfos = new ArrayList<>();
                SelectListPopInfo selectListPopInfo = new SelectListPopInfo();
                selectListPopInfos.clear();
                selectListPopInfo=new SelectListPopInfo();
                selectListPopInfo.setTitle("我是学生");
                selectListPopInfo.setType(1);
                selectListPopInfos.add(selectListPopInfo);
                selectListPopInfo=new SelectListPopInfo();
                selectListPopInfo.setTitle("我是家长");
                selectListPopInfo.setType(1);
                selectListPopInfos.add(selectListPopInfo);
                SelectListPopWindow selectListPopWindow=new SelectListPopWindow(getActivity(), "选择上课身份","确定",
                        recyclerView, selectListPopInfos, Constants.VERTICAL, 1, false,new SelectListPopWindow.OnSelectListener() {
                    @Override
                    public void onSelect(List<SelectListPopInfo> data) {
                    }
                });
                selectListPopWindow.showPop();
                break;
            case R.id.tv_payclass:
                classType = 0;
                changeClassType();
                break;
            case R.id.tv_videoclass:
                classType = 1;
                changeClassType();
                break;
            case R.id.rl_onclass:
                classStatusType = 1;
                change();
                break;
            case R.id.rl_unclass:
                classStatusType = 0;
                change();
                break;
        }
    }
    public  byte[] bitmapToByteArray(Bitmap bitmap) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        bitmap.compress(Bitmap.CompressFormat.PNG, 70, outputStream);
        return outputStream.toByteArray();
    }
    /**
     * 打开微信小程序页面
     */
    public void toUniappPage(){
//        startActivity(new Intent(getActivity(),MeetingPermissionActivity.class));
        String studentCode = RxSPTool.getString(getContext(), Constants.STUDENT_CODE);
        Log.i("zgj", "录播课"+studentCode);
        UniMPOpenConfiguration uniMPOpenConfiguration = new UniMPOpenConfiguration();
        try {
            if (classType == 0){//交付课 查看反馈
                String token = RxSPTool.getString(getContext(), Constants.PARENT_TOKEN);
                uniMPOpenConfiguration.path = pageRoute+token+"&studentCode="+studentCode;
            }else{//录播课
                String token = RxSPTool.getString(getContext(), Constants.PARENT_TOKEN);
                String memberCode = RxSPTool.getString(getContext(), Constants.MERCHANT_CODE);
                uniMPOpenConfiguration.path = pageRoute +
                        "&token="+token+
                        "&studentCode="+studentCode +"&memberCode="+memberCode +"&courseId="+courseId +"&userId="+userId;
            }
            Log.i("zgj", uniMPOpenConfiguration.path);
//            uniMPOpenConfiguration.splashClass = MySplashView.class;
            DXApplication.iUniMP = DCUniMPSDK.getInstance().openUniMP(getContext(), Constants.XCX_PKG_NAME,uniMPOpenConfiguration);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    public void getCourseById(String courseId){
        showDialog();
        Map<String,String> map = new HashMap<>();
        map.put("courseId",courseId);
        request(ApiProvider.getInstance(getActivity()).DFService.getCourseById(map), new BaseSubscriber<CourseInfoResponse>(getActivity()){
            @Override
            public void onCompleted() {
                super.onCompleted();
            }

            @Override
            public void onError(Throwable e) {
                super.onError(e);
                closeDialog();
            }


            @Override
            public void onNext(CourseInfoResponse response) {
                super.onNext(response);
                closeDialog();
                toUniappEnglishPage(response);
            }
        });
    }


    public void toUniappEnglishPage(CourseInfoResponse response){
//        startActivity(new Intent(getActivity(),MeetingPermissionActivity.class));
        try {
            String token = RxSPTool.getString(getActivity(), Constants.PARENT_TOKEN);
            UniMPOpenConfiguration uniMPOpenConfiguration = new UniMPOpenConfiguration();
            FeedBack feedBack = new FeedBack();
            feedBack.setExperience(response.getData().isExperience());
            feedBack.setId(response.getData().getId());
            uniMPOpenConfiguration.path = Constants.COURSE_FEEBACK_ENGLISH+token+"&data="+new Gson().toJson(feedBack);
//            uniMPOpenConfiguration.splashClass = MySplashView.class;
            Log.i("zgj",uniMPOpenConfiguration.path);
            DXApplication.iUniMP = DCUniMPSDK.getInstance().openUniMP(getActivity(), Constants.XCX_PKG_NAME,uniMPOpenConfiguration);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }




    /***
     *切换交付课 录播课ui
     */
    public void changeClassType(){
        tv_payclass.setBackground(null);
        tv_videoclass.setBackground(null);
        tv_payclass.setTextColor(getResources().getColor(R.color._a29d9d));
        tv_videoclass.setTextColor(getResources().getColor(R.color._a29d9d));
        tv_payclass.setTypeface(Typeface.DEFAULT);
        tv_videoclass.setTypeface(Typeface.DEFAULT);
        rl_unclass.setVisibility(View.INVISIBLE);
        rl_onclass.setVisibility(View.INVISIBLE);
        view_line.setVisibility(View.INVISIBLE);
        curriculumTypeId = "";
        tv_select_type.setText("全部");
        if (selectCourseTypePopInfos != null&&selectCourseTypePopInfos.size()>0){
            for (int i = 0;i<selectCourseTypePopInfos.size();i++){
                selectCourseTypePopInfos.get(i).setType(1);
                selectCourseTypePopInfos.get(i).setCheck(false);
            }
            selectCourseTypePopInfos.get(0).setCheck(true);
        }
        switch (classType){
            case 0:
                tv_payclass.setTextColor(getResources().getColor(R.color._428a6f));
                tv_payclass.setBackgroundResource(R.drawable.bg_radius4_white);
                rl_unclass.setVisibility(View.VISIBLE);
                rl_onclass.setVisibility(View.VISIBLE);
                view_line.setVisibility(View.VISIBLE);
                change();
                tv_payclass.setTypeface(Typeface.DEFAULT_BOLD);
                break;
            case 1:
                tv_videoclass.setTextColor(getResources().getColor(R.color._428a6f));
                tv_videoclass.setBackgroundResource(R.drawable.bg_radius4_white);
                if (videoGiveLessonsAdapter == null){
                    videoGiveLessonsAdapter = new VideoGiveLessonsAdapter(getActivity(),videoCourseData);
                }
                recyclerView.setAdapter(videoGiveLessonsAdapter);
                videoGiveLessonsAdapter.setOnItemChildClickListener(new BaseQuickAdapter.OnItemChildClickListener() {
                    @Override
                    public void onItemChildClick(BaseQuickAdapter adapter, View view, int position) {
                        switch (view.getId()){
                            case R.id.tv_gotostudy:
                                pageRoute = Constants.COURSE_VIDEO_DEATAIL;
                                courseId = videoCourseData.get(position).getCourseId();
                                userId = videoCourseData.get(position).getUserId();
                                toUniappPage();
                                break;
                        }
                    }
                });
                videoGiveLessonsAdapter.setEmptyView(R.layout.empty_data,recyclerView);
                tv_videoclass.setTypeface(Typeface.DEFAULT_BOLD);
                refresh = true;
                getRecordCoursePage();
                break;
        }
    }

    /**
     * 切换未上课 已上课ui
     */
    public void change(){
        view_unclass.setVisibility(View.INVISIBLE);
        view_onclass.setVisibility(View.INVISIBLE);
        switch (classStatusType){
            case 0:
                if (unGiveLessonsAdapter == null){
                    unGiveLessonsAdapter = new PayGiveLessonsAdapter(getActivity(),payCourseData);
                }
                recyclerView.setAdapter(unGiveLessonsAdapter);
                unGiveLessonsAdapter.setEmptyView(R.layout.empty_data,recyclerView);
                view_unclass.setVisibility(View.VISIBLE);
                break;
            case 1:
                if (onGiveLessonsAdapter == null){
                    onGiveLessonsAdapter = new PayGiveFinishLessonsAdapter(getActivity(),payCourseData);
                }
                recyclerView.setAdapter(onGiveLessonsAdapter);
                onGiveLessonsAdapter.setEmptyView(R.layout.empty_data,recyclerView);
                onGiveLessonsAdapter.setOnItemChildClickListener(new BaseQuickAdapter.OnItemChildClickListener() {
                    @Override
                    public void onItemChildClick(BaseQuickAdapter adapter, View view, int position) {
                        switch (view.getId()){//进入学习查看反馈
                            case R.id.tv_feedback:
                                if (payCourseData.get(position).getCourseType().contains("鼎英语")){
                                    getCourseById(payCourseData.get(position).getCourseId());
                                }else{
                                    pageRoute=Constants.COURSE_FEEBACK;
                                    toUniappPage();
                                }
                                break;
                            case R.id.tv_goonlearn:
                                gotoStudy(payCourseData.get(position).getCourseId());
                                break;
                        }
                    }
                });
                view_onclass.setVisibility(View.VISIBLE);
                break;
        }
        refresh=true;
        getDeliverCoursePage();
    }


    /**
     * 课程筛选条件弹窗
     */
    public void showPopSelectCourseType(){
        boolean isCheck = false;
        for (int i = 0;i<selectCourseTypePopInfos.size();i++){
            selectCourseTypePopInfos.get(i).setType(1);
            if (selectCourseTypePopInfos.get(i).isCheck()){//是否有选中筛选条件
                isCheck=true;
            }
        }
        if (!isCheck){//如果没有选中任何选项 将筛选项全部 设置选中状态
            selectCourseTypePopInfos.get(0).setCheck(true);
        }
        if (DXApplication.isTablet(getActivity())){//平板和手机使用不同的弹窗样式
            SelectCoursePopWindowPad selectListPopWindow = new SelectCoursePopWindowPad(getActivity(), "课程类型","确定",
                    recyclerView, selectCourseTypePopInfos, Constants.HORIZONTAL, 3, false,new SelectCoursePopWindowPad.OnSelectListener() {
                @Override
                public void onSelect(CourseTypeResponse.CourseType data) {
                    if (data.getEnName().equals("全部")){
                        curriculumTypeId = "";
                    }else{
                        curriculumTypeId = data.getId();
                    }
                    tv_select_type.setText(data.getEnName());
                    refresh=true;
                    if (classType == 0){
                        getDeliverCoursePage();
                    }else if (classType == 1){
                        getRecordCoursePage();
                    }
                }
            });
            selectListPopWindow.showPop();
        }else{
            SelectCoursePopWindow selectListPopWindow = new SelectCoursePopWindow(getActivity(), "课程类型","确定",
                    recyclerView, selectCourseTypePopInfos, Constants.HORIZONTAL, 3, false,new SelectCoursePopWindow.OnSelectListener() {
                @Override
                public void onSelect(CourseTypeResponse.CourseType data) {
                    if (data.getEnName().equals("全部")){
                        curriculumTypeId = "";
                    }else{
                        curriculumTypeId = data.getId();
                    }
                    tv_select_type.setText(data.getEnName());
                    refresh=true;
                    if (classType == 0){
                        getDeliverCoursePage();
                    }else if (classType == 1){
                        getRecordCoursePage();
                    }
                }
            });
            selectListPopWindow.showPop();
        }

    }




    /**
     * 获取交付课列表
     */
    boolean canLoadMore = true;
    private void getDeliverCoursePage() {
        showDialog();
        if (refresh) {
            pageNo = 1 ;
            canLoadMore = true;
        } else {
            pageNo++;
        }
        Map<String,String> map = new HashMap<>();
        map.put("courseStatus",classStatusType+"");
        map.put("pageNum",pageNo+"");
        map.put("pageSize","10");
        map.put("curriculumTypeId",curriculumTypeId);
        map.put("merchantCode",RxSPTool.getString(getActivity(),Constants.MERCHANT_CODE));
        request(ApiProvider.getInstance(getActivity()).DFService.getDeliverCoursePage(map), new BaseSubscriber<PayCourseDataResponse>(getActivity()){
            @Override
            public void onCompleted() {
                super.onCompleted();
                if (refresh) {
                    refreshLayout.finishRefresh();
                } else {
                    refreshLayout.finishLoadMore();
                }
            }

            @Override
            public void onError(Throwable e) {
                super.onError(e);
                closeDialog();
            }

            @Override
            public void onNext(PayCourseDataResponse response) {
                super.onNext(response);
                closeDialog();
                if(response.getData() != null){
                    if (refresh) {
                        payCourseData.clear();
                        payCourseData.addAll(response.getData().getData());
                        if(response.getData().getData().size()<10){
                            canLoadMore=false;
                        }
                        if (classStatusType == 0){
                            unGiveLessonsAdapter.notifyDataSetChanged();
                        }else{
                            onGiveLessonsAdapter.notifyDataSetChanged();
                        }
                    }else{
                        if(canLoadMore){
                            payCourseData.addAll(response.getData().getData());
                            if(response.getData().getData().size()<10){
                                canLoadMore = false;
                            }
                            if (classStatusType == 0){
                                unGiveLessonsAdapter.notifyItemChanged(payCourseData.size(),response.getData().getData());
                            }else{
                                onGiveLessonsAdapter.notifyItemChanged(payCourseData.size(),response.getData().getData());
                            }
                        }
                    }

                }
            }
        });
    }


    /**
     * 获取录播课列表
     */
    private void getRecordCoursePage() {
        showDialog();
        if (refresh) {
            pageNo = 1 ;
            canLoadMore = true;
        } else {
            pageNo++;
        }
        Map<String,String> map=new HashMap<>();
        map.put("pageNum",pageNo+"");
        map.put("pageSize","10");
        map.put("curriculumTypeId",curriculumTypeId);
        request(ApiProvider.getInstance(getActivity()).DFService.getRecordCoursePage(map), new BaseSubscriber<VideoCourseDataResponse>(getActivity()){
            @Override
            public void onCompleted() {
                super.onCompleted();
                if (refresh) {
                    refreshLayout.finishRefresh();
                } else {
                    refreshLayout.finishLoadMore();
                }
            }

            @Override
            public void onError(Throwable e) {
                super.onError(e);
                closeDialog();
            }

            @Override
            public void onNext(VideoCourseDataResponse response) {
                super.onNext(response);
                closeDialog();
                if(response.getData() != null){
                    if (refresh) {
                        videoCourseData.clear();
                        videoCourseData.addAll(response.getData().getData());
                        if(response.getData().getData().size()<10){
                            canLoadMore = false;
                        }
                    }else{
                        if(canLoadMore){
                            videoCourseData.addAll(response.getData().getData());
                            if(response.getData().getData().size()<10){
                                canLoadMore = false;
                            }
                        }
                    }
                    videoGiveLessonsAdapter.notifyDataSetChanged();
                }
            }
        });
    }

    /**
     * 获取课程筛选项
     */
    private void getCourseType()  {
        showDialog();
        request(ApiProvider.getInstance(getActivity()).DFService.getCourseType(), new BaseSubscriber<CourseTypeResponse>(getActivity()){
            @Override
            public void onCompleted() {
                super.onCompleted();
            }

            @Override
            public void onError(Throwable e) {
                super.onError(e);
                closeDialog();
            }

            @Override
            public void onNext(CourseTypeResponse response) {
                super.onNext(response);
                closeDialog();
                if (response.getData() != null&&response.getData().size()>0){
                    selectCourseTypePopInfos.clear();
                    selectCourseTypePopInfos.addAll(response.getData());
                    showPopSelectCourseType();
                }
            }
        });
    }
}
