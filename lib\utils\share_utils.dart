import 'dart:convert';

import 'package:student_end_flutter/common/user_option.dart';

import '../common/Config.dart';
import 'android_ios_plugin.dart';
import 'httpRequest.dart';
import '../utils/app_version_util.dart';
class ShareData {
  static var shareData = null;
  static var invitationInfo = null;
  static var shardInfo = null;
  static String userId = ""; //分享人code
  static String courseId = ''; //超人会员id或者商品id
  static String secCode = '';

  cleanShareData() {
    shareData = null;
    invitationInfo = null;
    shardInfo = null;
    userId = "";
    courseId = '';
    secCode = '';
  }
}

class ShareUtils {
  String imgLookId = "";
  String type = ""; //分享进入类型 1 商品  2超人俱乐部会员  3俱乐部  9会议 6新商品  8e签宝合同
  String invitationCode = ''; // 超人分享码
  String contractId = '';

  getShard(url) {
    if (url == null) {
      return;
    }
    List<String> parts = url.split("?");
    if (!parts[0].contains("pages/beingShared/index")) {
      if (parts[0].startsWith("/")) {
        parts[0] = parts[0].substring(1);
      }
      String path = parts[0];
      if (path.contains("Personalcenter/my/parentVipEquity") &&
          UserOption.parentMemberType == 5) {
        path = "Coursedetails/my/parentEquity";
      }
      url = "$path?${parts[1]}";
      String flag = "?";
      if (flag.contains("?")) {
        flag = "&";
      }
      url +=
          "${flag}token=${UserOption.token}&app=2&userCode=${UserOption.userInfoBeanEntity?.userCode}";
      url += "&parentMemberType=${UserOption.parentMemberType}";

      if (path.contains("Personalcenter/my/parentVipEquity")) {
        url +=
            "&user2Id=${UserOption.userId}&showParentVipBuyMember=${UserOption.showParentVipBuyMember}&phone=${UserOption.userInfoBeanEntity?.mobile}&nickName=${UserOption.userInfoBeanEntity?.nickName}";
      } else {
        url +=
            "&userId=${UserOption.userId}&userCode=${UserOption.userInfoBeanEntity?.userCode}&identityType=${UserOption.userInfoBeanEntity?.identityType}";
      }
      url += "&baseUrl=${Config.URL}&appVersion=${AppVersionUtil.appVersion ?? ''}&distinctId=${AppVersionUtil.distinctId ?? ''}";
      print("-----url----have---$url");
      AndroidIosPlugin.openUniApp(url);
      return;
    }
    var options = optionsFunc(parts);
    imgLookId = options["scene"];
    type = options["type"];
    if (options["type"] != "8") {
      ShareData.userId = options["scene"];
      if (options["id"] != "") {
        ShareData.courseId = options["id"];
      }
      if (options["type"] == "6") {
        getUserInfoByUserId();
      } else {
        setUserId();
      }
    }
  }

  getUserInfoByUserId() {
    var response =
        HttpUtil().get('${Config.getUserInfoByUserId}?userId=$imgLookId');
    response.then((value) async {
      if (type == "6") {
        ShareData.shardInfo = {
          "mobile": value["mobile"],
          "nickName": value["nickName"],
          "userId": value["userId"]
        };
      }
      print(ShareData.shardInfo);
      setUserId();
    });
  }

  // 设置userId
  setUserId() {
    var data = {
      "userId": ShareData.userId,
      "invitationCode": invitationCode,
      "secCode": ShareData.secCode
    };
    ShareData.invitationInfo = data;
    toPageByType();
  }

  // 根据type  1跳转详情界面   2,3,4跳转H5
  toPageByType() {
    String path = '';
    if (type == '1') {
      path =
          "Coursedetails/productDetils?scene=${ShareData.userId}&type=$type&id=${ShareData.courseId}&options=sharePage";
    } else if (type == '4') {
      path = "Recharge/payment/payment?type=$type&id=${ShareData.courseId}";
    } else if (type == "9") {
      path = 'meeting/meetIndex?id=${ShareData.courseId}';
      print(path);
    } else if (type == "6") {
      path = 'Coursedetails/productDetils?id=${ShareData.courseId}&options=sharePage';
    } else if (type == '8') {
      path = 'signature/contract/manageSigning?id=$contractId';
    } else {
      path =
          'supermanClub/superman/superman?type=$type&secCode=${ShareData.secCode}';
    }
    // path += "&token=${UserOption.token}&app=2&userCode=${UserOption.userInfoBeanEntity?.userCode}"
    //     "&identityType=${UserOption.userInfoBeanEntity?.identityType}&userId=${UserOption.userId}";
    path +=
        "&token=${UserOption.token}&app=2&userCode=${UserOption.userInfoBeanEntity?.userCode}"
        "&identityType=${UserOption.userInfoBeanEntity?.identityType}&baseUrl=${Config.URL}&appVersion=${AppVersionUtil.appVersion ?? ''}&distinctId=${AppVersionUtil.distinctId ?? ''}";
    path +=
        "&shardInfo=${Uri.encodeComponent(json.encode(ShareData.shardInfo ?? {}))}&invitationInfo=${Uri.encodeComponent(json.encode(ShareData.invitationInfo ?? {}))}&userId=${UserOption.userId}";
    print(path);
    AndroidIosPlugin.openUniApp(path);
    ShareData.shardInfo = null;
    ShareData.invitationInfo = null;
    ShareData.courseId = "";
    ShareData.userId = "";
    ShareData.secCode = "";
  }

  optionsFunc(parts) {
    var queryParams = {};
    if (parts.length > 1) {
      String queryString = parts[1];
      queryString.split("&").forEach((param) {
        List<String> keyValue = param.split("=");
        if (keyValue.length == 2) {
          queryParams[keyValue[0]] = keyValue[1];
        }
      });
    }
    return queryParams;
  }
}
