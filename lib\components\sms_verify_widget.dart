import 'dart:convert';
import 'dart:math';
import 'dart:typed_data';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:student_end_flutter/components/toast_utils.dart';

import '../common/Config.dart';
import '../utils/httpRequest.dart';
import 'package:image/image.dart' as img;

import '../utils/utils.dart';

///滑动拼图验证码
class BlockPuzzleCaptchaPage extends StatefulWidget {
  var onSuccess;

  BlockPuzzleCaptchaPage(this.onSuccess);

  @override
  _BlockPuzzleCaptchaPageState createState() => _BlockPuzzleCaptchaPageState();
}

class _BlockPuzzleCaptchaPageState extends State<BlockPuzzleCaptchaPage> {
  String uuid = "";
  var responseData = null;

  late Uint8List imageBytes;
  late Uint8List slideBytes;
  double _imageWidth = 0;
  double _imageHeight = 0;

  var sliderColor = Colors.white; //滑块的背景色
  var sliderIcon = Icons.arrow_forward; //滑块的图标
  GlobalKey _baseImageKey = GlobalKey(); //背景图key
  GlobalKey _slideImageKey = GlobalKey(); //滑块
  double _bottomSliderSize = 40;
  var movedXBorderColor = Colors.white; //滑块拖动时，左边已滑的区域边框颜色
  bool sliderMoveFinish = false; //滑块拖动结束
  bool checkResultAfterDrag = false; //拖动后的校验结果
  double sliderStartX = 0;
  double sliderXMoved = 0;
  int moveX = 0;

  @override
  void initState() {
    super.initState();
    uuid = Utils.getUuid();
    loadCaptcha();
  }

  ///获取拼图
  loadCaptcha() async {
    setState(() {
      sliderMoveFinish = false;
      checkResultAfterDrag = false;
      sliderColor = Colors.white; //滑块的背景色
      sliderIcon = Icons.arrow_forward; //滑块的图标
      movedXBorderColor = Colors.white; //滑块拖动时，左边已滑的区域边框颜色
      sliderStartX = 0;
      sliderXMoved = 0;
    });
    var response = await HttpUtil().get("${Config.getSlide}?uuid=$uuid");
    print("---response---$response");
    if (response != null) {
      setState(() {
        responseData = response;
        imageBytes = base64Decode(responseData["oriImage"]);
        slideBytes = base64Decode(responseData["cutImage"]);
        getImageSizeFromBase64();
      });
    }
  }

  ///校验拼图
  checkCaptcha() async {
    setState(() {
      sliderMoveFinish = true;
    });
    moveX = ((sliderXMoved / _imageWidth) * 100).toInt();
    var data = {"maveX": moveX, "uuid": uuid};
    try {
      var response = await HttpUtil().post(Config.checkSlide, data: data);
      if (response?.data != null && response?.data["data"] != null && response?.data["data"]["code"] == 20000) {
        ToastUtil.showShortSuccessToast("验证成功");
        checkSuccess(response?.data["data"]["data"]);
      } else {
        ToastUtil.showShortErrorToast("验证失败");
        checkFail();
      }
    } catch (e) {
      ToastUtil.showShortErrorToast("验证失败");
      checkFail();
    }
  }

  //校验通过
  void checkSuccess(data) {
    setState(() {
      checkResultAfterDrag = true;
    });
    updateSliderColorIcon();
    Future.delayed(const Duration(milliseconds: 1000)).then((v) {
      if (widget.onSuccess != null) {
        widget.onSuccess(data, uuid);
      }
      Get.back();
    });
  }

  //校验失败
  void checkFail() {
    setState(() {
      checkResultAfterDrag = false;
    });
    updateSliderColorIcon();
    Future.delayed(Duration(milliseconds: 1000)).then((v) {
      //刷新验证码
      loadCaptcha();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _topContainer(),
        Stack(
          alignment: Alignment.topCenter,
          children: [
            Container(
              decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(10),
                      bottomRight: Radius.circular(10))),
              margin: EdgeInsets.fromLTRB(15.w, 0, 15.w, 0),
              height: (_imageHeight + 50.h + 30.h),
            ),
            _middleContainer(),
            Positioned(bottom: 20.h, child: _bottomContainer())
          ],
        ),
      ],
    );
  }

  ///顶部，提示+关闭
  _topContainer() {
    return Container(
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(10),
              topRight: Radius.circular(10))),
      margin: EdgeInsets.fromLTRB(15.w, 0, 15.w, 0),
      padding: EdgeInsets.fromLTRB(10.w, 5.h, 10.w, 5.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Text(
            '请完成安全验证',
            style: TextStyle(
                fontFamily: "M",
                decoration: TextDecoration.none,
                color: Colors.black87,
                fontSize: 14.sp),
          ),
          Row(
            children: [
              GestureDetector(
                onTap: () {
                  loadCaptcha();
                },
                child: Icon(
                  Icons.refresh,
                  size: 30.w,
                  color: Colors.black26,
                ),
              ),
              SizedBox(
                width: 10.w,
              ),
              GestureDetector(
                onTap: () {
                  Get.back();
                },
                child: Icon(
                  Icons.close,
                  size: 30.w,
                  color: Colors.black26,
                ),
              )
            ],
          )
        ],
      ),
    );
  }

  ///显示验证码
  _middleContainer() {
    return responseData != null
        ? Stack(
            children: [
              Image.memory(
                imageBytes,
                fit: BoxFit.fitWidth,
                key: _baseImageKey,
                gaplessPlayback: true,
              ),
              Container(
                margin: EdgeInsets.fromLTRB(
                    sliderXMoved, responseData['ypos'] + 0.0, 0, 0),
                child: Image.memory(
                  slideBytes,
                  fit: BoxFit.fitHeight,
                  key: _slideImageKey,
                  gaplessPlayback: true,
                ),
              )
            ],
          )
        : const Center(
            child: CircularProgressIndicator(),
          );
  }

  ///底部，滑动区域
  _bottomContainer() {
    return responseData != null
        ? Container(
            height: 50.h,
            width: _imageWidth,
            child: Stack(
              alignment: AlignmentDirectional.centerStart,
              children: <Widget>[
                Container(
                  height: _bottomSliderSize,
                  decoration: BoxDecoration(
                    border: Border.all(
                      width: 1,
                      color: const Color(0xffe5e5e5),
                    ),
                    color: const Color(0xfff8f9fb),
                  ),
                ),
                Container(
                  alignment: Alignment.center,
                  child: Text(
                    '向右拖动滑块填充拼图',
                    style: TextStyle(
                        decoration: TextDecoration.none,
                        color: Colors.black26,
                        fontSize: 12.sp),
                  ),
                ),
                Container(
                  width: sliderXMoved,
                  height: _bottomSliderSize - 2,
                  decoration: BoxDecoration(
                    border: Border.all(
                      width: sliderXMoved > 0 ? 1 : 0,
                      color: movedXBorderColor,
                    ),
                    color: const Color(0xfff3fef1),
                  ),
                ),
                GestureDetector(
                  onPanStart: (startDetails) {
                    ///开始
                    sliderStartX = startDetails.localPosition.dx;
                  },
                  onPanUpdate: (updateDetails) {
                    ///更新
                    double _w1 = _baseImageKey.currentContext!.size!.width -
                        _slideImageKey.currentContext!.size!.width;
                    double offset =
                        updateDetails.localPosition.dx - sliderStartX;
                    if (offset < 0) {
                      offset = 0;
                    }
                    if (offset > _w1) {
                      offset = _w1;
                    }
                    setState(() {
                      sliderXMoved = offset;
                    });
                    //滑动过程，改变滑块左边框颜色
                    updateSliderColorIcon();
                  },
                  onPanEnd: (endDetails) {
                    ///结束
                    checkCaptcha();
                  },
                  child: Container(
                    width: _bottomSliderSize,
                    height: _bottomSliderSize,
                    margin: EdgeInsets.only(
                        left: sliderXMoved > 0 ? sliderXMoved : 1),
                    decoration: BoxDecoration(
                      border: const Border(
                        top: BorderSide(
                          width: 1,
                          color: Color(0xffe5e5e5),
                        ),
                        right: BorderSide(
                          width: 1,
                          color: Color(0xffe5e5e5),
                        ),
                        bottom: BorderSide(
                          width: 1,
                          color: Color(0xffe5e5e5),
                        ),
                      ),
                      color: sliderColor,
                    ),
                    child: Icon(
                      sliderIcon,
                      size: 20,
                      color: Colors.black26,
                    ),
                  ),
                )
              ],
            ))
        : Container();
  }

  //重设滑动颜色与图标
  void updateSliderColorIcon() {
    var _sliderColor; //滑块的背景色
    var _sliderIcon; //滑块的图标
    var _movedXBorderColor; //滑块拖动时，左边已滑的区域边框颜色

    //滑块的背景色
    if (sliderMoveFinish) {
      //拖动结束
      _sliderColor = checkResultAfterDrag ? Colors.green : Colors.red;
      _sliderIcon = checkResultAfterDrag ? Icons.check : Icons.close;
      _movedXBorderColor = checkResultAfterDrag ? Colors.green : Colors.red;
    } else {
      //拖动未开始或正在拖动中
      _sliderColor = sliderXMoved > 0 ? const Color(0xff447ab2) : Colors.white;
      _sliderIcon = Icons.arrow_forward;
      _movedXBorderColor = const Color(0xff447ab2);
    }

    sliderColor = _sliderColor;
    sliderIcon = _sliderIcon;
    movedXBorderColor = _movedXBorderColor;
    setState(() {});
  }

  //获取图片原始宽高
  getImageSizeFromBase64() {
    img.Image? image = img.decodeImage(Uint8List.fromList(imageBytes));
    if (image != null) {
      _imageWidth = image.width.toDouble();
      _imageHeight = image.height.toDouble();
    }
  }
}
