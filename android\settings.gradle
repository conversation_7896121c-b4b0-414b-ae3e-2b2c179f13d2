pluginManagement {
    def flutterSdkPath = {
        def properties = new Properties()
        file("local.properties").withInputStream { properties.load(it) }
        def flutterSdkPath = properties.getProperty("flutter.sdk")
        assert flutterSdkPath != null, "flutter.sdk not set in local.properties"
        return flutterSdkPath
    }()

    includeBuild("$flutterSdkPath/packages/flutter_tools/gradle")

    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
//        maven { url "https://mirrors.tencent.com/nexus/repository/maven-public/" }
        // 配置HMS Core SDK的Maven仓地址。
        maven {url 'https://developer.huawei.com/repo/'}
        maven {url 'https://developer.hihonor.com/repo'}
        maven { url "https://storage.googleapis.com/download.flutter.io" }

    }
}
plugins {
    id "dev.flutter.flutter-plugin-loader" version "1.0.0"
//    id "com.android.application" version "8.2.1" apply false
    id "com.android.application" version "7.3.1" apply false
    id "org.jetbrains.kotlin.android" version "1.8.22" apply false
}


dependencyResolutionManagement {
    repositories {
        google()
        mavenCentral()
//        maven { url "https://mirrors.tencent.com/nexus/repository/maven-public/" }
        // 配置HMS Core SDK的Maven仓地址。
        maven {url 'https://developer.huawei.com/repo/'}
        maven {url 'https://developer.hihonor.com/repo'}
        maven { url "https://storage.googleapis.com/download.flutter.io" }
    }
}

include ":app"
include ':ZXUniModule'
