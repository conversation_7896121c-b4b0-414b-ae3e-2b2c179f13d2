import 'package:student_end_flutter/generated/json/base/json_convert_content.dart';
import 'package:student_end_flutter/bean/user_info_bean_entity.dart';

UserInfoBeanEntity $UserInfoBeanEntityFromJson(Map<String, dynamic> json) {
  final UserInfoBeanEntity userInfoBeanEntity = UserInfoBeanEntity();
  final String? userId = jsonConvert.convert<String>(json['userId']);
  if (userId != null) {
    userInfoBeanEntity.userId = userId;
  }
  final String? nickName = jsonConvert.convert<String>(json['nickName']);
  if (nickName != null) {
    userInfoBeanEntity.nickName = nickName;
  }
  final String? mobile = jsonConvert.convert<String>(json['mobile']);
  if (mobile != null) {
    userInfoBeanEntity.mobile = mobile;
  }
  final String? headPortrait = jsonConvert.convert<String>(
      json['headPortrait']);
  if (headPortrait != null) {
    userInfoBeanEntity.headPortrait = headPortrait;
  }
  final int? currencyNumber = jsonConvert.convert<int>(json['currencyNumber']);
  if (currencyNumber != null) {
    userInfoBeanEntity.currencyNumber = currencyNumber;
  }
  final int? gradeLevel = jsonConvert.convert<int>(json['gradeLevel']);
  if (gradeLevel != null) {
    userInfoBeanEntity.gradeLevel = gradeLevel;
  }
  final int? identityType = jsonConvert.convert<int>(json['identityType']);
  if (identityType != null) {
    userInfoBeanEntity.identityType = identityType;
  }
  final String? identityTypeName = jsonConvert.convert<String>(
      json['identityTypeName']);
  if (identityTypeName != null) {
    userInfoBeanEntity.identityTypeName = identityTypeName;
  }
  final int? checkUser = jsonConvert.convert<int>(json['checkUser']);
  if (checkUser != null) {
    userInfoBeanEntity.checkUser = checkUser;
  }
  final String? userCode = jsonConvert.convert<String>(json['userCode']);
  if (userCode != null) {
    userInfoBeanEntity.userCode = userCode;
  }
  final String? bizUserId = jsonConvert.convert<String>(json['bizUserId']);
  if (bizUserId != null) {
    userInfoBeanEntity.bizUserId = bizUserId;
  }
  final int? certStatus = jsonConvert.convert<int>(json['certStatus']);
  if (certStatus != null) {
    userInfoBeanEntity.certStatus = certStatus;
  }
  final int? isBindCard = jsonConvert.convert<int>(json['isBindCard']);
  if (isBindCard != null) {
    userInfoBeanEntity.isBindCard = isBindCard;
  }
  final int? isBindPayPhone = jsonConvert.convert<int>(json['isBindPayPhone']);
  if (isBindPayPhone != null) {
    userInfoBeanEntity.isBindPayPhone = isBindPayPhone;
  }
  final int? signContractStatus = jsonConvert.convert<int>(
      json['signContractStatus']);
  if (signContractStatus != null) {
    userInfoBeanEntity.signContractStatus = signContractStatus;
  }
  final String? userPhone = jsonConvert.convert<String>(json['userPhone']);
  if (userPhone != null) {
    userInfoBeanEntity.userPhone = userPhone;
  }
  final double? totalCommissionAmount = jsonConvert.convert<double>(
      json['totalCommissionAmount']);
  if (totalCommissionAmount != null) {
    userInfoBeanEntity.totalCommissionAmount = totalCommissionAmount;
  }
  final double? freezeAmount = jsonConvert.convert<double>(
      json['freezeAmount']);
  if (freezeAmount != null) {
    userInfoBeanEntity.freezeAmount = freezeAmount;
  }
  final double? withdrawAmount = jsonConvert.convert<double>(
      json['withdrawAmount']);
  if (withdrawAmount != null) {
    userInfoBeanEntity.withdrawAmount = withdrawAmount;
  }
  final String? shareUserName = jsonConvert.convert<String>(
      json['shareUserName']);
  if (shareUserName != null) {
    userInfoBeanEntity.shareUserName = shareUserName;
  }
  final String? shareUserPhone = jsonConvert.convert<String>(
      json['shareUserPhone']);
  if (shareUserPhone != null) {
    userInfoBeanEntity.shareUserPhone = shareUserPhone;
  }
  final String? shareId = jsonConvert.convert<String>(json['shareId']);
  if (shareId != null) {
    userInfoBeanEntity.shareId = shareId;
  }
  final int? expireStatus = jsonConvert.convert<int>(json['expireStatus']);
  if (expireStatus != null) {
    userInfoBeanEntity.expireStatus = expireStatus;
  }
  final String? memberDay = jsonConvert.convert<String>(json['memberDay']);
  if (memberDay != null) {
    userInfoBeanEntity.memberDay = memberDay;
  }
  final String? partnerDay = jsonConvert.convert<String>(json['partnerDay']);
  if (partnerDay != null) {
    userInfoBeanEntity.partnerDay = partnerDay;
  }
  final String? expireTime = jsonConvert.convert<String>(json['expireTime']);
  if (expireTime != null) {
    userInfoBeanEntity.expireTime = expireTime;
  }
  final String? memberStartTime = jsonConvert.convert<String>(
      json['memberStartTime']);
  if (memberStartTime != null) {
    userInfoBeanEntity.memberStartTime = memberStartTime;
  }
  final int? parentMemberType = jsonConvert.convert<int>(
      json['parentMemberType']);
  if (parentMemberType != null) {
    userInfoBeanEntity.parentMemberType = parentMemberType;
  }
  final String? parentMemberStartTime = jsonConvert.convert<String>(
      json['parentMemberStartTime']);
  if (parentMemberStartTime != null) {
    userInfoBeanEntity.parentMemberStartTime = parentMemberStartTime;
  }
  final String? parentMemberEndTime = jsonConvert.convert<String>(
      json['parentMemberEndTime']);
  if (parentMemberEndTime != null) {
    userInfoBeanEntity.parentMemberEndTime = parentMemberEndTime;
  }
  final String? parentMemberDay = jsonConvert.convert<String>(
      json['parentMemberDay']);
  if (parentMemberDay != null) {
    userInfoBeanEntity.parentMemberDay = parentMemberDay;
  }
  final int? parentMemberStatus = jsonConvert.convert<int>(
      json['parentMemberStatus']);
  if (parentMemberStatus != null) {
    userInfoBeanEntity.parentMemberStatus = parentMemberStatus;
  }
  final String? parentShareId = jsonConvert.convert<String>(
      json['parentShareId']);
  if (parentShareId != null) {
    userInfoBeanEntity.parentShareId = parentShareId;
  }
  final String? parentShareUserName = jsonConvert.convert<String>(
      json['parentShareUserName']);
  if (parentShareUserName != null) {
    userInfoBeanEntity.parentShareUserName = parentShareUserName;
  }
  final String? parentShareUserPhone = jsonConvert.convert<String>(
      json['parentShareUserPhone']);
  if (parentShareUserPhone != null) {
    userInfoBeanEntity.parentShareUserPhone = parentShareUserPhone;
  }
  final String? partnerExpireTime = jsonConvert.convert<String>(
      json['partnerExpireTime']);
  if (partnerExpireTime != null) {
    userInfoBeanEntity.partnerExpireTime = partnerExpireTime;
  }
  final String? openId = jsonConvert.convert<String>(json['openId']);
  if (openId != null) {
    userInfoBeanEntity.openId = openId;
  }
  final String? merchantCode = jsonConvert.convert<String>(
      json['merchantCode']);
  if (merchantCode != null) {
    userInfoBeanEntity.merchantCode = merchantCode;
  }
  final String? julebuCode = jsonConvert.convert<String>(json['julebuCode']);
  if (julebuCode != null) {
    userInfoBeanEntity.julebuCode = julebuCode;
  }
  final String? brandCode = jsonConvert.convert<String>(json['brandCode']);
  if (brandCode != null) {
    userInfoBeanEntity.brandCode = brandCode;
  }
  final String? secMerchantPhone = jsonConvert.convert<String>(
      json['secMerchantPhone']);
  if (secMerchantPhone != null) {
    userInfoBeanEntity.secMerchantPhone = secMerchantPhone;
  }
  final String? parentAffiliationPartnerCode = jsonConvert.convert<String>(
      json['parentAffiliationPartnerCode']);
  if (parentAffiliationPartnerCode != null) {
    userInfoBeanEntity.parentAffiliationPartnerCode =
        parentAffiliationPartnerCode;
  }
  final String? parentAffiliationPartnerName = jsonConvert.convert<String>(
      json['parentAffiliationPartnerName']);
  if (parentAffiliationPartnerName != null) {
    userInfoBeanEntity.parentAffiliationPartnerName =
        parentAffiliationPartnerName;
  }
  final String? affiliationPartnerCode = jsonConvert.convert<String>(
      json['affiliationPartnerCode']);
  if (affiliationPartnerCode != null) {
    userInfoBeanEntity.affiliationPartnerCode = affiliationPartnerCode;
  }
  final String? affiliationPartnerName = jsonConvert.convert<String>(
      json['affiliationPartnerName']);
  if (affiliationPartnerName != null) {
    userInfoBeanEntity.affiliationPartnerName = affiliationPartnerName;
  }
  return userInfoBeanEntity;
}

Map<String, dynamic> $UserInfoBeanEntityToJson(UserInfoBeanEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['userId'] = entity.userId;
  data['nickName'] = entity.nickName;
  data['mobile'] = entity.mobile;
  data['headPortrait'] = entity.headPortrait;
  data['currencyNumber'] = entity.currencyNumber;
  data['gradeLevel'] = entity.gradeLevel;
  data['identityType'] = entity.identityType;
  data['identityTypeName'] = entity.identityTypeName;
  data['checkUser'] = entity.checkUser;
  data['userCode'] = entity.userCode;
  data['bizUserId'] = entity.bizUserId;
  data['certStatus'] = entity.certStatus;
  data['isBindCard'] = entity.isBindCard;
  data['isBindPayPhone'] = entity.isBindPayPhone;
  data['signContractStatus'] = entity.signContractStatus;
  data['userPhone'] = entity.userPhone;
  data['totalCommissionAmount'] = entity.totalCommissionAmount;
  data['freezeAmount'] = entity.freezeAmount;
  data['withdrawAmount'] = entity.withdrawAmount;
  data['shareUserName'] = entity.shareUserName;
  data['shareUserPhone'] = entity.shareUserPhone;
  data['shareId'] = entity.shareId;
  data['expireStatus'] = entity.expireStatus;
  data['memberDay'] = entity.memberDay;
  data['partnerDay'] = entity.partnerDay;
  data['expireTime'] = entity.expireTime;
  data['memberStartTime'] = entity.memberStartTime;
  data['parentMemberType'] = entity.parentMemberType;
  data['parentMemberStartTime'] = entity.parentMemberStartTime;
  data['parentMemberEndTime'] = entity.parentMemberEndTime;
  data['parentMemberDay'] = entity.parentMemberDay;
  data['parentMemberStatus'] = entity.parentMemberStatus;
  data['parentShareId'] = entity.parentShareId;
  data['parentShareUserName'] = entity.parentShareUserName;
  data['parentShareUserPhone'] = entity.parentShareUserPhone;
  data['partnerExpireTime'] = entity.partnerExpireTime;
  data['openId'] = entity.openId;
  data['merchantCode'] = entity.merchantCode;
  data['julebuCode'] = entity.julebuCode;
  data['brandCode'] = entity.brandCode;
  data['secMerchantPhone'] = entity.secMerchantPhone;
  data['parentAffiliationPartnerCode'] = entity.parentAffiliationPartnerCode;
  data['parentAffiliationPartnerName'] = entity.parentAffiliationPartnerName;
  data['affiliationPartnerCode'] = entity.affiliationPartnerCode;
  data['affiliationPartnerName'] = entity.affiliationPartnerName;
  return data;
}

extension UserInfoBeanEntityExtension on UserInfoBeanEntity {
  UserInfoBeanEntity copyWith({
    String? userId,
    String? nickName,
    String? mobile,
    String? headPortrait,
    int? currencyNumber,
    int? gradeLevel,
    int? identityType,
    String? identityTypeName,
    int? checkUser,
    String? userCode,
    String? bizUserId,
    int? certStatus,
    int? isBindCard,
    int? isBindPayPhone,
    int? signContractStatus,
    String? userPhone,
    double? totalCommissionAmount,
    double? freezeAmount,
    double? withdrawAmount,
    String? shareUserName,
    String? shareUserPhone,
    String? shareId,
    int? expireStatus,
    String? memberDay,
    String? partnerDay,
    String? expireTime,
    String? memberStartTime,
    int? parentMemberType,
    String? parentMemberStartTime,
    String? parentMemberEndTime,
    String? parentMemberDay,
    int? parentMemberStatus,
    String? parentShareId,
    String? parentShareUserName,
    String? parentShareUserPhone,
    String? partnerExpireTime,
    String? openId,
    String? merchantCode,
    String? julebuCode,
    String? brandCode,
    String? secMerchantPhone,
    String? parentAffiliationPartnerCode,
    String? parentAffiliationPartnerName,
    String? affiliationPartnerCode,
    String? affiliationPartnerName,
  }) {
    return UserInfoBeanEntity()
      ..userId = userId ?? this.userId
      ..nickName = nickName ?? this.nickName
      ..mobile = mobile ?? this.mobile
      ..headPortrait = headPortrait ?? this.headPortrait
      ..currencyNumber = currencyNumber ?? this.currencyNumber
      ..gradeLevel = gradeLevel ?? this.gradeLevel
      ..identityType = identityType ?? this.identityType
      ..identityTypeName = identityTypeName ?? this.identityTypeName
      ..checkUser = checkUser ?? this.checkUser
      ..userCode = userCode ?? this.userCode
      ..bizUserId = bizUserId ?? this.bizUserId
      ..certStatus = certStatus ?? this.certStatus
      ..isBindCard = isBindCard ?? this.isBindCard
      ..isBindPayPhone = isBindPayPhone ?? this.isBindPayPhone
      ..signContractStatus = signContractStatus ?? this.signContractStatus
      ..userPhone = userPhone ?? this.userPhone
      ..totalCommissionAmount = totalCommissionAmount ??
          this.totalCommissionAmount
      ..freezeAmount = freezeAmount ?? this.freezeAmount
      ..withdrawAmount = withdrawAmount ?? this.withdrawAmount
      ..shareUserName = shareUserName ?? this.shareUserName
      ..shareUserPhone = shareUserPhone ?? this.shareUserPhone
      ..shareId = shareId ?? this.shareId
      ..expireStatus = expireStatus ?? this.expireStatus
      ..memberDay = memberDay ?? this.memberDay
      ..partnerDay = partnerDay ?? this.partnerDay
      ..expireTime = expireTime ?? this.expireTime
      ..memberStartTime = memberStartTime ?? this.memberStartTime
      ..parentMemberType = parentMemberType ?? this.parentMemberType
      ..parentMemberStartTime = parentMemberStartTime ??
          this.parentMemberStartTime
      ..parentMemberEndTime = parentMemberEndTime ?? this.parentMemberEndTime
      ..parentMemberDay = parentMemberDay ?? this.parentMemberDay
      ..parentMemberStatus = parentMemberStatus ?? this.parentMemberStatus
      ..parentShareId = parentShareId ?? this.parentShareId
      ..parentShareUserName = parentShareUserName ?? this.parentShareUserName
      ..parentShareUserPhone = parentShareUserPhone ?? this.parentShareUserPhone
      ..partnerExpireTime = partnerExpireTime ?? this.partnerExpireTime
      ..openId = openId ?? this.openId
      ..merchantCode = merchantCode ?? this.merchantCode
      ..julebuCode = julebuCode ?? this.julebuCode
      ..brandCode = brandCode ?? this.brandCode
      ..secMerchantPhone = secMerchantPhone ?? this.secMerchantPhone
      ..parentAffiliationPartnerCode = parentAffiliationPartnerCode ??
          this.parentAffiliationPartnerCode
      ..parentAffiliationPartnerName = parentAffiliationPartnerName ??
          this.parentAffiliationPartnerName
      ..affiliationPartnerCode = affiliationPartnerCode ??
          this.affiliationPartnerCode
      ..affiliationPartnerName = affiliationPartnerName ??
          this.affiliationPartnerName;
  }
}