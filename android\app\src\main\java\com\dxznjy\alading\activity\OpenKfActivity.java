package com.dxznjy.alading.activity;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;

import android.os.Bundle;
import android.view.View;
import android.widget.TextView;
import android.window.OnBackInvokedDispatcher;

import com.blankj.rxbus.RxBus;
import com.dxznjy.alading.DXApplication;
import com.dxznjy.alading.R;
import com.dxznjy.alading.activity.givelessons.MeetingPermissionActivity;
import com.dxznjy.alading.common.Constants;
import com.dxznjy.alading.widget.PermissonTipsPopWindow;
import com.hjq.permissions.OnPermissionCallback;
import com.hjq.permissions.Permission;
import com.hjq.permissions.XXPermissions;
import com.qiyukf.unicorn.api.ConsultSource;
import com.qiyukf.unicorn.api.Unicorn;
import com.tencent.bugly.crashreport.CrashReport;
import com.vondear.rxtool.RxSPTool;

import java.util.List;
import java.util.concurrent.TimeUnit;

import butterknife.BindView;
import butterknife.OnClick;
import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Action;
import io.reactivex.functions.Consumer;

public class OpenKfActivity extends BaseDxActivity implements View.OnClickListener {
    @Override
    public int getLayoutId() {
        return R.layout.activity_open_kf;
    }


    @Override
    public void beforeInflateNofit() {
        super.beforeInflateNofit();
        setNoFits();
    }

    @Override
    public void initViewAndData() {
        inVisableDbTitleBar();
    }


    public void showPopPermissions(){
        if(XXPermissions.isGranted(OpenKfActivity.this, Permission.RECORD_AUDIO)
                &&XXPermissions.isGranted(OpenKfActivity.this, Permission.CAMERA)
                &&XXPermissions.isGranted(OpenKfActivity.this, Permission.MANAGE_EXTERNAL_STORAGE)){
            ConsultSource source = new ConsultSource("在线客服", "在线客服", null);
            Unicorn.openServiceActivity(OpenKfActivity.this, "在线客服", source);
            finish();
        }else{
            checkPermissions();
        }
    }

    /**
     * 请求授权麦克风,相册，存储权限
     */
    public void checkPermissions() {
        XXPermissions.with(this)
                // 申请单个权限
                .permission( Permission.RECORD_AUDIO)
                .permission( Permission.CAMERA)
                .permission( Permission.MANAGE_EXTERNAL_STORAGE)
                .request(new OnPermissionCallback() {
                    @Override
                    public void onGranted(@NonNull List<String> permissions, boolean allGranted) {
                        if (allGranted) {
                            ConsultSource source = new ConsultSource("在线客服", "在线客服", null);
                            Unicorn.openServiceActivity(OpenKfActivity.this, "在线客服", source);
                            finish();
                        }
                    }

                    @Override
                    public void onDenied(@NonNull List<String> permissions, boolean doNotAskAgain) {
                        XXPermissions.startPermissionActivity(OpenKfActivity.this);
                    }
                });
    }


    @Override
    public void onBackPressed() {
        super.onBackPressed();
    }
    @Override
    protected void onResume() {
        super.onResume();
        if(XXPermissions.isGranted(OpenKfActivity.this, Permission.RECORD_AUDIO)
                &&XXPermissions.isGranted(OpenKfActivity.this, Permission.CAMERA)
                &&XXPermissions.isGranted(OpenKfActivity.this, Permission.MANAGE_EXTERNAL_STORAGE)){
            ConsultSource source = new ConsultSource("在线客服", "在线客服", null);
            Unicorn.openServiceActivity(OpenKfActivity.this, "在线客服", source);
            finish();
        }
    }
    @OnClick({R.id.tv_sure,R.id.tv_cancel})
    public void onClick(View view) {
        switch (view.getId()){
            case R.id.tv_sure:
                showPopPermissions();
                break;
            case R.id.tv_cancel:
                finish();
                break;
        }
    }
}