<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:id="@+id/rl_bg"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:layout_marginBottom="10dp"
    android:background="@drawable/bg_radius4_f2faf7"
    android:layout_marginRight="10dp"
    >
    <TextView
        android:id="@+id/tv_title_vertical"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:gravity="center_vertical"
        android:paddingLeft="20dp"
        android:singleLine="true"
        android:textSize="14sp"
        android:ellipsize="end"
        android:textColor="@color/_555555"
        android:text="这是一个课程名称"/>
    <TextView
        android:id="@+id/tv_title_horizontal"
        android:layout_width="match_parent"
        android:layout_height="36dp"
        android:gravity="center"
        android:singleLine="true"
        android:ellipsize="end"
        android:visibility="gone"
        android:textSize="14sp"
        android:textColor="@color/_555555"
        android:text="这是一个课程名称"/>
    <ImageView
        android:id="@+id/img_select"
        android:layout_width="24dp"
        android:layout_height="20dp"
        android:layout_alignParentRight="true"
        android:src="@mipmap/icon_xuanzhong_d"/>
</RelativeLayout>