<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/DXMeeting.h</key>
		<data>
		1uj9B8jxU1aybMEIjdKqj+Xp+Ng=
		</data>
		<key>Headers/TCICMessageHandler.h</key>
		<data>
		KrJCQIxcXPxJfNvolD+en/HTzeg=
		</data>
		<key>Headers/TXAudioProcessor.h</key>
		<data>
		OliIBWqCi1x99AxD3JrIxEboJAc=
		</data>
		<key>Headers/TencentMeetingManager.h</key>
		<data>
		wkRTUQMc5/LEKZIMw1OB18TrERM=
		</data>
		<key>Headers/TencentMeetingSDK.h</key>
		<data>
		e0A5TVk9OLhm4gdhf1UgU+s/bnk=
		</data>
		<key>Info.plist</key>
		<data>
		SKlICIRDqLll9Yrh6gidrbD1krQ=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		8DyzFyltDP+cDYCZXpEvoHsiWSk=
		</data>
		<key>TXLiteAVSDK_Professional.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		pOe2r+M5FAPvJyAnIDB5PuWhT3Y=
		</data>
		<key>tcicimage.bundle/Info.plist</key>
		<data>
		w/nF0/kMN482gQwf4ogcJeew8qw=
		</data>
		<key>tcicimage.bundle/en.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			QOESmw4TB39FL447Wx0Vr2zEJMc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>tcicimage.bundle/ja.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			qfow4JJiKpptzEzNc12aI3sbXtY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>tcicimage.bundle/ko.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			aOitfaiiKswDW2PlEKLM+Wh7de4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>tcicimage.bundle/offline_fd3e59eb82cdbe2753e5a4bb41d6b61781fe6daf.zip</key>
		<data>
		dzhZ3E3whnFyMK53jHpMICq3zi0=
		</data>
		<key>tcicimage.bundle/tiwcache.json</key>
		<data>
		aHZCqLc4Tm2RAZw6NaQJnJFXMNU=
		</data>
		<key>tcicimage.bundle/zh-Hans.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			es4uc6e1O+hsKtGk9ryW5QdCTYI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>tcicimage.bundle/zh-Hant.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			VDWcM4eDkPREBgwuj0E7O8KGsDw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/DXMeeting.h</key>
		<dict>
			<key>hash</key>
			<data>
			1uj9B8jxU1aybMEIjdKqj+Xp+Ng=
			</data>
			<key>hash2</key>
			<data>
			C2Bhrc1PgKGBDpE21ZmblHETySA05f2C8Y+HSlTk7Uc=
			</data>
		</dict>
		<key>Headers/TCICMessageHandler.h</key>
		<dict>
			<key>hash</key>
			<data>
			KrJCQIxcXPxJfNvolD+en/HTzeg=
			</data>
			<key>hash2</key>
			<data>
			LJJTFWgf0nTMGU2qFvgC5J/mbaR9l01PwZfEKnLX45c=
			</data>
		</dict>
		<key>Headers/TXAudioProcessor.h</key>
		<dict>
			<key>hash</key>
			<data>
			OliIBWqCi1x99AxD3JrIxEboJAc=
			</data>
			<key>hash2</key>
			<data>
			BzjF79gcMJBk2Cbj6NIOCzSEgO1GY2sutKrt7ByIALE=
			</data>
		</dict>
		<key>Headers/TencentMeetingManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			wkRTUQMc5/LEKZIMw1OB18TrERM=
			</data>
			<key>hash2</key>
			<data>
			g1k9tijeBTR6pnEtMThZKaKBMwpJTAM9kg4oIU3IZbA=
			</data>
		</dict>
		<key>Headers/TencentMeetingSDK.h</key>
		<dict>
			<key>hash</key>
			<data>
			e0A5TVk9OLhm4gdhf1UgU+s/bnk=
			</data>
			<key>hash2</key>
			<data>
			tFDka8hCZ7wuBzSZzZM0nLP6Qi2dEbaHbTgzbJX+IIQ=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			8DyzFyltDP+cDYCZXpEvoHsiWSk=
			</data>
			<key>hash2</key>
			<data>
			z84ZcQ6lNklHHHFoMaK3ik5dpVTq7LBpDalP79vZ8ic=
			</data>
		</dict>
		<key>TXLiteAVSDK_Professional.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			pOe2r+M5FAPvJyAnIDB5PuWhT3Y=
			</data>
			<key>hash2</key>
			<data>
			5WJ2mc6QRoLNNxUq3odQOR2RiPsqpSpW+9KRM6r7VJY=
			</data>
		</dict>
		<key>tcicimage.bundle/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			w/nF0/kMN482gQwf4ogcJeew8qw=
			</data>
			<key>hash2</key>
			<data>
			5C7MXR7Z3wcAhNJrCpscHu3TMR0O/azvekRumze4Q30=
			</data>
		</dict>
		<key>tcicimage.bundle/en.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			QOESmw4TB39FL447Wx0Vr2zEJMc=
			</data>
			<key>hash2</key>
			<data>
			arpqe5VzXzQ31hac5pRQ9lORQAtapVSnwHrmwzL9cSQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>tcicimage.bundle/ja.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			qfow4JJiKpptzEzNc12aI3sbXtY=
			</data>
			<key>hash2</key>
			<data>
			2xjwjMwpCYjUfQZUWC/TxryCt2Y6Mjq2rvlnkyUWuAQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>tcicimage.bundle/ko.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			aOitfaiiKswDW2PlEKLM+Wh7de4=
			</data>
			<key>hash2</key>
			<data>
			AtNUklrgXnvE8XSYeBoxiwXMtKDU2Pc/RVDY6RKea5M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>tcicimage.bundle/offline_fd3e59eb82cdbe2753e5a4bb41d6b61781fe6daf.zip</key>
		<dict>
			<key>hash</key>
			<data>
			dzhZ3E3whnFyMK53jHpMICq3zi0=
			</data>
			<key>hash2</key>
			<data>
			/fnZgtC/1pxyT9ceWdGxMQVkQKWV6Zfpo3HjGw0j1rE=
			</data>
		</dict>
		<key>tcicimage.bundle/tiwcache.json</key>
		<dict>
			<key>hash</key>
			<data>
			aHZCqLc4Tm2RAZw6NaQJnJFXMNU=
			</data>
			<key>hash2</key>
			<data>
			J5FxFevdgV4NBP8k7vE9p1uCM73YJDoDh+MWhVHNwLk=
			</data>
		</dict>
		<key>tcicimage.bundle/zh-Hans.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			es4uc6e1O+hsKtGk9ryW5QdCTYI=
			</data>
			<key>hash2</key>
			<data>
			2RGCShMxTC6oQlRe0o9NPAvoJ0Q2qmGGSDmxycr1Kuc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>tcicimage.bundle/zh-Hant.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			VDWcM4eDkPREBgwuj0E7O8KGsDw=
			</data>
			<key>hash2</key>
			<data>
			EFeMEPpVzvbC7PD8BGt8v0yv4324GJOeSkEJiGRVsuc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
