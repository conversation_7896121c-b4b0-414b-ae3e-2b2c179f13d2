// Copyright (c) 2022 NetEase, Inc. All rights reserved.
// Use of this source code is governed by a MIT license that can be
// found in the LICENSE file.

group 'com.netease.meeting.plugin.meeting_plugin'
version '1.0'

buildscript {
    ext.kotlin_version = '1.6.10'
    repositories {
        google()
        maven { url "https://maven.aliyun.com/nexus/content/groups/public/" }
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:7.1.3'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

repositories {
    google()
    mavenCentral()
}

apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'

android {
    namespace 'com.example.netease_meeting_kit'
    compileSdkVersion 31

    defaultConfig {
        minSdkVersion 21
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8
    }

    lintOptions {
        disable 'InvalidPackage'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/audioManagerKit'
    }
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation(project(':netease_roomkit'))

//    def rtcVersion = project.ext.rtcVersion
//    println "use nertc version: $rtcVersion"
    [
            'base',
            'beauty',
            'facedetect',
            'segment',
            'aidenoise',
            'aihowling',
            'nenn',
            'videodenoise',
            'faceenhance',
            'superresolution',
            'audio3d',
    ]
            .forEach { name ->
                implementation("com.netease.yunxin:nertc-$name:5.6.33")
//                {
//            changing = rtcVersion.endsWith('SNAPSHOT')
//        }
            }
    def lifecycle_version = "2.5.1"
    implementation "androidx.lifecycle:lifecycle-process:$lifecycle_version"
}
