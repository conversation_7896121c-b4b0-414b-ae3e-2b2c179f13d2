import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_state_manager/src/simple/get_view.dart';
// import 'package:netease_meeting_kit/meeting_ui.dart';
import '../../components/input_have_icon.dart';
import '../../res/colors.dart';
import 'controllers/login_controller.dart';

class LoginPage extends GetView<LoginController> {
  @override
  Widget build(BuildContext context) {
    Get.put(LoginController());
    return Scaffold(
        backgroundColor: AppColors.white,
        body: Obx(() => GestureDetector(
          onTap: (){
            controller.setFocus();
          },
          child: Column(
            children: [
              getTitleUI(),
              Expanded(child: showUIFromShowType()),
            ],
          ),
        )));
  }

  getTitleUI() {
    return Container(
      height: 100.h,
      width: double.infinity,
      child:
      Stack(
        alignment: Alignment.center,
        children: [
          Positioned(
            left: 10.w,
              child: GestureDetector(
                onTap: (){
                  if(controller.showType.value == 0){
                    Get.back();
                  }else{
                    controller.cleanData();
                    controller.showType.value = 0;
                  }
                },
                child:  Icon(Icons.chevron_left,size: 35,),)),
          Text(getTitleName(),
              style: TextStyle(
                  color: AppColors.f333333, fontSize: 18.sp, fontFamily: "M"))
        ],
      ),
    );
  }

  getTitleName() {
    if (controller.showType.value == 1) {
      return "手机号验证码登录";
    } else if (controller.showType.value == 2) {
      return "手机号密码登录";
    }
    return "登录";
  }

  showUIFromShowType() {
    if (controller.showType.value == 1) {
      return _smsUI();
    } else if (controller.showType.value == 2) {
      return _pwdUI();
    }
    return _loginBtnUI();
  }

  ///密码登录
  _pwdUI() {
    return SingleChildScrollView(
      child: Column(
        children: [
          Image(
            image: AssetImage('assets/login/login_center.png'),
            fit: BoxFit.cover,
            width: 312.w,
            height: 308.h,
          ),
          Column(
            children: [
              InputHaveIcon().inputLeftHaveIcon(
                  height: 38.h,
                  top: 30.h,
                  left: 30.w,
                  right: 30.w,
                  radius: 40.0,
                  hintText: '请输入手机号',
                  radiusColor: AppColors.ececea,
                  keyboardType: TextInputType.phone,
                  controller: controller.phoneController,
                  fillColor: AppColors.c8c8c8,
                  cursorColor: AppColors.c8c8c8,
                  hintColor: AppColors.c8c8c8,
                  labelColor: AppColors.f333333,
                  fontSize: 14.sp,
                  img: "assets/login/icon_login_user.png",
                  maxLength: 11,
                  imgH: 18.h,
                  imgW: 18.w),
              InputHaveIcon().inputLeftHaveIcon(
                  height: 38.h,
                  top: 20.h,
                  left: 30.w,
                  right: 30.w,
                  radius: 40.0,
                  hintText: '请输入密码',
                  radiusColor: AppColors.ececea,
                  keyboardType: TextInputType.visiblePassword,
                  controller: controller.pwdController,
                  fillColor: AppColors.c8c8c8,
                  cursorColor: AppColors.c8c8c8,
                  hintColor: AppColors.c8c8c8,
                  labelColor: AppColors.f333333,
                  fontSize: 14.sp,
                  img: "assets/login/icon_login_ped.png",
                  imgH: 18.h,
                  imgW: 18.w,
                  isPwd: true),
              SizedBox(
                height: 10.h,
              ),
              _checkUI(),
              GestureDetector(
                child: Container(
                  width: 280.w,
                  height: 34.h,
                  alignment: AlignmentDirectional.center,
                  margin: EdgeInsets.only(top: 20.h),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                        colors: [AppColors.f88cfba, AppColors.f1d755c],
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter),
                    borderRadius: BorderRadius.circular(23),
                  ),
                  child: Text(
                    '登录',
                    style: TextStyle(
                        color: AppColors.white,
                        fontSize: 14.sp,
                        fontFamily: 'R'),
                  ),
                ),
                onTap: () {
                  controller.pwdLoginFun();
                },
              ),
            ],
          )
        ],
      ),
    );
  }

  ///验证码登录
  _smsUI() {
    return SingleChildScrollView(
        child: Column(
      children: [
        Image(
          image: AssetImage('assets/login/login_center.png'),
          fit: BoxFit.cover,
          width: 312.w,
          height: 308.h,
        ),
        Column(
          children: [
            InputHaveIcon().inputLeftHaveIcon(
                height: 38.h,
                top: 30.h,
                left: 30.w,
                right: 30.w,
                radius: 40.0,
                hintText: '请输入手机号',
                radiusColor: AppColors.ececea,
                keyboardType: TextInputType.phone,
                controller: controller.phoneController,
                fillColor: AppColors.c8c8c8,
                cursorColor: AppColors.c8c8c8,
                hintColor: AppColors.c8c8c8,
                labelColor: AppColors.f333333,
                fontSize: 14.sp,
                img: "assets/login/icon_login_user.png",
                imgH: 18.h,
                imgW: 18.w),
            Container(
              height: 38.h,
              margin: EdgeInsets.only(top: 20.h, left: 30.w, right: 30.w),
              padding: EdgeInsets.only(left: 15.w, right: 10.w),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(40),
                  border: Border.all(color: AppColors.ececea, width: 1),
                  color: Colors.white),
              child: Row(
                children: [
                  Image(
                    // image: AssetImage("assets/login/icon_login_code.png"),
                    image: AssetImage("assets/icon_null.png"),
                    height: 18.h,
                    width: 18.w,
                  ),
                  Container(
                    margin: EdgeInsets.only(left: 10.w, right: 10.w),
                    height: double.infinity,
                    width: 150.w,
                    child: TextField(
                      focusNode: controller.focusNode,
                      controller: controller.codeController,
                      cursorColor: AppColors.c8c8c8,
                      keyboardType: TextInputType.number,
                      onSubmitted: (String text) {},
                      decoration: InputDecoration(
                        hintText: "请输入验证码",
                        border: InputBorder.none,
                        fillColor: AppColors.c8c8c8,
                        hintStyle: TextStyle(
                            fontFamily: "R",
                            color: AppColors.c8c8c8,
                            fontSize: 14.sp),
                        labelStyle: TextStyle(
                          fontFamily: "R",
                          color: AppColors.f333333,
                          fontSize: 14.sp,
                        ),
                      ),
                    ),
                  ),
                  Container(
                    height: 20.h,
                    width: 1,
                    decoration: BoxDecoration(
                      color: AppColors.c8c8c8,
                    ),
                  ),
                  Expanded(
                      child: GestureDetector(
                    onTap: () {
                      controller.checkSlideOption();
                    },
                    child: Container(
                      alignment: Alignment.center,
                      child: Text(
                        controller.timerText.value,
                        style: TextStyle(
                            color: AppColors.ea6531,
                            fontSize: 12.sp,
                            fontFamily: "R"),
                      ),
                    ),
                  ))
                ],
              ),
            ),
            SizedBox(
              height: 10.h,
            ),
            _checkUI(),
            GestureDetector(
              child: Container(
                width: 280.w,
                height: 34.h,
                alignment: AlignmentDirectional.center,
                margin: EdgeInsets.only(top: 20.h),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                      colors: [AppColors.f88cfba, AppColors.f1d755c],
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter),
                  borderRadius: BorderRadius.circular(23),
                ),
                child: Text(
                  '登录',
                  style: TextStyle(
                      color: AppColors.white, fontSize: 14.sp, fontFamily: 'R'),
                ),
              ),
              onTap: () {
                controller.smsLoginFun();
              },
            ),
          ],
        )
      ],
    ));
  }

  _checkUI() {
    return GestureDetector(
      onTap: () {
        controller.changeCheck();
      },
      child: Container(
        alignment: Alignment.center,
        height: 20.h,
        width: double.infinity,
        child: Row(mainAxisAlignment: MainAxisAlignment.center, children: [
          Image(
              width: 15.w,
              height: 15.w,
              image: AssetImage(controller.isChecked.value
                  ? "assets/login/icon_chose.png"
                  : "assets/login/icon_normal.png")),
          SizedBox(
            width: 5.w,
          ),
          RichText(
            text: TextSpan(
              style: TextStyle(color: AppColors.c8c8c8, fontSize: 13.sp),
              children: <TextSpan>[
                TextSpan(text: '我已阅读并同意'),
                TextSpan(
                  text: '《用户服务协议》',
                  recognizer: TapGestureRecognizer()..onTap = () {
                    controller.clickUserAgree();
                  },
                ),
                TextSpan(text: '、'),
                TextSpan(
                  text: '《隐私政策》',
                  recognizer: TapGestureRecognizer()..onTap = () {
                    controller.clickPrivacy();
                  },
                ),
              ],
            ),
          )
        ]),
      ),
    );
  }

  _loginBtnUI() {
    return Column(
      mainAxisSize: MainAxisSize.max,
      children: [
        Image(
          image: AssetImage('assets/login/login_center.png'),
          fit: BoxFit.cover,
          width: 312.w,
          height: 308.h,
        ),
        Expanded(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              //手机验证码登录
              GestureDetector(
                child: Container(
                  width: 240.w,
                  height: 32.h,
                  alignment: AlignmentDirectional.center,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                        colors: [AppColors.f08c34, AppColors.ea6531],
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter),
                    borderRadius: BorderRadius.circular(23),
                  ),
                  child: Text(
                    '手机号验证码登录',
                    style: TextStyle(
                        color: AppColors.white,
                        fontSize: 14.sp,
                        fontFamily: 'R'),
                  ),
                ),
                onTap: () {
                  controller.cleanData();
                  controller.showType.value = 1;
                },
              ),
              //用户名密码登录
              GestureDetector(
                child: Container(
                  width: 240.w,
                  height: 32.h,
                  alignment: AlignmentDirectional.center,
                  margin: EdgeInsets.only(top: 15.h),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                        colors: [AppColors.f78d2aa, AppColors.f368e5c],
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter),
                    borderRadius: BorderRadius.circular(23),
                  ),
                  child: Text(
                    '用户名密码登录',
                    style: TextStyle(
                        color: AppColors.white,
                        fontSize: 14.sp,
                        fontFamily: 'R'),
                  ),
                ),
                onTap: () {
                  controller.cleanData();
                  controller.showType.value = 2;
                },
              ),
              SizedBox(
                height: 20.h,
              ),
              GestureDetector(
                child: Container(
                  alignment: AlignmentDirectional.center,
                  child: Text(
                    '点击修改密码',
                    style: TextStyle(
                        color: AppColors.c8c8c8,
                        fontSize: 12.sp,
                        fontFamily: 'R'),
                  ),
                ),
                onTap: () {
                  controller.gotoChangePwd();
                },
              ),
            ],
          ),
        )
      ],
    );
  }
}
