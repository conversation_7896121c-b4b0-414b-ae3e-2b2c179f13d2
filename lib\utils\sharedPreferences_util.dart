import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';

class SharedPreferencesUtil {
  static final String tokenKey = "TOKEN_KEY";
  static final String userId = "TOKEN_USERID";
  static final String userCode = "USER_CODE";
  static final String token = "TOKEN";
  static final String expiresAt = "EXPIRES_AT";
  static final String mobile = "MOBILE";
  static final String baseUrl = "";
  static final String parentMemberType = "parentMemberType";

  static final String agreeTips = "AGREE_TIPS"; //同意权限说明温馨提示

  ///网易会议
  static final String userUuid = "YX_USER_UUID";
  static final String yxUserToken = "YX_TOKEN";

  static final String popNoShowData = "POP_NOSHOW_DATA"; //缓存至本地的弹窗数据
  static final String wgtVersion = 'WGT_VERSION';

  static final String wgtAppId = 'WGT_APPID';
  /// 保存数据
  static saveData<T>(String key, T value) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    switch (T) {
      case String:
        prefs.setString(key, value as String);
        break;
      case int:
        prefs.setInt(key, value as int);
        break;
      case bool:
        prefs.setBool(key, value as bool);
        break;
      case double:
        prefs.setDouble(key, value as double);
        break;
    }
  }

  /// 读取数据
  static Future<T> getData<T>(String key) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();

    var res;
    switch (T) {
      case String:
        if (prefs.getString(key) == null) {
          res = "";
        } else {
          res = prefs.getString(key) as T;
        }
        break;
      case int:
        if (prefs.getInt(key) == null) {
          res = -1;
        } else {
          res = prefs.getInt(key) as T;
        }
        break;
      case bool:
        res = prefs.getBool(key) as T;
        break;
      case double:
        res = prefs.getDouble(key) as T;
        break;
    }
    return res;
  }

  /// 删除指定键的数据
  static Future<void> deleteData(String key) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.remove(key);
  }

  /// 清理数据（webView,图片,uni）
  static Future<void> clearAll() async {
    // 清理inappwebview/webview/uni缓存
    try {
      await WebStorageManager.instance().deleteAllData();
    } catch (e) {}
    // 清理图片缓存
    try {
      await DefaultCacheManager().emptyCache();
    } catch (e) {}
    try {
      await CachedNetworkImage.evictFromCache('');
    } catch (e) {}
  }
}
