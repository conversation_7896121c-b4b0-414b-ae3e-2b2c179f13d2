
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:student_end_flutter/utils/event_bus_utils.dart';

import '../bean/student_info_bean_entity.dart';
import '../common/CommonUtil.dart';
class LearnSelectStudentDialog extends StatefulWidget {
  List<StudentInfoBeanEntity> studentList;
  var onSuccess;

  LearnSelectStudentDialog(this.studentList, this.onSuccess);

  @override
  _LearnSelectStudentDialogState createState() =>
      _LearnSelectStudentDialogState();
}

class _LearnSelectStudentDialogState extends State<LearnSelectStudentDialog> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    double screenHeight = MediaQuery.of(context).size.height;
    return SingleChildScrollView(
      child: Column(
        children: [
          Container(
            alignment: Alignment.bottomCenter,
            height: screenHeight/3.5,
            child: Image(image: AssetImage("assets/dialog_icon.png")),
          ),
          Container(
            width: double.infinity,
            margin: EdgeInsets.only(left: 20.w, right: 20.w),
            padding: EdgeInsets.all(10.w),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(15)),
                color: Colors.white),
            child: Column(
              children: [
                SizedBox(
                  height: 40.h,
                  width:double.infinity,
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      Text(
                        '选择学员',
                        style: TextStyle(
                            decoration: TextDecoration.none,
                            fontSize: 16.sp,
                            fontFamily: "M",
                            color: Colors.black),
                      ),
                      Positioned(
                        top: 0,
                        right: 0,
                        child: GestureDetector(
                          onTap: () {

                            Navigator.pop(context);
                          },
                          child: Image.asset(
                            'assets/zx/close.png',
                            width: 26.w,
                            height: 26.w,
                          ),
                        ),
                      )
                    ],
                  ),
                ),
                SizedBox(
                  height: 10.w,
                ),
                SizedBox(
                  height: 220.h,
                  child: GridView.builder(
                    shrinkWrap: true,
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 1,
                        mainAxisSpacing: 10.w,
                        crossAxisSpacing: 10.w,
                        childAspectRatio: 6),
                    itemCount: widget.studentList.length,
                    itemBuilder: (BuildContext context, int index) {
                      return _selectStudentItemBuilder(context, index);
                    },
                  ),
                )
              ],
            ),
          )
        ],
      ),
    );
  }

  _selectStudentItemBuilder(BuildContext context, int index) {
    return GestureDetector(
      onTap: () {
        EventBusUtils.sendMsg("refreshUserInfo");
        Navigator.pop(context);
        if (widget.onSuccess != null) {
          widget.onSuccess(index);
        }
      },
      child: Container(
        height: 40.w,
        decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(
              width: 1,
              color: HexColor('EFEFEF'),
            ),
            borderRadius: BorderRadius.all(Radius.circular(25.w))),
        alignment: Alignment.center,
        child: Text(
          widget.studentList[index].studentName,
            style: TextStyle(
                decoration: TextDecoration.none,
                fontSize: 15.sp,
                fontFamily: "R",
                color: HexColor('555555'))
        ),
      ),
    );
  }
}
