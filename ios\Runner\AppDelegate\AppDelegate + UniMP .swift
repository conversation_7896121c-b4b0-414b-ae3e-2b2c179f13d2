//
//  AppDelegate + UniMP .swift
//  StuMetApp
//
//  Created by 许江泉 on 2024/11/25.
//
//#pragma mark - App 生命周期方法

extension AppDelegate {
    
    override func applicationDidBecomeActive(_ application: UIApplication) {
        super.applicationDidBecomeActive(application)
        DCUniMPSDKEngine.applicationDidBecomeActive(application)
    }
    
    override func applicationWillResignActive(_ application: UIApplication) {
        super.applicationWillResignActive(application)
        DCUniMPSDKEngine.applicationWillResignActive(application)
    }
    
    override func applicationDidEnterBackground(_ application: UIApplication) {
        super.applicationDidEnterBackground(application)
        DCUniMPSDKEngine.applicationDidEnterBackground(application)
    }
    
    override func applicationWillEnterForeground(_ application: UIApplication) {
        super.applicationWillEnterForeground(application)
        DCUniMPSDKEngine.applicationWillEnterForeground(application)
    }
    
    override func applicationWillTerminate(_ application: UIApplication) {
        super.applicationWillTerminate(application)
        DCUniMPSDKEngine.destory()
    }

   
}
