import 'package:student_end_flutter/generated/json/base/json_convert_content.dart';
import 'package:student_end_flutter/bean/course_type_bean_entity.dart';

CourseTypeBeanEntity $CourseTypeBeanEntityFromJson(Map<String, dynamic> json) {
  final CourseTypeBeanEntity courseTypeBeanEntity = CourseTypeBeanEntity();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    courseTypeBeanEntity.id = id;
  }
  final String? enName = jsonConvert.convert<String>(json['enName']);
  if (enName != null) {
    courseTypeBeanEntity.enName = enName;
  }
  return courseTypeBeanEntity;
}

Map<String, dynamic> $CourseTypeBeanEntityToJson(CourseTypeBeanEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['enName'] = entity.enName;
  return data;
}

extension CourseTypeBeanEntityExtension on CourseTypeBeanEntity {
  CourseTypeBeanEntity copyWith({
    String? id,
    String? enName,
  }) {
    return CourseTypeBeanEntity()
      ..id = id ?? this.id
      ..enName = enName ?? this.enName;
  }
}