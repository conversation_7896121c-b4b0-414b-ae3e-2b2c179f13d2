// Copyright (c) 2022 NetEase, Inc. All rights reserved.
// Use of this source code is governed by a MIT license that can be
// found in the LICENSE file.

part of meeting_assets;

class NEMeetingIconFont {
  static const String _family = 'iconfont';

  NEMeetingIconFont._();
  static const IconData icon_fail = IconData(0xe602,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_loading = IconData(0xe601,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_other_file = IconData(0xe61a,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_download = IconData(0xe611,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_unfold = IconData(0xe60e,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_file = IconData(0xe60f,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_image = IconData(0xe610,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_yx_tv_voice_offx = IconData(0xe761,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_yx_tv_voice_onx = IconData(0xe763,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_yx_tv_video_onx = IconData(0xe760,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_yx_tv_video_offx = IconData(0xe762,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_yx_tv_invitex = IconData(0xe764,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_yx_tv_attendeex = IconData(0xe765,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_yx_tv_duankaix = IconData(0xe768,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_switch_camera = IconData(0xe72d,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_yx_pc_successx = IconData(0xe76f,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_yx_returnx = IconData(0xe788,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_yx_input_clearx = IconData(0xe789,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_yx_allowx = IconData(0xe7a5,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_yx_tv_layout_bx = IconData(0xe7b2,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_yx_tv_layout_ax = IconData(0xe7b3,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_yx_tv_sharescreen = IconData(0xe7b9,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_share_system_audio = IconData(0xe744,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_yx_tv_more1x = IconData(0xe7fd,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_earpiece = IconData(0xe728,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_speaker = IconData(0xe72f,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_headset = IconData(0xe72e,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_certification1x = IconData(0xe817,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_copy1x = IconData(0xe818,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_search2_line1x = IconData(0xe81a,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_raisehands = IconData(0xe81b,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_beauty1x = IconData(0xe81c,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_triangle_down = IconData(0xe7f4,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_chat = IconData(0xe828,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_live = IconData(0xe848,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_whiteboard = IconData(0xe607,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_sip = IconData(0xe68b,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_h323 = IconData(0xe74f,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_virtual_background = IconData(0xe6b9,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_net_state = IconData(0xe6ef,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_device_audio = IconData(0xe6db,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_cloud_record_start = IconData(0xe6fb,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_cloud_record_stop = IconData(0xe6fd,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_cloud_recording = IconData(0xe6fc,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_security = IconData(0xe700,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_notify = IconData(0xe6c9,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_delete = IconData(0xe6ca,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_meeting_info_time = IconData(0xe73d,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_meeting_info_title = IconData(0xe615,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_history_message = IconData(0xe701,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_nickname = IconData(0xe705,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_bluetooth = IconData(0xe729,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_check_line = IconData(0xe7d3,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_disconnect = IconData(0xe6cc,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_reconnect = IconData(0xe6cd,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_asterisk = IconData(0xe6cb,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_pin = IconData(0xe6cf,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_focus = IconData(0xe6e3,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_all_members_32 = IconData(0xe6d4,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_all_members_16 = IconData(0xe6d6,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_chat_setting = IconData(0xe6d7,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_chat_muted = IconData(0xe6d3,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_arrow_down = IconData(0xe6d8,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_meeting_owner = IconData(0xe6ed,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_hand_up = IconData(0xe76c,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_narrow = IconData(0xe74a,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_system_phone = IconData(0xe6ea,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_sip_phone = IconData(0xe6e9,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_call_out = IconData(0xe6eb,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_system_call_in = IconData(0xe6fe,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_interpretation = IconData(0xe702,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_volume_up = IconData(0xe7c0,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_volume_down = IconData(0xe7d4,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_switch = IconData(0xe703,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_setting = IconData(0xe76d,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_camera = IconData(0xe727,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_settings = IconData(0xe726,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_settings_stroke = IconData(0xe712,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_audio = IconData(0xe725,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_members = IconData(0xe72a,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_copy = IconData(0xe71d,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_forbidden = IconData(0xe72c,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_add_picture = IconData(0xe72b,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_captions = IconData(0xe71b,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_pop_window_back = IconData(0xe60d,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_transcription = IconData(0xe721,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_editing = IconData(0xe723,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_unchecked = IconData(0xe730,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_checked = IconData(0xe731,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_call = IconData(0xe735,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_copy2 = IconData(0xe614,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_feedback = IconData(0xe737,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_info = IconData(0xe743,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_emoji_delete = IconData(0xe738,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_image_open = IconData(0xe73f,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_folder_open = IconData(0xe73e,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_emoji_open = IconData(0xe740,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_keyboard_open = IconData(0xe741,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_emoji_open_2 = IconData(0xe742,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_contacts = IconData(0xe734,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_call_batch = IconData(0xe733,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
  static const IconData icon_annotation = IconData(0xe85b,
      fontPackage: meetingAssetsPackageName, fontFamily: _family);
}
