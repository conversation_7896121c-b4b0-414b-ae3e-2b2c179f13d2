//
//  Constant.swift
//  常量文件
//
//  Created by smile on 2022/7/5.
//

import Foundation

let APPID = "__UNI__D7DA9FC"
let zxengine = "zxEngine"
let sharePath = "https://zx-share.dxznjy.com/#/home?&path="
let schemeStr = "zhengxuanurlscheme"
let schemeUrlPath = "path="
let openURL = "zhengxuanUrlscheme://"

/// 宽度
let SEGGMEN_WIDTH: CGFloat = 120

let SEGGMENSCREE_WIDTH: CGFloat = 160

let SEGGMEN_HEGIHT: CGFloat = 36

let INDICATOR_HEGIHT: CGFloat = 4

let INDICATOR_ROWCOUNT: CGFloat = 3.00

let SEGGMEN_SCREE: CGFloat = 30

let SEGGMENH_HEGIHT: CGFloat = 54

let INDICATOR_WIDTH: CGFloat = 48

let FOOTER_HEGIHT: CGFloat = 100

let MEDDLE_RADIUS: CGFloat = 4

let PLAYBUTOON: CGFloat = 26

let ICONIMAGE_HEIGHT: CGFloat = 128

let ICONIMAGE_MEBACKGROUD: CGFloat = 202

let YXERROR_CODE_SUCCESS : Int = 0

let ERROR_CODE_SUCCESS : Int = 20000

let YUNXINERROR_CODE_SUCCESS : Int = 3406

let YUNXINERRORR_CODE_SUCCESS : Int = 3423

let SCALE: CGFloat = 0.3

let TIME: CGFloat = 0.25

/// 间歇
let PADDING_MIN: CGFloat = 0.5
let PADDING_SMALL: CGFloat = 27
let PADDING_MEDDLE: CGFloat = 10
let PADDING_MEDLE: CGFloat = 2
let PADDING_OUTER: CGFloat = 14
let PADDING_LARGE2: CGFloat = 61

/// 控件间距
let statusCellMargin: CGFloat = 12
/// 头像的大小
let statusCellIconWidth: CGFloat = 35
/// 默认的图片大小
let statusPictureItemWidth: CGFloat = 90
/// 默认的图片间距
let statusPictureItemMargin: CGFloat = 8
/// 每行最大图片的数量
let statusCellPictureMaxCount: CGFloat = 3

let ITME_HEIGHT: CGFloat = 86

let PADDING_LARGE3: CGFloat = 16

let CELLHEIGHT_MIN: CGFloat = 148

let CELLHEIGHT_MAX: CGFloat = 132

let CELLHEIGHT: CGFloat = 68
