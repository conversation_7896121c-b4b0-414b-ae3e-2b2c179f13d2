<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center_horizontal"
    android:background="@color/white"
    tools:context=".activity.givelessons.MeetingPermissionActivity">
    <RelativeLayout
        android:id="@+id/rl_titlebar"
        android:layout_width="match_parent"
        android:layout_marginTop="20dp"
        android:layout_height="wrap_content"
        >
        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="36dp"
            >
            <ImageView
                android:id="@+id/img_back"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:padding="10dp"
                android:src="@mipmap/ic_back_black"
                />
        </RelativeLayout>
    </RelativeLayout>
    <ImageView
        android:layout_width="130dp"
        android:layout_height="130dp"
        android:layout_gravity="center"
        android:layout_marginTop="80dp"
        android:src="@mipmap/pic_jiance"/>
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textStyle="bold"
        android:textColor="@color/_333333"
        android:layout_marginTop="30dp"
        android:textSize="16sp"
        android:text="权限检测"/>
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/_666666"
        android:layout_marginTop="14dp"
        android:textSize="14sp"
        android:layout_marginBottom="36dp"
        android:text="请先检查设备权限是否已开启"/>
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="40dp"
        android:layout_marginRight="20dp">
        <ImageView
            android:id="@+id/img_voice"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:visibility="gone"
            android:layout_centerVertical="true"
            android:src="@mipmap/icon_wancheng"/>
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:textColor="@color/_555555"
            android:layout_toRightOf="@+id/img_voice"
            android:layout_marginLeft="4dp"
            android:textSize="14sp"
            android:text="扬声器检测"/>
        <TextView
            android:id="@+id/tv_voice_check"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingLeft="14dp"
            android:paddingRight="40dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:textColor="@color/_428a6f"
            android:textSize="@dimen/sp_14"
            android:text="检测"/>
    </RelativeLayout>
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginRight="20dp"
        android:layout_marginLeft="40dp">
        <View
            android:id="@+id/view_step1"
            android:layout_width="16dp"
            android:layout_height="20dp"
            android:visibility="invisible"
            android:background="@drawable/ver_dashed_line"/>
    </RelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="40dp"
        android:layout_marginRight="20dp"
        >
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:textColor="@color/_555555"
            android:textSize="14sp"
            android:layout_toRightOf="@+id/img_mic"
            android:layout_marginLeft="4dp"
            android:text="麦克风检测"/>
        <ImageView
            android:id="@+id/img_mic"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:visibility="gone"

            android:layout_centerVertical="true"
            android:src="@mipmap/icon_wancheng"/>
        <TextView
            android:id="@+id/tv_mic_check"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:paddingRight="40dp"
            android:textColor="@color/_428a6f"
            android:textSize="@dimen/sp_14"
            android:paddingLeft="14dp"
            android:text="检测"/>
    </RelativeLayout>
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginRight="20dp"
        android:layout_marginLeft="40dp">
        <View
            android:id="@+id/view_step2"
            android:layout_width="16dp"
            android:layout_height="20dp"
            android:visibility="invisible"
            android:background="@drawable/ver_dashed_line"/>
    </RelativeLayout>
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="40dp"
        android:layout_marginRight="20dp">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:textColor="@color/_555555"
            android:layout_toRightOf="@+id/img_camear"
            android:layout_marginLeft="4dp"
            android:textSize="14sp"
            android:text="摄像头检测"/>
        <ImageView
            android:id="@+id/img_camear"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:visibility="gone"
            android:layout_centerVertical="true"
            android:src="@mipmap/icon_wancheng"/>
        <TextView
            android:id="@+id/tv_camear_check"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:paddingRight="40dp"
            android:paddingLeft="14dp"
            android:textColor="@color/_428a6f"
            android:textSize="@dimen/sp_14"
            android:text="检测"/>
    </RelativeLayout>
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginRight="20dp"
        android:layout_marginLeft="40dp">
        <View
            android:id="@+id/view_step3"
            android:layout_width="16dp"
            android:layout_height="20dp"
            android:visibility="invisible"
            android:background="@drawable/ver_dashed_line"/>
    </RelativeLayout>
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="40dp"
        android:layout_marginRight="20dp">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:textColor="@color/_555555"
            android:layout_toRightOf="@+id/img_sdcard"
            android:layout_marginLeft="4dp"
            android:textSize="14sp"
            android:text="文件存储检测"/>
        <ImageView
            android:id="@+id/img_sdcard"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:visibility="gone"
            android:layout_centerVertical="true"
            android:src="@mipmap/icon_wancheng"/>
        <TextView
            android:id="@+id/tv_img_sdcard_check"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:paddingRight="40dp"
            android:paddingLeft="14dp"
            android:textColor="@color/_428a6f"
            android:textSize="@dimen/sp_14"
            android:text="检测"/>
    </RelativeLayout>

    <TextView
        android:id="@+id/tv_joinmeeting"
        android:layout_width="150dp"
        android:layout_height="40dp"
        android:textSize="14sp"
        android:layout_centerVertical="true"
        android:layout_alignParentRight="true"
        android:textColor="@color/white"
        android:gravity="center"
        android:layout_marginTop="100dp"
        android:background="@drawable/bg_radius20_428a6f"
        android:singleLine="true"
        android:text="加入会议"/>
</LinearLayout>