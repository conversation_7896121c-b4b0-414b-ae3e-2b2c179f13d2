package com.dxznjy.alading.fragment;


import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.dxznjy.alading.R;
import com.dxznjy.alading.adapter.homepage.StudyMaterialAdapter;
import com.dxznjy.alading.api.ApiProvider;
import com.dxznjy.alading.common.Constants;
import com.dxznjy.alading.entity.SelectListPopInfo;
import com.dxznjy.alading.http.BaseSubscriber;
import com.dxznjy.alading.response.UserInfoResponse;
import com.dxznjy.alading.widget.PermissonTipsPopWindow;
import com.dxznjy.alading.widget.SelectListPopWindow;
import com.hjq.permissions.OnPermissionCallback;
import com.hjq.permissions.Permission;
import com.hjq.permissions.XXPermissions;
import com.vondear.rxtool.RxSPTool;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;

public class FragmentHomePage extends BaseFragment implements View.OnClickListener {
    @BindView(R.id.recyclerView)
    RecyclerView recyclerView;
    @BindView(R.id.tv_select_type)
    TextView tv_select_type;
    @BindView(R.id.img_class_bg)
    ImageView img_class_bg;
    @BindView(R.id.ll_class_content)
    LinearLayout ll_class_content;
    @BindView(R.id.ll_noclass)
    LinearLayout ll_noclass;

    @BindView(R.id.tv_fristname)
    TextView tv_fristname;
    @BindView(R.id.tv_name)
    TextView tv_name;
    @BindView(R.id.tv_gradename)
    TextView tv_gradename;

    StudyMaterialAdapter studyMaterialAdapter;//学习资料
    List<String> data=new ArrayList<>();//学习资料数据
    PermissonTipsPopWindow permissonTipsPopWindow;

    @Override
    public void initViewAndData() {
        data.add("1");
        data.add("1");
        data.add("1");
        recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
        studyMaterialAdapter = new StudyMaterialAdapter(getActivity(),data);
        recyclerView.setAdapter(studyMaterialAdapter);
        studyMaterialAdapter.setEmptyView(R.layout.empty_data,recyclerView);
        studyMaterialAdapter.setOnItemChildClickListener(new BaseQuickAdapter.OnItemChildClickListener() {
            @Override
            public void onItemChildClick(BaseQuickAdapter adapter, View view, int position) {
                switch (view.getId()){
                    case R.id.tv_download:
                        if(!XXPermissions.isGranted(getActivity(), Permission.MANAGE_EXTERNAL_STORAGE)) {
                            permissonTipsPopWindow = new PermissonTipsPopWindow(getActivity(), recyclerView, new PermissonTipsPopWindow.OnCheckPermissonListener() {
                                @Override
                                public void onSuccess() {
                                    if(XXPermissions.isGranted(getActivity(), Permission.MANAGE_EXTERNAL_STORAGE)){//已授权文件管理权限
                                        showPopCourseDownLoad();
                                    }else{
                                        checkPermissionsStorage();
                                    }
                                }

                                @Override
                                public void onCancle() {

                                }
                            });
                            permissonTipsPopWindow.showQuanXianTips("我们需要您的文件存储权限，将用于下载学习资料，是否同意?");
                        }else{
                            showPopCourseDownLoad();
                        }
                        break;
                }
            }
        });
        getUserInfo();
    }


    public void checkPermissionsStorage() {
        XXPermissions.with(this)
                // 申请单个权限
                .permission( Permission.WRITE_EXTERNAL_STORAGE,Permission.READ_EXTERNAL_STORAGE)
                .request(new OnPermissionCallback() {
                    @Override
                    public void onGranted(@NonNull List<String> permissions, boolean allGranted) {
                        if (allGranted) {
                            showPopCourseDownLoad();
                        }
                    }

                    @Override
                    public void onDenied(@NonNull List<String> permissions, boolean doNotAskAgain) {
                    }
                });
    }



    @Override
    public int setContent() {
        return R.layout.fragment_homepage;
    }

    @OnClick({R.id.ll_select_type,R.id.tv_golessons})
    public void onClick(View view) {
        switch (view.getId()){
            case R.id.ll_select_type:
                showPopSelectCourseType();
                break;
            case R.id.tv_golessons:
                ll_noclass.setVisibility(View.VISIBLE);
                ll_class_content.setVisibility(View.INVISIBLE);
                img_class_bg.setVisibility(View.INVISIBLE);
                break;
        }
    }

    List<SelectListPopInfo> selectListPopInfos = new ArrayList<>();
    SelectListPopInfo selectListPopInfo = new SelectListPopInfo();
    public void showPopSelectCourseType(){
        selectListPopInfos.clear();
        selectListPopInfo=new SelectListPopInfo();
        selectListPopInfo.setTitle("全部");
        selectListPopInfo.setType(1);
        selectListPopInfos.add(selectListPopInfo);
        selectListPopInfo=new SelectListPopInfo();
        selectListPopInfo.setTitle("鼎英语");
        selectListPopInfo.setType(1);
        selectListPopInfo=new SelectListPopInfo();
        selectListPopInfo.setTitle("鼎英语");
        selectListPopInfo.setType(1);
        selectListPopInfos.add(selectListPopInfo);
        selectListPopInfo=new SelectListPopInfo();
        selectListPopInfo.setTitle("鼎英语");
        selectListPopInfo.setType(1);
        selectListPopInfos.add(selectListPopInfo);
        selectListPopInfo=new SelectListPopInfo();
        selectListPopInfo.setTitle("鼎英语");
        selectListPopInfo.setType(1);
        selectListPopInfos.add(selectListPopInfo);
        selectListPopInfo=new SelectListPopInfo();
        selectListPopInfo.setTitle("鼎英语");
        selectListPopInfo.setType(1);
        selectListPopInfos.add(selectListPopInfo);
        SelectListPopWindow selectListPopWindow=new SelectListPopWindow(getActivity(), "课程类型","确定",
                recyclerView, selectListPopInfos, Constants.HORIZONTAL, 3, false,new SelectListPopWindow.OnSelectListener() {
            @Override
            public void onSelect(List<SelectListPopInfo> data) {
                for (int i=0;i<data.size();i++){
                    if (data.get(i).isCheck()){
                        changeType(data.get(i).getTitle());
                        break;
                    }
                }
            }
        });
        selectListPopWindow.showPop();
    }

    public void changeType(String typeStr){
        tv_select_type.setText(typeStr);
        data.clear();
        if (typeStr.equals("全部")){
            data.add("1");
            data.add("1");
            data.add("1");
        }
        studyMaterialAdapter.notifyDataSetChanged();
    }


    public void showPopCourseDownLoad(){
        selectListPopInfos.clear();
        SelectListPopInfo selectListPopInfo=new SelectListPopInfo();
        selectListPopInfo.setType(1);
        selectListPopInfo.setCheck(false);
        selectListPopInfo.setTitle("可下载");
        selectListPopInfos.add(selectListPopInfo);
        selectListPopInfo=new SelectListPopInfo();
        selectListPopInfo.setType(1);
        selectListPopInfo.setTitle("可下载");
        selectListPopInfo.setCheck(true);
        selectListPopInfos.add(selectListPopInfo);
        selectListPopInfo=new SelectListPopInfo();
        selectListPopInfo.setTitle("不可下载状态");
        selectListPopInfo.setType(0);
        selectListPopInfos.add(selectListPopInfo);
        selectListPopInfo=new SelectListPopInfo();
        selectListPopInfo.setType(1);
        selectListPopInfo.setTitle("可下载");
        selectListPopInfo.setCheck(true);
        selectListPopInfos.add(selectListPopInfo);
        SelectListPopWindow selectListPopWindow=new SelectListPopWindow(getActivity(), "下载课程","下载",
                recyclerView, selectListPopInfos, Constants.VERTICAL, 0, true,new SelectListPopWindow.OnSelectListener() {
            @Override
            public void onSelect(List<SelectListPopInfo> data) {

            }
        });
        selectListPopWindow.showPop();
    }



    private void getUserInfo() {
        request(ApiProvider.getInstance(getActivity()).DFService.getUserInfo(), new BaseSubscriber<UserInfoResponse>(getActivity()){
            @Override
            public void onCompleted() {
                super.onCompleted();
            }

            @Override
            public void onError(Throwable e) {
                super.onError(e);
            }

            @Override
            public void onNext(UserInfoResponse response) {
                super.onNext(response);
                if (response.getData()!=null){
                    tv_fristname.setText(response.getData().getFirstName());
                    tv_gradename.setText(response.getData().getGradeName());
                    tv_name.setText(response.getData().getName());
                    RxSPTool.putString(getActivity(),Constants.STUDENT_NAME,response.getData().getName());
                }
            }
        });
    }
}
