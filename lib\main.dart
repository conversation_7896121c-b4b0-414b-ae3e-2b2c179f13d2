import 'dart:convert';
import 'dart:io';
import 'package:bot_toast/bot_toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get_navigation/src/root/get_material_app.dart';
import 'package:student_end_flutter/common/ne_user_option.dart';
import 'package:student_end_flutter/common/user_option.dart';
import 'package:student_end_flutter/pages/base/base_page.dart';
import 'package:student_end_flutter/dialog/dialog_sdk_description.dart';
import 'package:student_end_flutter/pages/splash/splash_page.dart';
import 'package:student_end_flutter/utils/config_service.dart';
import 'package:student_end_flutter/utils/im_service.dart';
import 'package:student_end_flutter/utils/ne_metting_utils.dart';
import 'package:student_end_flutter/utils/sharedPreferences_util.dart';
import 'package:tencent_cloud_chat_push/tencent_cloud_chat_push.dart';
import 'package:tx_im/im_home_page.dart';
import 'package:tx_im/im_module_service.dart';
import 'package:tx_im/im_service_interface.dart';

import 'package:tx_im/provider/local_setting.dart';
import 'package:tx_im/provider/login_user_Info.dart';
import 'package:tx_im/provider/theme.dart';
import 'package:tx_im/provider/user_guide_provider.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:provider/provider.dart';
import 'package:tencent_calls_uikit/tencent_calls_uikit.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:tx_im/src/routes.dart';
import 'package:tx_im/utils/init_step.dart';
import 'package:student_end_flutter/utils/app_version_util.dart';
import 'package:student_end_flutter/common/sensors_analytics.dart';

void main() async {
  await ScreenUtil.ensureScreenSize();

  // 启动时强制竖屏 start
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
  ]);
  WidgetsFlutterBinding.ensureInitialized();
  //启动时强制竖屏 end
  LocaleSettings.useDeviceLocale();
  runApp(
    // runAutoApp(
    TranslationProvider(
      child: MultiProvider(
        providers: [
          ChangeNotifierProvider(create: (_) => LoginUserInfo()),
          ChangeNotifierProvider(create: (_) => DefaultThemeData()),
          // ChangeNotifierProvider(create: (_) => CustomStickerPackageData()),
          ChangeNotifierProvider(
            create: (_) => LocalSetting(),
          ),
          ChangeNotifierProvider(create: (_) => UserGuideProvider()),
          // ChangeNotifierProvider(create: (_) => Chat(selectedConversation: null,)),
        ],
        child: MyApp(),
      ),
    ),
  );

  // /// 分享
  // NativeBridge.registerHandler(NativeMethods.navigateToFlutterPage, (params) async {
  //   Map<String, dynamic> data = params;
  //   print("--navigateToFlutterPage--main---${data["path"]}");
  //   ShareData.shareData = data["path"];
  // });
  // runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});
  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    agreeTips();
    ScreenUtil.init(context);
    initRouteListener(context);
    return GetMaterialApp(
      title: '鼎校甄选',
      debugShowCheckedModeBanner: false,
      locale: TranslationProvider.of(context).flutterLocale,
      supportedLocales: LocaleSettings.supportedLocales,
      localizationsDelegates: GlobalMaterialLocalizations.delegates,
      navigatorObservers: [
        BotToastNavigatorObserver(),
        TUICallKit.navigatorObserver
      ],
      builder: BotToastInit(),
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.white),
        useMaterial3: true,
        bottomNavigationBarTheme: const BottomNavigationBarThemeData(),
        splashFactory: NoSplash.splashFactory, // 全局移除水波纹效果
        highlightColor: Colors.transparent, // 全局移除点击高亮效果
      ),
      home: SplashPage(),
      navigatorKey: MyApp.navigatorKey,
      routes: {
        '/homePage': (context) => IMModuleHomePage(),
      },
      // navigatorObservers: [TUICallKit.navigatorObserver],
      //   bottomNavigationBarTheme: const BottomNavigationBarThemeData(
      //   ),
      //   splashFactory: NoSplash.splashFactory, // 全局移除水波纹效果
      //   highlightColor: Colors.transparent, // 全局移除点击高亮效果
      // ),
    );
  }

  initRouteListener(BuildContext context) {
    final routes = Routes();
    routes.addListener(() {
      final pageType = routes.pageType;
      if (pageType == "homePage") {
        InitStep.directToHomePage(context);
      }
    });
  }

 static agreeTips() async {
    int agreeTips = await SharedPreferencesUtil.getData<int>(
        SharedPreferencesUtil.agreeTips);
    print(agreeTips);
      if (agreeTips == 1) {
        UserOption.hasAgreedToPrivacyPolicy = true;
        // 初始化IMServiceSingleton
        IMServiceSingleton().service = IMService();
        // 用户同意SDK描述后，调用通知权限检查
        // SplashController.isOpenNotificationSettings(agreeTips);
      //同意过sdk权限弹窗
      UserOption.initToken();
      IMModuleService().TIMInit();
      initSensorsAnalytics();
      await AppVersionUtil.getVersion();
      await AppVersionUtil.getDistinctId();
      // ENUserOption.initToken();
      // NEMeetingUtil.commonInit();
    }
  }
}
