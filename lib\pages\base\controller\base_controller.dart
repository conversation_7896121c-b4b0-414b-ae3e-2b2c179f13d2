import 'dart:async';
import 'dart:convert';
import 'dart:io';
// import 'package:dio/dio.dart';
import 'package:crypto/crypto.dart';
import 'package:dio/dio.dart' as dio_pkg;
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:hex/hex.dart';
import 'package:intl/intl.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:student_end_flutter/common/Config.dart';
import 'package:student_end_flutter/common/user_option.dart';
import 'package:student_end_flutter/components/toast_utils.dart';
import 'package:student_end_flutter/dialog/dialog_show_share_info.dart';
import 'package:student_end_flutter/pages/base/base_model.dart';
import 'package:student_end_flutter/pages/base/check_notification_permissons_page.dart';
import 'package:student_end_flutter/pages/learn/learn_page.dart';
import 'package:student_end_flutter/pages/zx/zx_page.dart';
import 'package:student_end_flutter/utils/httpRequest.dart';
import 'package:tencent_cloud_chat_sdk/manager/v2_tim_manager.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_value_callback.dart';
import 'package:tx_im/dx_utils/IMHttpRequest.dart';
import 'package:tx_im/dx_utils/http_config.dart';
import 'package:tx_im/im_home_page.dart';
import 'package:tx_im/src/addGroup/add_group.dart';
import 'package:tx_im/im_module_service.dart';
import 'package:tx_im/user.dart';
import '../../../utils/android_ios_plugin.dart';
import '../../../common/pop_option.dart';
import '../../../utils/app_version_util.dart';
import '../../../utils/event_bus_utils.dart';
import '../../../utils/native_channel_utils.dart';
import '../../../utils/share_utils.dart';
import '../../main/home_page.dart';
import '../../mine/mine_page.dart';
import '../../../common/sensors_analytics_option.dart';
import '../../main/controller/home_controller.dart';

class BaseController extends GetxController
    with GetSingleTickerProviderStateMixin, WidgetsBindingObserver {
  // 选中的页面索引
  var currentIndex = 0.obs;
  var refeereIndex = 0.obs;
// 添加页面标识常量
  static const String SCREEN_HOME = 'HomePage';
  static const String SCREEN_ZX = 'ZxPage';
  static const String SCREEN_LEARN = 'LearnPage';
  static const String SCREEN_MESSAGE = 'IMModuleHomePage';
  static const String SCREEN_MINE = 'MinePage';
  Map<int, DateTime> _pageEnterTimeMap = {}; // 记录每个页面的进入时间
  // 页面列表
  final List<Widget> pages = [
    HomePage(),
    LearnPage(),
    IMModuleHomePage(),
    MinePage(),
  ];

  final List pagesRes = [
    {
      "normal": "assets/tab/icon_shouyehui.png",
      "chose": "assets/tab/icon_shouye.png",
      "lbl": "首页",
      "page": "HomePage"
    },
    {
      "normal": "assets/tab/icon_xuexihui.png",
      "chose": "assets/tab/icon_xuexi.png",
      "lbl": "学习",
      "page": "LearnPage"
    },
    {
      "normal": "assets/tab/icon_xiaoxihui.png",
      "chose": "assets/tab/icon_xiaoxi.png",
      "lbl": "消息",
      "page": "IMModuleHomePage"
    },
    {
      "normal": "assets/tab/icon_wodehui.png",
      "chose": "assets/tab/icon_wode.png",
      "lbl": "我的",
      "page": "MinePage"
    },
  ];
  TabController? tabController;
  RxBool isLogin = false.obs;

  RxInt unReadCount = 0.obs;

  String sharePath = "";
  // bool imInitSuccess = false;
  Timer? _timer;
  int _timerNum = 0;

  ///是否展示消息红点
  RxBool showMessageRedDot = false.obs;

  @override
  void onInit() {
    // 启动完毕后解锁旋转方向
    Future.delayed(Duration.zero, () {
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);
    });

    WidgetsBinding.instance.addObserver(this);

    UserOption.getUserInfoRequest(true); // 在getUserInfoRequest 中处理过角色切换了

    // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   // checkNotificationPermissions(); // 在帧回调后执行
    // });
    getMessageNotice();
    initToHomeTab();
    initWeChatUnInstall();
    isLogin.value = UserOption.token.isNotEmpty ? true : false;
    tabController = TabController(
      initialIndex: currentIndex.value,
      length: pages.length,
      vsync: this,
    );
    tabController?.addListener(_onTabChanged);
    // initIM();
    TrackingUtils.trackEvent('AppLaunch', {
      'banner_id': '',
      'common_fields': '',
      'avitcity_id': '',
      'goods_id': '',
      'url_path': ''
    });
    TrackingUtils.trackEvent('AppShow', {
      'banner_id': '',
      'common_fields': '',
      'avitcity_id': '',
      'goods_id': '',
      'url_path': ''
    });
    EventBusUtils.eventBus.on<EventUtils>().listen((event) {
      if (event.message == "pageSwitch") {
        if (event.preIndex != -1 &&
            _pageEnterTimeMap.containsKey(event.preIndex)) {
          final enterTime = _pageEnterTimeMap[event.preIndex]!;
          final leaveTime = DateTime.now();
          final duration = leaveTime.difference(enterTime).inMilliseconds;
          _trackPageLeave(event.preIndex, event.curIndex, duration);
        }
      }
    });
    super.onInit();
  }

  void _trackPageLeave(int? index, int? curIndex, int stayDuration) {
    String screenName = '';
    String event_name = 'AppPageLeave';
    if (index == -1 || index == curIndex) {
      return;
    }
    // IndexExposure
    switch (index) {
      case 0:
        screenName = SCREEN_HOME;
        break;
      case 1:
        screenName = SCREEN_LEARN;
        break;
      case 2:
        screenName = SCREEN_MESSAGE;
        break;
      case 3:
        screenName = SCREEN_MINE;
        break;
    }
    TrackingUtils.trackPageView(
      event_name,
      {
        'url_path': screenName,
        'refeere_path': '',
        'avitcity_id': '',
        'goods_id': '',
        'banner_id': '',
        'common_fields': '',
        'event_duration': stayDuration.toString()
      },
    );
  }

  // 手动触发页面浏览事件
  void _trackPageView(int index, int refeereIndex) {
    String title = '';
    String screenName = '';
    String event_name = 'AppViewScreen';
    if (index == refeereIndex) {
      return;
    }
    if (index == -1) {
      event_name = 'IndexExposure';
      index = 0;
    }
    _pageEnterTimeMap[index] = DateTime.now();
    // IndexExposure
    switch (index) {
      case 0:
        title = '首页';
        screenName = SCREEN_HOME;
        break;
      case 1:
        title = '学习';
        screenName = SCREEN_LEARN;
        break;
      case 2:
        title = '消息';
        screenName = SCREEN_MESSAGE;
        break;
      case 3:
        title = '我的';
        screenName = SCREEN_MINE;
        break;
    }
    TrackingUtils.trackPageView(
      event_name,
      {
        'url_path': screenName,
        'refeere_path': '',
        'avitcity_id': '',
        'goods_id': '',
        'banner_id': '',
        'common_fields': ''
      },
    );
  }

  @override
  onReady() {
    listenShareEvent();
    getMessageNotice(); // 初始化时获取消息状态
    // 首次进入时触发首页埋点
    _trackPageView(-1, 0);
  }

  /// 获取信息数据--判断红点
  Future<void> getMessageNotice() async {
    if (UserOption.token.isEmpty) {
      return;
    }
    try {
      String mobile = '';
      if (UserOption.mobile == '') {
        await UserOption.getUserInfoRequest(false);
      }
      mobile = UserOption.mobile ?? '';
      final response = await HttpUtil()
          .get('${Config.getMessageNotice}?status=0&mobile=$mobile');
      showMessageRedDot.value = (response ?? 0) > 0;
    } catch (e) {
      showMessageRedDot.value = false;
      print('获取消息通知失败: $e');
    }
  }

  /// 优惠卷去使用监听  家长权益更多跳转甄选
  initToHomeTab() {
    NativeBridge.registerHandler(NativeMethods.switchToHomeTab, (params) async {
      changeTab(params);
    });

    NativeBridge.registerHandler(NativeMethods.nativeToFlutter, (params) async {
      print("收到原生传递的参数: $params");
      Map map = jsonDecode(params);
      if (map.containsKey('event')) {
        switch (map['event']) {
          case 'audioRecord':
            {
              if (map.containsKey('params')) {
                final String filePath = map['params']['filePath'];
                final String id = map['params']['id'];

                try {
                  //读取PCM文件
                  final file = File(filePath);
                  final Uint8List pcmBytes = await file.readAsBytes();

                  // 腾讯云 ASR 配置
                  const String secretId =
                      'AKIDzFQQDrMituvlOwgOyNgzBB2jEWbUQI0X';
                  const String secretKey = 'W74vZ3NWSHxRky56xqbEh074pOos5RrS';
                  const String region = 'ap-guangzhou';
                  const String engineModelType = '16k_zh';
                  const String action = 'SentenceRecognition';
                  const String version = '2019-06-14';
                  const String service = 'asr';
                  const String algorithm = 'TC3-HMAC-SHA256';

                  // 获取当前 UTC 时间
                  final now = DateTime.now().toUtc();
                  final timestamp = (now.millisecondsSinceEpoch ~/ 1000);
                  final date = DateFormat('yyyy-MM-dd').format(now);

                  // 创建请求体
                  final requestBody = {
                    'ProjectId': 0,
                    'SubServiceType': 2,
                    'EngSerViceType': engineModelType,
                    'SourceType': 1,
                    'VoiceFormat': 'pcm',
                    'Data': base64Encode(pcmBytes),
                    'DataLen': pcmBytes.length,
                  };

                  final requestBodyJson = jsonEncode(requestBody);

                  // ************* 步骤 1：拼接规范请求串 *************
                  final canonicalHeaders =
                      'content-type:application/json; charset=utf-8\n'
                      'host:asr.tencentcloudapi.com\n'
                      'x-tc-action:${action.toLowerCase()}\n';
                  final signedHeaders = 'content-type;host;x-tc-action';

                  final canonicalRequest = '''
POST
/

$canonicalHeaders
$signedHeaders
${sha256.convert(utf8.encode(requestBodyJson)).toString()}''';

                  print('Canonical Request:');
                  print(canonicalRequest);
                  print('---');

                  // ************* 步骤 2：拼接待签名字符串 *************
                  final credentialScope = '$date/$service/tc3_request';
                  final hashedCanonicalRequest =
                      sha256.convert(utf8.encode(canonicalRequest)).toString();

                  final stringToSign = '''
$algorithm
$timestamp
$credentialScope
$hashedCanonicalRequest''';

                  print('String to Sign:');
                  print(stringToSign);
                  print('---');

                  // ************* 步骤 3：计算签名 *************
                  List<int> sign(List<int> key, String msg) {
                    return Hmac(sha256, key).convert(utf8.encode(msg)).bytes;
                  }

                  final tc3Key = utf8.encode('TC3$secretKey');
                  final secretDate = sign(tc3Key, date);
                  final secretService = sign(secretDate, service);
                  final secretSigning = sign(secretService, 'tc3_request');

                  final signatureBytes = Hmac(sha256, secretSigning)
                      .convert(utf8.encode(stringToSign))
                      .bytes;
                  final signature = HEX.encode(signatureBytes);

                  print('Signature: $signature');
                  print('---');

                  // ************* 步骤 4：拼接 Authorization *************
                  final authorization =
                      '$algorithm Credential=$secretId/$credentialScope, SignedHeaders=$signedHeaders, Signature=$signature';

                  print('Authorization: $authorization');
                  print('---');

                  final dio = Dio();

                  final options = Options(
                    headers: {
                      'Authorization': authorization,
                      'Content-Type': 'application/json; charset=utf-8',
                      'Host': 'asr.tencentcloudapi.com',
                      'X-TC-Action': action,
                      'X-TC-Version': version,
                      'X-TC-Timestamp': timestamp.toString(),
                      'X-TC-Region': 'ap-guangzhou',
                    },
                  );

                  final response = await dio.post(
                    'https://asr.tencentcloudapi.com/',
                    data: requestBodyJson,
                    options: options,
                  );
                  print('Response status: ${response.statusCode}');
                  print('Response data: ${response.data}');
                  if (response.data.containsKey('Response')) {
                    if (response.data['Response'].containsKey('Result')) {
                      String text = response.data['Response']['Result'];
                      final Map<String, dynamic> params = {
                        'id': id,
                        'transcribeContent': text,
                      };
                      print('识别结果: $text');
                      await HttpUtil()
                          .postBaseBean(Config.transcribe, data: params);
                    }
                  }
                } catch (e) {
                  print('发生错误: $e');
                }

//                 // //腾讯云ASR配置
//                 // const String secretId = 'AKIDzFQQDrMituvlOwgOyNgzBB2jEWbUQI0X';
//                 // const String secretKey = 'W74vZ3NWSHxRky56xqbEh074pOos5RrS';
//                 // const String region = 'ap-guangzhou';
//                 // const String engineModelType = '16k_zh'; // 16k中文普通话
//                 //
//                 // //生成签名
//                 // final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
//                 // final auth = TencentCloudAuth(
//                 //   secretId: secretId,
//                 //   secretKey: secretKey,
//                 //   service: 'asr',
//                 //   region: region,
//                 // );
//
//                 // 需要设置环境变量 TENCENTCLOUD_SECRET_ID，值为示例的 AKIDz8krbsJ5yKBZQpn74WFkmLPx3*******
//                 // final secretId = Platform.environment['TENCENTCLOUD_SECRET_ID'];
//                 final secretId = 'AKIDzFQQDrMituvlOwgOyNgzBB2jEWbUQI0X';
//                 // 需要设置环境变量 TENCENTCLOUD_SECRET_KEY，值为示例的 Gu5t9xGARNpq86cd98joQYCN3*******
//                 // final secretKey = Platform.environment['TENCENTCLOUD_SECRET_KEY'];
//                 final secretKey = 'W74vZ3NWSHxRky56xqbEh074pOos5RrS';
//
//                 final service = 'cvm';
//                 final host = 'cvm.tencentcloudapi.com';
//                 final endpoint = 'https://$host';
//                 final region = 'ap-guangzhou';
//                 final action = 'DescribeInstances';
//                 final version = '2017-03-12';
//                 final algorithm = 'TC3-HMAC-SHA256';
//                 // final timestamp = 1551113065;
//                 // 获取当前时间戳
//                 final timestamp = (DateTime.now().millisecondsSinceEpoch / 1000).toInt();
//                 final date = DateFormat('yyyy-MM-dd').format(DateTime.fromMillisecondsSinceEpoch(timestamp * 1000, isUtc: true));
//
//                 // ************* 步骤 1：拼接规范请求串 *************
//                 final httpRequestMethod = 'POST';
//                 final canonicalUri = '/';
//                 final canonicalQuerystring = '';
//                 final ct = 'application/json; charset=utf-8';
//                 final payload = '{"Limit": 1, "Filters": [{"Values": ["未命名"], "Name": "instance-name"}]}';
//                 final canonicalHeaders = 'content-type:$ct\nhost:$host\nx-tc-action:${action.toLowerCase()}\n';
//                 final signedHeaders = 'content-type;host;x-tc-action';
//                 final hashedRequestPayload = sha256.convert(utf8.encode(payload));
//                 final canonicalRequest = '''
// $httpRequestMethod
// $canonicalUri
// $canonicalQuerystring
// $canonicalHeaders
// $signedHeaders
// $hashedRequestPayload''';
//                 print(canonicalRequest);
//
//                 // ************* 步骤 2：拼接待签名字符串 *************
//                 final credentialScope = '$date/$service/tc3_request';
//                 final hashedCanonicalRequest = sha256.convert(utf8.encode(canonicalRequest));
//                 final stringToSign = '''
// $algorithm
// $timestamp
// $credentialScope
// $hashedCanonicalRequest''';
//                 print(stringToSign);
//
//                 // ************* 步骤 3：计算签名 *************
//                 List<int> sign(List<int> key, String msg) {
//                   final hmacSha256 = Hmac(sha256, key);
//                   return hmacSha256.convert(utf8.encode(msg)).bytes;
//                 }
//
//                 final secretDate = sign(utf8.encode('TC3$secretKey'), date);
//                 final secretService = sign(secretDate, service);
//                 final secretSigning = sign(secretService, 'tc3_request');
//                 final signature = Hmac(sha256, secretSigning).convert(utf8.encode(stringToSign)).toString();
//                 print(signature);
//
//                 // ************* 步骤 4：拼接 Authorization *************
//                 final authorization = '$algorithm Credential=$secretId/$credentialScope, SignedHeaders=$signedHeaders, Signature=$signature';
//                 print(authorization);
//
//
//                 // final headers = authorization.getHeaders(now);
//                 // // final headers = auth.getHeaders(now);
//                 //
//                 // //请求体
//                 // final requestBody = {
//                 //   'ProjectId': 0,
//                 //   'SubServiceType': 2, // 2表示一句话识别
//                 //   'EngSerViceType': engineModelType,
//                 //   'SourceType': 1, // 1表示语音数据直接上传
//                 //   'VoiceFormat': 'pcm',
//                 //   'Data': base64Encode(pcmBytes),
//                 //   'DataLen': pcmBytes.length,
//                 // };
//
//                 final dio = Dio();
//                 // 设置请求配置
//                 final options = Options(
//                   method: 'POST',
//                   headers: {
//                     'Authorization': authorization,
//                     'Content-Type': 'application/json',
//                     'Host': 'cvm.tencentcloudapi.com',
//                     'X-TC-Action': 'DescribeInstances',
//                     'X-TC-Version': '2017-03-12',
//                     'X-TC-Timestamp': timestamp,
//                     'X-TC-Region': 'ap-guangzhou',
//                   },
//                 );
//
//                 // 请求数据
//                 final data = {
//                   "Offset": 0,
//                   "Limit": 10
//                 };
//
//                 try {
//                   final response = await dio.post(
//                     'https://cvm.tencentcloudapi.com/',
//                     data: data,
//                     options: options,
//                   );
//
//                   print('Response status: ${response.statusCode}');
//                   print('Response data: ${response.data}');
//                 } catch (e) {
//                   print('Error: $e');
//                 }
//
//                 // //发送
//                 // final dio = dio_pkg.Dio();
//                 // final response = await dio.post(
//                 //   'https://asr.tencentcloudapi.com/',
//                 //   data: requestBody,
//                 //   options: dio_pkg.Options(
//                 //     headers: headers,
//                 //     contentType: 'application/json',
//                 //   ),
//                 // );
//                 //
//                 // //响应
//                 // String text = '';
//                 // if (response.data['Response']?['Result'] != null) {
//                 //   text = response.data['Response']['Result'];
//                 // }
//                 //
//                 // //结果

//
//               } catch (e) {
//                 print('识别失败: $e');
//                 if (e is dio_pkg.DioException) {
//                   print('错误响应: ${e.response?.data}');
//                 }
//               }
              }
              break;
            }
        }
      }
    });
  }

  groupShareFunc() {
    ToastUtil.showLoadingDialog(text: "正在为您搜索");
    _timer = Timer.periodic(Duration(seconds: 1), (timer) {
      _timerNum++;
      // if((imInitSuccess && selfInfoModel.loginInfo!=null && selfInfoModel.loginInfo!.userID != null) || _timerNum >= 3){
      //   ToastUtil.closeLoadingDialog();
      //   Get.to(AddGroupPage(data:sharePath));
      //   sharePath = "";
      //   _timer?.cancel();
      //   return;
      // }
      if (TimUser.currentUserId.isNotEmpty || _timerNum >= 3) {
        //token 保持一致
        IMHttpUtil.heads = HttpUtil.heads;
        IMHttpUtil().options?.headers = HttpUtil.heads;
        ToastUtil.closeLoadingDialog();
        Get.to(AddGroupPage(data: sharePath));
        sharePath = "";
        _timer?.cancel();
        return;
      }
    });
  }

  /// 分享方法监听
  listenShareEvent() async {
    var params = await AndroidIosPlugin.findShareData();
    String path = '';
    if (Platform.isIOS) {
      path = params;
    } else {
      if (params.isNotEmpty) {
        Map<String, dynamic> data = json.decode(params);
        path = data["path"];
      }
    }
    if (path.isEmpty && sharePath.isNotEmpty) {
      path = sharePath;
    } else {
      if (!Platform.isIOS) {
        Map<String, dynamic> data = json.decode(params);
        path = data["path"];
      }
    }
    if (path.isNotEmpty) {
      if (path.startsWith("path=")) {
        path = path.substring(5);
      }
      if (path.contains("addgroup")) {
        sharePath = path;
        if (UserOption.token.isEmpty) {
          UserOption.toLoginPage();
          return;
        }
        groupShareFunc();
      } else {
        ShareData.shareData = path;
        Get.dialog(
          ShowShareInfoDialog(),
          barrierDismissible: false,
        );
      }
    }
  }

  initWeChatUnInstall() {
    NativeBridge.registerHandler(NativeMethods.initWeChatUnInstall,
        (params) async {
      ToastUtil.showShortErrorToast("微信未安装");
    });
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      // App回到前台时触发当前页面埋点
      _trackPageView(currentIndex.value, refeereIndex.value);
      TrackingUtils.trackEvent('AppShow', {
        'banner_id': '',
        'common_fields': '',
        'avitcity_id': '',
        'goods_id': '',
        'url_path': ''
      });

      // 判断im是否登录状态
      V2TIMManager().getLoginStatus().then((V2TimValueCallback value) async {
        if (value.code != 0) {
          IMModuleService().TIMLogin();
        }
      });
      UserOption.getUserInfoRequest(false);
      listenShareEvent();
      isLogin.value = UserOption.token.isNotEmpty ? true : false;
    } else if (state == AppLifecycleState.paused) {
      TrackingUtils.trackEvent('AppHide', {
        'banner_id': '',
        'common_fields': '',
        'avitcity_id': '',
        'goods_id': '',
        'url_path': ''
      });
      EventBusUtils.sendPageSwitchMsg("pageSwitch", -1, currentIndex.value);
    } else if (state == AppLifecycleState.inactive) {
    } else if (state == AppLifecycleState.detached) {
      // 不杀死 im数据会有问题
      exit(0);
    }
  }

  _onTabChanged() {
    getMessageNotice();
    IMModuleService().getTotalUnreadCount().then((value) {
      unReadCount.value = value;
      IMModuleService().unreadMsgCountNotifier.value = value;
    });
    if (tabController?.index == 0) {
      currentIndex.value = tabController!.index;
      return;
    }
    if (tabController?.index != currentIndex.value) {
      if (UserOption.checkNoTokenJump()) {
        return;
      }
      currentIndex.value = tabController!.index;
    }
    // HomeController.closeTab();
  }

  changeTab(index) {
    getMessageNotice();
    refeereIndex.value = currentIndex.value;
    // 记录新页面的进入时间
    if (index == 0) {
      tabController?.animateTo(index);
      currentIndex.value = index;
      _trackPageView(index, refeereIndex.value);
      EventBusUtils.sendPageSwitchMsg("pageSwitch", index, refeereIndex.value);
      return;
    } else {
      _trackPageView(index, refeereIndex.value); // 切换tab时触发埋点
      EventBusUtils.sendPageSwitchMsg("pageSwitch", index, refeereIndex.value);
    }
    if (UserOption.checkNoTokenJump()) {
      return;
    }

    tabController?.animateTo(index);
    currentIndex.value = index;

    if (PopOption.alreadyShowPopList.isNotEmpty &&
        PopOption.alreadyShowPopList.contains(index + 1)) {
      return;
    }
    //切换时 展示活动弹窗
    PopOption.currentTabIndex = index + 1;
    EventBusUtils.sendMsg("showPop");
  }

  @override
  void onClose() {
    WidgetsBinding.instance.removeObserver(this);
    tabController?.removeListener(() {});
    tabController?.dispose();
    _timer?.cancel();
    super.onClose();
  }
}
