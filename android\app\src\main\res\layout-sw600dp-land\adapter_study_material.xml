<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="80dp"
    android:orientation="horizontal"
    android:layout_marginBottom="20dp"
    >
    <RelativeLayout
        android:layout_width="110dp"
        android:layout_height="80dp">
        <ImageView
            android:layout_width="110dp"
            android:layout_height="80dp"
            android:src="@mipmap/ic_launcher"
            android:scaleType="fitXY"/>
     <TextView
         android:layout_width="match_parent"
         android:layout_height="20dp"
         android:background="@drawable/bg_radius4_notop_halfblack"
         android:text="共2课时"
         android:textSize="12sp"
         android:textColor="@color/white"
         android:gravity="center"
         android:layout_alignParentBottom="true"/>
    </RelativeLayout>
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_marginLeft="6dp"
        android:layout_height="match_parent">
        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="15sp"
            android:textColor="@color/_555555"
            android:textStyle="bold"
            android:maxLines="2"
            android:ellipsize="end"
            android:text="这是名称这是名称这是名称这是名称这是名称这是名称这是名称这是名称这是名称这是名称"/>
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_below="@+id/tv_title"
            android:layout_above="@+id/tv_classstatus"
            android:gravity="center_vertical"
            android:orientation="horizontal">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="12sp"
                android:layout_alignParentBottom="true"
                android:textColor="@color/_a09e9e"
                android:gravity="center"
                android:singleLine="true"
                android:text="课程类型: 录播课"/>
        </LinearLayout>
        <LinearLayout
            android:id="@+id/tv_classstatus"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:orientation="horizontal">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="12sp"
                android:textColor="@color/_a09e9e"
                android:gravity="center"
                android:singleLine="true"
                android:text="状态:"/>
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="12sp"
                android:textColor="@color/_428a6f"
                android:gravity="center"
                android:textStyle="bold"
                android:singleLine="true"
                android:text="已学习"/>
        </LinearLayout>
        <ImageView
            android:id="@+id/tv_download"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_centerVertical="true"
            android:layout_alignParentRight="true"
            android:layout_alignParentBottom="true"
            android:src="@mipmap/icon_kexiazai"
            />
    </RelativeLayout>
</LinearLayout>