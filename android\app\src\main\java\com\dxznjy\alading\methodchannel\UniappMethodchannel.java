package com.dxznjy.alading.methodchannel;

import static android.provider.Settings.EXTRA_APP_PACKAGE;

import static androidx.core.app.NotificationCompat.EXTRA_CHANNEL_ID;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.text.TextUtils;
import android.util.Log;
import android.widget.Toast;
import android.content.Intent;
import android.provider.Settings;

import androidx.annotation.NonNull;
import androidx.core.app.NotificationManagerCompat;

import com.dxznjy.alading.DXApplication;
import com.dxznjy.alading.R;
import com.dxznjy.alading.activity.OpenKfActivity;
import com.dxznjy.alading.common.Constants;
import com.dxznjy.alading.entity.AppShareParams;
import com.dxznjy.alading.entity.CalendarBean;
import com.dxznjy.alading.entity.DxnParams;
import com.dxznjy.alading.entity.MiniParams;
import com.dxznjy.alading.util.AppUtils;
import com.dxznjy.alading.util.CalendarUtils;
import com.dxznjy.alading.util.WxUtils;
import com.google.gson.Gson;
import com.tencent.mm.opensdk.modelmsg.SendMessageToWX;
import com.tencent.mm.opensdk.modelmsg.WXImageObject;
import com.tencent.mm.opensdk.modelmsg.WXMediaMessage;
import com.tencent.mm.opensdk.openapi.IWXAPI;
import com.vondear.rxtool.RxSPTool;

import org.json.JSONObject;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

import io.dcloud.feature.sdk.DCSDKInitConfig;
import io.dcloud.feature.sdk.Interface.IDCUniMPPreInitCallback;
import io.dcloud.feature.sdk.MenuActionSheetItem;
import io.dcloud.uniplugin.TxMeetingUtil;
import io.dcloud.common.DHInterface.ICallBack;
import io.dcloud.feature.sdk.DCUniMPSDK;
import io.dcloud.feature.unimp.config.UniMPOpenConfiguration;
import io.flutter.embedding.engine.plugins.FlutterPlugin;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;


public class UniappMethodchannel implements FlutterPlugin {
    String methodChannelName = Constants.openAndroidIos;
    Context mContext;

    static MethodChannel methodChannel;
    private static final String APP_ID = "wx79465579ceea44ae";
    public static IWXAPI api;

    @Override
    public void onAttachedToEngine(@NonNull FlutterPluginBinding binding) {
        mContext = binding.getApplicationContext();
        methodChannel=new MethodChannel(binding.getBinaryMessenger(),methodChannelName);
        methodChannel.setMethodCallHandler(new MethodChannel.MethodCallHandler() {
            @Override
            public void onMethodCall(@NonNull MethodCall call, @NonNull MethodChannel.Result result) {
                String methodCall=call.method;
         /*       if (methodCall.equals(Constants.openUniApp)){
                    String uniappPathData = call.arguments.toString();
                    WxUtils.toUniappPage(mContext,uniappPathData);
                }    */
                if (methodCall.equals("openUniApp")) {
//                    initUniappSdk();
                    List<String> args = call.arguments();
                    if(args != null && args.size()>1){
                        String uniAppPathData = args.get(0);
                        String wgtAppId = args.get(1);
                        toUniappPage(uniAppPathData, wgtAppId);
                        result.success("打开小程序");
                    }
                }else  if (methodCall.equals(Constants.exitApp)){
                    android.os.Process.killProcess(android.os.Process.myPid());
                    System.exit(0);
                }else if (methodCall.equals(Constants.openDxnUniApp)){//
                    if(!AppUtils.isWxInstall(mContext)){//未安装微信
                        MethodChannel methodChannel = new MethodChannel(DXApplication.app.flutterEngineInstance.getDartExecutor().getBinaryMessenger(),Constants.androidIosBackMeeting);
                        methodChannel.invokeMethod(Constants.initWeChatUnInstall,"");
                    }else {
                        DxnParams dxnParams = new Gson().fromJson(call.arguments.toString(), DxnParams.class);
                        WxUtils.startWxUniapp(dxnParams);
                    }
                }else if(methodCall.equals(Constants.openShareMiniProgram)){
                    MiniParams miniParams = new Gson().fromJson(call.arguments.toString(), MiniParams.class);
                    WxUtils.shareMiniProgram(mContext,miniParams);
                }else if (methodCall.equals(Constants.opneCustomerService)){//在线客服
                    DXApplication.startUserActivity(mContext, OpenKfActivity.class);
                }else if (methodCall.equals(Constants.openShareApp)){//微信分享
                    AppShareParams appShareParams = new Gson().fromJson(call.arguments.toString(), AppShareParams.class);
                    WxUtils.shareWeb(mContext,appShareParams);
                }else if(methodCall.equals(Constants.openShareAppPic)){
                    sharePic(call.arguments.toString());
                }else if(methodCall.equals(Constants.getAppSharePath)){//分享路径
                    String sharePath = RxSPTool.getString(mContext,Constants.SHARE_PATH);
                    if(!TextUtils.isEmpty(sharePath)){
                        result.success(sharePath);
                        RxSPTool.putString(mContext,Constants.SHARE_PATH,"");
                    }
                }else if (methodCall.equals(Constants.twoBackFinish)){
                    RxSPTool.putString(mContext,Constants.canBackFinish,call.arguments.toString());
                }else if (methodCall.equals(Constants.creatCalendar)){
                    CalendarBean calendarBean = new Gson().fromJson(call.arguments.toString(), CalendarBean.class);
                    creatCalendar(calendarBean);
                }   else if (methodCall.equals(Constants.openNotificationSettings)){
                    checkNotifySetting();
                }else if(methodCall.equals(Constants.isOpenNotificationSettings)){
                    result.success(isOpenNotificationSettings());
                }else if (methodCall.equals("getAppBasePath")) {
                    result.success(getAppBasePath());
                } else if (methodCall.equals("releaseWgtToRunPath")) {
                    releaseWgtToRunPath(
                            call.argument("wgtPath"),
                            call.argument("appId"),
                            result
                    );
                }
            }
        });
    }

    /**
     * 初始化小程序sdk
     */
    public void initUniappSdk() {
        MenuActionSheetItem item = new MenuActionSheetItem("关于", "gy");
        List<MenuActionSheetItem> sheetItems = new ArrayList<>();
        sheetItems.add(item);
        //小程序第三方sdk初始化
        DCSDKInitConfig config = new DCSDKInitConfig.Builder()
                .setCapsule(false)
                .setMenuDefFontSize("16px")
                .setMenuDefFontColor("#ff00ff")
                .setMenuDefFontWeight("normal")
                .setMenuActionSheetItems(sheetItems)
                .setEnableBackground(false)//开启后台运行
                .setUniMPFromRecents(false)
                .build();
        DCUniMPSDK.getInstance().initialize(mContext, config, new IDCUniMPPreInitCallback() {
            @Override
            public void onInitFinished(boolean isSuccess) {
                Log.e("unimp", "onInitFinished-----------" + isSuccess);
            }
        });


    }



    /**
     * 判断是否开启通知权限
     * @return true 已开启，false 未开启
     */
    private String isOpenNotificationSettings() {
        NotificationManagerCompat manager = NotificationManagerCompat.from(mContext);
        boolean isOpened = manager.areNotificationsEnabled();
        if (isOpened) {
            return "true";
        } else {
            return "false";
        }
    }

    /**
     * 检查通知权限
     * 如果没有开启通知权限，则跳转到应用通知设置界面
     */
    private void checkNotifySetting() {
        NotificationManagerCompat manager = NotificationManagerCompat.from(mContext);
        boolean isOpened = manager.areNotificationsEnabled();
        if (isOpened) {
        } else {
            try {
                // 根据isOpened结果，判断是否需要提醒用户跳转去打开App通知权限
                Intent intent = new Intent();
                intent.setAction(Settings.ACTION_APP_NOTIFICATION_SETTINGS);
                //8.0（含8.0）以上可以用
                intent.putExtra(EXTRA_APP_PACKAGE, mContext.getPackageName());
                intent.putExtra(EXTRA_CHANNEL_ID, mContext.getApplicationInfo().uid);
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK );
                // 5.0——7.1 之间的版本使用
//                intent.putExtra("app_package", mContext.getPackageName());
//                intent.putExtra("app_uid", mContext.getApplicationInfo().uid);
                mContext.startActivity(intent);
            } catch (Exception e) {
                e.printStackTrace();
                // 出现异常则跳转到应用设置界面
                Intent intent = new Intent();
                intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                Uri uri = Uri.fromParts("package",mContext.getPackageName(), null);
                intent.setData(uri);
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                mContext.startActivity(intent);
            }
        }
    }


    @Override
    public void onDetachedFromEngine(@NonNull FlutterPluginBinding binding) {

    }
    public void sharePic(String bytes ){
        if(!AppUtils.isWxInstall(mContext)){//未安装微信
            Toast.makeText(mContext,"微信未安装",Toast.LENGTH_SHORT).show();
        }else{
            try {
                byte[] arr = Base64.getDecoder().decode(bytes.getBytes());
                Bitmap bmp = BitmapFactory.decodeByteArray(arr, 0, arr.length);
                //初始化 WXImageObject 和 WXMediaMessage 对象
                WXImageObject imgObj = new WXImageObject(bmp);
                WXMediaMessage msg = new WXMediaMessage();
                msg.mediaObject = imgObj;
                Bitmap thumbBmp = BitmapFactory.decodeResource(mContext.getResources(), R.mipmap.ic_share_default);
                msg.thumbData =bitmapToByteArray(thumbBmp);
                //构造一个Req
                SendMessageToWX.Req req = new SendMessageToWX.Req();
                req.transaction ="鼎校甄选";
                req.message = msg;
                req.scene = SendMessageToWX.Req.WXSceneSession;
                //调用api接口，发送数据到微信
                DXApplication.api.sendReq(req);
            } catch (Exception ex) {
            }
        }
    }

    public  byte[] bitmapToByteArray(Bitmap bitmap) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        bitmap.compress(Bitmap.CompressFormat.PNG, 70, outputStream);
        return outputStream.toByteArray();
    }


    public void creatCalendar(CalendarBean calendarBean) {
        String rule = "FREQ=DAILY;COUNT=1";//
        //提前30分钟提醒
        if(calendarBean.getBeginTimestamp() > System.currentTimeMillis() + 1000 * 60 * 30){
            CalendarUtils.addCalendarEventRemind(mContext, mContext.getString( R.string.app_name), calendarBean.getRemindName(),
                    calendarBean.getBeginTimestamp(), calendarBean.getEndTimestamp(), 30,rule,new CalendarUtils.onCalendarRemindListener() {
                        @Override
                        public void onFailed(Status error_code) {
                        }
                        @Override
                        public void onSuccess() {
//                        Toast.makeText(mContext,"课程提醒添加成功",Toast.LENGTH_SHORT).show();
                        }
                    });
        }
        //准点提醒
        CalendarUtils.addCalendarEventRemind(mContext, mContext.getString( R.string.app_name), calendarBean.getRemindName(),
                calendarBean.getBeginTimestamp(), calendarBean.getEndTimestamp(), 0,rule,new CalendarUtils.onCalendarRemindListener() {
                    @Override
                    public void onFailed(Status error_code) {
                    }
                    @Override
                    public void onSuccess() {
                        Toast.makeText(mContext,"课程提醒添加成功",Toast.LENGTH_SHORT).show();
                    }
                });
    }
    /*     *
     * 打开微信小程序页面
     */
    public void toUniappPage(String data, String wgtAppId){
        Log.i("zgj",data);
        try {
            UniMPOpenConfiguration uniMPOpenConfiguration=new UniMPOpenConfiguration();
            if(data.contains("{")){
                uniMPOpenConfiguration.extraData = new JSONObject(data);
            }else{
                uniMPOpenConfiguration.path = data;
            }
//            if (wgtAppId.isEmpty())  {
                wgtAppId = Constants.XCX_PKG_NAME;
//            }
            DXApplication.iUniMP= DCUniMPSDK.getInstance().openUniMP(mContext, wgtAppId,uniMPOpenConfiguration);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // 获取应用外部存储目录下的 apps 文件夹
    private String getAppBasePath() {
        File appsDir = new File(mContext.getExternalFilesDir(null), "apps");
        // 确保目录存在
        if (!appsDir.exists()) {
            appsDir.mkdirs();
        }
        return appsDir.getAbsolutePath();
    }

    //解压wgt到路径
    private void releaseWgtToRunPath(String wgtPath, String appId, final MethodChannel.Result result) {
        DCUniMPSDK.getInstance().releaseWgtToRunPathFromePath(appId, wgtPath, new ICallBack() {
            @Override
            public Object onCallBack(int code, Object pArgs) {
                if (code == 1) {
                    result.success("WGT释放成功");
                } else {//释放wgt失败
                    String errorMsg = "WGT释放失败, code=" + code;
                    result.error("RELEASE_ERROR", errorMsg, null);
                    Toast.makeText(mContext, "资源释放失败", Toast.LENGTH_SHORT).show();
                }
                result.error("RELEASE_ERROR", "", null);
                return  "";
            }
        });
    }
}

