package com.dxznjy.alading.adapter.study;

import android.content.Context;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dxznjy.alading.R;

import java.util.List;


public class StudyAdapter extends BaseQuickAdapter<String, BaseViewHolder> {

    public StudyAdapter(Context context, List<String> data) {
        super(R.layout.adapter_study, data);
        this.mContext = context;
    }

    @Override
    protected void convert(final BaseViewHolder helper, String item) {
        helper.addOnClickListener(R.id.tv_gostudy);
    }
}
