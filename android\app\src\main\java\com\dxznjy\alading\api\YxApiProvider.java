package com.dxznjy.alading.api;


import android.content.Context;
import android.util.Log;

import com.dxznjy.alading.DXApplication;
import com.dxznjy.alading.common.Constants;
import com.dxznjy.alading.common.GlobalConfig;
import com.dxznjy.alading.http.CustomGsonConverterFactory;
import com.dxznjy.alading.util.CheckSumBuilder;

import java.io.IOException;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import okhttp3.Interceptor;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.logging.HttpLoggingInterceptor;
import retrofit2.Retrofit;
import retrofit2.adapter.rxjava.RxJavaCallAdapterFactory;
import retrofit2.converter.gson.GsonConverterFactory;

/**
 * Created by Administrator on 2016/9/3.
 */

public class YxApiProvider {
    LiveNetworkMonitor networkMonitor;
    //    Context context;
    public static final String BASE_URL = GlobalConfig.getYXUrl();
    private YxApiProvider(Context context){
        HttpLoggingInterceptor loggingInterceptor = new HttpLoggingInterceptor(new HttpLoggingInterceptor.Logger() {
            @Override
            public void log(String message) {
                if (message.length() > 4000) {
                    for (int i = 0; i < message.length(); i += 4000) {
                        //当前截取的长度<总长度则继续截取最大的长度来打印
                        if (i + 4000 < message.length()) {
                            Log.i(" zgj" + i, message.substring(i, i + 4000));
                        } else {
                            //当前截取的长度已经超过了总长度，则打印出剩下的全部信息
                            Log.i(" zgj" + i, message.substring(i, message.length()));
                        }
                    }
                } else {
                    //直接打印
                    Log.i(" zgj", message);
                }
            }
        });
        loggingInterceptor.setLevel(HttpLoggingInterceptor.Level.BODY);
        networkMonitor = new LiveNetworkMonitor(DXApplication.app);
        OkHttpClient.Builder builder = new OkHttpClient().newBuilder();
        builder.retryOnConnectionFailure(true);
        builder.readTimeout(100, TimeUnit.SECONDS);
        builder.connectTimeout(100, TimeUnit.SECONDS);
        builder.addInterceptor(loggingInterceptor);
        builder.addInterceptor(new Interceptor() {
            @Override
            public Response intercept(Chain chain) throws IOException {
                boolean connected = networkMonitor.isConnected();
                String curTime = System.currentTimeMillis()+"";
                String uuid = UUID.randomUUID().toString();
                if (connected) {
                    Request.Builder requestBuilder = chain
                            .request()
                            .newBuilder();
                        requestBuilder
                                .addHeader("Content-Type", "application/json")
                                .addHeader("Accept", "application/json")
                                .addHeader("AppKey", Constants.YX_APP_KEY)
                                .addHeader("Nonce",uuid)
                                .addHeader("CurTime",curTime+"")
                                .addHeader("CheckSum", CheckSumBuilder.getCheckSum(uuid,curTime+"",Constants.YX_APP_SECRET))
                                .build();
                    Response response = chain.proceed(requestBuilder.build());
                    return response;
                } else {
                    throw new RuntimeException("网络出错，请检查网络连接");
                }
            }
        });

        OkHttpClient client = builder.build();
        retrofit =
                new Retrofit.Builder().baseUrl(BASE_URL)
                        .client(client)
                        .addConverterFactory(CustomGsonConverterFactory.create())
                        .addConverterFactory(GsonConverterFactory.create())
                        .addCallAdapterFactory(RxJavaCallAdapterFactory.create())
                        .build();
        DFService = retrofit.create(com.dxznjy.alading.api.service.DFService.class);
    }
    private static YxApiProvider instance;
    public static YxApiProvider getInstance(Context context){
        if (instance == null){
            instance = new YxApiProvider(context);
        }
        return instance;
    }
    public com.dxznjy.alading.api.service.DFService DFService;
    public Retrofit retrofit;

}
