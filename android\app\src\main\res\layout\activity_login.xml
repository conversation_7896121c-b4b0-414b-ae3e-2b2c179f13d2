<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/white"
    tools:context=".activity.LoginActivity">
    <ImageView
        android:layout_width="match_parent"
        android:layout_height="280dp"
        android:src="@mipmap/pic_peitu"
        android:scaleType="fitXY"
        />
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        >
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:layout_above="@+id/tv_login">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="24dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="24dp"
                android:orientation="horizontal">
                <LinearLayout
                    android:id="@+id/ll_namepwd"
                    android:layout_width="0dp"
                    android:layout_height="44dp"
                    android:layout_weight="1"
                    android:background="@mipmap/btn_zuobian_h"
                    android:gravity="center"
                    >

                    <TextView
                        android:id="@+id/tv_namepwd"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginRight="20dp"
                        android:gravity="center"
                        android:text="@string/userpwd_login"
                        android:textColor="@color/_428a6f"
                        android:textSize="16sp"
                        android:textStyle="bold" />
                </LinearLayout>
                <LinearLayout
                    android:id="@+id/ll_phonecode"
                    android:layout_width="0dp"
                    android:layout_height="44dp"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:layout_marginLeft="-20dp"
                    android:background="@mipmap/btn_youbian_n"
                    >
                    <TextView
                        android:id="@+id/tv_phonecode"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="10dp"
                        android:gravity="center"
                        android:textSize="16sp"
                        android:textColor="@color/_223843"
                        android:text="@string/phonecode_login"/>
                </LinearLayout>
            </LinearLayout>
            <LinearLayout
                android:id="@+id/ll_namepwd_ui"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="24dp"
                    android:layout_marginRight="24dp"
                    android:layout_marginTop="40dp"
                    android:gravity="center_vertical"
                    android:orientation="vertical">
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/phone"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/_333333"/>
                    <EditText
                        android:id="@+id/ed_phoneandstnum"
                        android:layout_width="match_parent"
                        android:layout_height="48dp"
                        android:background="@null"
                        android:textSize="14sp"
                        android:textColorHint="@color/_c8c8c8"
                        android:textColor="@color/_555555"
                        android:maxLength="11"
                        android:text=""
                        android:inputType="number"
                        android:textCursorDrawable="@null"
                        android:hint="@string/input_phone_tips"/>
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/_f2f2f2"/>
                </LinearLayout>
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="24dp"
                    android:layout_marginRight="24dp"
                    android:layout_marginTop="20dp"
                    android:gravity="center_vertical"
                    android:orientation="vertical">
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/pwd"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/_333333"/>
                    <EditText
                        android:id="@+id/ed_pwd"
                        android:layout_width="match_parent"
                        android:layout_height="48dp"
                        android:background="@null"
                        android:textSize="14sp"
                        android:textColorHint="@color/_c8c8c8"
                        android:inputType="textPassword"
                        android:textColor="@color/_555555"
                        android:textCursorDrawable="@null"
                        android:text=""
                        android:maxLength="15"
                        android:hint="@string/input_pwd_tips"/>
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/_f2f2f2"/>
                </LinearLayout>
            </LinearLayout>


            <LinearLayout
                android:id="@+id/ll_phonecode_ui"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="gone"
                android:orientation="vertical">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="24dp"
                    android:layout_marginRight="24dp"
                    android:layout_marginTop="40dp"
                    android:gravity="center_vertical"
                    android:orientation="vertical">
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="手机号"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/_333333"/>
                    <EditText
                        android:id="@+id/ed_phone"
                        android:layout_width="match_parent"
                        android:layout_height="48dp"
                        android:background="@null"
                        android:textSize="14sp"
                        android:textColorHint="@color/_c8c8c8"
                        android:textColor="@color/_555555"
                        android:textCursorDrawable="@null"
                        android:inputType="number"
                        android:text=""
                        android:maxLength="11"
                        android:hint="请输入手机号"/>
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/_f2f2f2"/>
                </LinearLayout>
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="24dp"
                    android:layout_marginRight="24dp"
                    android:layout_marginTop="20dp"
                    android:orientation="vertical">
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="验证码"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/_333333"/>
                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="48dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">
                        <EditText
                            android:id="@+id/ed_phonecode"
                            android:layout_width="match_parent"
                            android:layout_height="48dp"
                            android:background="@null"
                            android:textSize="14sp"
                            android:layout_toLeftOf="@+id/tv_getcode"
                            android:textColorHint="@color/_c8c8c8"
                            android:textColor="@color/_555555"
                            android:textCursorDrawable="@null"
                            android:maxLength="6"
                            android:inputType="number"
                            android:hint="请输入验证码"/>
                        <TextView
                            android:layout_width="90dp"
                            android:layout_height="32dp"
                            android:id="@+id/tv_getcode"
                            android:layout_alignParentRight="true"
                            android:layout_centerVertical="true"
                            android:textSize="12sp"
                            android:gravity="center"
                            android:textColor="@color/white"
                            android:layout_marginLeft="10dp"
                            android:background="@drawable/bg_radius4_428a6f"
                            android:text="@string/get_code"/>
                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:layout_toLeftOf="@+id/tv_getcode"
                            android:layout_alignParentBottom="true"
                            android:background="@color/_f2f2f2"/>
                    </RelativeLayout>
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>
        <TextView
            android:id="@+id/tv_login"
            android:layout_width="220dp"
            android:layout_height="40dp"
            android:layout_alignParentBottom="true"
            android:gravity="center"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="120dp"
            android:textSize="14sp"
            android:textColor="@color/white"
            android:background="@drawable/bg_radius20_428a6f"
            android:text="@string/login"/>
    </RelativeLayout>
</LinearLayout>