<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@mipmap/bg_shangke"
    android:orientation="vertical"
    tools:ignore="MissingDefaultResource">
    <TextView
        android:layout_width="match_parent"
        android:layout_height="30dp"
        android:text="我的"
        android:gravity="center"
        android:textStyle="bold"
        android:textSize="16sp"
        android:textColor="@color/_333333"/>
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@mipmap/pic_gerenzhong_pad">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="200dp"
            android:orientation="vertical"
            android:layout_marginLeft="-1dp"
            android:layout_marginRight="-1dp"
            android:paddingBottom="10dp"
            android:paddingLeft="30dp"
            android:paddingRight="30dp"
            >
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <LinearLayout
                    android:id="@+id/ll_name"
                    android:layout_width="40dp"
                    android:layout_centerVertical="true"
                    android:gravity="center"
                    android:background="@drawable/bg_circle_12c287"
                    android:layout_height="40dp">
                    <TextView
                        android:id="@+id/tv_fristname"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text=""
                        android:textSize="16sp"
                        android:textColor="@color/white"
                        />
                </LinearLayout>
                <RelativeLayout
                    android:layout_width="240dp"
                    android:layout_height="40dp"
                    android:layout_toRightOf="@+id/ll_name"
                    android:layout_marginLeft="8dp"
                    android:background="@drawable/bg_radius10_white"
                    android:layout_centerVertical="true"
                    android:orientation="vertical">
                    <TextView
                        android:id="@+id/tv_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/_333333"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="12dp"
                        android:textStyle="bold"
                        android:textSize="14sp"
                        android:text="学员姓名"/>
                    <TextView
                        android:layout_width="80dp"
                        android:layout_height="24dp"
                        android:textColor="@color/white"
                        android:textSize="12sp"
                        android:id="@+id/tv_gradename"
                        android:layout_alignParentRight="true"
                        android:layout_marginRight="12dp"
                        android:layout_centerVertical="true"
                        android:gravity="center"
                        android:background="@mipmap/icon_biaoqian_pad"
                        android:text=""/>
                </RelativeLayout>

            </RelativeLayout>
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="10dp"
                android:background="@drawable/bg_radius20_white">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_above="@+id/tv_exit"
                    android:gravity="center"
                    android:orientation="vertical">
                    <ImageView
                        android:layout_width="60dp"
                        android:layout_height="60dp"
                        android:src="@mipmap/ic_mine_nodata"/>
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="12sp"
                        android:layout_marginTop="10dp"
                        android:textColor="@color/_666666"
                        android:text="即将开放，请等待"/>
                </LinearLayout>
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_above="@+id/tv_exit"
                    android:layout_marginTop="10dp"
                    android:visibility="gone"
                    android:layout_marginBottom="20dp"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:orientation="horizontal"
                        android:layout_weight="1">
                        <LinearLayout
                            android:id="@+id/ll_stbg"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:layout_weight="1">
                            <ImageView
                                android:layout_width="30dp"
                                android:layout_height="30dp"
                                android:src="@mipmap/icon_skbaogao_pad"/>
                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="6dp"
                                android:textSize="12sp"
                                android:gravity="center"
                                android:textColor="@color/_555555"
                                android:text="试课报告\n"/>
                        </LinearLayout>
                        <LinearLayout
                            android:id="@+id/ll_chljc"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:layout_weight="1">
                            <ImageView
                                android:layout_width="30dp"
                                android:layout_height="30dp"
                                android:src="@mipmap/icon_cihuijiance_pad"/>
                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="6dp"
                                android:textSize="12sp"
                                android:gravity="center"
                                android:textColor="@color/_555555"
                                android:text="词汇量检测\n报告"/>
                        </LinearLayout>
                        <LinearLayout
                            android:id="@+id/ll_ctb"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:layout_weight="1">
                            <ImageView
                                android:layout_width="30dp"
                                android:layout_height="30dp"
                                android:src="@mipmap/icon_cuotiben_pad"/>
                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="6dp"
                                android:textSize="12sp"
                                android:gravity="center"
                                android:textColor="@color/_555555"
                                android:text="错题本\n"/>
                        </LinearLayout>
                        <LinearLayout
                            android:id="@+id/ll_qwfx"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:layout_weight="1">
                            <ImageView
                                android:layout_width="30dp"
                                android:layout_height="30dp"
                                android:src="@mipmap/icon_fuxi_pad"/>
                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="6dp"
                                android:textSize="12sp"
                                android:gravity="center"
                                android:textColor="@color/_555555"
                                android:text="趣味复习\n"/>
                        </LinearLayout>
                    </LinearLayout>
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:orientation="horizontal"
                        android:layout_weight="1">
                        <LinearLayout
                            android:id="@+id/ll_xxnr"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:layout_weight="1">
                            <ImageView
                                android:layout_width="30dp"
                                android:layout_height="30dp"
                                android:src="@mipmap/icon_dayin_pad"/>
                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="6dp"
                                android:textSize="12sp"
                                android:gravity="center"
                                android:textColor="@color/_555555"
                                android:text="学习内容\n打印"/>
                        </LinearLayout>
                        <LinearLayout
                            android:id="@+id/ll_pyf"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:layout_weight="1">
                            <ImageView
                                android:layout_width="30dp"
                                android:layout_height="30dp"
                                android:src="@mipmap/icon_pinyinfakangyiwang_pad"/>
                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="6dp"
                                android:textSize="12sp"
                                android:gravity="center"
                                android:textColor="@color/_555555"
                                android:text="拼音法\n抗遗忘"/>
                        </LinearLayout>
                        <LinearLayout
                            android:id="@+id/ll_kyw"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:layout_weight="1">
                            <ImageView
                                android:layout_width="30dp"
                                android:layout_height="30dp"
                                android:src="@mipmap/icon_21tian_pad"/>
                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="6dp"
                                android:textSize="12sp"
                                android:gravity="center"
                                android:textColor="@color/_555555"
                                android:text="21天\n抗遗忘"/>
                        </LinearLayout>
                        <LinearLayout
                            android:id="@+id/ll_txjc"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:layout_weight="1">
                            <ImageView
                                android:layout_width="30dp"
                                android:layout_height="30dp"
                                android:src="@mipmap/icon_tingxie_pad"/>
                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="6dp"
                                android:textSize="12sp"
                                android:gravity="center"
                                android:textColor="@color/_555555"
                                android:text="听写检测\n报告"/>
                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>
                <TextView
                    android:id="@+id/tv_exit"
                    android:layout_width="240dp"
                    android:layout_height="36dp"
                    android:text="退出登录"
                    android:gravity="center"
                    android:layout_marginBottom="12dp"
                    android:background="@drawable/bg_radius20_f7f6f6"
                    android:textSize="14sp"
                    android:layout_centerHorizontal="true"
                    android:textColor="@color/_428a6f"
                    android:layout_alignParentBottom="true"/>
            </RelativeLayout>
        </LinearLayout>
    </RelativeLayout>
</LinearLayout>