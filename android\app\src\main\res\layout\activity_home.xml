<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:tl="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    >

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <com.dxznjy.alading.widget.NoScrollViewPager
            android:id="@+id/vp_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@+id/tl_2"
            />
            <com.flyco.tablayout.CommonTabLayout
                android:id="@+id/tl_2"
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:layout_alignParentBottom="true"
                app:tl_iconWidth="24dp"
                app:tl_iconHeight="24dp"
                tl:tl_indicator_color="@color/_435a70"
                tl:tl_indicator_height="0dp"
                android:visibility="gone"
                tl:tl_textSelectColor="@color/_435a70"
                tl:tl_textUnselectColor="@color/_435a70"
                tl:tl_textsize="12sp"
                tl:tl_underline_color="@color/_435a70"
                tl:tl_underline_height="0dp" />
    </RelativeLayout>

</LinearLayout>