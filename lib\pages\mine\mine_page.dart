import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_state_manager/src/simple/get_view.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:student_end_flutter/common/CommonUtil.dart';
import 'package:student_end_flutter/pages/base/controller/base_controller.dart';
import 'package:tx_im/utils/permissionUtils.dart';
import '../../common/scan_code_analysis.dart';
import '../../res/colors.dart';
import 'controller/mine_controller.dart';
import './settings_page.dart';

class MinePage extends GetView<MineController> {
  @override
  Widget build(BuildContext context) {
    Get.put(MineController());
    final BaseController baseController = Get.find<BaseController>();
    return Scaffold(
      backgroundColor: HexColor('F3F8FC'),
      body: RefreshIndicator(
        color: AppColors.f2E896F,
        backgroundColor: Colors.white,
        notificationPredicate: (ScrollNotification notifation) {
          ScrollMetrics scrollMetrics = notifation.metrics;
          if (scrollMetrics.minScrollExtent == 0) {
            return true;
          } else {
            return false;
          }
        },
        onRefresh: controller.onRefresh,
        child: _buildContent(context),
      ),
    );
  }

  _buildContent(BuildContext context) {
    final BaseController baseController = Get.find<BaseController>();
    return SingleChildScrollView(
      child: Obx(() => Column(
            children: [
              Container(
                alignment: Alignment.center,
                padding: EdgeInsets.only(left: 10.w, right: 10.w, bottom: 0.h),
                width: double.infinity,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      HexColor("#EEFFF5"),
                      HexColor('F3F8FC'),
                    ],
                  ),
                ),
                child: Column(
                  children: [
                    SizedBox(
                      height: 50.h,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        //
                        GestureDetector(
                          onTap: () {
                            controller.utilsClick({'name': '联系客服'}, context);
                          },
                          child: Image(
                            fit: BoxFit.fill,
                            width: 16.h,
                            height: 16.h,
                            image: AssetImage(
                                "assets/mine/icon-customer-service.png"),
                          ),
                        ),

                        SizedBox(
                          width: 15.w,
                        ),
                        GestureDetector(
                          onTap: () {
                            controller.topClick({'name': '信息中心'});
                            baseController.showMessageRedDot.value = false;
                          },
                          child: Stack(
                            children: [
                              Container(
                                padding: EdgeInsets.only(right: 2.w),
                                child: Image(
                                  fit: BoxFit.fill,
                                  width: 16.h,
                                  height: 16.h,
                                  image: AssetImage(
                                      "assets/mine/icon-xiaoxitixing.png"),
                                ),
                              ),
                              Obx(() {
                                return baseController.showMessageRedDot.value
                                    ? Positioned(
                                        right: 0,
                                        top: 0,
                                        child: Container(
                                          width: 6,
                                          height: 6,
                                          decoration: BoxDecoration(
                                            color: Colors.red,
                                            shape: BoxShape.circle,
                                          ),
                                        ),
                                      )
                                    : const SizedBox();
                              }),
                            ],
                          ),
                        ),

                        SizedBox(
                          width: 15.w,
                        ),
                        GestureDetector(
                          onTap: () {
                            // Get.off(()=>SettingsPage());
                            Get.to(SettingsPage());
                          },
                          child: Image(
                            fit: BoxFit.fill,
                            width: 16.h,
                            height: 16.h,
                            image: AssetImage("assets/mine/icon-shezhi.png"),
                          ),
                        ),

                        SizedBox(
                          width: 15.w,
                        ),
                        InkWell(
                            onTap: () async {
                              var cameraStatus =
                              await Permission.camera.request();
                              if (cameraStatus.isGranted) {
                                Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                        builder: (context) =>
                                            QRScannerScreenanAlysis()));
                              } else {
                                permissionUtils.showSettingDialog(context,
                                    title: "权限请求",
                                    content: "我们需要访问您的相机来扫描图片。如果您愿意，请在设置中打开权限");
                              }
                            },
                            child: Image(
                                width: 19,
                                height: 19,
                                image: AssetImage(
                                    "tx-im/assets/images/icon_scancode.png")),
                          ),
                      ],
                    ),
                    SizedBox(
                      height: 10.w,
                    ),
                    Stack(
                      children: [
                        Row(
                          children: [
                            GestureDetector(
                              onTap: () {
                                controller.headUrlClick();
                              },
                              child: ClipRRect(
                                borderRadius:
                                    BorderRadius.all(Radius.circular(50)),
                                child: Image(
                                    fit: BoxFit.fill,
                                    width: 50.h,
                                    height: 50.h,
                                    image: controller.getHead()),
                              ),
                            ),
                            SizedBox(
                              width: 10.w,
                            ),
                            Expanded(
                                child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  controller.userInfoBeanEntity!.value.nickName,
                                  style: TextStyle(
                                      fontFamily: "R",
                                      fontSize: 16.sp,
                                      color: Colors.black),
                                ),
                                SizedBox(
                                  height: 4.h,
                                ),
                                Wrap(
                                  spacing: 3.0, // 间距
                                  runSpacing: 5.0, // 行间距
                                  children: _topRoleItemUI(),
                                )
                              ],
                            ))
                          ],
                        ),
                        Positioned(
                            right: 0,
                            top: 5.h,
                            child: GestureDetector(
                              onTap: () {
                                controller.integrationCenterClick();
                              },
                              child: Row(
                                children: [
                                  Text(
                                    controller.showHasSignedInToday.value
                                        ? "已签到"
                                        : '签到领取积分',
                                    style: TextStyle(
                                        fontFamily: "R",
                                        fontSize: 12.sp,
                                        color: controller
                                                .showHasSignedInToday.value
                                            ? HexColor("888896")
                                            : HexColor("#006738")),
                                  ),
                                  Icon(
                                    Icons.chevron_right,
                                    size: 20.w,
                                    color: HexColor("888896"),
                                  )
                                ],
                              ),
                            )),

                      ],
                    ),
                    SizedBox(
                      height: 15.h,
                    ),
                    Offstage(
                      offstage: !controller.roleUseList.isEmpty,
                      child: _goOpenUI(),
                    ),
                    Offstage(
                      offstage: !controller.roleUseList.isNotEmpty,
                      child: _roleOpenUI(),
                    ),
                    // Offstage(
                    //   offstage: !controller.roleUseList.isEmpty,
                    //   child: _showNoOpenEarnings(),
                    // ),
                  ],
                ),
              ),
              SizedBox(
                height: 15.h,
              ),
              Container(
                  margin: EdgeInsets.only(left: 10.w, right: 10.w),
                  padding: EdgeInsets.only(
                      left: 10.w, right: 10.w, bottom: 10.h, top: 10.h),
                  decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.all(Radius.circular(5))),
                  child: Obx(() {
                    // 确保在这里访问 controller.topClickList 以建立依赖关系
                    return Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: _topBtnUI(),
                    );
                  })),
              // if (controller.roleUseList.isNotEmpty)
              //   Container(
              //     width: double.infinity,
              //     margin: EdgeInsets.only(left: 10.w, right: 10.w),
              //     decoration: BoxDecoration(
              //         image: DecorationImage(
              //             image: AssetImage("assets/mine/pic_shaixuan.png"),
              //             fit: BoxFit.fill)),
              //     child: Column(
              //       children: [
              //         _tabUI(),
              //         SizedBox(
              //           height: 8.h,
              //         ),
              //         _moneyUI(false),
              //         SizedBox(
              //           height: 15.h,
              //         ),
              //         _contentUI(),
              //       ],
              //     ),
              //   ),
              Container(
                width: double.infinity,
                margin: EdgeInsets.only(left: 10.w, right: 10.w, top: 10.h),
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.all(Radius.circular(5))),
                child: Column(
                  children: [
                    Container(
                      width: double.infinity,
                      margin:
                          EdgeInsets.only(left: 10.w, top: 10.w, bottom: 10.h),
                      child: Text(
                        "工具服务",
                        style: TextStyle(
                          color: Colors.black,
                          fontSize: 14.sp,
                          fontFamily: "M",
                        ),
                      ),
                    ),
                    GridView.builder(
                      padding: EdgeInsets.zero,
                      shrinkWrap: true,
                      physics: NeverScrollableScrollPhysics(),
                      itemCount: controller.utilsClickList.length,
                      itemBuilder: (BuildContext context, int index) {
                        return _btnUI(
                            controller.utilsClickList, index, "utils", context);
                      },
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 4,
                          mainAxisSpacing: 10.w,
                          crossAxisSpacing: 10.w,
                          childAspectRatio: 1),
                    )
                  ],
                ),
              ),
              SizedBox(
                height: 20.h,
              ),
              // Visibility(
              //     visible: true,
              //     child: GestureDetector(
              //       onTap: () {
              //         UserOption.logoutFunc(false);
              //       },
              //       child: Container(
              //         alignment: Alignment.center,
              //         width: double.infinity,
              //         margin: EdgeInsets.only(
              //             left: 10.w, right: 10.w, bottom: 10.h),
              //         padding: EdgeInsets.all(10.w),
              //         decoration: BoxDecoration(
              //             color: Colors.white,
              //             borderRadius: BorderRadius.all(Radius.circular(5))),
              //         child: Text(
              //           "退出登录",
              //           style: TextStyle(
              //               fontSize: 14.sp,
              //               fontFamily: "M",
              //               color: HexColor("666666")),
              //         ),
              //       ),
              //     )),
              // InkWell(
              //   onTap: (){
              //     controller.onClickCheckVer(isShowTips: true);
              //   },
              //   child: Row(
              //     mainAxisAlignment: MainAxisAlignment.end,
              //     crossAxisAlignment: CrossAxisAlignment.start,
              //     children: [
              //       Text("版本号：${AppVersionUtil.appVersion}",style: TextStyle(fontSize: 12,color: HexColor("666666")),),
              //       Visibility(
              //         visible: controller.isNeedUp.value,
              //           child: Container(
              //         height: 5,
              //         width: 5,
              //         decoration: BoxDecoration(
              //             color: Colors.red,
              //             borderRadius: BorderRadius.all(Radius.circular(60))
              //         ),
              //       )),
              //       SizedBox(
              //         width: 10.w,
              //       ),
              //     ],
              //   ),
              // ),
              SizedBox(
                height: 10.h,
              ),
            ],
          )),
    );
  }

  _showNoOpenEarnings() {
    return _moneyUI(true);
  }

  _roleOpenUI() {
    if (controller.roleUseList.isEmpty) {
      return Container();
    }
    return Stack(
      children: [
        Image(
          // img_quanyi_bg1
          image: AssetImage("assets/mine/img_quanyi_bg1.png"),
        ),
        Positioned(
            top: 6.h,
            child: Row(
              children: [
                SizedBox(
                  width: 15.w,
                ),
                Image(
                    width: 26.w,
                    height: 26.h,
                    image: AssetImage("assets/mine/img_quanyi_ava.png")),
                SizedBox(
                  width: 5.w,
                ),
                Text(
                  controller.roleUseList.isNotEmpty
                      ? "尊贵的${controller.roleUseList[controller.currentTabIndex.value]}~"
                      : "",
                  style: TextStyle(
                      color: HexColor("2d2424"),
                      fontFamily: "M",
                      fontWeight: FontWeight.w600,
                      fontSize: 14.sp),
                ),
                SizedBox(
                  width: 5.w,
                ),
                Visibility(
                    visible: true,
                    child: GestureDetector(
                      onTap: () {
                        controller.seeRewards();
                      },
                      child: Text(
                        "查看权益",
                        style: TextStyle(
                            color: HexColor("5a4f40"),
                            fontFamily: "R",
                            decoration: TextDecoration.underline,
                            fontSize: 12.sp),
                      ),
                    ))
              ],
            )),
        Positioned(
            left: 70.w,
            bottom: 3.h,
            child: Text(
              controller.getExpireTime(),
              style: TextStyle(color: HexColor("64543f"), fontSize: 12.sp),
            )),
        Positioned(
            right: 10,
            top: 10,
            child: Visibility(
                visible: controller.showParentVipBuyMember.value ||
                    controller.showBuyMember.value,
                child: GestureDetector(
                  onTap: () {
                    controller.atOnceOpen();
                  },
                  child: Image(
                      width: 80,
                      image: AssetImage("assets/mine/icon_huiyuanxufei.png")),
                )))
      ],
    );
  }

  _goOpenUI() {
    return Stack(
      children: [
        // Image(
        //   width: double.infinity,
        //   height: 95.h,
        //   fit: BoxFit.fitWidth,
        //   image: AssetImage("assets/mine/img_jiazhang_kaitong_bg.png"),
        // ),
        AspectRatio(
          aspectRatio: 3 / 1, // 请替换为图片实际宽高比（宽/高）
          child: Image(
            width: double.infinity,
            fit: BoxFit.cover, // 保持图片填充并裁剪溢出
            image: AssetImage("assets/mine/img_jiazhang_kaitong_bg.png"),
          ),
        ),
        Positioned(
            right: 30,
            top: 30,
            child: GestureDetector(
                onTap: () {
                  controller.atOnceOpen();
                },
                child: Image(
                  width: 80.w,
                  fit: BoxFit.fitWidth,
                  image: AssetImage("assets/mine/icon_btn_open.png"),
                ))),
      ],
    );
  }

  // _goOpenUI() {
  //   return Stack(
  //     children: [
  //       // Image(
  //       //   width: double.infinity,
  //       //   height: 95.h,
  //       //   fit: BoxFit.fitWidth,
  //       //   image: AssetImage("assets/mine/img_jiazhang_kaitong_bg.png"),
  //       // ),
  //       Container(
  //         height: 120.h,
  //             decoration: BoxDecoration(
  //                 image: DecorationImage(
  //                     image: AssetImage("assets/mine/img_kaitong_bg.png"),
  //                     fit: BoxFit.fill)),
  //       ),
  //       // AspectRatio(
  //       //   aspectRatio: 3 / 1, // 请替换为图片实际宽高比（宽/高）
  //       //   child: Image(
  //       //     width: double.infinity,
  //       //     fit: BoxFit.cover, // 保持图片填充并裁剪溢出
  //       //     image: AssetImage("assets/mine/img_kaitong_bg.png"),
  //       //   ),
  //       // ),
  //       Positioned(
  //           left: 30.w,
  //           top: 40.h,
  //           child: Text('成为超级会员，享超值权益~',style: TextStyle(
  //               fontFamily: "R",
  //               fontSize: 13.sp,
  //               color: HexColor("#F0D6BB")),)),
  //       Positioned(
  //           left: 8.w,
  //           top: 85.h,
  //           child:Container(
  //             width:500.w,
  //             child: Row(
  //               mainAxisAlignment: MainAxisAlignment.start,
  //               children: [
  //                 Image(
  //                   width: 20.w,
  //                   height: 20.h,
  //                   image: AssetImage("assets/mine/icon_wdhuiyuanjia.png"),
  //                 ),
  //                 SizedBox(
  //                   width: 5.w,
  //                 ),
  //                 Container(
  //                   child: Column(
  //                     children: [
  //                      Text('会员价',style: TextStyle(
  //                          fontFamily: "R",
  //                          fontSize: 12.sp,
  //                          color: HexColor("#0F3B2F")),),
  //                       SizedBox(
  //                         height: 2.h,
  //                       ),
  //                     Container(
  //                       decoration: BoxDecoration(
  //                         gradient: LinearGradient(
  //                           begin: Alignment.topLeft,
  //                           end: Alignment.bottomCenter,
  //                           colors: [
  //                             HexColor("#FFF1E3"),
  //                             HexColor('#EEEEEE'),
  //                           ],
  //                         ),
  //                       ),
  //                       child: Text('购买享808折',style: TextStyle(
  //                           fontFamily: "R",
  //                           fontSize: 12.sp,
  //                           color: HexColor("#B4683D")),),
  //                     )
  //                     ],
  //                   ),
  //                 ),
  //                 SizedBox(
  //                   width: 15.w,
  //                 ),
  //                 Image(
  //                   width: 20.w,
  //                   height: 20.h,
  //                   image: AssetImage("assets/mine/icon_wdhuiyuanquan.png"),
  //                 ),
  //                 SizedBox(
  //                   width: 5.w,
  //                 ),
  //                 Container(
  //                   child: Column(
  //                     mainAxisAlignment: MainAxisAlignment.start,
  //                     children: [
  //                       Text('会员券',style: TextStyle(
  //   fontFamily: "R",
  //   fontSize: 12.sp,
  //   color: HexColor("#0F3B2F")),),
  //                       SizedBox(
  //                         height: 2.h,
  //                       ),
  //                       Container(
  //                         decoration: BoxDecoration(
  //                           gradient: LinearGradient(
  //                             begin: Alignment.topLeft,
  //                             end: Alignment.bottomCenter,
  //                             colors: [
  //                               HexColor("#FFF1E3"),
  //                               HexColor('#EEEEEE'),
  //                             ],
  //                           ),
  //                         ),
  //                         child: Text('1年12张',style: TextStyle(
  //                             fontFamily: "R",
  //                             fontSize: 12.sp,
  //                             color: HexColor("#B4683D")),),
  //                       )
  //                     ],
  //                   ),
  //                 ),
  //                 SizedBox(
  //                   width: 15.w,
  //                 ),
  //                 Image(
  //                   width: 20.w,
  //                   height: 20.h,
  //                   image: AssetImage("assets/mine/icon_wdruhuili.png"),
  //                 ),
  //                 SizedBox(
  //                   width: 5.w,
  //                 ),
  //                 Container(
  //                   child: Column(
  //                     mainAxisAlignment: MainAxisAlignment.start,
  //                     children: [
  //                       Text('入会礼',style: TextStyle(
  //                           fontFamily: "R",
  //                           fontSize: 12.sp,
  //                           color: HexColor("#0F3B2F")),),
  //                       SizedBox(
  //                         height: 2.h,
  //                       ),
  //                       Container(
  //                         decoration: BoxDecoration(
  //                           gradient: LinearGradient(
  //                             begin: Alignment.topLeft,
  //                             end: Alignment.bottomCenter,
  //                             colors: [
  //                               HexColor("#FFF1E3"),
  //                               HexColor('#EEEEEE'),
  //                             ],
  //                           ),
  //                         ),
  //                         child: Text('1000元超值礼',style: TextStyle(
  //                             fontFamily: "R",
  //                             fontSize: 12.sp,
  //                             color: HexColor("#B4683D")),),
  //                       )
  //                     ],
  //                   ),
  //                 ),
  //                 SizedBox(
  //                   width: 8.w,
  //                 ),
  //                 Image(
  //                   width: 20.w,
  //                   height: 20.h,
  //                   image: AssetImage("assets/mine/icon_wdgengduo.png"),
  //                 ),
  //               ],
  //             ),
  //           )),
  //       Positioned(
  //           right: 30,
  //           top: 30,
  //           child: GestureDetector(
  //               onTap: () {
  //                 controller.atOnceOpen();
  //               },
  //               child: Image(
  //                 width: 80.w,
  //                 fit: BoxFit.fitWidth,
  //                 image: AssetImage("assets/mine/icon_btn_open.png"),
  //               ))),
  //     ],
  //   );
  // }

  _moneyUI(isShowDetail) {
    return Stack(
      children: [
        Image(
          width: double.infinity,
          height: 106.h,
          image: AssetImage("assets/mine/pic_shouyibeijing.png"),
          fit: BoxFit.fitWidth,
        ),
        Positioned(
            right: 10,
            top: 10,
            child: Visibility(
                visible: isShowDetail,
                child: GestureDetector(
                  onTap: () {
                    controller.showDetails();
                  },
                  child: Row(
                    children: [
                      Text(
                        "收益明细",
                        style: TextStyle(
                            fontFamily: "R",
                            fontSize: 13.sp,
                            color: HexColor("174f3f")),
                      ),
                      Icon(
                        Icons.chevron_right,
                        size: 20.w,
                        color: HexColor("174f3f"),
                      )
                    ],
                  ),
                ))),
        Padding(
          padding: EdgeInsets.all(10.h),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "我的收益",
                style: TextStyle(
                    color: HexColor("174f3f"),
                    fontFamily: "M",
                    fontWeight: FontWeight.bold,
                    fontSize: 14.sp),
              ),
              SizedBox(
                height: 10.h,
              ),
              Row(
                children: [
                  Expanded(
                    flex: 1,
                    child: _moneyItem(controller.creditCount.value, "积分余额"),
                  ),
                  Container(
                    margin: EdgeInsets.only(left: 20.w, right: 20.w),
                    height: 50.h,
                    width: 1.w,
                    color: Color.fromRGBO(181, 181, 181, 0.1),
                  ),
                  Expanded(
                    flex: 1,
                    child: GestureDetector(
                      onTap: () {
                        controller.shoppingMall();
                      },
                      child: _moneyItem(controller.dingBi.value, "鼎币余额"),
                    ),
                  )
                ],
              )
            ],
          ),
        )
      ],
    );
  }

  _moneyItem(text1, text2) {
    return SizedBox(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(text1,
              style: TextStyle(
                  color: HexColor("174f3f"),
                  fontFamily: "M",
                  fontWeight: FontWeight.bold,
                  fontSize: 16.sp)),
          SizedBox(
            height: 5.h,
          ),
          Text(text2,
              style: TextStyle(
                  color: HexColor("174f3f"), fontFamily: "R", fontSize: 14.sp)),
        ],
      ),
    );
  }

  _contentUI() {
    return GridView.builder(
      padding: EdgeInsets.zero,
      shrinkWrap: true,
      physics: NeverScrollableScrollPhysics(),
      itemCount: controller
          .roleClickList[
              controller.roleUseList[controller.currentTabIndex.value]]
          ?.length,
      itemBuilder: (BuildContext context, int index) {
        return _btnUI(
            controller.roleClickList[
                controller.roleUseList[controller.currentTabIndex.value]],
            index,
            "vip",
            context);
      },
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 4,
          mainAxisSpacing: 10.w,
          crossAxisSpacing: 10.w,
          childAspectRatio: 1),
    );
  }

  _tabUI() {
    return SizedBox(
      height: 40.h,
      child: Stack(
        children: [
          ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: controller.roleUseList.length,
              itemExtent: 80.w, // 宽度
              itemBuilder: (BuildContext context, int index) {
                return _itemBuilder(index);
              }),
          AnimatedPositioned(
            duration: Duration(milliseconds: 100),
            top: 30.w,
            left: getItemOffset(controller.currentTabIndex.value).dx,
            child: Image.asset(
              'assets/zx/icon_move.png',
              height: 20.w,
              width: 20.w,
            ),
          ),
        ],
      ),
    );
  }

  _itemBuilder(int index) {
    String name = controller.roleUseList[index];
    return Container(
      alignment: Alignment.center,
      child: InkWell(
        onTap: () {
          controller.changeTab(index);
        },
        child: Container(
          key: controller.keys[index],
          padding: EdgeInsets.all(
            5.w,
          ),
          child: Text(
            name,
            style: TextStyle(
              fontSize:
                  index == controller.currentTabIndex.value ? 15.sp : 14.sp,
              fontWeight: index == controller.currentTabIndex.value
                  ? FontWeight.w500
                  : FontWeight.normal,
              color: index == controller.currentTabIndex.value
                  ? HexColor('333333')
                  : HexColor('5A5A5A'),
            ),
          ),
        ),
      ),
    );
  }

  _topBtnUI() {
    List<Widget> itemUI = [];
    final BaseController baseController = Get.find<BaseController>();
    for (int i = 0; i < controller.topClickList.length; i++) {
      var item = controller.topClickList[i];
      var content = Container(
        width: 80.w,
        decoration: BoxDecoration(
          // borderRadius: BorderRadius.circular(40),
          border: controller.topClickList.length == (i + 1)
              ? Border(
                  right: BorderSide(
                    // 只设置右边框
                    color: HexColor('F0F0F0'), // 边框颜色
                    width: 0,
                    // 边框宽度
                  ),
                )
              : Border(
                  right: BorderSide(
                    // 只设置右边框
                    color: HexColor('#F0F0F0'), // 边框颜色
                    width: 1.0,
                    // 边框宽度
                  ),
                ),
          // color: Colors.primaries[index %
          //     Colors.primaries.length],
        ),
        child: Stack(children: [
          GestureDetector(
            onTap: () {
              if (item["name"] != '我的钱包') {
                controller.topClick(item);
              }
            },
            child: SizedBox(
                child: Center(
              child: Column(
                children: [
                  item["img"]!.isEmpty
                      ? Text(
                          item['name'] == '积分'
                              ? controller.creditCount.value
                              : item['name'] == '鼎币'
                                  ? controller.dingBi.value
                                  : controller.couponCount.value,
                          style: TextStyle(
                              fontSize: 18.sp,
                              color: HexColor("#006738"),
                              fontFamily: "R"))
                      : Image(
                          width: 26.w,
                          height: 26.h,
                          image: AssetImage(item["img"]!)),
                  SizedBox(
                    height: 3.h,
                  ),
                  Text(
                    item["name"]!,
                    style: TextStyle(
                        fontSize: 13.sp,
                        color: HexColor("333333"),
                        fontFamily: "R"),
                  )
                ],
              ),
            )),
          ),
          Obx(() {
            final showRedDot = baseController.showMessageRedDot.value;
            return item["name"] == '信息中心' && showRedDot
                ? Positioned(
                    right: 10.w,
                    top: 0,
                    child: Container(
                      width: 8.w,
                      height: 8.w,
                      decoration: BoxDecoration(
                        color: Colors.red,
                        shape: BoxShape.circle,
                      ),
                    ),
                  )
                : const SizedBox();
          })
        ]),
      );
      itemUI.add(content);
    }
    return itemUI;
  }

  _topRoleItemUI() {
    List<Widget> itemUI = [];
    for (int i = 0; i < controller.roleList.length; i++) {
      var content = controller.roleList[i] == '超级会员'
          ? Container(
              padding: EdgeInsets.symmetric(horizontal: 10, vertical: 1),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(3.w)),
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomCenter,
                  colors: [
                    HexColor('#FFBF6B'),
                    HexColor("#F5AF15"),
                  ],
                ),
              ),
              child: Image(
                width: 48.w,
                height: 17.h,
                image: AssetImage("assets/mine/huiyuan_title.png"),
              ))
          : controller.roleList[i] == '家长会员'
              ? Container(
                  width: 82.w,
                  height: 20.h,
                  decoration: BoxDecoration(
                      image: DecorationImage(
                          image:
                              AssetImage("assets/mine/pic-jiazhanghuiyuan.png"),
                          fit: BoxFit.fill)),
                )
              : Container(
                  padding: EdgeInsets.only(
                      left: 5.w, right: 5.w, top: 2.h, bottom: 2.h),
                  decoration: BoxDecoration(
                    color: HexColor("#009B55"),
                    borderRadius: BorderRadius.all(Radius.circular(2.w)),
                  ),
                  child: Text(
                    controller.roleList[i],
                    style: TextStyle(
                        fontSize: 11.sp,
                        color: HexColor("ffffff"),
                        fontFamily: "R"),
                  ),
                );
      itemUI.add(content);
    }
    return itemUI;
  }

  _btnUI(arr, int index, String type, BuildContext context) {
    // if(arr[index]["name"] == "邀请有礼"){
    //   if(!(controller.showActivityIcon.value && (controller.ifShowInvite == "1" || controller.ifShowInvite == "2"))){
    //     return Container();
    //   }
    // }
    return GestureDetector(
      onTap: () {
        if (type == "utils") {
          controller.utilsClick(arr[index], context);
        } else {
          controller.roleClick(arr[index]);
        }
      },
      child: Column(
        children: [
          Image(
              width: 26.w,
              height: 26.h,
              image: AssetImage(arr[index]["img"].toString())),
          SizedBox(
            height: 8.h,
          ),
          Text(
            textAlign: TextAlign.center,
            arr[index]["name"].toString(),
            style: TextStyle(
                fontSize: 12.sp, color: HexColor("666666"), fontFamily: "R"),
          )
        ],
      ),
    );
  }

  Offset getItemOffset(int index) {
    final RenderBox? renderBox =
        controller.keys[index].currentContext?.findRenderObject() as RenderBox?;
    if (renderBox != null) {
      final Offset position =
          renderBox.localToGlobal(Offset.zero); // 获取全局位置（相对于屏幕）
      controller.bottomPosW = position.dx + renderBox.size.width / 2 - 20.w;
      return Offset(position.dx + renderBox.size.width / 2 - 20.w, position.dy);
    }
    return Offset(controller.bottomPosW, 0);
  }
}
