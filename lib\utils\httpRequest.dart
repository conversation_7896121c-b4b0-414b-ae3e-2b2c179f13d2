import 'dart:collection';
import 'package:bot_toast/bot_toast.dart';
import 'package:dio/dio.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio/io.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';
import 'package:student_end_flutter/common/user_option.dart';
import 'package:student_end_flutter/components/toast_utils.dart';
import 'package:student_end_flutter/utils/app_version_util.dart';
import 'package:tx_im/dx_utils/IMHttpRequest.dart';
import 'package:tx_im/dx_utils/http_config.dart';
import '../bean/base_bean/base_bean_entity.dart';
import '../common/Config.dart';
import 'dart:io';


class HttpUtil {
  static HttpUtil? instance;
  late Dio dio;
  BaseOptions? options;
  CancelToken cancelToken = CancelToken();
  static LinkedHashMap<String, dynamic> heads = LinkedHashMap();
  late String teachModel;

  // 单例模式获取HttpUtil实例
  static HttpUtil? getInstance() {
    instance ??= HttpUtil();
    return instance;
  }

  ///需要学生token的接口传入url
  HttpUtil({url="", sendLoginToken = false}) {

    // 只在用户同意隐私政策后收集设备信息
    if (UserOption.hasAgreedToPrivacyPolicy) {
      heads.putIfAbsent("dx-source", () => "ZHEN_XUAN##PHONE##APP");
      heads.putIfAbsent("temp-dx-source", () => Platform.isAndroid ? "ZHENXUAN##ANDROID##APP" : "ZHENXUAN##IOS##APP");
      heads.putIfAbsent("dx-app-version", () => AppVersionUtil.appVersion ?? '');
    } else {
      // 用户未同意隐私政策时，只使用基本的不涉及个人信息的头信息
      heads.putIfAbsent("dx-source", () => "ZHEN_XUAN##PHONE##APP");
    }
    // heads.putIfAbsent("dx-source", () => "ZHEN_XUAN##STUDENT##APP");
    // heads.putIfAbsent("dx-source", () => "ZHEN_XUAN##PHONE##APP");
    //
    // heads.putIfAbsent("temp-dx-source", () => Platform.isAndroid ? "ZHENXUAN##ANDROID##APP" : "ZHENXUAN##IOS##APP");
    // heads.putIfAbsent("dx-app-version", () => AppVersionUtil.appVersion ?? '');
    if(UserOption.token.isNotEmpty){
      heads.remove(UserOption.tokenKey);
      if(url.isNotEmpty){
        heads.putIfAbsent("Accept", () => "application/json");
        heads.putIfAbsent("Content-Type", () => "application/json");
        sendLoginToken
          ? heads.putIfAbsent(UserOption.tokenKey, () => UserOption.token)
          : heads.putIfAbsent(UserOption.tokenKey, () => UserOption.studentToken);
      }else{
        if(UserOption.payToken.isNotEmpty){
          heads.putIfAbsent(UserOption.tokenKey, () => UserOption.payToken);
        }else{
          heads.putIfAbsent(UserOption.tokenKey, () => UserOption.token);
        }
      }
    }
    if(url==Config.getDynamicGatewayFlag){
      heads.remove(UserOption.tokenKey);
    }
    //token 保持一致
    IMHttpUtil.heads = HttpUtil.heads;
    IMHttpUtil().options?.headers = HttpUtil.heads;
    print(heads.toString());
    print('2222222222222222222222222222222222222222222222222222');
    options = BaseOptions(
      baseUrl: Config.URL,
      connectTimeout: const Duration(milliseconds: 120000),
      receiveTimeout: const Duration(milliseconds: 60000),
      headers: heads,
      contentType: Headers.jsonContentType,
      responseType: ResponseType.json,
    );

    dio = Dio(options);
      // ..interceptors.add(
      //   PrettyDioLogger(
      //     requestHeader: true,
      //     requestBody: true,
      //     filter: (options, args) {
      //       //  return !options.uri.path.contains('posts');
      //       return !args.isResponse || !args.hasUint8ListData;
      //     },
      //   ),
      // );

    // 忽略证书过期报错
    (dio.httpClientAdapter as DefaultHttpClientAdapter).onHttpClientCreate = (client) {
      client.badCertificateCallback = (cert, host, port) => true;
    };
  }

  // 判断是否有网络连接
  static Future<bool> isNetworkAvailable() async {
    if(Platform.isIOS){
      return true;
    }
    try {
      List<ConnectivityResult> resultList = await Connectivity().checkConnectivity();
      ConnectivityResult result = resultList.isNotEmpty ? resultList[0] : ConnectivityResult.none;
      // 如果是手机数据网络或者 Wi-Fi 网络
      if (result == ConnectivityResult.mobile
          || result == ConnectivityResult.wifi
          || result == ConnectivityResult.vpn
          || result == ConnectivityResult.other) {
        return true;
      } else {
        // 如果没有网络连接
        ToastUtil.showShortErrorToast("网络不可用，请检查网络连接");
        return false;
      }
    } catch (e) {
      ToastUtil.showShortErrorToast("网络检查失败，请稍后再试");
      return false;
    }
  }

  // 公共的请求错误处理方法
  void _handleError(DioError e, String url) {
    print("-_handleError-$url--$e");
    String errorMessage = '';
    if (e.response != null && e.response?.statusCode == 401) {
      UserOption.toLoginPage();
      errorMessage = '登录过期，请重新登录';
    } else if (e.type == DioErrorType.connectionTimeout) {
      errorMessage = '连接超时，请刷新重试';
    } else if (e.type == DioErrorType.sendTimeout) {
      errorMessage = '请求超时，请刷新重试';
    } else if (e.type == DioErrorType.receiveTimeout) {
      errorMessage = '响应超时，请刷新重试';
    } else if (e.type == DioErrorType.badResponse) {
      errorMessage = '服务器异常，请刷新重试';
    } else if (e.type == DioErrorType.cancel) {
      errorMessage = '请求取消';
    } else {
      errorMessage = '';
    }
    BotToast.closeAllLoading();
    if (errorMessage.isNotEmpty) {
      BotToast.showText(text: errorMessage);
    }
  }

  // 公共方法：处理响应数据
  static tryResponse(Response? response) {
    if (response == null) {
      return;
    }
    if (response.data == null) {
      response.data = {};
    }
  }

  // 请求数据是否成功
  bool _isRequestSuccessful(Response? response) {
    if (response == null || response.data == null) {
      return false;
    }
    Map responseData = response.data;
    /// 兼容两种数据返回格式
    if (responseData.containsKey('code')) {
      if (response.data['code'] == null) {
        return false;
      }
      if (!response.data['code'].toString().startsWith("2")) {
        if (response.data['code'] == 40011) {
          BotToast.showText(text: "${response.data['message'] ?? "请刷新重试"}");
          Future.delayed(const Duration(seconds: 2), () {
            UserOption.toLoginPage();
          });
          return false;
        } else if (response.data['code'] == 40008 || response.statusCode == 401) {
          Future.delayed(const Duration(seconds: 2), () {
            UserOption.toLoginPage();
          });
          return false;
        }
      }
    } else if (responseData.containsKey('status')) {
      if (response.data['status'] == null) {
        return false;
      }
      if (response.data['status'] != 1) {
          BotToast.showText(text: "${response.data['message'] ?? "请刷新重试"}");
          Future.delayed(const Duration(seconds: 2), () {
            UserOption.toLoginPage();
          });
          return false;
      }
    }
    return true;
  }

  // GET请求
  Future<T?> get<T>(String url, {Map<String, dynamic>? data, Options? options, CancelToken? cancelToken}) async {
    
    bool isConnected = await isNetworkAvailable();
    if (!isConnected) return null;
    try {
      final response = await dio.get(url, queryParameters: data, options: options, cancelToken: cancelToken);
      BotToast.closeAllLoading();
      print("--get--response-$url--$response");
      if (_isRequestSuccessful(response)) {
        BaseBeanEntity? baseBeanEntity = BaseBeanEntity.fromJson(response.data);
        if (baseBeanEntity.code == 20000 || baseBeanEntity.status == 1 ) {
          return baseBeanEntity.data;
        }else{
          BotToast.showText(text: baseBeanEntity.message ?? "服务器返回失败");
        }
      }
    } on DioError catch (e) {
      if(url==Config.getDynamicGatewayFlag){
        HttpConfig.URL = Config.auditURL;
        Config.URL = Config.auditURL;
      }
      _handleError(e, url);
    }
    return null;
  }

  Future<Response?> getNoBaseBean<T>(String url, {Map<String, dynamic>? data, Options? options, CancelToken? cancelToken}) async {
    bool isConnected = await isNetworkAvailable();
    if (!isConnected) return null;
    try {
      final response = await dio.get(url, queryParameters: data, options: options, cancelToken: cancelToken);
      BotToast.closeAllLoading();
      print("--get--response-$url--$response");
      if (_isRequestSuccessful(response)) {
        return response;
      }
    } on DioError catch (e) {
      _handleError(e, url);
    }
    return null;
  }


  // POST请求
  Future<Response?> post(String url, {Map<String, dynamic>? data, Options? options, CancelToken? cancelToken}) async {
    bool isConnected = await isNetworkAvailable();
    if (!isConnected) return null;
    try {
      final response = await dio.post(url, data: data, options: options, cancelToken: cancelToken);
      BotToast.closeAllLoading();
      print("--post--response-$url$response");
      if (_isRequestSuccessful(response)) {
        return response;
      }
    } on DioError catch (e) {
      _handleError(e, url);
    }
    return null;
  }




  // POST请求
  Future<T?> postBaseBean<T> (String url, {Map<String, dynamic>? data, Options? options, CancelToken? cancelToken}) async {
    
    bool isConnected = await isNetworkAvailable();
    if (!isConnected) return null;
    try {
      final response = await dio.post(url, data: data, options: options, cancelToken: cancelToken);
      BotToast.closeAllLoading();
      print("--get--response-$url--$response");
      if (_isRequestSuccessful(response)) {
        BaseBeanEntity? baseBeanEntity = BaseBeanEntity.fromJson(response.data);
        if (baseBeanEntity.code == 20000 || baseBeanEntity.status == 1 ) {
          return baseBeanEntity.data;
        }else{
          BotToast.showText(text: baseBeanEntity.message ?? "服务器返回失败");
        }
      }
    } on DioError catch (e) {
      if(url==Config.getDynamicGatewayFlag){
        HttpConfig.URL = Config.auditURL;
        Config.URL = Config.auditURL;
      }
      _handleError(e, url);
    }
    return null;
  }

  Future<Response?> postYx(String url, { data, Options? options, CancelToken? cancelToken}) async {
    bool isConnected = await isNetworkAvailable();
    if (!isConnected) return null;
    try {
      final response = await dio.post(url, data: data, options: options, cancelToken: cancelToken);
      BotToast.closeAllLoading();
      print("--post--response-$url$response");
      if (_isRequestSuccessful(response)) {
        return response;
      }
    } on DioError catch (e) {
      _handleError(e, url);
    }
    return null;
  }

  // PUT请求
  Future<Response?> put(String url, {Map<String, dynamic>? data, Options? options, CancelToken? cancelToken}) async {
    bool isConnected = await isNetworkAvailable();
    if (!isConnected) return null;

    try {
      final response = await dio.put(url, data: data, options: options, cancelToken: cancelToken);
      BotToast.closeAllLoading();
      print("--put--response-$url$response");
      if (_isRequestSuccessful(response)) {
        return response;
      }
    } on DioError catch (e) {
      _handleError(e, url);
    }
    return null;
  }
  // DELETE请求
  Future<Response?> delete(String url, {Map<String, dynamic>? data, Options? options, CancelToken? cancelToken}) async {
    bool isConnected = await isNetworkAvailable();
    if (!isConnected) return null;

    try {
      final response = await dio.delete(url, queryParameters: data, options: options, cancelToken: cancelToken);
      print("--delete--response-$url$response");
      BotToast.closeAllLoading();
      if (_isRequestSuccessful(response)) {
        return response;
      }
    } on DioError catch (e) {
      _handleError(e, url);
    }
    return null;
  }
  // 下载文件
  Future downloadFile(String urlPath, String savePath) async {
    bool isConnected = await isNetworkAvailable();
    if (!isConnected) return null;

    try {
      final response = await dio.download(urlPath, savePath, onReceiveProgress: (count, total) {
        print("$count $total");
      });
      print('Download success: $response');
    } on DioError catch (e) {
      _handleError(e, urlPath);
    }
  }

  // 取消请求
  void cancelRequests(CancelToken token) {
    token.cancel("Request cancelled");
  }
}

//拼接url
class PathParam {
  static String paramToString(Map<String, String> map) {
    String url = '?';
    map.forEach((key, value) {
      url += '$key=$value&';
    });
    if (url.endsWith('&')) {
      url = url.substring(0, url.length - 1);
    }
    return url;
  }
}
