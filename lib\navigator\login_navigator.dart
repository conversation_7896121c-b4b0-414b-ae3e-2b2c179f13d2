
import 'path_generator.dart';
import 'mini_program_path.dart';

class LoginNavigator {
  const LoginNavigator._();
  /// 修改密码
  /// 示例：Personalcenter/password/PasswordChange?
  static MiniProgramPath passwordChange() {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
        pathComponents: ['Personalcenter', 'password', 'PasswordChange'],
        isAddUserToken: false,
        queryParams: {}
    );
    return MiniProgramPath(path);
  }
}