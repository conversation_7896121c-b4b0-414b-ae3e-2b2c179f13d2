import 'package:bot_toast/bot_toast.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ToastUtil {
  static void showShortErrorToast(text) {
    showToastUI(Icon(
      Icons.error,
      color: Colors.red,
      size: 20.w,
    ), text);
  }

  static void showShortSuccessToast(text) {
    showToastUI(Icon(
      Icons.check_circle,
      color: Colors.green,
      size: 20.w,
    ), text);
  }

  /// 顶部 带icon
  static void showToastUI(icon,text) {
    BotToast.showCustomText(
      align: const Alignment(0, -0.8),
      toastBuilder: (context) =>
          Container(
            padding: EdgeInsets.symmetric(vertical: 10, horizontal: 20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black26,
                  blurRadius: 6.0,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                icon,
                SizedBox(width: 5.w),
                Text(
                  text,
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: 14.sp,
                  ),
                ),
              ],
            ),
          ),
      duration: Duration(seconds: 3), // Toast显示的时长
    );
  }

  /// 底部 只有文字
  static void showToastText(text) {
    BotToast.showCustomText(
      toastBuilder: (context) =>
          Container(
            padding: EdgeInsets.symmetric(vertical: 10, horizontal: 20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black26,
                  blurRadius: 6.0,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Text(
                  text,
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: 14.sp,
                  ),
                ),
              ],
            ),
          ),
      duration: Duration(seconds: 3), // Toast显示的时长
    );
  }

  // 显示加载弹框
  static void showLoadingDialog({text = "加载中..."}) {
    print('vvvvvvvvvvvvvvvvvvvvvvv333333333333333333333333333');
    BotToast.showCustomLoading(
      toastBuilder: (cancelFunc) {
        return Container(
          padding: EdgeInsets.symmetric(vertical: 15,horizontal: 40),
          constraints: BoxConstraints(minWidth: 100, minHeight: 100), // 添加约束
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.6),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
              SizedBox(height: 10),
              Text(
                text,
                style: TextStyle(color: Colors.white,fontFamily: "R",fontSize: 14.sp),
              ),
            ],
          ),
        );
      },
      allowClick: false, // 是否允许点击背景关闭
    );
  }

  // 关闭加载弹框
  static void closeLoadingDialog() {
    BotToast.closeAllLoading();
  }
}