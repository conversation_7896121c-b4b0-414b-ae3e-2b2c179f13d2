import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:student_end_flutter/common/CommonUtil.dart';
import 'package:student_end_flutter/res/colors.dart';
import 'package:student_end_flutter/utils/android_ios_plugin.dart';
import 'dart:async';
import '../common/sensors_analytics_option.dart';
import '../common/ne_user_option.dart';
import '../utils/ne_metting_utils.dart';
import '../utils/sharedPreferences_util.dart';

class ActivityPopDialog extends StatefulWidget {
  String url_path;
  String imageUrl;
  String activityId;
  String color;
  int popupInterval; // 弹窗显示时间（秒）
  final Function(bool isChecked) onClickSure;
  final Function(bool isChecked) onClickClose;
  // url_path
  ActivityPopDialog({
    required this.url_path,
    required this.activityId,
    required this.imageUrl,
    required this.color,
    required this.popupInterval,
    required this.onClickSure,
    required this.onClickClose,
  });

  @override
  _ActivityPopDialogState createState() => _ActivityPopDialogState();
}

class _ActivityPopDialogState extends State<ActivityPopDialog> {
  Timer? _autoCloseTimer;

  @override
  void initState() {
    super.initState();
    // 启动自动关闭定时器
    if (widget.popupInterval > 0) {
      _autoCloseTimer = Timer(Duration(seconds: widget.popupInterval), () {
        if (mounted) {
          Navigator.pop(context);
          widget.onClickClose(false);
        }
      });
    }
  }

  @override
  void dispose() {
    _autoCloseTimer?.cancel();
    super.dispose();
  }

  bool _isChecked = false; // 复选框状态变量
  @override
  Widget build(BuildContext context) {
    double screenHeight = MediaQuery.of(context).size.height;
    double screenWidth = MediaQuery.of(context).size.width;
    return PopScope(
      canPop: false,
        child: GestureDetector(
        onTap: () {
          Navigator.pop(context);
          widget.onClickSure(_isChecked);
          TrackingUtils.trackEvent(
              'PopupClick', {'common_fields': '弹框确认按钮点击', 'avitcity_id': widget.activityId,'goods_id':'','banner_id':'','url_path': widget.url_path});
        },
          child: Container(
            color: Colors.transparent,
            width: screenWidth,
            height: screenHeight,
            child: Container(
              alignment: Alignment.center,
              child: SingleChildScrollView(
                child: Container(
                  width: screenWidth * 0.9,
                  margin: EdgeInsets.only(left: 20.w, right: 20.w),
                  padding:
                  EdgeInsets.only(left: 6.w, right: 6.w, bottom: 20.h, top: 20.h),
                  child: Column(
                    children: [
                      Stack(
                        children: [
                          Container(
                            margin: EdgeInsets.only(top: 40.h),
                            child: ClipRRect(
                              borderRadius: BorderRadius.all(Radius.circular(10)),
                              child: LayoutBuilder(
                                builder: (context, constraints) {
                                  return CachedNetworkImage(
                                    fit: BoxFit.contain,
                                    width: constraints.maxWidth,
                                    imageUrl: widget.imageUrl,
                                    placeholder: (context, url) => Center(
                                      child: CircularProgressIndicator(),
                                    ),
                                    errorWidget: (context, url, error) => Container(
                                      color: Colors.grey[200],
                                      child: Center(
                                        child: Icon(Icons.error_outline, color: Colors.grey),
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),
                          Positioned(
                            top: 0.h,
                            right: 0.w,
                            child: GestureDetector(
                              onTap: () {
                                Navigator.pop(context);
                                widget.onClickClose(_isChecked);
                              },
                              child: Image.asset(
                                color: Colors.white,
                                'assets/zx/close.png',
                                width: 30.w,
                                height: 30.w,
                              ),
                            ),
                          )
                        ],
                      ),
                      const SizedBox(height: 10),
                      TextButton(
                        onPressed: () => {Navigator.pop(context), widget.onClickSure(_isChecked)
                          ,TrackingUtils.trackEvent(
                              'PopupClick', {'common_fields': '弹框确认按钮点击', 'avitcity_id': widget.activityId,'goods_id':'','banner_id':'','url_path': widget.url_path})},
                        style: TextButton.styleFrom(
                          backgroundColor: HexColor(widget.color.substring(1)),
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20),
                          ),
                          minimumSize: const Size(180, 40),
                        ),
                        child: const Text('确认'),
                      ),
                      const SizedBox(height: 5),
                      Container(
                        alignment: Alignment.center,
                        height: 20.h,
                        width: double.infinity,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            GestureDetector(
                              onTap: () {
                                TrackingUtils.trackEvent(
                                    'PopupNeverShowClick', {
                                  'common_fields': '选择今日不再提醒',
                                  'avitcity_id': widget.activityId,'goods_id':'','banner_id':'','url_path': widget.url_path
                                });
                                setState(() {
                                  _isChecked = !_isChecked;
                                });
                              },
                              child: Image(
                                  width: 20.w,
                                  height: 20.w,
                                  color: Colors.white,
                                  image: AssetImage(_isChecked
                                      ? "assets/login/icon_chose.png"
                                      : "assets/login/icon_normal.png")),
                            ),
                            SizedBox(
                              width: 7.w,
                            ),
                            Text(
                              '今日不再提醒',
                              style: TextStyle(
                                fontFamily: "R",
                                fontSize: 15.sp,
                                color: Colors.white,
                                decoration: TextDecoration.none,
                                fontWeight: FontWeight.normal,
                              ),
                              maxLines: 1,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),

    );
  }
}
