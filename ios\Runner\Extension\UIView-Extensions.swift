//
//  UIView-Extensions.swift
//  beans_dbzj_app_ios
//
//  Created by yj9527 on 2022/2/9.
//

import Foundation
import HandyJSON
import QMUIKit
import SwiftyJSON
import UIKit
import Toast_Swift

extension UIView {
    func addCorner(_ cornerRadius: CGFloat) {
        self.layer.cornerRadius = cornerRadius
        self.layer.masksToBounds = true
        
    }
}


extension UIView {
    @IBInspectable
    var radius: CGFloat {
        get {
            layer.cornerRadius
        }
        set {
            clipsToBounds = true
            layer.cornerRadius = newValue
        }
    }

    @IBInspectable
    var borderWidth: CGFloat {
        get {
            layer.borderWidth
        }
        set {
            layer.borderWidth = newValue
        }
    }

    //  边线颜色
    @IBInspectable var borderColor: UIColor {
        get {
            return UIColor(cgColor: layer.borderColor!)
        } set {
            layer.borderColor = newValue.cgColor
        }
    }

}


extension UIView {
    /// x
    var x: CGFloat {
        get { return frame.origin.x }
        set(newValue) {
            var tempFrame: CGRect = frame
            tempFrame.origin.x = newValue
            frame = tempFrame
        }
    }

    var y: CGFloat {
        get { return frame.origin.y }
        set(newValue) {
            var tempFrame: CGRect = frame
            tempFrame.origin.y = newValue
            frame = tempFrame
        }
    }


    /// height
    var height: CGFloat {
        get { return frame.size.height }
        set(newValue) {
            var tempFrame: CGRect = frame
            tempFrame.size.height = newValue
            frame = tempFrame
        }
    }

    /// width
    var width: CGFloat {
        get { return frame.size.width }
        set(newValue) {
            var tempFrame: CGRect = frame
            tempFrame.size.width = newValue
            frame = tempFrame
        }
    }


    /// centerX
    var centerX: CGFloat {
        get { return center.x }
        set(newValue) {
            var tempCenter: CGPoint = center
            tempCenter.x = newValue
            center = tempCenter
        }
    }

    /// centerY
    var centerY: CGFloat {
        get { return center.y }
        set(newValue) {
            var tempCenter: CGPoint = center
            tempCenter.y = newValue
            center = tempCenter
        }
    }
    
    // MARK: - maxX

    var maxX: CGFloat {
        return frame.maxX
    }

    // MARK: - maxY

    var maxY: CGFloat {
        return frame.maxY
    }

    // MARK: - minY

    var minY: CGFloat {
        return frame.minY
    }

    var bottom: CGFloat {
        return frame.size.height + frame.origin.y
    }

    var right: CGFloat {
        return frame.size.width + frame.origin.x
    }
}

extension UIView {
    
    func l_showToastMessage(with message: String, time: TimeInterval = 2.0, postion: ToastPosition = .center) {
        if message.count != 0 {
            makeToast(message, duration: time, position: postion)
        }
    }
}

extension UIView {
    // 添加圆角
    func viewWithCorner(corners: UIRectCorner, cornerRadii: CGSize) {
        let maskPath = UIBezierPath(roundedRect: self.bounds, byRoundingCorners: corners, cornerRadii: cornerRadii)
        let maskLayer = CAShapeLayer()
        maskLayer.frame = self.bounds
        maskLayer.path = maskPath.cgPath
        self.layer.mask = maskLayer
    }
    
    func normalCorner(cornerRadius: CGFloat) {
        self.layer.cornerRadius = cornerRadius
        self.layer.masksToBounds = true
    }
    
}

extension UIView {
    
    class func creatWith(bgColor: UIColor,
                         radius: CGFloat = 0.0,
                         borderColor: UIColor = UIColor.clear,
                         borderWidth: CGFloat = 0.0) -> UIView {
        let view = UIView.init()
        view.backgroundColor = bgColor
        view.layer.cornerRadius = radius
        view.layer.masksToBounds = true
        view.layer.borderColor = borderColor.cgColor
        view.layer.borderWidth = borderWidth
        
        return view
    }
    
    func setHorizontalGradient(colorOne: UIColor, colorTwo: UIColor) {
        let gradientLayer = CAGradientLayer()
        gradientLayer.frame = bounds
        gradientLayer.colors = [colorOne.cgColor, colorTwo.cgColor]
        gradientLayer.startPoint = CGPoint(x: 0.5, y: 0)
        gradientLayer.endPoint = CGPoint(x: 0.5, y: 1.0)
        layer.insertSublayer(gradientLayer, at: 0)
    }
}
extension UIDevice {
    static var isLandscape: Bool {
        return UIDevice.current.orientation.isLandscape
    }
}
extension UIBezierPath {
    func addLine(from: CGPoint, to otherPoint: CGPoint) {
        move(to: from)
        addLine(to: otherPoint)
    }
}

extension UIView {
    func animateLine(startCenter: CGPoint, endCenter: CGPoint) -> CGPath{
        // 创建贝塞尔曲线作为虚线的路径
        let path = UIBezierPath()
        path.addLine(from: startCenter, to: endCenter)
        return path.cgPath
    }
}

// 扩展 CAShapeLayer 来添加一个创建动画的方法
extension CAShapeLayer {
    func addStrokeEndAnimation(fromValue: Double, toValue: Double, duration: CFTimeInterval, timingFunctionName: CAMediaTimingFunctionName) {
        let animation = CABasicAnimation(keyPath: "strokeEnd")
        animation.fromValue = fromValue
        animation.toValue = toValue
        animation.duration = duration
        animation.timingFunction = CAMediaTimingFunction(name: timingFunctionName)
        self.add(animation, forKey: "strokeEndAnimation")
    }
    
    func configureMicrophoneShapeLayer(strokeColor: UIColor, lineWidth: CGFloat, lineDashPattern: [NSNumber]?) {
        self.strokeColor = strokeColor.cgColor
        self.lineWidth = lineWidth
        self.lineDashPattern = lineDashPattern
    }
    
}
