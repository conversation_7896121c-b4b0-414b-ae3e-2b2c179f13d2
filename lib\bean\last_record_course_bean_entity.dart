import 'package:student_end_flutter/generated/json/base/json_field.dart';
import 'package:student_end_flutter/generated/json/last_record_course_bean_entity.g.dart';
import 'dart:convert';
export 'package:student_end_flutter/generated/json/last_record_course_bean_entity.g.dart';

@JsonSerializable()
class LastRecordCourseBeanEntity {
	late String id = '';
	late String userId = '';
	late String studentCode = '';
	late String studentName = '';
	late String courseId = '';
	late String courseName = '';
	late int courseType = 0;
	late String lastStudyTime = '';
	late String createdTime = '';
	late String updatedTime = '';
	late int refundStatus = 0;

	LastRecordCourseBeanEntity();

	factory LastRecordCourseBeanEntity.fromJson(Map<String, dynamic> json) => $LastRecordCourseBeanEntityFromJson(json);

	Map<String, dynamic> toJson() => $LastRecordCourseBeanEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}