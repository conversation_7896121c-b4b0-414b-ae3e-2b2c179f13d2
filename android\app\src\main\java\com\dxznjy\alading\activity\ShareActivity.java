package com.dxznjy.alading.activity;

import androidx.appcompat.app.AppCompatActivity;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.util.Log;

import com.dxznjy.alading.DXApplication;
import com.dxznjy.alading.MainActivity;
import com.dxznjy.alading.R;
import com.dxznjy.alading.common.Constants;
import com.dxznjy.alading.entity.ToFlutterPage;
import com.dxznjy.alading.widget.MySplashView;
import com.google.gson.Gson;
import com.tekartik.sqflite.Constant;
import com.vondear.rxtool.RxSPTool;

import java.util.concurrent.TimeUnit;

import io.dcloud.feature.sdk.DCUniMPSDK;
import io.dcloud.feature.unimp.config.UniMPOpenConfiguration;
import io.flutter.plugin.common.MethodChannel;
import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Action;
import io.reactivex.functions.Consumer;

public class ShareActivity extends AppCompatActivity {
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_share);
        Uri uri = getIntent().getData();
        if (uri != null) {
            startActivity(new Intent(ShareActivity.this, MainActivity.class));
            String query = uri.getQuery();
            //获取分享指定参数值
            String methodChannelName="androidIosBackMeeting";
            String json = "{\"path\":\"" + query + "\"}";
            RxSPTool.putString(this, Constants.SHARE_PATH,json);
            finish();
        }
    }
}