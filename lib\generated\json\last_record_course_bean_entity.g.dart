import 'package:student_end_flutter/generated/json/base/json_convert_content.dart';
import 'package:student_end_flutter/bean/last_record_course_bean_entity.dart';

LastRecordCourseBeanEntity $LastRecordCourseBeanEntityFromJson(
    Map<String, dynamic> json) {
  final LastRecordCourseBeanEntity lastRecordCourseBeanEntity = LastRecordCourseBeanEntity();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    lastRecordCourseBeanEntity.id = id;
  }
  final String? userId = jsonConvert.convert<String>(json['userId']);
  if (userId != null) {
    lastRecordCourseBeanEntity.userId = userId;
  }
  final String? studentCode = jsonConvert.convert<String>(json['studentCode']);
  if (studentCode != null) {
    lastRecordCourseBeanEntity.studentCode = studentCode;
  }
  final String? studentName = jsonConvert.convert<String>(json['studentName']);
  if (studentName != null) {
    lastRecordCourseBeanEntity.studentName = studentName;
  }
  final String? courseId = jsonConvert.convert<String>(json['courseId']);
  if (courseId != null) {
    lastRecordCourseBeanEntity.courseId = courseId;
  }
  final String? courseName = jsonConvert.convert<String>(json['courseName']);
  if (courseName != null) {
    lastRecordCourseBeanEntity.courseName = courseName;
  }
  final int? courseType = jsonConvert.convert<int>(json['courseType']);
  if (courseType != null) {
    lastRecordCourseBeanEntity.courseType = courseType;
  }
  final String? lastStudyTime = jsonConvert.convert<String>(
      json['lastStudyTime']);
  if (lastStudyTime != null) {
    lastRecordCourseBeanEntity.lastStudyTime = lastStudyTime;
  }
  final String? createdTime = jsonConvert.convert<String>(json['createdTime']);
  if (createdTime != null) {
    lastRecordCourseBeanEntity.createdTime = createdTime;
  }
  final String? updatedTime = jsonConvert.convert<String>(json['updatedTime']);
  if (updatedTime != null) {
    lastRecordCourseBeanEntity.updatedTime = updatedTime;
  }
  final int? refundStatus = jsonConvert.convert<int>(json['refundStatus']);
  if (refundStatus != null) {
    lastRecordCourseBeanEntity.refundStatus = refundStatus;
  }
  return lastRecordCourseBeanEntity;
}

Map<String, dynamic> $LastRecordCourseBeanEntityToJson(
    LastRecordCourseBeanEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['userId'] = entity.userId;
  data['studentCode'] = entity.studentCode;
  data['studentName'] = entity.studentName;
  data['courseId'] = entity.courseId;
  data['courseName'] = entity.courseName;
  data['courseType'] = entity.courseType;
  data['lastStudyTime'] = entity.lastStudyTime;
  data['createdTime'] = entity.createdTime;
  data['updatedTime'] = entity.updatedTime;
  data['refundStatus'] = entity.refundStatus;
  return data;
}

extension LastRecordCourseBeanEntityExtension on LastRecordCourseBeanEntity {
  LastRecordCourseBeanEntity copyWith({
    String? id,
    String? userId,
    String? studentCode,
    String? studentName,
    String? courseId,
    String? courseName,
    int? courseType,
    String? lastStudyTime,
    String? createdTime,
    String? updatedTime,
    int? refundStatus,
  }) {
    return LastRecordCourseBeanEntity()
      ..id = id ?? this.id
      ..userId = userId ?? this.userId
      ..studentCode = studentCode ?? this.studentCode
      ..studentName = studentName ?? this.studentName
      ..courseId = courseId ?? this.courseId
      ..courseName = courseName ?? this.courseName
      ..courseType = courseType ?? this.courseType
      ..lastStudyTime = lastStudyTime ?? this.lastStudyTime
      ..createdTime = createdTime ?? this.createdTime
      ..updatedTime = updatedTime ?? this.updatedTime
      ..refundStatus = refundStatus ?? this.refundStatus;
  }
}