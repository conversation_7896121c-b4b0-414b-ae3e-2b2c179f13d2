import 'package:calendar_date_picker2/calendar_date_picker2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../common/CommonUtil.dart';

/// 更改日期回调方法类型
typedef DateCallback = ValueChanged<List<DateTime>>;

/// 操作按钮类型
enum DateDialogBtnType {
  cancel,
  confirm
}

/// 选择日期弹框
class SelectDateDialog extends StatefulWidget {
  /// 选择日期弹框
  /// [onChange] - 选择日期时的回调（用于实时响应日期选择）
  /// [onConfirm] - 点击确认按钮时的回调（用于确认选择）
  /// [onCancel] - 点击取消按钮回调
  const SelectDateDialog({
    super.key,
    required this.onChange,
    this.onConfirm,
    required this.onCancel,
    this.initDate,
    this.confirmBtnTitle = '确认',
    this.cancelBtnTitle = '取消筛选',
    this.canSelectTime = false
  });

  final DateCallback onChange;
  
  final DateCallback? onConfirm;

  final VoidCallback onCancel;

  final DateTime? initDate;

  final String confirmBtnTitle;

  final String cancelBtnTitle;

  final bool canSelectTime;

  @override
  State<SelectDateDialog> createState() => _SelectDateDialogState();
}

class _SelectDateDialogState extends State<SelectDateDialog> {
  final CalendarDatePicker2Config config = CalendarDatePicker2Config(
    calendarType: CalendarDatePicker2Type.single
  );

  late List<DateTime> _selectedDates;
  late TimeOfDay _selectedTime;

  @override
  void initState() {
    super.initState();
    final now = DateTime.now();
    // 默认选中今天
    _selectedDates = [widget.initDate ?? now];
    // 默认选中当前时间
    _selectedTime = TimeOfDay.fromDateTime(widget.initDate ?? now);
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    return Align(
      alignment: Alignment.bottomCenter,
      child: Material(
        child: SizedBox(
          height: screenSize.height / 2,
          child: Column(
            children: [
              if (widget.canSelectTime)
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.h),
                  child: InkWell(
                    onTap: _showTimePicker,
                    child: Container(
                      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: HexColor('eeeeee'),
                          width: 1.w
                        ),
                        borderRadius: BorderRadius.circular(8.w),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            '选择时间：${_selectedTime.format(context)}:${_selectedDates.isNotEmpty ? _selectedDates.first.second.toString().padLeft(2, '0') : '00'}',
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: HexColor('333333'),
                            ),
                          ),
                          Icon(
                            Icons.access_time,
                            size: 18.w,
                            color: HexColor('666666'),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              Expanded(
                child: CalendarDatePicker2(
                  config: config,
                  value: _selectedDates,
                  onValueChanged: _handleDateSelection,
                ),
              ),
              // SizedBox(height: 10.h,),
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 20),
                child: Row(
                  children: [
                    Spacer(),
                    _buildControllerBtn(DateDialogBtnType.confirm),
                    SizedBox(width: 10.w,),
                    _buildControllerBtn(DateDialogBtnType.cancel)
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  /// 构建控制按钮
  Widget _buildControllerBtn(DateDialogBtnType btnType) {
    switch(btnType) {
      case DateDialogBtnType.confirm: // 确认按钮
        return InkWell(
          onTap: _confirmSelection,
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
            decoration: BoxDecoration(
              border: Border.all(
                color: HexColor('339378'),
                width: 1.w
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(widget.confirmBtnTitle),
          ),
        );
      case DateDialogBtnType.cancel: // 取消按钮
        return InkWell(
          onTap: _cancelSelection,
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
            decoration: BoxDecoration(
              border: Border.all(
                  color: HexColor('838b93'),
                  width: 1.w
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(widget.cancelBtnTitle),
          ),
        );
    }
  }

  /// 显示时间选择器
  Future<void> _showTimePicker() async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: _selectedTime,
      builder: (context, child) {
        return MediaQuery(
          data: MediaQuery.of(context).copyWith(alwaysUse24HourFormat: true),
          child: child!,
        );
      },
    );
    if (picked != null && picked != _selectedTime) {
      setState(() {
        _selectedTime = picked;
      });
      _updateDateWithTime();
    }
  }

  /// 更新日期，加入选择的时间
  void _updateDateWithTime() {
    if (_selectedDates.isEmpty) return;
    
    final DateTime currentDate = _selectedDates.first;
    final int seconds = currentDate.hour == _selectedTime.hour && 
                       currentDate.minute == _selectedTime.minute ? 
                       currentDate.second : 0;
                       
    final DateTime newDateTime = DateTime(
      currentDate.year,
      currentDate.month,
      currentDate.day,
      _selectedTime.hour,
      _selectedTime.minute,
      seconds
    );
    
    setState(() {
      _selectedDates = [newDateTime];
    });
  }

  /// 确认日期选择
  void _confirmSelection() {
    if (_selectedDates.isNotEmpty) {
      if (widget.canSelectTime) {
        _updateDateWithTime();
      }
      // 如果提供了onConfirm回调，则使用onConfirm，否则使用onChange
      if (widget.onConfirm != null) {
        widget.onConfirm!(_selectedDates);
      } else {
        widget.onChange(_selectedDates);
      }
    }
    Navigator.pop(context);
  }

  /// 取消选择
  void _cancelSelection() {
    widget.onCancel();
    Navigator.pop(context);
  }

  // 处理选择的日期更改
  void _handleDateSelection(List<DateTime> dates) {
    setState(() {
      _selectedDates = dates;
    });
    if(_selectedDates.isNotEmpty) {
      if (widget.canSelectTime) {
        _updateDateWithTime();
      }
      // 仅通知日期选择变更，但不执行筛选操作
      widget.onChange(_selectedDates);
    }
  }
}
