package com.dxznjy.alading.adapter;


import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentStatePagerAdapter;

import com.dxznjy.alading.fragment.BaseFragment;

import java.util.List;



public class HomePageAdapter extends FragmentStatePagerAdapter {
    private final List<BaseFragment> mFragments;
    private final String[] titles;

    public HomePageAdapter(FragmentManager fm, List<BaseFragment> fragments, String[] titles) {
        super(fm);
        this.titles = titles;
        this.mFragments = fragments;
    }

    @Override
    public int getCount() {
        return mFragments.size();
    }

    @Override
    public CharSequence getPageTitle(int position) {
        return titles[position];
    }

    @Override
    public Fragment getItem(int position) {
        return mFragments.get(position);
    }

}