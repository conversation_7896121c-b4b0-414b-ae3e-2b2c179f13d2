package com.dxznjy.alading.adapter.givelessons;

import android.content.Context;
import android.text.TextUtils;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dxznjy.alading.R;
import com.dxznjy.alading.response.VideoCourseDataResponse;

import java.util.List;


public class VideoGiveLessonsAdapter extends BaseQuickAdapter<VideoCourseDataResponse.VideoCourseData, BaseViewHolder> {
    public VideoGiveLessonsAdapter(Context context, List<VideoCourseDataResponse.VideoCourseData> data) {
        super(R.layout.adapter_video_give_lessons, data);
        this.mContext = context;
    }
    @Override
    protected void convert(final BaseViewHolder helper, VideoCourseDataResponse.VideoCourseData item) {
        helper.addOnClickListener(R.id.tv_gotostudy);
        helper.setText(R.id.tv_coursename,item.getCourseName());
        helper.setText(R.id.tv_studentname,item.getStudentName());
        if (TextUtils.isEmpty(item.getLastStudyTime())){
            helper.setText(R.id.tv_laststudytime,"上次学习：-");
            helper.setText(R.id.tv_gotostudy,"开始学习");
        }else{
            helper.setText(R.id.tv_laststudytime,"上次学习："+item.getLastStudyTime());
            helper.setText(R.id.tv_gotostudy,"继续学习");
        }
        helper.addOnClickListener(R.id.tv_gotostudy);
    }
}
