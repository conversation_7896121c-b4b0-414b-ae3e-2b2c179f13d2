import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class DialogUtils {
  static void showSimpleDialog({
    required String content,
    VoidCallback? leftButtonAction,
    VoidCallback? rightButtonAction,
  }) {
    Get.dialog(
      CupertinoAlertDialog(
        content: Padding(
          padding: EdgeInsets.symmetric(vertical: 16.h), // 增加上下间距
          child: Text(content, style: TextStyle(fontSize: 18.sp,fontFamily: "M")),
        ),
        actions: [
          CupertinoDialogAction(
            onPressed: () {
              Get.back(); // 关闭弹窗
              leftButtonAction?.call();
            },
            child: Text(
              "取消",
              style: TextStyle(fontSize: 16.sp,  fontFamily: "R",color: Colors.black),
            ),
          ),
          CupertinoDialogAction(
            onPressed: () {
              Get.back(); // 关闭弹窗
              rightButtonAction?.call();
            },
            child: Text(
              "确定",
              style: TextStyle(fontSize: 16.sp,  fontFamily: "R",color: Colors.black),
            ),
          ),
        ],
      ),
      barrierColor: Colors.black26,
      barrierDismissible: false, // 点击外部不可关闭
    );
  }

  // 显示消息提示框（Snackbar）
  static void showSnackbar({
    required String title,
    required String message,
    Duration duration = const Duration(seconds: 2),
    SnackPosition snackPosition = SnackPosition.BOTTOM,
  }) {
    Get.snackbar(
      title,
      message,
      duration: duration,
      snackPosition: snackPosition,
      backgroundColor: Colors.black.withOpacity(0.7),
      colorText: Colors.white,
      margin: EdgeInsets.all(16),
      borderRadius: 8,
      icon: Icon(Icons.info_outline, color: Colors.white),
    );
  }
}