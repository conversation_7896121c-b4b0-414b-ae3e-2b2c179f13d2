class Config {
  static const int REQUEST_FIRST = 1;
  static const int REQUEST_SECOND = 2;
  static const int REQUEST_THIRD = 3;
  static const int REQUEST_FOURTH = 4;
  static const int REQUEST_FIFTH = 5;
  static const int REQUEST_SIXTH = 6;
  static const String ZXMiniOriginalId = "gh_d43ada85807b"; //甄选原始id
  static const String shareAppUrl = "https://zx-share.dxznjy.com/#/home/<USER>/";
  // static String formalURL = "http://zxmeet01.ngrok.dxznjy.com";
  // static String auditURL = "http://test139.ngrok.dxznjy.com"; //审核使用的url
  // static String auditURL = "https://gateway.dxznjy.com"; //审核使用的url
  // static String formalURL = "https://gateway.dxznjy.com";
  // static String auditURL = "https://dxcs179.ngrok.dxznjy.com"; //审核使用的url
  // static String formalURL = "https://test139.ngrok.dxznjy.com";
  //  static String formalURL = "https://wdbscrm.ngrok.dxznjy.com";
  // static String auditURL = "https://wdbscrm.ngrok.dxznjy.com";

  // static String formalURL = "https://test2-k8s.ngrok.dxznjy.com";
  // static String auditURL = "https://test2-k8s.ngrok.dxznjy.com";

  static String auditURL = "https://dxcs179.ngrok.dxznjy.com";
  static String formalURL = "https://dxcs179.ngrok.dxznjy.com";


  static String URL = formalURL;
  static String getDynamicGatewayFlag =
      "$URL/zx/common/getDynamicGatewayFlag"; //进入app获取ip
  static String openScreen = "$URL/zx/wap/homePage/openScreen";
  // /wap/homePage/openScreen
  static String bannerList = "$URL/zx/wap/layout/banner/list"; //首页banner
  // wap/homePage/homelnfo
  static String homelnfo = "$URL/zx/wap/homePage/homeInfo"; //首页信息改版
  static String bannerNewList = "$URL/zx/wap/homePage/banner/list"; //首页信息改版
  static String porcelainInfo = "$URL/zx/wap/homePage/porcelain"; //首页信息改版
  static String specialList = "$URL/zx/wap/homePage/special/list"; //首页信息改版
  static String categoryGoodsList =
      "$URL/zx/wap/homePage/categoryGoods/list"; //首页信息改版
  // /wap/homePage/categoryGoods/list
  // /wap/homePage/special/list
  // /wap/homePage/porcelain
  // /wap/homePage/banner/list
  static String homeTypeList =
      "$URL/zx/operation/goods/recommendation/category/list"; //首页 热门推荐等F
  static String activityList =
      "$URL/zx/wap/layout/popup/list/query"; //首页 获取活动弹窗数据
  // http://sxl.ngrok,dxznjy.com/zx /wap/credit/hasSignedlnToday
  static String hasSignedInToday = "$URL/zx/wap/credit/hasSignedInToday"; //是否签到
  static String goodsEnableList =
      "$URL/zx/wap/goods/recommendation/enable/list"; //首页商品
  static String getQrcodeByCode =
      "$URL/scrm/qywechat/getQrcodeByCode"; //首页添加教练码
  static String zxGoodCourse =
      "$URL/zx/wap/homePage/zxGoodCourse/list"; //甄选好课列表

  static String getUserInfoByUserId = "$URL/zx/user/getUserInfoByUserId"; //分享返回
  static String getUserInfo = "$URL/zx/user/userInfoNew"; //获取用户信息
  static String loginApi = "$URL/new/security/v2/login/zx/app"; //登录
  static String checkRegister = "$URL/zx/common/checkRegister";
  static String checkZxAppRegister = "$URL/zx/common/checkZxAppRegister";
  static String sendSms = "$URL/new/security/sms/";
  static String getSlide = "$URL/new/security/captcha/image/slide"; //获取滑动块验证图片
  static String checkSlide = "$URL/new/security/captcha/check/slide"; //滑动块验证
  static String zxCoursesList =
      "$URL/zx/wap/homePage/zxGoodCourse/goodsList"; // 甄选课程列表
  static String queryMerchantAndStudentRecord =
      "$URL/znyy/course/queryMerchantAndStudentRecord"; // 查询记录的门店和学生信息
  static String getMerchantAndStudentList =
      "$URL/znyy/course/getMerchantAndStudentList"; // 获取学员列表
  // /wap/homePage/zxGoodCourse/list
  static String getMemberStudentList =
      "$URL/znyy/course/getMemberStudentList"; // 获取学员进度
  // znyy/course/getMemberStudentList
  static String saveMerchantAndStudentList =
      "$URL/znyy/course/saveMerchantAndStudentList"; // 记录选择的门店和学生信息
  static String getStudentToken =
      "$URL/new/security/v2/login/student/member/token"; //获取学生token
  static String getLatestDeliverCourse =
      "$URL/zx/student/course/getLatestDeliverCourse"; // 获取距当前时间最近的交付课
  static String getLatestRecordCourse =
      "$URL/zx/student/course/getLatestRecordCourse"; // 查询上次学习的录播课
  static String getCourseMeetingInfo =
      "$URL/zx/student/course/getCourseMeetingInfo"; // 交付课上课获取会议id
  static String getReviewMeetingInfo =
      "$URL/zx/student/course/getReviewMeetingInfo"; // 复习课上课获取会议id
  static String getQueryMemberInMeeting =
      "$URL/media/meeting/queryMemberInMeeting"; // 查询成员是否在会议中
  //我的
  static String getCreditCount = "$URL/zx/wap/credit/getCreditCount"; // 积分余额
  static String getDingBi = "$URL/zx/wap/invite/getDingBi"; // 鼎币余额
  // /wap/coupon/user/getCount
  static String getCoupon = "$URL/zx/wap/coupon/user/getCount"; //优惠劵
  static String infoUserCode = "$URL/mps/user/info/user/code"; // 判断是否实名认证
  static String getSchoolMerchantByName =
      "$URL/znyy/school/recharge/getSchoolMerchantByName"; // 判断是否是门店
  static String listLoginCode = "$URL/mps/account/list/login/code"; // 获取账号信息
  static String getPayToken =
      "$URL/new/security/login/school/member/token"; // 时长充值获取的token
  static String getNewsStore =
      "$URL/znyy/school/currentAdmin"; // 获取当前门店code和type
  static String getYunXinToken =
      "https://meeting.yunxinroom.com/scene/meeting/api/v2/batch-get-user"; // 获取云信token
  static String regYunXin =
      "https://meeting.yunxinroom.com/scene/meeting/api/v2/add-user"; //
  static String getAccountByType = "$URL/media/v2/meeting/getAccountByType";

  static String transcribe = "$URL/media/wap/speech-to-text/transcribe";


  //获取学生最近课程
  static String getStudentLatestCourse = "$URL/zx/student/course/getStudentLatestCourse";

  //获取未上课列表
  static String getDeliverCoursePage = "$URL/zx/student/course/getDeliverCoursePage";

  //获取录上课列表
  static String getRecordCoursePage =
      "$URL/zx/student/course/getRecordCoursePage";
  //获取复习课列表
  static String getReviewCourse = "$URL/zx/student/course/getReviewCourse";

  //获取已上课 鼎英语课程 相关数据
  static String getCourseById = "$URL/deliver/app/parent/getCourseById";
  //获取课程类型筛选项
  static String getCourseType = "$URL/zx/student/course/getCourseType";
  static String ifShowInvite =
      "$URL/zx/wap/invite/ifShowInvite"; // 判断邀请有礼活动是否开始

  static String getPYFId =
      "$URL/znyy/bvstatus/findByEnCodeAndEnType"; // 获取拼音法课程大类id
  static String getPYFMemberCode =
      "$URL/znyy/pd/mobile/funReview/exist/entrance"; // 获取拼音法门店
  static String getAIReadMemberCode =
      "$URL/znyy/superReadReview/queryMerchantList"; // 获取ai智阅门店
  static String getMessageNotice =
      "$URL/zx/message/notice/status/num"; // 获取信息中心数据
  static String getStudentCourseSummary =
      "$URL/zx/wap/course/student/course/summary"; // 获取学生课程总结

  static String queryCourseTool =
      "$URL/znyy/curriculum/type/queryCourseAndTool"; // 学习页工具课程大类查询

  static String queryGeneralTool =
      "$URL/znyy/curriculumTool/findGeneralTool"; // 查询通用工具

  static String queryRecentlyToolList =
      "$URL/znyy/curriculumTool/app/recentlyToolList"; // 查询最近使用工具

  static String postToolClick =
      "$URL/znyy/curriculumTool/app/toolClick"; // 工具点击
  static String checkVersion = "$URL/znyy/app/check/version"; // 版本更新

  static String checkWgtVersion =
      "$URL/znyy/bvstatus/findByEnCodeForMap"; // 检查wgt版本

  /// 请假相关接口
  static String leaveApplyInfo =
      "$URL/deliver/class/studentLeave/app/planStudyInfo"; // 请假与补课时间弹窗回显接口
  static String sendLeaveApply =
      "$URL/deliver/class/studentLeave/app/parentLeave"; // 发送请假申请
  static String saveMakeupClassTime =
      "$URL/deliver/class/studentLeave/app/saveMakeUpClassTime"; // 保存请假补课时间
  static String makeUpClassTimes =
      "$URL/deliver/web/student/contact/info/getClassTimeList"; // 获取可以补课的时间列表

  static String saveToken = "$URL/dsx/math/wap/token"; //  保存token
}
