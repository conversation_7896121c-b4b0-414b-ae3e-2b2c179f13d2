//
//  TencentMeetingSDK.h
//  TencentMeetingSDK
//
//  Created by mima:0000 on 2025/8/7.
//

#import <Foundation/Foundation.h>

// 修正头文件导入方式，使用尖括号并添加框架名称作为前缀
#import <TencentMeetingSDK/TencentMeetingManager.h>

//! Project version number for TencentMeetingSDK.
FOUNDATION_EXPORT double TencentMeetingSDKVersionNumber;

//! Project version string for TencentMeetingSDK.
FOUNDATION_EXPORT const unsigned char TencentMeetingSDKVersionString[];

// In this header, you should import all the public headers of your framework using statements like #import <TencentMeetingSDK/PublicHeader.h>
