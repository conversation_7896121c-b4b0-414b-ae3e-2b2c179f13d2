import 'dart:convert';
import 'package:http/http.dart' as http;

class ConfigService {
  static const String configUrl = "https://document.dxznjy.com/applet/config/dxconfig.json";

  /// 默认小程序分享
  static String zxShareType = "mini";

  static Future<Map<String, dynamic>?> fetchConfig() async {
    try {
      final response = await http.get(Uri.parse(configUrl));
      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        print("请求失败：${response.statusCode}");
        return null;
      }
    } catch (e) {
      print("请求出错：$e");
      return null;
    }
  }

  static getZxShareConfig() async {
    Map<String, dynamic>? config = await fetchConfig();
    if (config != null) {
      zxShareType = config["zxShareType"];
      print("zxShareType${zxShareType}");
    } else {
      print("获取配置失败");
    }
  }
}
