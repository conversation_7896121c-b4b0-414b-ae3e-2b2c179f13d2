//
//  CurriculumParamModel.swift
//  Runner
//
//  Created by 许江泉 on 2025/2/25.
//

import UIKit
import HandyJSON

struct CurriculumParam: HandyJSON  {
    var studentCode: String = ""
    var studentToken: String = ""
    var parentToken: String = ""
    var merchantCode: String = ""
    var meetingNum: String = ""
    var courseId: String = ""
    var userId: String = ""
    var selected: Int = 0
}
