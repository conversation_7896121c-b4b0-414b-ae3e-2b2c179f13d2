import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class TabView extends StatelessWidget {
  TabView(this.pageList, this.titleList);
  final List<Widget> pageList;
  final List titleList;
  
  PageController _pageController = PageController(initialPage: 0);

  @override
  Widget build(BuildContext context) {
    return Column(
        children: [
          SizedBox(
            height: 40.w,
            child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: titleList.length,
                itemExtent: 50,
                itemBuilder: (BuildContext context, int index) {
                  return _itemBuilder(index);
                }),
          ),
          Expanded(
            child: PageView(
              controller: _pageController,
              children: pageList,
            ),
          ),
        ],
    );
  }

  _itemBuilder(int index) {
    String title = titleList[index];
    return InkWell(
      onTap: () {
        _pageController.jumpToPage(index);
      },
      child: Text(title,style: TextStyle(fontSize: 15),),
    );
  }
}
