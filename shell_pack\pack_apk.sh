#!/bin/bash
###
# @Descripttion:
# @version:
# @Author: <PERSON>
# @Date: 2024-08-11 09:48:41
# @LastEditors: <PERSON>
# @LastEditTime: 2024-08-17 17:46:39
###

# shellcheck disable=SC1091
# shellcheck disable=SC2154
source ./pack_config.sh
source ./pgyer_upload.sh

chmod a+x pgyer_upload.sh

flutterBuild() {
  echo "$build_type"
  echoMsg "$upload_type"
  flutter build apk --no-shrink --dart-define=CHANNEL="$1" --dart-define=DEBUG="$build_type" --"$build_type" --no-tree-shake-icons
  if [[ $project_build_type == 1 ]]; then
    cp -R "$flutter_release_apk_path"*.apk "$export_apk_release_path"
  else
    cp -R "$flutter_debug_apk_path"*.apk "$export_apk_debug_path"
  fi

}

# 构建渠道包
apkBuild() {
  echoMsg "开始打包"
  if [[ $apk_chanhels_length == 0 || $upload_type == 2 ]]; then
    flutterBuild "Normal"
  elif [[ $pack_apk_channel == 0 && $apk_chanhels_length != 0 ]]; then
    echoMsg "开始构建: 全部渠道包"
    for ((i = 0; i < "$apk_chanhels_length"; i++)); do
      echoMsg "正在构建: ${apk_channels[$i]}渠道包"
      flutterBuild apk_channels["$i"]
    done
  else
    flutterBuild apk_channels["$pack_apk_channe"]
  fi
}

# 打包apk
buidApk() {
  mycmd=apkBuild
  echo "$mycmd"

  if $mycmd; then
    if [ "$project_build_type" == 1 ]; then
      expord_path="$export_apk_release_path"
    else
      expord_path="$export_apk_debug_path"
    fi
    echoMsg "upload_type:$upload_type"
    if [ "$upload_type" == 2 ]; then
      # echo 开始上传蒲公英
      for f in "$expord_path"*.apk; do
        [[ -e "$f" ]] || break
        echo "$f"
        uploadPgyer "$api_key" "$f"
      done
    else
      echoMsg "apk 打包成功"
      open "$export_apk_release_path"
    fi
  else
    echoMsg "apk 打包失败"
  fi
}
