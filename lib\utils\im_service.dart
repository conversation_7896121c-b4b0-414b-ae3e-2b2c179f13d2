import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:student_end_flutter/bean/student_app_bean_entity.dart';
import 'package:student_end_flutter/common/ne_user_option.dart';
import 'package:student_end_flutter/dialog/dialog_learn_select_identity.dart';
import 'package:student_end_flutter/main.dart';
import 'package:student_end_flutter/pages/learn/controller/learn_controller.dart';
import 'package:student_end_flutter/utils/android_ios_plugin.dart';
import 'package:student_end_flutter/utils/ne_metting_utils.dart';
import 'package:student_end_flutter/components/toast_utils.dart';
import 'package:student_end_flutter/common/Config.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_conversation.dart';
import 'package:tx_im/im_service_interface.dart';
import 'package:tx_im/im_home_page.dart';
import 'package:tx_im/src/chat.dart';
import '../common/sensors_analytics_option.dart';

import '../common/user_option.dart';
import 'httpRequest.dart';

///与主工程的通信
class IMService implements IMServiceInterface {
  @override
  switchRole(String oldRole, String newRole, BuildContext context) {}

  @override
  logout() {}

  @override
  pushOfflineMessageClick(V2TimConversation conversation) {
    // MyApp.navigatorKey.currentState?.push(
    // MyApp.navigatorKey.currentState?.push(handleMeetingNotification
    //   MaterialPageRoute(builder: (_) => Chat(selectedConversation: conversation),)
    // );
  }
  
  @override
  Future<void> handleMeetingNotification(String meetingId, String studentCode) async {
    try {
      ToastUtil.showLoadingDialog();
      LearnController? controller;
      // 设置为学生身份
      var response = HttpUtil()
          .get('${Config.getAccountByType}?phone=$studentCode&userType=6');
      response.then((value) async {
        String userUuid = '';
        String userToken = '';
        String schoolId = '';
        String userType = '';
        String scene = '';
        if (value is Map) {
          if (value.containsKey('userUuid')) {
            userUuid = value["userUuid"];
          }
          if (value.containsKey('userToken')) {
            userToken = value["userToken"];
          }
          if (value.containsKey('appId')) {
            schoolId = value["appId"];
          }
          if (value.containsKey('userType')) {
            userType = value["userType"].toString();
          }
          if (value.containsKey('scene')) {
            scene = value["scene"].toString();
          }
          var saveTokenResponse =
              HttpUtil().post('${Config.saveToken}?token=${UserOption.token}');
          saveTokenResponse.then((response) async {
            if (response!.data is Map) {
              Map dataMap = response.data as Map;
              if (dataMap.containsKey('data')) {
                String tokenKey = dataMap['data'];
                Map params = {
                  'schoolId': schoolId,
                  'classId': meetingId,
                  'userId': userUuid,
                  'token': userToken,
                  'role': userType,
                  'studentCode': studentCode,
                  'token_key': tokenKey,
                  'scene': scene,
                };
                AndroidIosPlugin.openLessonsDetail(jsonEncode(params));
              }
            }
          });
        }
      });
    } catch (e) {
      print('进入会议失败: $e');
      ToastUtil.closeLoadingDialog();
    }
  }
}