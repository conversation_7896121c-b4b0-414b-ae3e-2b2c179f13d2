import 'package:student_end_flutter/generated/json/base/json_field.dart';
import 'package:student_end_flutter/generated/json/last_student_info_bean_entity.g.dart';
import 'dart:convert';
export 'package:student_end_flutter/generated/json/last_student_info_bean_entity.g.dart';

@JsonSerializable()
class LastStudentInfoBeanEntity {
	late String studentCode = '';
	late String studentName = '';
	late List<LastStudentInfoBeanMerchantVos> merchantVos = [];

	LastStudentInfoBeanEntity();

	factory LastStudentInfoBeanEntity.fromJson(Map<String, dynamic> json) => $LastStudentInfoBeanEntityFromJson(json);

	Map<String, dynamic> toJson() => $LastStudentInfoBeanEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class LastStudentInfoBeanMerchantVos {
	late String name = '';
	late String realName = '';
	late String mobile = '';
	late int isEnable = 0;
	late String merchantCode = '';
	late String province = '';
	late String city = '';
	late String area = '';
	late String address = '';
	dynamic regTime;
	late String merchantName = '';
	late int isCheck = 0;
	late String longitude = '';
	late String latitude = '';
	dynamic expireDate;
	late String marketChargePerson = '';
	late String consultingChargePerson = '';
	late String teachingChargePerson = '';
	late String marketChargePersonPhone = '';
	late String consultingChargePersonPhone = '';
	late String teachingChargePersonPhone = '';
	late String roleTag = '';
	late String channelManagerCode = '';
	late String refereeCode = '';

	LastStudentInfoBeanMerchantVos();

	factory LastStudentInfoBeanMerchantVos.fromJson(Map<String, dynamic> json) => $LastStudentInfoBeanMerchantVosFromJson(json);

	Map<String, dynamic> toJson() => $LastStudentInfoBeanMerchantVosToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}