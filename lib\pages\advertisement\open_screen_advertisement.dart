import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:student_end_flutter/utils/sharedPreferences_util.dart';
import 'package:student_end_flutter/utils/app_version_util.dart';
import '../base/base_page.dart';
import 'package:get/get.dart';
import '../../common/CommonUtil.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_state_manager/src/simple/get_view.dart';
import 'dart:async';
import '../../utils/android_ios_plugin.dart';
import '../base/check_notification_permissons_page.dart';

class GuidePage extends StatefulWidget {
  const GuidePage({super.key});

  @override
  State<GuidePage> createState() => _GuidePageState();
}

class _GuidePageState extends State<GuidePage> {
  final PageController _controller = PageController();
  int _currentPage = 0;
  int _countDown = 3; // 3秒倒计时
  Timer? _timer;
  Timer? _countDownTimer;
  bool _canSkip = false; // 是否可以跳过
  bool _imagesPrecached = false; // 标记图片是否已预加载

  final List<Map<String, String>> _pages = [
    {'image': 'assets/splash-screen-1.png'},
    {'image': 'assets/splash-screen-2.png'},
    {'image': 'assets/splash-screen-3.png'}
  ];

  @override
  void initState() {
    super.initState();
    _startCountDown();
    _startAutoScroll();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 确保只在首次调用时预加载图片
    if (!_imagesPrecached) {
      _precacheImages();
      _imagesPrecached = true;
    }
  }

  void _precacheImages() {
    for (var page in _pages) {
      precacheImage(AssetImage(page['image']!), context);
    }
  }

  @override
  void dispose() {
    _timer?.cancel();
    _countDownTimer?.cancel();
    _controller.dispose();
    super.dispose();
  }

  void _startCountDown() {
    // 开始3秒倒计时
    _countDownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_countDown > 0) {
        setState(() {
          _countDown--;
        });
      } else {
        setState(() {
          _canSkip = true;
        });
        _countDownTimer?.cancel();
      }
    });
  }

  void _startAutoScroll() {
    _timer = Timer.periodic(const Duration(seconds: 2), (timer) {
      if (_currentPage < _pages.length - 1) {
        _controller.nextPage(
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeInOut,
        );
      } else {
        _timer?.cancel();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          PageView.builder(
            controller: _controller,
            itemCount: _pages.length,
            onPageChanged: (int page) {
              setState(() => _currentPage = page);
            },
            itemBuilder: (_, index) {
              return _buildPage(_pages[index]);
            },
          ),
          Positioned(
            right: 20.w,
            top: 40.h,
            child: Container(
              height: 30.h,
              child: ElevatedButton(
                onPressed: _canSkip ? _goBasePage : null, // 3秒内不可点击
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.white.withOpacity(_canSkip ? 1.0 : 0.5),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(25.r),
                  ),
                ),
                child: Text(
                  _canSkip ? '跳过' : '跳过($_countDown)',
                  style: TextStyle(fontSize: 14.sp, color: const Color(0xFFC9C9DA)),
                ),
              ),
            ),
          ),
          Positioned(
            bottom: 40.h,
            left: 0,
            right: 0,
            child: Column(
              children: [
                _buildIndicator(),
                SizedBox(height: 20.h),
                _buildEnterButton(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPage(Map<String, String> page) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      child: Image.asset(
        page['image']!,
        fit: BoxFit.cover,
        excludeFromSemantics: true,
        filterQuality: FilterQuality.high,
      ),
    );
  }

  Widget _buildIndicator() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(_pages.length, (index) {
        return Container(
          width: 8.w,
          height: 8.h,
          margin: EdgeInsets.symmetric(horizontal: 4.w),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: _currentPage == index ? const Color(0xFF009B55) : const Color(0xFFD8D8D8),
          ),
        );
      }),
    );
  }

  Widget _buildEnterButton() {
    return AnimatedOpacity(
      opacity: _currentPage == 2 ? 1.0 : 0.0,
      duration: const Duration(milliseconds: 300),
      child: Visibility(
        visible: _currentPage == 2,
        child: SizedBox(
          width: 198.w,
          height: 40.h,
          child: ElevatedButton(
            onPressed: _enterApp,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF009B55),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25.r),
              ),
            ),
            child: Text('进入鼎校',
              style: TextStyle(fontSize: 16.sp, color: Colors.white),
            ),
          ),
        ),
      ),
    );
  }

  void _goBasePage() {
    _timer?.cancel();
    _countDownTimer?.cancel();

    // 添加调试信息
    print("准备跳转到主页面，当前页: $_currentPage");

    AndroidIosPlugin.isOpenNotificationSettings("").then((value) {
      print("通知权限检查结果: $value");
      if (value == "true") {
        print("跳转到BasePage");
        Get.off(() => BasePage());
      } else {
        print("跳转到CheckNotificationPermissonsPage");
        Get.off(() => CheckNotificationPermissonsPage());
      }
    }).catchError((error) {
      // 添加错误处理
      print("检查通知权限时出错: $error");
      // 出错时也跳转到BasePage，避免卡在引导页
      Get.off(() => BasePage());
    });
  }

  void _enterApp() async {
    try {
      print("_enterApp被调用，当前页: $_currentPage");
      _timer?.cancel();
      _countDownTimer?.cancel();

      if (_currentPage < _pages.length - 1) {
        print("翻到下一页");
        await _controller.nextPage(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeIn,
        );
      } else {
        print("已经是最后一页，准备保存数据并跳转");
        // 确保所有异步操作完成后再跳转
        await Future.wait([
          SharedPreferencesUtil.saveData('hasShownGuide', 1),
          SharedPreferencesUtil.saveData(
            'lastShownGuideVersion',
            await AppVersionUtil.getVersion(),
          ),
        ] as Iterable<Future>);
        print("数据保存完成，准备跳转");
        _goBasePage();
      }
    } catch (e) {
      print("进入应用出错: $e");
      // 出错时也尝试跳转，避免卡在引导页
      _goBasePage();
    }
  }
}