import Flutter
import Bugly
import SensorsAnalyticsSDK
import TencentMeetingSDK

@main
@objc class AppDelegate: FlutterAppDelegate {
    private let engine = FlutterEngine(name: z<PERSON>gine, project: nil)
    var flutterChannelManager: FlutterChannelManager?
    var paths: String?
    override func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
        setupUI()
        commonInit()
        setupUniMPSDK(launchOptions)
        let options = SAConfigOptions(
            serverURL: "https://gateway.dxznjy.com/log/do/sensors", launchOptions: launchOptions
        )
        options.enableLog = true
        SensorsAnalyticsSDK.start(configOptions: options)
        return super.application(application, didFinishLaunchingWithOptions: launchOptions)
    }
}



/// 设置适配 UITableView
extension AppDelegate {
    private func setupUI() {
        if #available(iOS 11, *) { UIScrollView.appearance().contentInsetAdjustmentBehavior = .never }
        if #available(iOS 15.0, *) { UITableView.appearance().sectionHeaderTopPadding = 0.0 }
        engine.run(withEntrypoint: nil)
        let controller = FlutterViewController(engine: engine, nibName: nil, bundle: nil)
        flutterChannelManager = FlutterChannelManager(controller: controller)
        let nav = BaseNavViewController(rootViewController: controller)
        nav.isNavigationBarHidden = true
        window = UIWindow(frame: UIScreen.main.bounds)
        window?.rootViewController = nav
        window?.makeKeyAndVisible()
    }
}

/// 初始化配置
extension AppDelegate {
    private func commonInit() {
        GeneratedPluginRegistrant.register(with: engine)
        WXApi.registerApp(SDKConfig.wechatAppId, universalLink: SDKConfig.baseUniversalLink)
        WXSDKEngine.registerModule(NSStringFromClass(ZXUniModule.self), with: ZXUniModule.self)
        let option = QYSDKOption(appKey: SDKConfig.kServiceQYAppkey)
        option.appName = DeviceInfo.getAppName()
        QYSDK.shared().register(with: option)
        Bugly.start(withAppId: SDKConfig.kBuglyKey)
    }
    
}

/// 设置小程序sdk
extension AppDelegate {
    private func setupUniMPSDK(_ options: [UIApplication.LaunchOptionsKey: Any]?)  {
        ///先清理小程序再initSDK
        DCUniMPSDKEngine.destory()
        clearUniMPCache{
            let option = NSMutableDictionary(dictionary: options ?? [:])
            option.setValue(NSNumber.init(value:true), forKey: "debug")
            DCUniMPSDKEngine.initSDKEnvironment(launchOptions: option as! [AnyHashable: Any])
            UrlProtocolResolver.checkUniMPResoutce()
        }
    }
    
}

func clearUniMPCache(completion: @escaping () -> Void) {
        let fileManager = FileManager.default
        
        // 清理运行目录
        let runPath = DCUniMPSDKEngine.getUniMPRunPath(withAppid: APPID)
        do {
            try fileManager.removeItem(atPath: runPath)
            print("File removed successfully")
        } catch let error {
            print("Error removing file: \(error)")
        }
        // 清理缓存目录
        let cacheDirs = [
            FileManager.SearchPathDirectory.cachesDirectory,
            .libraryDirectory,
            .documentDirectory
        ]
        for dir in cacheDirs {
            if let path = NSSearchPathForDirectoriesInDomains(dir, .userDomainMask, true).first {
                let uniPaths = [
                    (path as NSString).appendingPathComponent("DCUniMP"),
                    (path as NSString).appendingPathComponent("PDRCore"),
                    (path as NSString).appendingPathComponent("HBuilder"),
                    (path as NSString).appendingPathComponent("UniMP")
                ]
                
                for uniPath in uniPaths {
                    if fileManager.fileExists(atPath: uniPath) {
                        do {
                            try fileManager.removeItem(atPath: uniPath)
                            print("清理缓存目录成功: \(uniPath)")
                        } catch let error {
                            print("清理缓存目录失败: \(error)")
                        }
                    }
                }
            }
        }
        // 清理临时目录
        let tempDir = NSTemporaryDirectory()
        let tempPaths = [
            (tempDir as NSString).appendingPathComponent("DCUniMP"),
            (tempDir as NSString).appendingPathComponent("PDRCore")
        ]
        for tempPath in tempPaths {
            if fileManager.fileExists(atPath: tempPath) {
                do {
                    try fileManager.removeItem(atPath: tempPath)
                    print("清理临时目录成功: \(tempPath)")
                } catch let error {
                    print("清理临时目录失败: \(error)")
                }
            }
        }
        completion()
    }
