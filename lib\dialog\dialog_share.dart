import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:student_end_flutter/common/Config.dart';
import 'package:student_end_flutter/common/user_option.dart';
import 'package:student_end_flutter/components/toast_utils.dart';
import 'package:student_end_flutter/navigator/home_navigator.dart';

import '../bean/utils/share_app_android_ios.dart';
import '../utils/android_ios_plugin.dart';
import '../utils/config_service.dart';
import '../common/sensors_analytics_option.dart';
class ShareDialog extends StatefulWidget {
  ShareAppAndroidIos shareAppAndroidIos;
  String goodsId;
  String url;
  ShareDialog(this.shareAppAndroidIos, this.goodsId,this.url);
  @override

  @override
  _ShareDialogState createState() => _ShareDialogState();
}

class _ShareDialogState extends State<ShareDialog> {

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 150.h,
      padding: EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Column(
        children: [
          Text("分享到",style: TextStyle(
            fontFamily: "M",
            fontSize: 16.sp,
            color: Colors.black
          ),),
          SizedBox(
            height: 25.h,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              GestureDetector(
                onTap: (){
                    TrackingUtils.trackEvent(
                        'WXSharingClick', {'goods_id':widget.goodsId.isNotEmpty?widget.goodsId:'','common_fields':'','avitcity_id':'','banner_id':'', 'url_path': widget.url});
                  shareFunc();
                },
                child: Column(
                  children: [
                    Image(width: 40.w,height: 40.h,image: AssetImage("assets/weiimg.png")),
                    Text("微信",style: TextStyle(
                        fontFamily: "R",
                        fontSize: 12.sp,
                        color: Colors.black26
                    ))
                  ],
                ),
              ),
              Visibility(
                  visible: widget.goodsId.isNotEmpty,
                  child: GestureDetector(
                    onTap: (){
                      TrackingUtils.trackEvent(
                          'PosterSharingClick', {'goods_id': widget.goodsId,'common_fields':'','avitcity_id':'','banner_id':'', 'url_path': widget.url});
                      Get.back();
                      posterShareFunc();
                    },
                    child: Column(
                      children: [
                        Image(width: 40.w,height: 40.h,image: AssetImage("assets/share_img.png")),
                        Text("海报分享",style: TextStyle(
                            fontFamily: "R",
                            fontSize: 12.sp,
                            color: Colors.black26
                        ))
                      ],
                    ),
                  ),
              ),
             GestureDetector(
               onTap: (){
                  TrackingUtils.trackEvent(
                      'LinkSharingClick', {'goods_id': widget.goodsId.isNotEmpty?widget.goodsId:'','common_fields':'','avitcity_id':'','banner_id':'', 'url_path': widget.url});
                 copyFunc();
               },
               child:  Column(
                 children: [
                   Image(width: 40.w,height: 40.h,image: AssetImage("assets/share_lj.png")),
                   Text("复制链接",style: TextStyle(
                       fontFamily: "R",
                       fontSize: 14.sp,
                       color: Colors.black26
                   ))
                 ],
               ),
             )
            ],
          )
        ],
      ),
    );
  }

  shareFunc(){
    if(ConfigService.zxShareType == "mini"){
      ShareAppAndroidIosWithAppId share = ShareAppAndroidIosWithAppId();
      share.miniProgramId = Config.ZXMiniOriginalId;
      share.path = widget.shareAppAndroidIos.path;
      share.logo = widget.shareAppAndroidIos.imageUrl;
      share.title = widget.shareAppAndroidIos.title;
      AndroidIosPlugin.openShareMiniProgram(jsonEncode(share));
    }else{
      AndroidIosPlugin.openShareApp(jsonEncode(widget.shareAppAndroidIos));

    }
  }

  posterShareFunc() {
    if(UserOption.checkNoTokenJump()){
      return;
    }
    var path = HomeNavigator.posterSharing(goodsId: widget.goodsId, type: "6",).path;
    AndroidIosPlugin.openUniApp(path);
  }

  copyFunc(){
    try{
      Clipboard.setData(ClipboardData(text: Config.shareAppUrl + widget.shareAppAndroidIos.path));
      ToastUtil.showShortSuccessToast("复制成功");
    }catch(e){
      ToastUtil.showShortErrorToast("复制失败");
    }
  }
}