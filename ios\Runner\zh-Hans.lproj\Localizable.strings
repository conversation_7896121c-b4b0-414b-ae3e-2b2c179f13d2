/* 
  Localizable.strings
  Runner

  Created by 许江泉 on 2025/2/8.
  
*/
Login = "登录";
Sent = "已发送";
PhoneNumber = "手机号";
VerificatCode = "验证码";
LoginPassword = "登录密码";
VerificationCode = "获取验证码";
PleaseEnterPhoneNumber = "请输入手机号";
PleaseEnterLoginPassword = "请输入登录密码";
PleaseEnterVerificationCode = "请输入验验证码";
PleaseFilmobilePhonenumber = "请填写手机号";
PasswordisemptyPleaseenter = "密码为空，请输入";
PleaseObtaintheverificationCode = "请先获取验证码";
VerificationCodeCannotbeempty = "验证码不能为空，请输入";
Cellphonenumberdoesntexist = "手机号码不存在";
MobilePhoneNumberEnteredWrongPleasereenter = "手机号输入错误，请重新输入";
ClickRefresh = "点击刷新";
StartClassNow = "立即上课";
Viewfeedback = "查看反馈";
Logout = "退出登录";
Opensoonpleasewait = "即将开放，请等待";
School = "学堂";
CFBundleDisplayName = "鼎校甄选";
Confirm = "确认";
ConfirmExitLogin = "确认退出登录?";
Cancel = "取消";
VolumeToast = "为保证上课质量，请增大音量~";
UserPasswordLogin = "用户名密码登录";
PhoneNumberVerificatCodeLogin = "手机号验证码登录";
