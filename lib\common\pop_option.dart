import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:student_end_flutter/bean/pop_show_bean_entity.dart';
import 'package:student_end_flutter/common/user_option.dart';
import 'package:student_end_flutter/pages/base/controller/base_controller.dart';

import '../bean/popup_model_entity.dart';
import '../dialog/dialog_activity_pop.dart';
import '../generated/json/base/json_convert_content.dart';
import '../navigator/home_navigator.dart';
import '../utils/android_ios_plugin.dart';
import '../utils/event_bus_utils.dart';
import '../utils/sharedPreferences_util.dart';
import '../common/sensors_analytics_option.dart';
import '../utils/app_version_util.dart';
class PopOption{
  static String popNoShowData ='';//弹窗缓存json
  static List<PopShowBeanEntity> popNoShowListData = [];//展示弹窗数据列表
  static int tabCount = 5;//app tab数量
  static List<PopupModelData> popModelDataList = [];//返回接口弹窗数据列表
  static int currentTabIndex = -1;//当前tab下标
  static int screenNameIndex = -1;//当前tab下标

  static List<int> alreadyShowPopList = [];//已经展示的弹窗tab下标

  static initPopShowData(items) async {
    popModelDataList = items;
    popNoShowData = await SharedPreferencesUtil.getData<String>(
        SharedPreferencesUtil.popNoShowData);
    if (popNoShowData.isNotEmpty && popNoShowData != '[]' && popNoShowData != null) {
      popNoShowListData = jsonConvert.convertListNotNull<PopShowBeanEntity>(jsonDecode(popNoShowData))!;
      if(tabCount>popNoShowData.length){
        //如果本地数据数量小于当前版本tab数量，就初始化数据
        creatList();
      }else{
        for(int i = 0; i < popNoShowListData.length; i++){
          DateTime currentDate = DateTime.now();
          DateTime todayShowData = DateTime.parse(popNoShowListData[i].todayShowData);
          if(currentDate.year != todayShowData.year || currentDate.month != todayShowData.month || currentDate.day != todayShowData.day){
            popNoShowListData[i].popNoShowListId = [];
            popNoShowListData[i].todayShowData = currentDate.toString();
          }
        }
      }
      //遍历数据，判断是否过期 过期了 就全部展示
    }else{
      //如果没有数据，就初始化数据
      creatList();
    }

  }

  static creatList(){
    for (int i = 0; i < tabCount; i++) {
      DateTime currentDate = DateTime.now();
      PopShowBeanEntity popShowBeanEntity = PopShowBeanEntity();
      popShowBeanEntity.todayShowData = currentDate.toString();
      popShowBeanEntity.popNoShowListId = [];
      popNoShowListData.add(popShowBeanEntity);
    }
  }


  /// 保存数据到本地
  static savePopShowData() async {
    String jsonString = jsonEncode(popNoShowListData);
    SharedPreferencesUtil.saveData(SharedPreferencesUtil.popNoShowData, jsonString);
  }

  //添加当前关闭的弹窗id到不再显示列表
  static popCloseTodayNoShow(index,id) {
    popNoShowListData[index-1].popNoShowListId.add(id);
    savePopShowData();
  }




  static getCurrentPopData(index) {
    //1,2,3,5才展示弹窗数据 兼容小程序下标 1,2,3,4
    screenNameIndex=index-1;
    if(index ==4){
      return ;
    }else if(index == 5){
      index = 4;
    }
    currentTabIndex = index;

    //获取当前tab下标 去筛选要当前tab下展示的弹窗
    List<PopupModelData> temp = [];
    for (int i = 0; i < popModelDataList.length; i++) {
      if (popModelDataList[i].position.contains(index.toString())) {
        temp.add(popModelDataList[i]);
      }
    }
    print(temp);
    //判断当前tab下标 是否有需要弹窗数据
    if (popNoShowListData[index-1].popNoShowListId.isNotEmpty ) {
      if(popNoShowListData[index-1].popNoShowListId.length == temp.length){
        //如果不再显示列表的id数量和弹窗数据的id数量相同，就清空弹窗数据
        temp.clear();
      }else{
        //判断当前tab下标 不展示的弹窗id 从弹窗数据中移除返回
        for (var i = 0; i < temp.length; i++) {
          for (var j = 0; j < popNoShowListData[index-1].popNoShowListId.length; j++) {
            if (temp[i].id == popNoShowListData[index-1].popNoShowListId[j]) {
              temp.removeAt(i);
              i--;
              break;
            }
          }
        }
      }
    }
    showActivityPop(temp);
  }

  static showActivityPop(List<PopupModelData> items){
    if(items.isEmpty){
      return;
    }
    PopupModelData currentPopup= items[0];
    String  currentImageUrl = currentPopup.popupPicUrl;
    String  currentBtnBgc = currentPopup.color.trim().isEmpty || currentPopup.color.length < 7 ? '#339378' : currentPopup.color;
    String  currrentPopId = currentPopup.id;
    String screenName ='';
    switch (screenNameIndex) {
      case 0:
        screenName = 'HomePage';
        break;
      case 1:
        screenName = 'ZxPage';
        break;
      case 2:
        screenName = 'LearnPage';
        break;
      case 3:
        screenName = 'IMModuleHomePage';
        break;
      case 4:
        screenName = 'MinePage';
        break;
    }
    TrackingUtils.trackPageView('OpenPrePopupEv', {
      'common_fields': '预热活动弹框曝光',
      'avitcity_id': currentPopup.id,'goods_id':'','url_path':screenName

    });
    Get.dialog(
        ActivityPopDialog(
          url_path:screenName,
          activityId:currrentPopId,
          imageUrl: currentImageUrl, 
          color: currentBtnBgc,
          popupInterval: currentPopup.popupInterval,
          onClickSure: (bool isChecked) {
            if(UserOption.checkNoTokenJump()) return;
            onPopClick(isChecked, currentPopup);
            items.removeAt(0);
            if(items.isNotEmpty){
              showActivityPop(items);
            }
            alreadyShowPopList.add(currentTabIndex);
          },
          onClickClose: (bool isChecked){
            if(isChecked){
              PopOption.popCloseTodayNoShow(currentTabIndex, currrentPopId);
            }
            items.removeAt(0);
            if(items.isNotEmpty){
              showActivityPop(items);
            }
            alreadyShowPopList.add(currentTabIndex);
          },),
        barrierDismissible: false
    );
  }
  /// 弹窗确认处理
  static onPopClick(bool isChecked, PopupModelData popupModelData) {
    final popupId = popupModelData.id;
    if (isChecked) {
      PopOption.popCloseTodayNoShow(currentTabIndex, popupId);
    }
    if(popupModelData.goodsId.isNotEmpty){
      String path = HomeNavigator.goodsDetailsHaveBannerId(goodsId: popupModelData.goodsId, bannerId: popupModelData.id,title:'activityId').path;
      AndroidIosPlugin.openUniApp( path );
    }else{
      int? identityType = UserOption.userInfoBeanEntity?.identityType;
      if(popupModelData.popupLinkUrl == 'Personalcenter/my/nomyEquity' && identityType == 4){
        String path = HomeNavigator.myEquity().path;
        AndroidIosPlugin.openUniApp( path );
      }else{
        if(popupModelData.popupLinkUrl.isNotEmpty){
          BaseController controller = Get.find<BaseController>();
          String path = "";
            // tab 跳转映射
          final tabMap = {
            'pages/index/index': 0,
            'pages/selection/index': 1,
            'pages/home/<USER>/index': 2,
            'pages/home/<USER>/index': 4,
          };
            // 判断是否为tab跳转
          for (final entry in tabMap.entries) {
            if (popupModelData.popupLinkUrl.contains(entry.key)) {
              controller.changeTab(entry.value);
              return;
            }
          }

          if(popupModelData.popupLinkUrl.startsWith("/")){
            popupModelData.popupLinkUrl = popupModelData.popupLinkUrl.substring(1);
          }
          if (popupModelData.popupLinkUrl.contains('?')) {
            path = '${popupModelData.popupLinkUrl}&bannerId=${popupModelData.id}';
          } else {
            path = '${popupModelData.popupLinkUrl}?bannerId=${popupModelData.id}';
          }
          if(popupModelData.popupLinkUrl.contains('groupActivityId')){
            path =popupModelData.popupLinkUrl;
          }
          print(path);
          print(popupModelData.popupLinkUrl);
          print('ttttttttttttttttttttttttttttttttttttttttttttttttt');
          path += "&token=${UserOption.token}&app=2&nickName=${UserOption.userInfoBeanEntity?.nickName}&userCode=${UserOption.userInfoBeanEntity?.userCode}"
              "&identityType=${UserOption.userInfoBeanEntity?.identityType}&userId=${UserOption.userId}&phone=${UserOption.userInfoBeanEntity?.mobile}&appVersion=${AppVersionUtil.appVersion ?? ''}&distinctId=${AppVersionUtil.distinctId ?? ''}";
          if(path.isNotEmpty){
            AndroidIosPlugin.openUniApp( path );
          }
        }
      }
    }
  }
}
