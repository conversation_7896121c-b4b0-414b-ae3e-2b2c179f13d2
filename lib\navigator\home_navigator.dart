
import 'package:flutter/cupertino.dart';
import 'package:student_end_flutter/common/user_option.dart';
import 'mini_program_path.dart';
import 'path_generator.dart';
class HomeNavigator {
  const HomeNavigator._();
  /// 首页搜索
  /// 示例：interestModule/searchPage?pageNum=1&pageSize=20
  static MiniProgramPath homeSearch() {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
        pathComponents: ['interestModule', 'searchPage'],
        queryParams: {
          'pageNum': 1,  // 固定参数
          'pageSize': 20,
        }
    );
    return MiniProgramPath(path);
  }

  /// 会议中心
  /// 示例：meeting/meetIndex?
  static MiniProgramPath conferenceCenter() {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
        pathComponents: ['meeting', 'meetIndex'],
        queryParams: {
          'page':1,
          'cateType':2,
          'indexShow':0,
        }
    );
    return MiniProgramPath(path);
  }

  /// 鼎币商城
  /// 示例：shoppingMall/index?goodsTypeListStr=6&pageNum=1
  static MiniProgramPath shoppingMall()  {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
        pathComponents: ['shoppingMall', 'index'],
        queryParams: {
          'goodsTypeListStr': 6,  // 固定参数
          'pageNum': 1,
          'pageSize': 20,
          'userId': UserOption.userId,
        }
    );
    return MiniProgramPath(path);
  }

  /// 文化中心
  /// 示例：memberCenter/index
  static MiniProgramPath memberCenter() {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
        pathComponents: ['memberCenter', 'index'],
        queryParams: {
          'userId': UserOption.userId,
          'eventType': "ENTER",
        }
    );
    return MiniProgramPath(path);
  }
  static MiniProgramPath memberCenterDetails({
    @required required String id
  }) {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
        pathComponents: ['meeting', 'meetIndex'],
        queryParams: {
          'id': id,
        }
    );
    return MiniProgramPath(path);
  }
  /// 家长会员
  /// 示例：Personalcenter/my/parentVipEquity
  static MiniProgramPath parentVipEquity() {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
        pathComponents: ['Personalcenter', 'my', 'parentVipEquity'],
        isNeedUserId: false,
        queryParams: {
          'expire': 1,
        }
    );
    return MiniProgramPath(path);
  }

  ///超级俱乐部
  /// 示例：supermanClub/clubManagement/clubUpgrade
  static MiniProgramPath supermanClub() {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
        pathComponents: ['supermanClub', 'clubManagement', 'clubUpgrade'],
        queryParams: {}
    );
    return MiniProgramPath(path);
  }

  ///超级合伙人
  /// 示例：partnerApplication/index
  static MiniProgramPath partnerApplication() {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
        pathComponents: ['partnerApplication', 'index'],
        queryParams: {}
    );
    return MiniProgramPath(path);
  }

  /// 首页弹窗跳转到活动页面
  static MiniProgramPath goodsDetailsActivity({
    @required required String goodsId,
    @required required String activityId,
  }) {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
        pathComponents: ['Coursedetails', 'productDetils'],
        queryParams: {
          'id': goodsId,
          'activityId': activityId
        }
    );
    return MiniProgramPath(path);
  }

  static MiniProgramPath goodsDetails({
    @required required String goodsId,
  }) {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
        pathComponents: ['Coursedetails', 'productDetils'],
        queryParams: {
          'id': goodsId,
        }
    );
    return MiniProgramPath(path);
  }

  static MiniProgramPath goodsDetailsHaveBannerId({
    @required required String goodsId,@required required String bannerId,required String title
  }) {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
        pathComponents: ['Coursedetails', 'productDetils'],
        queryParams: {
          'id': goodsId,
          title: bannerId,
        }
    );
    return MiniProgramPath(path);
  }
//专题this.specialName = option.specialName
//     this.specialId = option.specialId
  static MiniProgramPath specialFeature({
    @required required String specialId,@required required String specialName }) {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
        pathComponents: ['specialTitle', 'special'],
        queryParams: {
          'specialId': specialId,
          'specialName': specialName,
        }
    );
    return MiniProgramPath(path);
  }
  //首页更多
  static MiniProgramPath specialMore({
    @required required String categoryId}) {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
        pathComponents: ['learnMore', 'learnmore'],
        queryParams: {
          'categoryId': categoryId,
        }
    );
    return MiniProgramPath(path);
  }
//
  /// 只是商品的分享
  static MiniProgramPath shareUrl({
    @required required String goodsId,@required required String type,
  }) {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
        isAddUserToken: false,
        pathComponents: ['pages', 'beingShared', "index"],
        queryParams: {
          'scene': UserOption.userId,
          'type': type,
          'id': goodsId,
          'source':'app'
        }
    );
    return MiniProgramPath(path);
  }

  static MiniProgramPath myEquity() {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
        pathComponents: ['Coursedetails', "my", 'myEquity'],
        queryParams: {
          'nickName': UserOption.userInfoBeanEntity?.nickName,
          'phone': UserOption.userInfoBeanEntity?.mobile,
        }
    );
    return MiniProgramPath(path);
  }

  static MiniProgramPath commentStar() {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
        pathComponents: ['qyWechat', "CommentStar"],
        queryParams: {
          // 'nickName': UserOption.userInfoBeanEntity?.nickName,
          // 'phone': UserOption.userInfoBeanEntity?.mobile,
        }
    );
    return MiniProgramPath(path);
  }

  // 海报分享
  static MiniProgramPath posterSharing({
    @required required String goodsId,@required required String type
}) {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
        pathComponents: ['splitContent', "poster", "index"],
        queryParams: {
          'type': type,
          'id': goodsId,
        }
    );
    return MiniProgramPath(path);
  }
}

