#!/bin/bash
#
# 通过shell脚本来实现将本地app文件通过API上传到蒲公英
# https://www.pgyer.com/doc/view/api#fastUploadApp
##
# 参数说明：
# $1: 蒲公英api_key
# $2: 要上传的文件路径(ipa/apk)
#

source ./pack_config.sh

printHelp() {
    echo "Usage: $0 api_key file"
    echo "Example: $0 <your_api_key> <your_apk_or_ipa_path>"
}

# 新增：统一输出函数，方便调试
echoMsg() {
    echo "================= $1 =================="
}

uploadPgyer() {
    api_key=$1
    file=$2
    # check api_key exists
    if [ -z "$api_key" ]; then
        echo "api_key is empty"
        printHelp
        exit 1
    fi

    # check file exists
    if [ ! -f "$file" ]; then
        echo "file not exists"
        printHelp
        exit 1
    fi

    if [[ $file =~ ipa$ ]]; then
        app_type="ios"
    elif [[ $file =~ apk$ ]]; then
        app_type="android"
    else
        echo "file type not support"
        printHelp
        exit 1
    fi

    # ---------------------------------------------------------------
    # functions
    # ---------------------------------------------------------------

    # 执行命令并捕获结果的函数
    execCommand() {
        echoMsg "执行命令: $*"
        # 使用-d提交表单（application/x-www-form-urlencoded格式），而非-F
        result=$(eval "$@")
        # 打印API返回结果，方便调试
        echo "API响应: $result"
    }

    # ---------------------------------------------------------------
    # 获取上传凭证
    # ---------------------------------------------------------------

    echoMsg "获取凭证"

    echoMsg "_api_key: ${api_key}"
#    execCommand "curl -s -F '_api_key=${api_key}' -F 'buildType=${app_type}' https://api.pgyer.com/apiv2/app/getCOSToken"
    execCommand "curl -s \
        -H 'Content-Type: application/x-www-form-urlencoded' \
        -d '_api_key=${api_key}' \
        -d 'buildType=${app_type}' \
        https://api.pgyer.com/apiv2/app/getCOSToken"
    [[ "${result}" =~ \"endpoint\":\"([^\"]+)\" ]] && endpoint=$(echo "${BASH_REMATCH[1]}" | sed 's/\\\//\//g')
    [[ "${result}" =~ \"key\":\"([^\"]+)\" ]] && key="${BASH_REMATCH[1]}"
    [[ "${result}" =~ \"signature\":\"([^\"]+)\" ]] && signature="${BASH_REMATCH[1]}"
    [[ "${result}" =~ \"x-cos-security-token\":\"([^\"]+)\" ]] && x_cos_security_token="${BASH_REMATCH[1]}"

    # 调试：打印解析出的凭证
    echo "解析出的凭证: "
    echo "endpoint: $endpoint"
    echo "key: $key"
    echo "signature: $signature"
    echo "x_cos_security_token: $x_cos_security_token"

    if [ -z "$key" ] || [ -z "$signature" ] || [ -z "$x_cos_security_token" ] || [ -z "$endpoint" ]; then
        echoMsg "get upload token failed"
        exit 1
    fi

    # ---------------------------------------------------------------
    # 上传文件
    # ---------------------------------------------------------------

    echoMsg "上传文件"

    execCommand "curl -s -o /dev/null -w '%{http_code}' --form-string 'key=${key}' --form-string 'signature=${signature}' --form-string 'x-cos-security-token=${x_cos_security_token}' -F 'file=@${file}' ${endpoint}"
    if [ "$result" -ne 204 ]; then
        echoMsg "Upload failed"
        exit 1
    fi

    # ---------------------------------------------------------------
    # 检查结果
    # ---------------------------------------------------------------

    echoMsg "检查结果"

    # shellcheck disable=SC2034
    for i in {1..60}; do
        execCommand "curl -s http://www.pgyer.com/apiv2/app/buildInfo?_api_key=${api_key}\&buildKey=${key}"
        [[ "${result}" =~ \"code\":([0-9]+) ]] && code=$(echo ${BASH_REMATCH[1]})
        [[ "${result}" =~ \"buildQRCodeURL\":\"([\:\_\.\/\\A-Za-z0-9\-]+)\" ]] && buildQRCodeURL=$(echo ${BASH_REMATCH[1]} | sed 's!\\\/!/!g')
        if [ "$code" -eq 0 ]; then
            echo "$result"
            if [ "$app_type" == "ios" ]; then
                pgyer_ios_code_url=$buildQRCodeURL
                echo "$pgyer_ios_code_url"
            else
                pgyer_android_code_url=$buildQRCodeURL
                echo "$pgyer_android_code_url"
            fi
            open "$buildQRCodeURL"
            echoMsg "蒲公英上传成功"
            break
        else
            sleep 1
        fi
    done
}
