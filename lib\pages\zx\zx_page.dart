import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

import 'package:flutter/rendering.dart';
import 'package:flutter_carousel_widget/flutter_carousel_widget.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:student_end_flutter/common/CommonUtil.dart';
import 'package:student_end_flutter/components/water_layout_list.dart';
import 'package:student_end_flutter/pages/zx/controller/zx_controller.dart';
import '../../navigator/home_navigator.dart';
import '../../navigator/selection_navigator.dart';
import '../../res/colors.dart';
import '../../utils/android_ios_plugin.dart';
import '../../common/sensors_analytics_option.dart';

// class ZxPage extends GetView<ZxController> {
class ZxPage extends StatefulWidget {
  @override
  _ZxPageState createState() => _ZxPageState();
}


class _ZxPageState extends State<ZxPage> with WidgetsBindingObserver {
  @override
  @override
  void initState() {
    print('4555555555555555555555555555555555555555555555555555555555555555555555555');
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    print('444444444444444444444444444444444444444444444444444444444444444444');
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      // App回到前台时触发当前页面埋点
      // 应用回到前台
      controller.sendPageViewEvent();
    } else if (state == AppLifecycleState.paused) {
      // 应用进入后台
      controller.sendPageLeaveEvent();

    } else if (state == AppLifecycleState.inactive) {
      print('1111111111111111111111111111111111111111111inactive');
    } else if (state == AppLifecycleState.detached) {
      // 不杀死 im数据会有问题
      // 应用被销毁
      controller.sendPageLeaveEvent();
    }
  }
  final ZxController controller = Get.put(ZxController());
  @override
  Widget build(BuildContext context) {
    // 获取屏幕宽度
    double screenWidth = MediaQuery.of(context).size.width;
    Get.put(ZxController());
    return Scaffold(
      // backgroundColor: HexColor('F3F8FC'),
      appBar: AppBar(
        title: Text(
          '甄选好课',
          style: TextStyle(
            color: HexColor('333333'),
            fontSize: 16.sp,
            fontFamily: "R",
          ),
        ),
        centerTitle: true,
        shadowColor: Colors.white,
        backgroundColor: HexColor("f2f3f5"),
        leading: IconButton(
          padding: const EdgeInsets.only(left: 16),
          icon: Icon(
            Icons.arrow_back_ios,
            color: HexColor("2a2e35"),
            size: 20,
          ),
          onPressed: () {
            Get.back();
          },
        ),
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topRight,
              end: Alignment.bottomLeft,
              colors: [
                HexColor("#EEFFF5"), // 起始颜色
                HexColor("#FDFDFD"), // 结束颜色
              ],
            ),
          ),
        ),
      ),
      body: RefreshIndicator(
        notificationPredicate: (ScrollNotification notifation) {
          ScrollMetrics scrollMetrics = notifation.metrics;
          if (scrollMetrics.minScrollExtent == 0) {
            return true;
          } else {
            return false;
          }
        },
        onRefresh: controller.onRefresh,
        child:_buildContent(screenWidth, context),
      ),
    );
  }

  _buildContent(double screenWidth, BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topRight,
          end: Alignment. bottomLeft,
          colors: [
            HexColor("#F1FCF9"), // 起始颜色
            HexColor("FDFDFD"), // 结束颜色
          ],
        ),
      ),
      child: NestedScrollView(
        controller: controller.scrollController,
        headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
          return <Widget>[
            SliverToBoxAdapter(
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topRight,
                    end: Alignment.bottomLeft,
                    colors: [
                      HexColor("#EEFFF5"), // 起始颜色
                      HexColor("FDFDFD"), // 结束颜色
                    ],
                  ),
                ),
                child: Column(children: [
                  SizedBox(
                    height: 5.h,
                  ),
                  Row(
                    children: [
                      Expanded(child: _searchUI(UniqueKey())),
                    ],
                  ),
                  SizedBox(
                    height: 10.h,
                  ),
                  // 水平滚动列表
                  Obx(() => FlutterCarousel(
                        items: _tabBannerUI(screenWidth),
                        options: FlutterCarouselOptions(
                          autoPlay: false,
                          enlargeCenterPage: false,
                          autoPlayInterval:
                              const Duration(seconds: 3), // 自动播放间隔
                          autoPlayAnimationDuration:
                              const Duration(milliseconds: 800), // 动画持续时间
                          autoPlayCurve: Curves.linear,
                          enableInfiniteScroll: false,
                          showIndicator: false,
                          // aspectRatio: 2.0,
                          height:
                              controller.buttonList.length > 5 ? 150.h : 80.h,
                          viewportFraction: 1,
                          onPageChanged: (index, reason) {
                            controller.tabIndex.value = index;
                          },
                        ),
                      )),
                  Obx(
                    () => controller.buttonList.length > 10
                        ? Container(
                            height: 20.h,
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                for (int i = 0;
                                    i <
                                        (controller.buttonList.length / 10)
                                            .ceil();
                                    i++)
                                  Container(
                                    width: controller.tabIndex.value == i
                                        ? 6.w
                                        : 18.w,
                                    height: 6.h,
                                    margin: EdgeInsets.only(right: 6.w),
                                    decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(40),
                                        color: controller.tabIndex.value == i
                                            ? HexColor('66CF87')
                                            : HexColor('E5EFF6')),
                                  ),
                                // Container(
                                //   width: controller.tabIndex.value==1?6.w:18.w,
                                //   height: 6.h,
                                //   margin:EdgeInsets.only(left: 6.w),
                                //   decoration: BoxDecoration(
                                //       borderRadius: BorderRadius.circular(40),
                                //       color:controller.tabIndex.value==1?HexColor('66CF87'):HexColor('E5EFF6')),
                                // )
                              ],
                            ),
                          )
                        : Container(height: 10.h,),
                  ),
                  // // 显示滚动记录
                  // Expanded(
                  //   child: ListView.builder(
                  //     itemCount: scrollHistory.length,
                  //     itemBuilder: (context, index) {
                  //       return ListTile(
                  //         title: Text('记录 ${index + 1}'),
                  //         subtitle: Text('位置: ${scrollHistory[index].toStringAsFixed(1)}'),
                  //       );
                  //     },
                  //   ),
                  // ),
                  // Obx(
                  //   () => FlutterCarousel(
                  //     items: _bannerUI(),
                  //     options: FlutterCarouselOptions(
                  //       autoPlay: true,
                  //       enlargeCenterPage: true,
                  //       enableInfiniteScroll: true,
                  //       aspectRatio: 2.0,
                  //       viewportFraction: 0.95,
                  //       onPageChanged: (index, reason) {},
                  //     ),
                  //   ),
                  // ),
                ]),
              ),
            ),
            Obx(
              () => SliverOverlapAbsorber(
                handle: SliverOverlapAbsorberHandle(),
                sliver: SliverAppBar(
                  automaticallyImplyLeading: false,
                  toolbarHeight: controller.showTable.value ? 48.w : 153.w,
                  pinned: true,
                  backgroundColor: Colors.transparent, // 1. 设置AppBar背景透明
                  elevation: 0, // 移除阴影
                  expandedHeight: controller.showTable.value ? 48.w : 153.w,
                  collapsedHeight: controller.showTable.value ? 48.w : 153.w,
                  flexibleSpace: Container(
                    // color: Colors.blue.withOpacity(0.1),
                      child: Column(
                        children: [
                          Offstage(
                            offstage: controller.showTable.value,
                            child: SizedBox(height: 10.h),
                          ),

                          Offstage(
                            offstage: controller.showTable.value,
                            child: _searchUI(UniqueKey()),
                          ),
                          SizedBox(height: 5.h),
                          Offstage(
                            offstage: controller.showTable.value,
                            child: Container(
                              color: Colors.white,
                              child: _tabUI(),
                            ),
                          ),
                          Offstage(
                              offstage: controller.showTable.value,
                              child: Container(
                                  color: Colors.white,
                                  height: 46.w,
                                  child: Row(
                                    children: [
                                      _measurenBtn(),
                                      // SizedBox(width: 10.w),
                                      _priceBtn(),
                                    ],
                                  ))),
                          Offstage(
                              offstage: !controller.showTable.value,
                              child: Container(
                                  // color: Colors.white,
                                  height: 40.w,
                                  child: Row(
                                    children: [
                                      _measurenBtn(),
                                      // SizedBox(width: 10.w),
                                      _priceBtn(),
                                    ],
                                  ))),
                        ],
                      )),
                ),
              ),
            ),
          ];
        },
        // padding: EdgeInsets.symmetric(horizontal: 10.w),
        body: Column(
          children: [
            SizedBox(
              height: 45.w,
            ),
            Obx(
              () => SizedBox(
                height: controller.showTable.value ? 0.w : 103.w,
              ),
            ),
            Expanded(
              child: Padding(
                padding: EdgeInsets.all(10.w),
                child: Obx(
                  () => courseListPage(context),
                ),
              ),
            ),
          ],
        ),
        // Container(
        //   padding: EdgeInsets.symmetric(horizontal: 10.w),
        //   child: Column(
        //     children: [
        //       SizedBox(
        //         height: 60.w,
        //       ),
        //       Obx(()=>SizedBox(
        //         height: controller.showTable.value?0.w:113.w,
        //       ),),
        //       Expanded(
        //         child: Obx(() => SingleChildScrollView(
        //          child:courseListPage(),
        //         )),
        //       ),
        //     ],
        //   ),
        // )
      ),
    );
  }

  _tabBannerUI(double screenWidth) {
    int buttonListNum = (controller.buttonList.length / 10).ceil();
    if (controller.buttonList.isEmpty) {
      return [Container()];
    }
    List<Widget> bannerNode = [];
    if (buttonListNum > 1) {
      for (int i = 0; i < buttonListNum; i++) {
        print(5 + (i * 10));
        print(10 + (i * 10));
        var node = Container(
          key: UniqueKey(),
          width: 340.w,
          alignment: Alignment.topLeft,
          padding: EdgeInsets.only(top: 10.w, bottom: 12.w),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10), color: Colors.white),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _tabTopUI(convertToRequiredType(controller.buttonList.sublist(
                  i * 10,
                  controller.buttonList.length > (5 + (i * 10))
                      ? (5 + (i * 10))
                      : controller.buttonList.length))),
              SizedBox(height: 10.h),
              controller.buttonList.length > (5 + (i * 10))
                  ? _tabTopUI(convertToRequiredType(controller.buttonList
                      .sublist(
                          5 + (i * 10),
                          controller.buttonList.length > 10 + (i * 10)
                              ? 10 + (i * 10)
                              : controller.buttonList.length)))
                  : Container(),
            ],
          ),
        );
        bannerNode.add(node);
      }
      return bannerNode;
    } else {
      if (controller.buttonList.length <= 5) {
        return [
          // for (int index = 0; index < controller.buttonList.length; index++) {
          //
          // }
          Container(
            width: 340.w,
            alignment: Alignment.topLeft,
            padding: EdgeInsets.only(top: 10.w, bottom: 12.w),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10), color: Colors.white),
            child: Row(
              mainAxisAlignment:MainAxisAlignment.spaceAround,
              children: [
                for (int index = 0; index < controller.buttonList.length; index++)
                GestureDetector(
                    onTap: () {
                      controller.changeTab(index);
                      // controller.onBannerListClick();
                    },
                    child: Container(
                        key: UniqueKey(),
                        width: (340.w / controller.buttonList.length -
                            40 / controller.buttonList.length)
                            .w,
                        decoration: BoxDecoration(
                          // borderRadius: BorderRadius.circular(40),
                          border: controller.buttonList.length == 5
                              ? Border(
                            right: BorderSide(
                              // 只设置右边框
                              color: HexColor('F3F8FC'), // 边框颜色
                              width: 0,
                              // 边框宽度
                            ),
                          )
                              : Border(
                            right: BorderSide(
                              // 只设置右边框
                              color: controller.buttonList.length - 1 == index
                                  ? HexColor('ffffff')
                                  : HexColor('efefef'), // 边框颜色
                              width: 1.0,
                              // 边框宽度
                            ),
                          ),
                          // color: Colors.primaries[index %
                          //     Colors.primaries.length],
                        ),
                        // margin: const EdgeInsets.all(8),
                        child: Center(
                            child: Column(
                              children: [
                                // Text(controller.buttonList.value[index]['picUrl']),
                                Offstage(
                                  offstage:
                                  controller.buttonList[index]['picUrl'].isEmpty,
                                  child: CachedNetworkImage(
                                    height: 34.h,
                                    fit: BoxFit.fill,
                                    imageUrl: controller.buttonList[index]['picUrl']!,
                                  ),
                                ),
                                Offstage(
                                  offstage:
                                  controller.buttonList[index]['picUrl'].isNotEmpty,
                                  child: CachedNetworkImage(
                                    height: 34.h,
                                    fit: BoxFit.fill,
                                    imageUrl: 'https://document.dxznjy.com/course/55e56ae65d264039bf9a8924e8134ca3.png',
                                  ),
                                ),
                                SizedBox(
                                  height: 8.h,
                                ),
                                Text(
                                  controller.buttonList[index]['name']!,
                                  style: const TextStyle(color: Colors.black),
                                ),
                              ],
                            ))))
              ],
            ),
          )
        ];
      } else {
        return [
          Container(
            width: 340.w,
            alignment: Alignment.topLeft,
            padding: EdgeInsets.only(top: 10.w, bottom: 12.w),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10), color: Colors.white),
            child: Column(
              key: UniqueKey(),
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _tabTopUI(
                    convertToRequiredType(controller.buttonList.sublist(0, 5))),
                SizedBox(height: 10.h),
                _tabTopUI(convertToRequiredType(controller.buttonList
                    .sublist(5, controller.buttonList.length))),
              ],
            ),
          )

        ];
      }

    }
  }

  _bannerUI() {
    List<Widget> bannerNode = [];
    for (int i = 0; i < controller.bannerList.length; i++) {
      var node = GestureDetector(
        onTap: () {
          TrackingUtils.trackEvent('SelectionBannerClick', {
            'banner_id': controller.bannerList[i].id,
            'common_fields': controller.bannerList[i].bannerName,
            'avitcity_id': controller.bannerList[i].activityId.isNotEmpty
                ? controller.bannerList[i].activityId
                : '',
            'goods_id': controller.bannerList[i].goodsId.isNotEmpty
                ? controller.bannerList[i].goodsId
                : '',
            'url_path': 'ZxPage'
          });
          controller.onBannerListClick(i);
        },
        child: ClipRRect(
          borderRadius: BorderRadius.all(Radius.circular(10)),
          child: CachedNetworkImage(
            width: double.infinity,
            fit: BoxFit.fill,
            imageUrl: controller.bannerList[i].bannerPicUrl,
          ),
          // Image.network(controller.bannerList[i].bannerPicUrl, fit: BoxFit.fill,),
        ),
      );
      bannerNode.add(node);
    }
    return bannerNode;
  }

  _measurenBtn() {
    return InkWell(
      onTap: () {
        controller.changeSales();
      },
      child: Container(
        padding: EdgeInsets.only(right: 10.w, left: 12.w),
        child: Row(
          children: [
            Text('销量'),
            SizedBox(
              width: 2.w,
            ),
            Obx(() => Image.asset(
                  fit: BoxFit.fitWidth,
                  controller.salesImg.value,
                  height: 12.w,
                  width: 12.w,
                )),
          ],
        ),
      ),
    );
  }

  _priceBtn() {
    return InkWell(
      onTap: () {
        controller.changePrice();
      },
      child: Container(
        padding: EdgeInsets.only(right: 10.w, left: 10.w),
        child: Row(
          children: [
            Text('价格'),
            SizedBox(
              width: 2.w,
            ),
            Obx(() => Image.asset(
                  fit: BoxFit.fitWidth,
                  controller.priceImg.value,
                  height: 12.w,
                  width: 12.w,
                )),
          ],
        ),
      ),
    );
  }

  _tabTopUI(List<Map<String, String>> items) {
    final itemWidth = (340.w / 5 - 30.w / 5).w;
    return Row(
      key: UniqueKey(),
      mainAxisAlignment:items.length==5? MainAxisAlignment.spaceAround:MainAxisAlignment.start,
      children: items.map((item) {
        return GestureDetector(
          onTap: () {
            controller.changeTabItem(item);
          },
          child: Container(
            padding: EdgeInsets.all(0.w),
            width: itemWidth,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Image.asset(
                //   item['img'] ?? 'assets/default_image.png',
                //   height: 34.h,
                //   fit: BoxFit.contain,
                // ),
                // 使用空安全的方式处理 picUrl
                if (item['picUrl'] != null && item['picUrl']!.isNotEmpty)
                  CachedNetworkImage(
                    height: 34.h,
                    fit: BoxFit.fill,
                    imageUrl: item['picUrl']!,
                  )
                else
                  //
                  CachedNetworkImage(
                    height: 34.h,
                    fit: BoxFit.fill,
                    imageUrl:'https://document.dxznjy.com/course/55e56ae65d264039bf9a8924e8134ca3.png',
                  ),
                SizedBox(height: 8.h),
                Text(
                  item['name'] ?? '未命名',
                  style: TextStyle(
                    color: controller.courseId.value == item['id']
                        ? HexColor('#249F67')
                        : Colors.black,
                    fontSize: 12.sp,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  _searchUI(Key key) {
    return Row(
      key: key,
      children: [
        // GestureDetector(
        // onTap: () {
        // Get.back();
        // },
        // child: Image.asset(
        // width: 20.w,
        // height: 16.w,
        // fit: BoxFit.fill,
        // 'assets/zx/ic_back_black.png',
        // ),
        // ),
        Expanded(
            child: Container(
          height: 34.h,
          margin: EdgeInsets.only(left: 10.w, right: 10.w),
          padding: EdgeInsets.only(left: 15.w, right: 10.w),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(40),
              // border: Border.all(color: const Color(0xFF076E57), width: 1),
              color: Colors.white),
          child: Row(
            children: [
              Image(
                image: AssetImage('assets/home/<USER>'),
                height: 20.w,
                width: 20.w,
              ),
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    TrackingUtils.trackEvent('IndexSearchHandleClick', {
                      'banner_id': '',
                      'common_fields': '',
                      'avitcity_id': '',
                      'goods_id': '',
                      'url_path': 'ZxPage'
                    });
                    String uniApp = HomeNavigator.homeSearch().path;
                    AndroidIosPlugin.openUniApp(uniApp);
                  },
                  child: Container(
                    width: double.infinity,
                    color: Colors.white,
                    alignment: Alignment.centerLeft,
                    margin: EdgeInsets.only(left: 10.w, right: 10.w),
                    height: double.infinity,
                    child: Text(
                      "请输入",
                      style: TextStyle(
                        color: HexColor('BEBEBE'),
                        fontSize: 14.sp,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ))
      ],
    );
  }

  Widget courseListPage(BuildContext context) {
    if (controller.coursesDataList.isEmpty) {
      return Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image(
              width: 50.w,
              height: 50.h,
              image: AssetImage("assets/icon_null.png")),
          SizedBox(
            height: 5.h,
          ),
          Text(
            "暂无数据",
            style: TextStyle(
                fontSize: 14.sp, color: HexColor("666666"), fontFamily: "R"),
          )
        ],
      );
    }
    return WaterLayoutList().waterLayoutList(
        context,
        controller.coursesDataList,
        2,
        controller.buttonList[controller.currentTabIndex.value]['name']);
  }

  // Widget goodsListPage() {
  //   if (controller.goodsDataList.isEmpty) {
  //     return Column(
  //       mainAxisAlignment: MainAxisAlignment.center,
  //       children: [
  //         Image(
  //             width: 50.w,
  //             height: 50.h,
  //             image: AssetImage("assets/icon_null.png")),
  //         SizedBox(
  //           height: 5.h,
  //         ),
  //         Text(
  //           "暂无数据",
  //           style: TextStyle(
  //               fontSize: 14.sp, color: HexColor("666666"), fontFamily: "R"),
  //         )
  //       ],
  //     );
  //   }
  //   return WaterLayoutList().waterLayoutList(controller.goodsDataList, 2,pageList[controller.currentTabIndex.value]);
  // }

  /// tab切换栏
  Widget _tabUI() {
    return Container(
      // width: 350.w,
      height: 39.h,
      child: Stack(
        children: [
          ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: controller.buttonList.length,
            keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
            // itemExtent: 75.w,
            itemBuilder: (BuildContext context, int index) {
              return _itemBuilder(index);
            },
          ),
          // Obx(
          //       () => AnimatedPositioned(
          //     duration: Duration(milliseconds: 100),
          //     top: 30.w,
          //     left: controller.currentTabIndex.value * 60.w + 20.w,
          //     child: Image.asset(
          //       'assets/zx/icon_move.png',
          //       height: 20.w,
          //       width: 20.w,
          //     ),
          //   ),
          // ),
        ],
      ),
    );
  }

  List<Map<String, String>> convertToRequiredType(
      List<Map<dynamic, dynamic>> originalList) {
    return originalList.map((originalMap) {
      Map<String, String> convertedMap = {};
      originalMap.forEach((key, value) {
        convertedMap[key.toString()] = value.toString();
      });
      return convertedMap;
    }).toList();
  }

  Widget _itemBuilder(int index) {
    String title = controller.buttonList[index]['name'];
    return Obx(
      () => Container(
        alignment: Alignment.center,
        child: InkWell(
          onTap: () {
            TrackingUtils.trackEvent('ProductNavigationClick', {
              'banner_id': '',
              'common_fields': title,
              'avitcity_id': '',
              'goods_id': '',
              'url_path': 'HomePage'
            });
            controller.changeTab(index);
          },
          child: Container(
            // key: _keys[index],
            // color: Colors.blue,
            padding: EdgeInsets.only(left: 12.w, right: 10.w, top: 1.h),
            child: Text(
              title,
              style: TextStyle(
                fontSize:
                    index == controller.currentTabIndex.value ? 15.sp : 14.sp,
                fontWeight: index == controller.currentTabIndex.value
                    ? FontWeight.w500
                    : FontWeight.normal,
                color: index == controller.currentTabIndex.value
                    ? HexColor('009B55')
                    : HexColor('555555'),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
