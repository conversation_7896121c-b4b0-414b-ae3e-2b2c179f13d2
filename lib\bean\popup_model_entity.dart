import 'package:student_end_flutter/generated/json/base/json_field.dart';
import 'package:student_end_flutter/generated/json/popup_model_entity.g.dart';
import 'dart:convert';
export 'package:student_end_flutter/generated/json/popup_model_entity.g.dart';

@JsonSerializable()
class PopupModelEntity {
	late int code;
	late bool success;
	late String message;
	late List<PopupModelData> data;
	dynamic total;

	PopupModelEntity();

	factory PopupModelEntity.fromJson(Map<String, dynamic> json) => $PopupModelEntityFromJson(json);

	Map<String, dynamic> toJson() => $PopupModelEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class PopupModelData {
	late String id;
	late String popupName;
	late String popupPicUrl;
	late int popupType;
	late String goodsId;
	late String color;
	late int weight;
	late String position;
	late int upType;
	late String activityId;
	late String goodsName;
	late String popupLinkUrl;
	late int popupInterval;
	late int popupStatus;
	late String effectiveStartTime;
	late String effectiveEndTime;
	dynamic lastPopTime;
	late int needLogin;
	late String createdTime;
	late String updatedTime;

	PopupModelData();

	factory PopupModelData.fromJson(Map<String, dynamic> json) => $PopupModelDataFromJson(json);

	Map<String, dynamic> toJson() => $PopupModelDataToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}