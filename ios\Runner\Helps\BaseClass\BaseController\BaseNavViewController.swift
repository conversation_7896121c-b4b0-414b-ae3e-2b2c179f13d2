//
//  BaseNavViewController.swift
//  XappTest
//
//  Created by yj9527 on 2023/2/12.
//

import QMUIKit
import UIKit

class BaseNavViewController: QMUINavigationController {
    override func viewDidLoad() {
        super.viewDidLoad()
    }

    // MARK: - 统一返回按钮配置

    override func pushViewController(_ viewController: UIViewController, animated: <PERSON><PERSON>) {
        if viewControllers.count > 0 {
            viewController.hidesBottomBarWhenPushed = true
            viewController.navigationItem.leftBarButtonItem = UIBarButtonItem(image: R.image.icon_back(), style: .plain, target: self, action: #selector(handleBackAction))
        }

        super.pushViewController(viewController, animated: animated)
    }

    // MARK: - 返回事件处理

    @objc private func handleBackAction() {
        popViewController(animated: true)
    }
}
