import 'package:student_end_flutter/generated/json/base/json_field.dart';
import 'package:student_end_flutter/generated/json/banner_bean_entity.g.dart';
import 'dart:convert';
export 'package:student_end_flutter/generated/json/banner_bean_entity.g.dart';

@JsonSerializable()
class BannerBeanEntity {
	late int code = 0;
	late bool success = false;
	late String message = '';
	late List<BannerBeanData> data = [];
	late int total = 0;

	BannerBeanEntity();

	factory BannerBeanEntity.fromJson(Map<String, dynamic> json) => $BannerBeanEntityFromJson(json);

	Map<String, dynamic> toJson() => $BannerBeanEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class BannerBeanData {
	late String id = '';
	late String bannerName = '';
	late String bannerPicUrl = '';
	late String bannerPosition = '';
	late int bannerType = 0;
	late String goodsId = '';
	late String activityId = '';
	late String goodsName = '';
	late String bannerLinkUrl = '';
	late int weight = 0;
	late int bannerStatus = 0;
	late String effectiveStartTime = '';
	late String effectiveEndTime = '';
	late int needLogin = 0;
	late String createdTime = '';
	late String updatedTime = '';
	late String goodsSharePoster = '';
	late List<dynamic> goodsShareTextList = [];

	BannerBeanData();

	factory BannerBeanData.fromJson(Map<String, dynamic> json) => $BannerBeanDataFromJson(json);

	Map<String, dynamic> toJson() => $BannerBeanDataToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}