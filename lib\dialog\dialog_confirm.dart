import 'package:flutter/material.dart';
import 'package:student_end_flutter/utils/utils.dart';

import '../common/CommonUtil.dart';

class ConfirmDialog extends StatelessWidget {
  final String title;
  final String content;
  final String confirmText;
  final String cancelText;
  final Function() onConfirm;
  final Function() onCancel;

  const ConfirmDialog({
    super.key,
    required this.title,
    required this.content,
    this.confirmText = "确定进入",
    this.cancelText = "取消",
    required this.onConfirm,
    required this.onCancel,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      backgroundColor: Colors.white,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 20, horizontal: 16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 12),
            Text(
              content,
              style: TextStyle(
                fontSize: 14,
                color: HexColor('555555'),
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                GestureDetector(
                  onTap: () {
                    Navigator.of(context).pop();
                    onConfirm();
                  },
                  child: Text(
                    confirmText,
                    style: TextStyle(
                      fontSize: 16,
                      color: HexColor('69998D'),
                    ),
                  ),
                ),
                Container(
                  height: 18,
                  color: HexColor('979797'),
                  width: 1,
                ),
                GestureDetector(
                  onTap: () {
                    Navigator.of(context).pop();
                    onCancel();
                  },
                  child: Text(
                    cancelText,
                    style: TextStyle(
                      fontSize: 16,
                      color: HexColor('28886F'),
                    ),
                  ),
                ),
              ],
            )
          ],
        ),
      ),
    );
  }
}