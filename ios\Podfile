source 'https://mirrors.tuna.tsinghua.edu.cn/git/CocoaPods/Specs.git'
# Uncomment this line to define a global platform for your project
# platform :ios, '13.0'
# source 'https://github.com/CocoaPods/Specs.git'
platform :ios, '13.0'
# CocoaPods analytics sends network stats synchronously affecting flutter build latency.
ENV['COCOAPODS_DISABLE_STATS'] = 'true'

project 'Runner', {
  'Debug' => :debug,
  'Profile' => :release,
  'Release' => :release,
}


def flutter_root
  generated_xcode_build_settings_path = File.expand_path(File.join('..', 'Flutter', 'Generated.xcconfig'), __FILE__)
  unless File.exist?(generated_xcode_build_settings_path)
    raise "#{generated_xcode_build_settings_path} must exist. If you're running pod install manually, make sure flutter pub get is executed first"
  end

  File.foreach(generated_xcode_build_settings_path) do |line|
    matches = line.match(/FLUTTER_ROOT\=(.*)/)
    return matches[1].strip if matches
  end
  raise "FLUTTER_ROOT not found in #{generated_xcode_build_settings_path}. Try deleting Generated.xcconfig, then run flutter pub get"
end

require File.expand_path(File.join('packages', 'flutter_tools', 'bin', 'podhelper'), flutter_root)

flutter_ios_podfile_setup

target 'Runner' do
  use_frameworks!
  use_modular_headers!
  

  pod 'Toast-Swift'
  
  pod 'TIMPush'

  # 解析数据
  pod 'SwiftyJSON'
  pod 'HandyJSON'

  # 腾讯UI
  pod 'QMUIKit/QMUIMainFrame'
  pod 'QMUIKit/QMUIComponents/NavigationBarTransition'
  pod 'QMUIKit/QMUIComponents/QMUIButton'
  pod 'QMUIKit/QMUIComponents/QMUITextField'
  pod 'QMUIKit/QMUIComponents/QMUIModalPresentationViewController'

  # 布局
  pod 'SnapKit'
  pod 'SnapKitExtend'
  pod 'SensorsAnalyticsSDK'
  pod 'Masonry'
  
  # 腾讯
  pod 'TCICSDK_Pro', '********'
  pod 'TXLiteAVSDK_Professional', '12.6.18866'
  
  # 加载空数据DZNEmptyDataSet, swift版本
  pod 'EmptyDataSet-Swift', '~> 5.0.0'

  # 小程序
  pod 'unimp', '= 4.29', :subspecs => ['Core', 'Audio','Video']
  
  # 支付
  pod 'WechatOpenSDK'
  
  # 客服
  pod 'QY_NIM_iOS_SDK','= 10.2.0'
  
  #其他
  pod 'DynamicColor'
  
  pod 'R.swift'
  
  pod 'Bugly'
  
  flutter_install_all_ios_pods File.dirname(File.realpath(__FILE__))
  target 'RunnerTests' do
    inherit! :search_paths
  end
end


post_install do |installer|
  installer.pods_project.targets.each do |target|

    # Flutter 的额外配置（由 Flutter 自动生成）
    flutter_additional_ios_build_settings(target)

    # 通用配置（所有 Pod 生效）
    target.build_configurations.each do |config|
      # 权限定义
      config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= [
        '$(inherited)',
        'PERMISSION_PHOTOS=1',
        'PERMISSION_CAMERA=1',
        'PERMISSION_MICROPHONE=1',
        'PERMISSION_CONTACTS=1',
        'PERMISSION_LOCATION=1'
      ]

      # 排除模拟器 arm64 架构
      config.build_settings["EXCLUDED_ARCHS[sdk=iphonesimulator*]"] = "arm64"

      # 最低 iOS 版本
      config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '13.0'
    end
    
    bitcode_strip_path = `xcrun --find bitcode_strip`.chop!
      def strip_bitcode_from_framework(bitcode_strip_path, framework_relative_path)
        framework_path = File.join(Dir.pwd, framework_relative_path)
        command = "#{bitcode_strip_path} #{framework_path} -r -o #{framework_path}"
        puts "Stripping bitcode: #{command}"
        system(command)
      end
      framework_paths = [
        "/Pods/YXAlog/YXAlog_iOS.framework/YXAlog_iOS"
      ]
      framework_paths.each do |framework_relative_path|
        strip_bitcode_from_framework(bitcode_strip_path, framework_relative_path)
      end
    
    # --- 针对 HandyJSON 的特殊配置 ---
    if target.name == 'HandyJSON'  # 错误点：缺少换行或 then
      target.build_configurations.each do |config|
        # 设置 Swift 编译模式为增量编译
        config.build_settings['SWIFT_COMPILATION_MODE'] = 'incremental'
        # 关闭优化（解决 Release 模式下的编译错误）
        config.build_settings['SWIFT_OPTIMIZATION_LEVEL'] = '-Onone'
      end
    end
  end
end

