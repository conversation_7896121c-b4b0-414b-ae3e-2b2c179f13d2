package com.dxznjy.alading.util;

import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;

import java.util.List;

public class AppUtils {
    /**
     * 检测是否安装微信
     *
     * @param context
     * @return
     */
    public static boolean isWxInstall(Context context) {
        //通过包名找应用

            boolean bHas = true;
            try {
                context.getPackageManager().getPackageInfo("com.tencent.mm", PackageManager.GET_GIDS);
            } catch (PackageManager.NameNotFoundException e) {
                // 抛出找不到的异常，说明该程序已经被卸载
                bHas = false;
            }
            return bHas;
    }

    /**
     * 检测是否安装支付宝
     *
     * @param context
     * @return
     */
    public static boolean isAlipayInstall(Context context) {
        //通过包名找应用

        boolean bHas = true;
        try {
            context.getPackageManager().getPackageInfo("com.eg.android.AlipayGphone", PackageManager.GET_GIDS);
        } catch (PackageManager.NameNotFoundException e) {
            // 抛出找不到的异常，说明该程序已经被卸载
            bHas = false;
        }
        return bHas;
    }
}
