import 'package:student_end_flutter/generated/json/base/json_convert_content.dart';
import 'package:student_end_flutter/bean/review_course_list_bean_entity.dart';

ReviewCourseListBeanEntity $ReviewCourseListBeanEntityFromJson(
    Map<String, dynamic> json) {
  final ReviewCourseListBeanEntity reviewCourseListBeanEntity = ReviewCourseListBeanEntity();
  final int? currentPage = jsonConvert.convert<int>(json['currentPage']);
  if (currentPage != null) {
    reviewCourseListBeanEntity.currentPage = currentPage;
  }
  final int? totalPage = jsonConvert.convert<int>(json['totalPage']);
  if (totalPage != null) {
    reviewCourseListBeanEntity.totalPage = totalPage;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    reviewCourseListBeanEntity.size = size;
  }
  final List<ReviewCourseListBeanData>? data = (json['data'] as List<dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<ReviewCourseListBeanData>(
          e) as ReviewCourseListBeanData)
      .toList();
  if (data != null) {
    reviewCourseListBeanEntity.data = data;
  }
  return reviewCourseListBeanEntity;
}

Map<String, dynamic> $ReviewCourseListBeanEntityToJson(
    ReviewCourseListBeanEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['currentPage'] = entity.currentPage;
  data['totalPage'] = entity.totalPage;
  data['size'] = entity.size;
  data['data'] = entity.data.map((v) => v.toJson()).toList();
  return data;
}

extension ReviewCourseListBeanEntityExtension on ReviewCourseListBeanEntity {
  ReviewCourseListBeanEntity copyWith({
    int? currentPage,
    int? totalPage,
    int? size,
    List<ReviewCourseListBeanData>? data,
  }) {
    return ReviewCourseListBeanEntity()
      ..currentPage = currentPage ?? this.currentPage
      ..totalPage = totalPage ?? this.totalPage
      ..size = size ?? this.size
      ..data = data ?? this.data;
  }
}

ReviewCourseListBeanData $ReviewCourseListBeanDataFromJson(
    Map<String, dynamic> json) {
  final ReviewCourseListBeanData reviewCourseListBeanData = ReviewCourseListBeanData();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    reviewCourseListBeanData.id = id;
  }
  final int? meetingId = jsonConvert.convert<int>(json['meetingId']);
  if (meetingId != null) {
    reviewCourseListBeanData.meetingId = meetingId;
  }
  final String? meetingNum = jsonConvert.convert<String>(json['meetingNum']);
  if (meetingNum != null) {
    reviewCourseListBeanData.meetingNum = meetingNum;
  }
  final String? subject = jsonConvert.convert<String>(json['subject']);
  if (subject != null) {
    reviewCourseListBeanData.subject = subject;
  }
  final int? startTime = jsonConvert.convert<int>(json['startTime']);
  if (startTime != null) {
    reviewCourseListBeanData.startTime = startTime;
  }
  final int? endTime = jsonConvert.convert<int>(json['endTime']);
  if (endTime != null) {
    reviewCourseListBeanData.endTime = endTime;
  }
  final String? destroyTime = jsonConvert.convert<String>(json['destroyTime']);
  if (destroyTime != null) {
    reviewCourseListBeanData.destroyTime = destroyTime;
  }
  final String? password = jsonConvert.convert<String>(json['password']);
  if (password != null) {
    reviewCourseListBeanData.password = password;
  }
  final int? state = jsonConvert.convert<int>(json['state']);
  if (state != null) {
    reviewCourseListBeanData.state = state;
  }
  final String? meetingShortNum = jsonConvert.convert<String>(
      json['meetingShortNum']);
  if (meetingShortNum != null) {
    reviewCourseListBeanData.meetingShortNum = meetingShortNum;
  }
  final String? roomArchiveId = jsonConvert.convert<String>(
      json['roomArchiveId']);
  if (roomArchiveId != null) {
    reviewCourseListBeanData.roomArchiveId = roomArchiveId;
  }
  final String? userNickName = jsonConvert.convert<String>(
      json['userNickName']);
  if (userNickName != null) {
    reviewCourseListBeanData.userNickName = userNickName;
  }
  final String? meetingStartBegin = jsonConvert.convert<String>(
      json['meetingStartBegin']);
  if (meetingStartBegin != null) {
    reviewCourseListBeanData.meetingStartBegin = meetingStartBegin;
  }
  final String? meetingEndBegin = jsonConvert.convert<String>(
      json['meetingEndBegin']);
  if (meetingEndBegin != null) {
    reviewCourseListBeanData.meetingEndBegin = meetingEndBegin;
  }
  final String? sharedScreenStart = jsonConvert.convert<String>(
      json['sharedScreenStart']);
  if (sharedScreenStart != null) {
    reviewCourseListBeanData.sharedScreenStart = sharedScreenStart;
  }
  final String? sharedScreenEnd = jsonConvert.convert<String>(
      json['sharedScreenEnd']);
  if (sharedScreenEnd != null) {
    reviewCourseListBeanData.sharedScreenEnd = sharedScreenEnd;
  }
  final String? studentCode = jsonConvert.convert<String>(json['studentCode']);
  if (studentCode != null) {
    reviewCourseListBeanData.studentCode = studentCode;
  }
  final String? studentUuid = jsonConvert.convert<String>(json['studentUuid']);
  if (studentUuid != null) {
    reviewCourseListBeanData.studentUuid = studentUuid;
  }
  final String? studentToken = jsonConvert.convert<String>(
      json['studentToken']);
  if (studentToken != null) {
    reviewCourseListBeanData.studentToken = studentToken;
  }
  final String? inviteUrl = jsonConvert.convert<String>(json['inviteUrl']);
  if (inviteUrl != null) {
    reviewCourseListBeanData.inviteUrl = inviteUrl;
  }
  final String? courseId = jsonConvert.convert<String>(json['courseId']);
  if (courseId != null) {
    reviewCourseListBeanData.courseId = courseId;
  }
  final String? courseName = jsonConvert.convert<String>(json['courseName']);
  if (courseName != null) {
    reviewCourseListBeanData.courseName = courseName;
  }
  final String? orderId = jsonConvert.convert<String>(json['orderId']);
  if (orderId != null) {
    reviewCourseListBeanData.orderId = orderId;
  }
  final String? courseTimeStr = jsonConvert.convert<String>(
      json['courseTimeStr']);
  if (courseTimeStr != null) {
    reviewCourseListBeanData.courseTimeStr = courseTimeStr;
  }
  final String? createTime = jsonConvert.convert<String>(json['createTime']);
  if (createTime != null) {
    reviewCourseListBeanData.createTime = createTime;
  }
  final dynamic updateTime = json['updateTime'];
  if (updateTime != null) {
    reviewCourseListBeanData.updateTime = updateTime;
  }
  final dynamic scheduledMembers = json['scheduledMembers'];
  if (scheduledMembers != null) {
    reviewCourseListBeanData.scheduledMembers = scheduledMembers;
  }
  final String? studentName = jsonConvert.convert<String>(json['studentName']);
  if (studentName != null) {
    reviewCourseListBeanData.studentName = studentName;
  }
  return reviewCourseListBeanData;
}

Map<String, dynamic> $ReviewCourseListBeanDataToJson(
    ReviewCourseListBeanData entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['meetingId'] = entity.meetingId;
  data['meetingNum'] = entity.meetingNum;
  data['subject'] = entity.subject;
  data['startTime'] = entity.startTime;
  data['endTime'] = entity.endTime;
  data['destroyTime'] = entity.destroyTime;
  data['password'] = entity.password;
  data['state'] = entity.state;
  data['meetingShortNum'] = entity.meetingShortNum;
  data['roomArchiveId'] = entity.roomArchiveId;
  data['userNickName'] = entity.userNickName;
  data['meetingStartBegin'] = entity.meetingStartBegin;
  data['meetingEndBegin'] = entity.meetingEndBegin;
  data['sharedScreenStart'] = entity.sharedScreenStart;
  data['sharedScreenEnd'] = entity.sharedScreenEnd;
  data['studentCode'] = entity.studentCode;
  data['studentUuid'] = entity.studentUuid;
  data['studentToken'] = entity.studentToken;
  data['inviteUrl'] = entity.inviteUrl;
  data['courseId'] = entity.courseId;
  data['courseName'] = entity.courseName;
  data['orderId'] = entity.orderId;
  data['courseTimeStr'] = entity.courseTimeStr;
  data['createTime'] = entity.createTime;
  data['updateTime'] = entity.updateTime;
  data['scheduledMembers'] = entity.scheduledMembers;
  data['studentName'] = entity.studentName;
  return data;
}

extension ReviewCourseListBeanDataExtension on ReviewCourseListBeanData {
  ReviewCourseListBeanData copyWith({
    int? id,
    int? meetingId,
    String? meetingNum,
    String? subject,
    int? startTime,
    int? endTime,
    String? destroyTime,
    String? password,
    int? state,
    String? meetingShortNum,
    String? roomArchiveId,
    String? userNickName,
    String? meetingStartBegin,
    String? meetingEndBegin,
    String? sharedScreenStart,
    String? sharedScreenEnd,
    String? studentCode,
    String? studentUuid,
    String? studentToken,
    String? inviteUrl,
    String? courseId,
    String? courseName,
    String? orderId,
    String? courseTimeStr,
    String? createTime,
    dynamic updateTime,
    dynamic scheduledMembers,
    String? studentName,
  }) {
    return ReviewCourseListBeanData()
      ..id = id ?? this.id
      ..meetingId = meetingId ?? this.meetingId
      ..meetingNum = meetingNum ?? this.meetingNum
      ..subject = subject ?? this.subject
      ..startTime = startTime ?? this.startTime
      ..endTime = endTime ?? this.endTime
      ..destroyTime = destroyTime ?? this.destroyTime
      ..password = password ?? this.password
      ..state = state ?? this.state
      ..meetingShortNum = meetingShortNum ?? this.meetingShortNum
      ..roomArchiveId = roomArchiveId ?? this.roomArchiveId
      ..userNickName = userNickName ?? this.userNickName
      ..meetingStartBegin = meetingStartBegin ?? this.meetingStartBegin
      ..meetingEndBegin = meetingEndBegin ?? this.meetingEndBegin
      ..sharedScreenStart = sharedScreenStart ?? this.sharedScreenStart
      ..sharedScreenEnd = sharedScreenEnd ?? this.sharedScreenEnd
      ..studentCode = studentCode ?? this.studentCode
      ..studentUuid = studentUuid ?? this.studentUuid
      ..studentToken = studentToken ?? this.studentToken
      ..inviteUrl = inviteUrl ?? this.inviteUrl
      ..courseId = courseId ?? this.courseId
      ..courseName = courseName ?? this.courseName
      ..orderId = orderId ?? this.orderId
      ..courseTimeStr = courseTimeStr ?? this.courseTimeStr
      ..createTime = createTime ?? this.createTime
      ..updateTime = updateTime ?? this.updateTime
      ..scheduledMembers = scheduledMembers ?? this.scheduledMembers
      ..studentName = studentName ?? this.studentName;
  }
}