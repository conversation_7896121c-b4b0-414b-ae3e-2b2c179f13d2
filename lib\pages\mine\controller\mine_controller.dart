import 'dart:async';
import 'dart:convert';
import 'package:bot_toast/bot_toast.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:student_end_flutter/bean/utils/share_app_android_ios.dart';
import 'package:student_end_flutter/components/verification_code_input.dart';
import 'package:student_end_flutter/pages/base/controller/base_controller.dart';
import 'package:student_end_flutter/utils/event_bus_utils.dart';
import 'package:student_end_flutter/utils/sharedPreferences_util.dart';
import '../../../bean/popup_model_entity.dart';
import '../../../bean/user_info_bean_entity.dart';
import '../../../common/Config.dart';
import '../../../common/pop_option.dart';
import '../../../common/user_option.dart';
import '../../../components/toast_utils.dart';
import '../../../dialog/dialog_hint_pop.dart';
import '../../../dialog/dialog_share.dart';
import '../../../generated/json/base/json_convert_content.dart';
import '../../../navigator/mine_navigator.dart';
import '../../../utils/android_ios_plugin.dart';
import '../../../utils/app_version_util.dart';
import '../../../utils/httpRequest.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:student_end_flutter/navigator/home_navigator.dart';
import '../../../common/sensors_analytics_option.dart';

///会员续费  会员开通两种状态
class MineController extends GetxController
    with GetSingleTickerProviderStateMixin {
  static const String phoneNumber = "************"; // 投诉电话

  var topClickList = [
    {"img": "assets/mine/icon-qianbao.png", "name": "我的钱包"},
    {"img": "", "name": "积分", 'num': ''},
    {"img": "", "name": "鼎币", 'num': ''},
    // {"img": "assets/mine/icon_daishouhuo.png", "name": "收货地址"},
    {"img": "", "name": "优惠券", 'num': ''}
  ];

  List<Map<String, String>> utilsClickList = [
    {"img": "assets/mine/icon-dingdan.png", "name": "我的订单"},
    {"img": "assets/mine/icon_daishouhuo.png", "name": "收货地址"},
    {"img": "assets/mine/icon-wodeshoucang.png", "name": "我的收藏"},
    {"img": "assets/mine/icon-wodeshoucang.png", "name": "我的拼团"},
    {"img": "assets/mine/icon_tuijianfantuijian.png", "name": "试课列表"},
    {"img": "assets/mine/icon_cleanCache.png", "name": "清除缓存"},
    // {"img": "assets/mine/icon_chongzhi.png", "name": "时长充值"},
    // {"img": "assets/mine/icon_shangke.png", "name": "上课信息对接表"},
    // {"img": "assets/mine/icon_yijian.png", "name": "意见反馈"},
    // {"img": "assets/mine/icon_kefu.png", "name": "联系客服"},
    // {"img": "assets/mine/icon_yaoqingyouli.png", "name": "邀请有礼"},
    // {"img": "assets/mine/icon_hetong.png", "name": "合同管理"},
    // {"img": "assets/mine/icon_tousudianhua.png", "name": "投诉电话"},
    // {"img": "assets/mine/icon_protocolList.png", "name": "协议及政策"},
    // {"img": "assets/mine/icon_yingxiao.png", "name": "营销小组"},
    // {"img": "assets/mine/icon_pintuan.png", "name": "我的拼团"},
    // {"img": "assets/mine/icon_zhuxiao.png", "name": "注销账号"},
    // {"img": "assets/mine/icon_cleanCache.png", "name": "清除缓存"}
  ].obs;

  var inViteGift = {"img": "assets/mine/icon_yaoqingyouli.png", "name": "邀请有礼"};

  var currentTabIndex = 0.obs;
  var roleClickList = {
    "家长会员": [
      {"img": "assets/mine/icon_huiyuanfenxiang.png", "name": "会员分享"},
      {"img": "assets/mine/icon_shouyimingxi.png", "name": "收益明细"},
    ],
    "超级会员": [
      {"img": "assets/mine/icon_tuijianhuiyuan.png", "name": "推荐会员"},
      {"img": "assets/mine/icon_shouyimingxi.png", "name": "收益明细"},
      {"img": "assets/mine/icon_huiyuanxinxi.png", "name": "会员消息"},
    ],
    "超级合伙人": [
      {"img": "assets/mine/icon_tuijianhuiyuan.png", "name": "会员"},
      {"img": "assets/mine/icon_shouyimingxi.png", "name": "我的采购"},
      {"img": "assets/mine/icon_huiyuanxinxi.png", "name": "收益明细"},
      {"img": "assets/mine/icon_huiyuanxinxi.png", "name": "实名认证"},
      {"img": "assets/mine/icon_huiyuanxinxi.png", "name": "会员消息"},
      {"img": "assets/mine/icon_huiyuanxinxi.png", "name": "编码"},
      {"img": "assets/mine/icon_huiyuanxinxi.png", "name": "成长中心"},
      {"img": "assets/mine/icon_huiyuanxinxi.png", "name": "会员分享"},
      {"img": "assets/mine/icon_huiyuanxinxi.png", "name": "购买会员"},
      {"img": "assets/mine/icon_huiyuanxinxi.png", "name": "预估收益"},
      {"img": "assets/mine/icon_huiyuanxinxi.png", "name": "喜报"},
    ],
    "超级俱乐部": [
      {"img": "assets/mine/icon_tuijianhuiyuan.png", "name": "会员"},
      {"img": "assets/mine/icon_shouyimingxi.png", "name": "采购单"},
      {"img": "assets/mine/icon_huiyuanxinxi.png", "name": "邀请码采购"},
      {"img": "assets/mine/icon_huiyuanxinxi.png", "name": "收益明细"},
      {"img": "assets/mine/icon_huiyuanxinxi.png", "name": "实名认证"},
      {"img": "assets/mine/icon_huiyuanxinxi.png", "name": "编码"},
      {"img": "assets/mine/icon_huiyuanxinxi.png", "name": "成长中心"},
      {"img": "assets/mine/icon_huiyuanxinxi.png", "name": "预估收益"},
    ],
    "超级品牌": [
      {"img": "assets/mine/icon_tuijianhuiyuan.png", "name": "会员"},
      {"img": "assets/mine/icon_shouyimingxi.png", "name": "采购单"},
      {"img": "assets/mine/icon_huiyuanxinxi.png", "name": "邀请码采购"},
      {"img": "assets/mine/icon_huiyuanxinxi.png", "name": "收益明细"},
      {"img": "assets/mine/icon_huiyuanxinxi.png", "name": "实名认证"},
      {"img": "assets/mine/icon_huiyuanxinxi.png", "name": "编码"},
      {"img": "assets/mine/icon_huiyuanxinxi.png", "name": "成长中心"},
      {"img": "assets/mine/icon_huiyuanxinxi.png", "name": "预估收益"},
    ]
  };
  List<GlobalKey> keys = [];

  RxList<String> roleList = RxList<String>([]);
  RxList<String> roleUseList = RxList<String>([]);
  var isMerchant = false.obs;
  RxString dingBi = "0".obs;
  RxString creditCount = "0".obs;
  RxString couponCount = "0".obs;
  Rx<UserInfoBeanEntity> userInfoBeanEntity = UserInfoBeanEntity().obs;
  RxBool showActivityIcon = false.obs;
  RxBool showHasSignedInToday = false.obs;
  int activityId = -1;
  RxString ifShowInvite = "".obs;
  RxBool showParentVipBuyMember = false.obs;
  RxBool showBuyMember = false.obs;

  RxBool isDisable = false.obs;
  RxBool isNeedUp = false.obs;

  var refreshTime;
  double bottomPosW = 30.w;

  @override
  Future<void> onInit() async {
    super.onInit();
    onClickCheckVer();
    await UserOption.getUserInfoRequest(false);
    final BaseController baseController = Get.find<BaseController>();
    baseController.getMessageNotice(); // 进入页面时获取消息状态
    EventBusUtils.eventBus.on<EventUtils>().listen((event) {
      print("==refreshUserInfo===mine=");
      var now = DateTime.now();
      if (refreshTime == null) {
        refreshTime = now;
        refreshUserInfo();
      }
      if (now.difference(refreshTime) > Duration(seconds: 2)) {
        refreshUserInfo();
      }
    });
  }

  Future<void> onRefresh() async {
    UserOption.getUserInfoRequest(false);
    // ifShowInviteFunc();
  }

  refreshUserInfo() {
    userInfoBeanEntity.value = UserOption.userInfoBeanEntity!;
    //切换账号与刷新时 重新判断身份是否展示邀请有礼
    showActivityIcon.value = false;
    print(utilsClickList);
    print('vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv');
    if (utilsClickList[5]["name"] == "邀请有礼") {
      utilsClickList.removeAt(5);
    }
    print("${utilsClickList.length}");
    isHaveRole();
    setVipRenew();
    //身份判断是否展示邀请有礼
    // ifShowInviteFunc();
  }

  /// 续费按钮
  setVipRenew() {
    showParentVipBuyMember.value = UserOption.showParentVipBuyMember;
    showBuyMember.value = UserOption.showBuyMember;
  }

  /// 是否超级会员  0家长  1超人 4超级会员  parentMemberType == 5家长会员
  isHaveRole() {
    roleList.value = [];
    roleUseList.value = [];
    if (userInfoBeanEntity.value.identityType == '') {
      showActivityIcon.value = true;
    }
    if (userInfoBeanEntity.value.identityType == 0) {
      showActivityIcon.value = true;
      roleList.add("家长");
    }
    if (userInfoBeanEntity.value.identityType == 1) {
      showActivityIcon.value = true;
      roleList.add("超人");
    }
    if (userInfoBeanEntity.value.identityType == 4) {
      showActivityIcon.value = true;
      roleList.add("超级会员");
      roleUseList.add("超级会员");
    }
    if (userInfoBeanEntity.value.parentMemberType == 5) {
      showActivityIcon.value = true;
      roleList.add("家长会员");
      roleUseList.add("家长会员");
    }
    if (userInfoBeanEntity.value.merchantCode.isNotEmpty) {
      showActivityIcon.value = true;
      roleList.add("超级合伙人");
    }
    if (userInfoBeanEntity.value.julebuCode.isNotEmpty) {
      showActivityIcon.value = true;
      roleList.add("超级俱乐部");
    }
    // if(userInfoBeanEntity.value.brandCode.isNotEmpty){
    //   showActivityIcon.value = true;
    //   roleList.add("超级品牌");
    // }
    getCreditCount();
    getDingBi();
    getCouponCount();
    getHasSignedlnToday();
    keys = List.generate(roleList.length, (index) => GlobalKey());
  }

  changeTab(index) {
    currentTabIndex.value = index;
  }

  getExpireTime() {
    if (utilsClickList[utilsClickList.length - 1]['name'] == '会员消息') {
      utilsClickList.removeLast();
    }
    if (roleUseList.isEmpty) {
      return "";
    }
    if (roleUseList[currentTabIndex.value] == "家长会员") {
      return "已持续${userInfoBeanEntity.value.parentMemberDay}天 有效期至${userInfoBeanEntity.value.parentMemberEndTime}";
    } else if (roleUseList[currentTabIndex.value] == "超级会员") {
      if (utilsClickList[utilsClickList.length - 1]['name'] != '会员消息') {
        utilsClickList.add(
          {"img": "assets/mine/icon-huiyuan.png", "name": "会员消息"},
        );
      }
      return "已持续${userInfoBeanEntity.value.memberDay}天 有效期至${userInfoBeanEntity.value.expireTime}";
    } else {
      return "";
    }
  }

  getHead() {
    if (userInfoBeanEntity.value.headPortrait == "") {
      return AssetImage("assets/mine/home_avaUrl.png");
    }
    return NetworkImage(userInfoBeanEntity.value.headPortrait);
  }

  @override
  void onClose() {
    super.onClose();
  }

  getHasSignedlnToday() {
    var response = HttpUtil()
        .get('${Config.hasSignedInToday}?userId=${UserOption.userId}');
    response.then((value) async {
      showHasSignedInToday.value = value;
    });
  }

  /// 积分余额
  getCreditCount() {
    var response =
        HttpUtil().get('${Config.getCreditCount}?userId=${UserOption.userId}');
    response.then((value) async {
      creditCount.value = value ?? '0';
      topClickList[1]['num'] = creditCount.value;
    });
  }

  /// 鼎币余额
  getDingBi() {
    var response = HttpUtil().get(Config.getDingBi);
    response.then((value) async {
      dingBi.value = value ?? '0';
      topClickList[2]['num'] = dingBi.value;
    });
  }

  getCouponCount() {
    var response = HttpUtil()
        .get('${Config.getCoupon}?userId=${UserOption.userId}&useStatus=1');
    response.then((value) async {
      couponCount.value = value.toString();
      topClickList[3]['num'] = couponCount.value;
    });
  }

  /// 判断是不是门店
  getSchoolMerchantByName() {
    if (isDisable.value) {
      ToastUtil.showShortErrorToast("请勿重复点击");
      return;
    }
    isDisable.value = true;
    Timer(Duration(seconds: 3), () {
      isDisable.value = false;
    });
    BotToast.showLoading();
    var response = HttpUtil().get(Config.getSchoolMerchantByName);
    response.then((value) async {
      isMerchant.value = value;
      if (isMerchant.value == true) {
        getPayToken();
      } else {
        BotToast.closeAllLoading();
        isDisable.value = false;
        ToastUtil.showShortErrorToast("您还不是门店");
      }
    });
  }

  /// 是否有邀请有礼
  ifShowInviteFunc() {
    var response = HttpUtil().get(Config.ifShowInvite);
    response.then((value) async {
      activityId = value["activityId"];
      ifShowInvite.value = value["ifShowInvite"];
      if (utilsClickList[5]["name"] != "邀请有礼") {
        //  切换账号 和已登录刷新 未添加，符合身份才增加邀请有礼
        if ((showActivityIcon.value &&
            (ifShowInvite == "1" || ifShowInvite == "2"))) {
          utilsClickList.insert(5, inViteGift);
        }
      }
    });
  }

  /// 时长充值获取的token
  getPayToken() {
    try {
      var responseToken = HttpUtil()
          .get("${Config.getPayToken}?memberToken=${UserOption.token}");
      responseToken.then((patToken) async {
        String payToken = patToken["token"];
        UserOption.payToken = payToken;
        var response = HttpUtil().get(Config.getNewsStore);
        response.then((value) async {
          BotToast.closeAllLoading();
          int schoolType = value["schoolType"];
          String merchantCode = value["merchantCode"];
          String path = MineNavigator.recharge(
                  schoolType: schoolType.toString(),
                  merchantCode: merchantCode,
                  paytoken: payToken)
              .path;
          AndroidIosPlugin.openUniApp(path);
          UserOption.payToken = "";
        });
      });
    } catch (e) {
      isDisable.value = false;
      BotToast.closeAllLoading();
    }
  }

  ///////////////////点击事件//////////////////
  /// 头像
  headUrlClick() {
    String uniApp = MineNavigator.mineInfo().path;
    AndroidIosPlugin.openUniApp(uniApp);
  }

  /// 积分中心
  integrationCenterClick() {
    TrackingUtils.trackEvent('IntegralCenterClick', {
      'common_fields': showHasSignedInToday.value
          ? "已签到"
          : '签到领取积分',
      'avitcity_id': '',
      'goods_id': '',
      'banner_id': '',
      'url_path': 'MinePage'
    });
    String uniApp = MineNavigator.mypoints().path;
    AndroidIosPlugin.openUniApp(uniApp);
  }

  /// 查看权益
  seeRewards() {
    String path = MineNavigator.parentEquity().path;
    AndroidIosPlugin.openUniApp(path);
  }

  /// 鼎币商城
  shoppingMall() {
    String path = MineNavigator.shoppingMall().path;
    AndroidIosPlugin.openUniApp(path);
  }

  /// 立即开通
  atOnceOpen() {
    String path = MineNavigator.parentVipEquity().path;
    AndroidIosPlugin.openUniApp(path);
  }

  /// 顶部按钮
  topClick(data) async {
    print('333333333333333333333333333333333333333333333333333');
    print(data);
    String path = '';
    print(data["name"]);
    switch (data["name"]) {
      case "信息中心":
        path = MineNavigator.informationCenter().path;
        await AndroidIosPlugin.openUniApp(path).then((_) {
          // 返回刷新消息
          Get.find<BaseController>().getMessageNotice();
        });
      case '鼎币':
        path = HomeNavigator.shoppingMall().path;
        break;
      case '积分':
        if(roleUseList.length>0){
          path = MineNavigator.incomeDetails().path;
        }else{
          path = MineNavigator.incomeDetails(type: 0).path;
        }

      case "优惠券":
        path = MineNavigator.coupons().path;
        break;
    }
    AndroidIosPlugin.openUniApp(path);
  }

  /// 工具与服务
  Future<void> utilsClick(data, context) async {
    String path = '';
    switch (data["name"]) {
      case "试课列表":
        path = MineNavigator.recommend().path;
        break;
      case "时长充值":
        getSchoolMerchantByName();
        break;
      case "我的订单":
        path = MineNavigator.myOrder().path;
        break;
      case "我的收藏":
        path = MineNavigator.myCollection().path;
        break;
      case "收货地址":
        path = MineNavigator.shippingAddress().path;
        break;
      // case "上课信息对接表":
      //   path = MineNavigator.onlineJoinTable().path;
      //   break;
      case "意见反馈":
        path = MineNavigator.suggest().path;
        break;
      case "合同管理":
        path = MineNavigator.contract().path;
        break;
      case "邀请有礼":
        if (ifShowInvite.value == "1") {
          path =
              MineNavigator.invitationGifts(activityId: activityId.toString())
                  .path;
        } else {
          ToastUtil.showShortErrorToast("活动未开始");
        }
        break;
      case "协议及政策":
        path = MineNavigator.protocolList().path;
        break;
      case "联系客服":
        return contactCustomerService();
      case "投诉电话":
        return complaintsHotline();
      case "注销账号":
        return UserOption.logoutFunc(true);
      case "营销小组":
        return showMarketingCodeDialog(context);
      case "我的拼团":
        path = MineNavigator.myGroupPurchase().path;
        break;
      case "会员消息":
        path = MineNavigator.memberMessage().path;
        break;
      case "清除缓存":
        await SharedPreferencesUtil.clearAll();
        ToastUtil.showShortSuccessToast("缓存已清理");
        return;
    }
    if (path.isNotEmpty) {
      AndroidIosPlugin.openUniApp(path);
    }
  }

  /// 营销码验证弹窗
  void showMarketingCodeDialog(BuildContext context) {
    VerificationCodeInput.show(
      context,
      title: '输入营销码',
      buttonText: '确认',
      initialErrorText: null,
      buttonColor: Colors.green,
      onVerify: (code) async {
        if (code == '184887') {
          Future.delayed(Duration(milliseconds: 300), () {
            AndroidIosPlugin.openUniApp(MineNavigator.marketingGroup().path);
          });
          return true;
        } else {
          return false;
        }
      },
    );
  }

  /// 角色部分按钮
  roleClick(data) {
    String path = '';
    if (roleUseList[currentTabIndex.value] == "家长会员") {
      switch (data["name"]) {
        case "会员分享":
          return shareVIP();
        case "收益明细":
          path = MineNavigator.incomeDetails().path;
          break;
      }
    } else if (roleUseList[currentTabIndex.value] == "超级会员") {
      switch (data["name"]) {
        case "推荐会员":
          return shareVIP();
        case "收益明细":
          path = MineNavigator.incomeDetails().path;
          break;
        case "会员消息":
          path = MineNavigator.memberMessage().path;
          break;
      }
    }
    if (path.isNotEmpty) {
      AndroidIosPlugin.openUniApp(path);
    }
  }

  showDetails() {
    String path = MineNavigator.incomeDetails(type: 0).path;
    AndroidIosPlugin.openUniApp(path);
  }

  /// 分享
  shareVIP() {
    String path = MineNavigator.shareVIP().path;
    ShareAppAndroidIos shareAppAndroidIos = ShareAppAndroidIos();
    shareAppAndroidIos.path = path;
    Get.bottomSheet(
      ShareDialog(shareAppAndroidIos, '', 'MinePage'),
      backgroundColor: Colors.transparent, // 透明背景
      isDismissible: true, // 点击外部关闭
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
    );
  }

  /// 联系客服
  contactCustomerService() {
    AndroidIosPlugin.opneCustomerService();
  }

  /// 投诉电话
  complaintsHotline() {
    DialogUtils.showSimpleDialog(
      content: phoneNumber,
      rightButtonAction: () {
        launchPhoneCall();
      },
    );
  }

  // 拨号功能
  Future<void> launchPhoneCall() async {
    final Uri phoneUri = Uri.parse("tel:$phoneNumber");
    if (await canLaunchUrl(phoneUri)) {
      await launchUrl(phoneUri);
    } else {
      throw "无法拨打电话";
    }
  }

  /// 更新版本
  onClickCheckVer({isShowTips = false}) async {
    isNeedUp.value = await AppVersionUtil.checkVersion(isShowTips: isShowTips);
  }
}
