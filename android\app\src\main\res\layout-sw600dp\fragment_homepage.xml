<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/_F7FAFB">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1.2"
            android:background="@mipmap/ic_home_bg_top"
            >
            <RelativeLayout
                android:id="@+id/rl_homepage_top"
                android:layout_width="match_parent"
                android:layout_height="200dp"
                android:paddingTop="20dp"
                >
                <LinearLayout
                    android:id="@+id/ll_name"
                    android:layout_width="48dp"
                    android:layout_marginLeft="14dp"
                    android:layout_centerVertical="true"
                    android:gravity="center"
                    android:background="@drawable/bg_circle_12c287"
                    android:layout_height="48dp">
                    <TextView
                        android:id="@+id/tv_fristname"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="王"
                        android:textSize="20sp"
                        android:textColor="@color/white"
                        />
                </LinearLayout>
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_toRightOf="@+id/ll_name"
                    android:layout_marginLeft="10dp"
                    android:layout_centerVertical="true"
                    android:orientation="vertical">
                    <TextView
                        android:id="@+id/tv_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/_333333"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:text="学员姓名"/>
                    <TextView
                        android:id="@+id/tv_gradename"
                        android:layout_width="80dp"
                        android:layout_height="20dp"
                        android:textColor="@color/white"
                        android:layout_marginTop="6dp"
                        android:textSize="12sp"
                        android:gravity="center"
                        android:background="@mipmap/icon_biaoqian"
                        android:text="小学几年级"/>
                </LinearLayout>
                <ImageView
                    android:layout_width="150dp"
                    android:layout_height="150dp"
                    android:layout_alignParentRight="true"
                    android:layout_marginRight="20dp"
                    android:layout_marginTop="20dp"
                    android:src="@mipmap/ic_home_top_pet"/>
            </RelativeLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp"
                android:layout_marginTop="-60dp"
                android:layout_below="@+id/rl_homepage_top"
                android:background="@mipmap/ic_home_kecheng_bg"
                android:layout_marginBottom="10dp"
                android:paddingLeft="20dp"
                android:paddingRight="20dp"
                android:paddingBottom="10dp"
                android:orientation="vertical">
                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="40dp">
                    <View
                        android:layout_width="36dp"
                        android:layout_height="4dp"
                        android:layout_below="@+id/tv_tips"
                        android:layout_marginTop="-6dp"
                        android:background="@drawable/bg_green_line"/>
                    <TextView
                        android:id="@+id/tv_tips"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:textSize="16sp"
                        android:textColor="@color/_333333"
                        android:layout_centerVertical="true"
                        android:textStyle="bold"
                        android:text="今日待上课程"/>
                </RelativeLayout>
                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="10dp"
                    >
                    <ImageView
                        android:id="@+id/img_class_bg"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:scaleType="fitXY"
                        android:src="@mipmap/pic_daishangke"/>
                    <LinearLayout
                        android:id="@+id/ll_class_content"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:paddingTop="14dp"
                        android:paddingLeft="20dp"
                        android:paddingRight="14dp"
                        android:layout_marginBottom="10dp"
                        >
                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">
                            <ImageView
                                android:id="@+id/img_head1"
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:src="@mipmap/ic_launcher"/>
                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textSize="14sp"
                                android:layout_toRightOf="@+id/img_head1"
                                android:layout_centerVertical="true"
                                android:textColor="@color/_555555"
                                android:singleLine="true"
                                android:layout_marginLeft="14dp"
                                android:text="主讲师:张老师"/>


                        </RelativeLayout>
                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:textSize="16sp"
                            android:textColor="@color/_3f8970"
                            android:singleLine="true"
                            android:textStyle="bold"
                            android:layout_marginTop="14dp"
                            android:text="注意力学习课"/>
                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:textSize="14sp"
                            android:layout_marginTop="4dp"
                            android:textColor="@color/_555555"
                            android:singleLine="true"
                            android:layout_marginBottom="10dp"
                            android:text="上课时间：2024-11-07 19:30:00"/>

                        <TextView
                            android:id="@+id/tv_golessons"
                            android:layout_width="160dp"
                            android:layout_height="44dp"
                            android:textSize="14sp"
                            android:textColor="#DADEE3"
                            android:gravity="center"
                            android:layout_gravity="center"
                            android:background="@mipmap/ic_shangke"
                            />
                    </LinearLayout>
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:id="@+id/ll_noclass"
                        android:visibility="gone"
                        android:background="@drawable/bg_radius10_white">
                        <ImageView
                            android:layout_width="44dp"
                            android:layout_height="44dp"
                            android:src="@mipmap/icon_zanwukengcheng"/>
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="#B0AEAE"
                            android:textSize="14sp"
                            android:layout_marginTop="10dp"
                            android:text="你今天没有待上的课程呦~"/>
                    </LinearLayout>
                </RelativeLayout>
            </LinearLayout>
        </RelativeLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:orientation="vertical"
            android:layout_marginLeft="14dp"
            android:layout_marginRight="14dp"
            android:layout_weight="1"
            android:layout_marginBottom="20dp"
            android:paddingLeft="14dp"
            android:paddingRight="14dp"
            android:background="@mipmap/ic_home_xuexiziliao_bg">
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="14dp"
                >
                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:orientation="vertical"
                    android:layout_centerVertical="true"
                    android:layout_height="wrap_content">
                    <View
                        android:layout_width="36dp"
                        android:layout_height="4dp"
                        android:layout_marginTop="-6dp"
                        android:layout_below="@+id/tv_study_tips"
                        android:background="@drawable/bg_green_line"/>
                    <TextView
                        android:id="@+id/tv_study_tips"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="16sp"
                        android:textColor="@color/_333333"
                        android:textStyle="bold"
                        android:text="学习资料"/>
                </RelativeLayout>
                <LinearLayout
                    android:id="@+id/ll_select_type"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:layout_alignParentRight="true"
                    android:layout_marginRight="14dp"
                    android:layout_centerVertical="true"
                    android:orientation="horizontal">
                    <TextView
                        android:id="@+id/tv_select_type"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/_555555"
                        android:textSize="14sp"
                        android:text="全部"/>
                    <ImageView
                        android:layout_width="14dp"
                        android:layout_height="14dp"
                        android:layout_marginLeft="4dp"
                        android:src="@mipmap/ic_drop_black"/>
                </LinearLayout>
            </RelativeLayout>
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_marginTop="14dp"
                android:layout_marginBottom="20dp"
                android:layout_height="match_parent"/>
        </LinearLayout>
    </LinearLayout>
</RelativeLayout>