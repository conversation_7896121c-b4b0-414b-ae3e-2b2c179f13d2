
// nodoc
enum PackageImages {
  Image_1,
  Image_2,
  Image_3,
  Image_4,
}
const _$PackageImageTypeMap = {
  PackageImages.Image_1: 'assets/images/emptyImage.png',
  PackageImages.Image_2: 'assets/images/im_emptyIcon_1.png',
  PackageImages.Image_3: 'assets/images/im_emptyIcon_2.png',
  PackageImages.Image_4: 'assets/images/im_emptyIcon_3.png',
};

extension convert on PackageImages? {
  String? encode() => _$PackageImageTypeMap[this!];

  PackageImages? key(String value) => decodePackageImage(value);

  PackageImages? decodePackageImage(String value) {
    return _$PackageImageTypeMap.entries
        .singleWhere((element) => element.value == value)
        .key;
  }
}
