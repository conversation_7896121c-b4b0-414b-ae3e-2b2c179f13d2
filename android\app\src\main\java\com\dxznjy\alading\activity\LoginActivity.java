package com.dxznjy.alading.activity;

import android.animation.Animator;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.graphics.Typeface;
import android.graphics.drawable.ColorDrawable;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.RelativeLayout;
import android.widget.SeekBar;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.dxznjy.alading.DXApplication;
import com.dxznjy.alading.R;
import com.dxznjy.alading.api.ApiProvider;
import com.dxznjy.alading.api.YxApiProvider;
import com.dxznjy.alading.common.Constants;
import com.dxznjy.alading.http.BaseResponse;
import com.dxznjy.alading.http.BaseSubscriber;
import com.dxznjy.alading.response.CheckSlideResponse;
import com.dxznjy.alading.response.LoginResponse;
import com.dxznjy.alading.response.SlideRespose;
import com.dxznjy.alading.response.YXUserInfoResponse;
import com.dxznjy.alading.util.ScreenUtils;
import com.dxznjy.alading.util.SetParamsUtil;
import com.dxznjy.alading.widget.LoginSelectUserPopWindow;
import com.dxznjy.alading.widget.captcha.Captcha;
import com.dxznjy.alading.widget.captcha.TextSeekbar;
import com.google.gson.Gson;
import com.hjq.permissions.OnPermissionCallback;
import com.hjq.permissions.Permission;
import com.hjq.permissions.XXPermissions;
import com.vondear.rxtool.RxRegTool;
import com.vondear.rxtool.RxSPTool;

import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import butterknife.BindView;
import butterknife.OnClick;
import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Action;
import io.reactivex.functions.Consumer;
import okhttp3.RequestBody;

public class LoginActivity extends BaseDxActivity implements View.OnClickListener {

    Integer  loginType = Constants.USERNAMELOGIN;
    boolean isCountDown = false;
    /**
     * 界面交互ui
     */
    @BindView(R.id.ll_phonecode_ui)
    LinearLayout ll_phonecode_ui;
    @BindView(R.id.ll_namepwd_ui)
    LinearLayout ll_namepwd_ui;
    @BindView(R.id.ll_namepwd)
    LinearLayout ll_namepwd;
    @BindView(R.id.ll_phonecode)
    LinearLayout ll_phonecode;
    @BindView(R.id.tv_namepwd)
    TextView tv_namepwd;
    @BindView(R.id.tv_phonecode)
    TextView tv_phonecode;
    @BindView(R.id.tv_getcode)
    TextView tv_getcode;

    /**
     * 账号登录相关
     */
    @BindView(R.id.ed_phoneandstnum)
    EditText ed_phoneandstnum;
    @BindView(R.id.ed_pwd)
    EditText ed_pwd;
    @BindView(R.id.ed_phone)
    EditText ed_phone;
    @BindView(R.id.ed_phonecode)
    EditText ed_phonecode;
    String studentCode="";

    String uuid = System.currentTimeMillis()+"";

    @Override
    public void beforeInflateNofit() {
        super.beforeInflateNofit();
        setNoFits();
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_login;
    }

    @Override
    public void initViewAndData() {
        inVisableDbTitleBar();
        String loginInfo = RxSPTool.getString(LoginActivity.this, Constants.STUDENT_INFO);
        if (!TextUtils.isEmpty(loginInfo)){
            startActivity(new Intent(LoginActivity.this,HomeActivity.class));
            finish();
        }
    }

    @OnClick({R.id.ll_namepwd,R.id.ll_phonecode,R.id.tv_login,R.id.tv_getcode})
    public void onClick(View view) {
        switch (view.getId()){
            case R.id.ll_namepwd:
                loginType = Constants.USERNAMELOGIN;
                changeLoginModel();
                break;
            case R.id.ll_phonecode:
                loginType = Constants.PHONECODELOGIN;
                changeLoginModel();
                break;
            case R.id.tv_login:
                checkLoginInfo();
                break;
            case R.id.tv_getcode:
                if (TextUtils.isEmpty(ed_phone.getText().toString())){
                    showToast("请输入手机号");
                    return;
                }
                if (ed_phone.getText().toString().length()<11){
                    showToast("手机号错误，请重新输入");
                    return;
                }
                if (!isValidPhoneNumber(ed_phone.getText().toString())) {
                    showToast("手机号错误，请重新输入");
                    return;
                }
                if (!isCountDown){
                    showMsgPicture();
                }
                break;
        }
    }
    // 校验手机号格式的正则表达式
    private boolean isValidPhoneNumber(String phoneNumber) {
        String regex = "^1[3-9]\\d{9}$";
        return phoneNumber.matches(regex);
    }
    public void getPermissions() {
        XXPermissions.with(this)
                // 申请单个权限
                .permission(Permission.MANAGE_EXTERNAL_STORAGE)
                .request(new OnPermissionCallback() {
                    @Override
                    public void onGranted(@NonNull List<String> permissions, boolean allGranted) {
                        if (allGranted) {
                            login();
                        }
                    }

                    @Override
                    public void onDenied(@NonNull List<String> permissions, boolean doNotAskAgain) {
                    }
                });
    }
    /**
     * 账号密码登录 /手机号 验证码登录
     */
    public void login(){
        showDialog();
        Map<String,String> map = new HashMap<>();
        if (loginType == Constants.USERNAMELOGIN){
            map.put("username",ed_phoneandstnum.getText().toString().trim());
            map.put("password",ed_pwd.getText().toString().trim());
            if (!TextUtils.isEmpty(studentCode)){
                map.put("studentCode",studentCode);
            }
        }else if (loginType == Constants.PHONECODELOGIN){
            map.put("username",ed_phone.getText().toString().trim());
            map.put("smsCode",ed_phonecode.getText().toString().trim());
            if (!TextUtils.isEmpty(studentCode)){
                map.put("studentCode",studentCode);
            }
        }
        request(ApiProvider.getInstance(this).DFService.loginPwd(map), new BaseSubscriber<LoginResponse>(this){
            @Override
            public void onCompleted() {
                super.onCompleted();
            }

            @Override
            public void onError(Throwable e) {
                super.onError(e);
                closeDialog();
            }

            @Override
            public void onNext(LoginResponse response) {
                super.onNext(response);
                closeDialog();
                if (response.getData().getCallbackData() != null&&response.getData().getCallbackData().size() != 0){//家长身份登录 只有1个学员直接登录
                    RxSPTool.putString(LoginActivity.this,Constants.PARENT_TOKEN,response.getData().getToken());
                    if (response.getData().getCallbackData().size()>1){
                        showSelectUserPop(response.getData().getCallbackData());
                    }else{
                        studentCode = response.getData().getCallbackData().get(0).getStudentCode();
                        login();
                    }
                }else{//学员登录成功 保存学生信息 与code 进入首页
                    RxSPTool.putString(LoginActivity.this,Constants.STUDENT_INFO,new Gson().toJson(response.getData()));
                    RxSPTool.putString(LoginActivity.this,Constants.STUDENT_CODE,studentCode);
                    startActivity(new Intent(LoginActivity.this,HomeActivity.class));
                    finish();
                }
            }
        });
    }


    /**
     * 选择学员弹窗
     * @param data  学员数据
     */
    public void showSelectUserPop(List<LoginResponse.CallbackData> data){
        for (int i=0;i<data.size();i++){
            data.get(i).setType(1);
            data.get(i).setCheck(false);
        }
        LoginSelectUserPopWindow loginSelectUserPopWindow = new LoginSelectUserPopWindow(
                this, "选择学员", "确定", rootView, data, Constants.VERTICAL,
                0, false, new LoginSelectUserPopWindow.OnSelectListener() {
            @Override
            public void onSelect(List<LoginResponse.CallbackData> data) {
                LoginResponse.CallbackData  selectUser = new LoginResponse.CallbackData();
                for (int i=0;i<data.size();i++){
                    if (data.get(i).isCheck()){
                        selectUser=data.get(i);
                        break;
                    }
                }
                studentCode=selectUser.getStudentCode();
                login();
//                addUser(selectUser.getStudentCode(),selectUser.getRealName());
            }
        });
        loginSelectUserPopWindow.showPop();
    }
    /**
     * 图片验证码弹窗与发送验证码
     */
    ImageView img_content;
    OnChangeListener onChangeListener;
    PopupWindow popupWindow;


    //处理滑动条逻辑
    private boolean isResponse;
    private boolean isDown;
    ImageView bg,cut;
    int scale;
    int mProgress=0;
    private  boolean isCheckFail=false;
    public void showMsgPicture(){
        View view = LayoutInflater.from(this).inflate(R.layout.pop_msg_picture, null);
        LinearLayout ll_close = view.findViewById(R.id.ll_close);
        TextView tv_sure = view.findViewById(R.id.tv_sure);
        TextView tv_change = view.findViewById(R.id.tv_change);
        TextSeekbar seekbar = view.findViewById(R.id.seekbar);
        img_content = view.findViewById(R.id.img_content);
        RelativeLayout rl_content = view.findViewById(R.id.rl_content);
        ImageView img_refresh = view.findViewById(R.id.img_refresh);
        TextView tv_seek_tips = view.findViewById(R.id.tv_seek_tips);
        getCaptchaImage();
        img_refresh.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                startRefresh(view);
            }
        });
        scale = ScreenUtils.getScreenWidth(this)/310;
        if (DXApplication.isTablet(this)) {
            scale = 2;
        }
        onChangeListener = new OnChangeListener() {
            @Override
            public void onchange(SlideRespose.Slide result) {
                bg = new ImageView(LoginActivity.this);
                bg.setScaleType(ImageView.ScaleType.FIT_XY);
                LinearLayout.LayoutParams
                        layoutParams = new LinearLayout.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,155*scale);
                bg.setLayoutParams(layoutParams);
                bg.setImageBitmap(Base64StringToImage(result.getOriImage()));
                rl_content.addView(bg);
                cut = new ImageView(LoginActivity.this);
                LinearLayout.LayoutParams layoutParamscut = new LinearLayout.LayoutParams(result.getCutImageWidth()*scale,
                                result.getCutImageHeight()*scale);
                layoutParamscut.topMargin = result.getYpos()*scale;
                cut.setLayoutParams(layoutParamscut);
                cut.setImageBitmap(Base64StringToImage(result.getCutImage()));
                cut.setScaleType(ImageView.ScaleType.FIT_CENTER);
                rl_content.addView(cut);
            }
            @Override
            public void onChangeFail() {
                isCheckFail = true;
                Log.i("zgj",mProgress+"");
                seekbar.setProgressDrawable(getResources().getDrawable(R.drawable.custom_seekbar_progress_error));
                seekbar.setThumb(getResources().getDrawable(R.drawable.ic_thumb_e));
                seekbar.setThumbOffset(0);
                if (mProgress>98){
                    seekbar.setProgress(99);
                }
                new Handler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                seekbar.setProgress(0);
                                tv_seek_tips.setVisibility(View.VISIBLE);
                                RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) cut.getLayoutParams();
                                params.leftMargin =0;
                                cut.setLayoutParams(params);
                                seekbar.setProgressDrawable(getResources().getDrawable(R.drawable.custom_seekbar_progress_default));
                                seekbar.setThumb(getResources().getDrawable(R.drawable.ic_thumb_n));
                                seekbar.setThumbOffset(0);
                            }
                        });
                    }
                }, 500);
            }

            @Override
            public void onChangeSuccess() {
                seekbar.setProgressDrawable(getResources().getDrawable(R.drawable.custom_seekbar_progress_finish));
                seekbar.setThumb(getResources().getDrawable(R.drawable.ic_thumb_s));
                seekbar.setThumbOffset(0);
                seekbar.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        popupWindow.dismiss();
                    }
                },300);
            }

            @Override
            public void onRefresh() {
                tv_seek_tips.setVisibility(View.VISIBLE);
                seekbar.setProgressDrawable(getResources().getDrawable(R.drawable.custom_seekbar_progress_default));
                seekbar.setThumb(getResources().getDrawable(R.drawable.ic_thumb_n));
                seekbar.setThumbOffset(0);
                seekbar.setProgress(0);
            }
        };
        seekbar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (isCheckFail){
                    isCheckFail = false;
                    return;
                }
                mProgress=progress;
                if(cut != null&&cut.getLayoutParams() != null){
                    if (isDown) {  //手指按下
                        isDown = false;
                        if (progress > 10) { //
                            tv_seek_tips.setVisibility(View.VISIBLE);
                            isResponse = false;
                            RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) cut.getLayoutParams();
                            params.leftMargin = 0;
                            cut.setLayoutParams(params);
                        } else {
                            tv_seek_tips.setVisibility(View.GONE);
                            isResponse = true;
                            seekbar.setProgressDrawable(getResources().getDrawable(R.drawable.custom_seekbar_progress_change));
                            seekbar.setThumb(getResources().getDrawable(R.drawable.ic_thumb_scroll));
                            seekbar.setThumbOffset(0);
                        }
                    }
                    if (isResponse) {
                        RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) cut.getLayoutParams();
                        params.leftMargin = (bg.getWidth()-cut.getWidth())/100* progress;
//                        }
                        cut.setLayoutParams(params);
                    } else {
                        seekBar.setProgress(0);
                    }
                }
            }
            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                isDown = true;
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                if(cut != null&&cut.getLayoutParams() != null) {
                    if (isResponse) {
                        RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) cut.getLayoutParams();
                        checkSlide(params.leftMargin*100/bg.getWidth());
                    }
                }
            }
        });
        if (DXApplication.isTablet(this)) {
            popupWindow = new PopupWindow(view, 770, ViewGroup.LayoutParams.WRAP_CONTENT, true);
        }else{
            popupWindow = new PopupWindow(view, ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT, true);
        }
        popupWindow.setOutsideTouchable(true);
        // 设置PopupWindow的背景
        popupWindow.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
// 设置PopupWindow是否能响应点击事件
        popupWindow.setTouchable(true);
        popupWindow.setOnDismissListener(new PopupWindow.OnDismissListener() {
            @Override
            public void onDismiss() {
                backgroundAlpha(1f);
            }
        });
        tv_sure.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
            }
        });
        tv_change.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                getCaptchaImage();
            }
        });
        ll_close.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                popupWindow.dismiss();
            }
        });
        backgroundAlpha(0.5f);
        popupWindow.showAtLocation(rootView, Gravity.CENTER, 0, 0);
    }

    private void startRefresh(View v) {
        //点击刷新按钮，启动动画
        v.animate().rotationBy(360).setDuration(500)
                .setInterpolator(new AccelerateDecelerateInterpolator())
                .setListener(new Animator.AnimatorListener() {
                    @Override
                    public void onAnimationStart(Animator animation) {

                    }

                    @Override
                    public void onAnimationEnd(Animator animation) {
                        onChangeListener.onRefresh();
                        getCaptchaImage();
                    }

                    @Override
                    public void onAnimationCancel(Animator animation) {

                    }

                    @Override
                    public void onAnimationRepeat(Animator animation) {

                    }
                });
    }

    public void backgroundAlpha(float f) {
        WindowManager.LayoutParams lp = getWindow().getAttributes();
        lp.alpha = f;
        getWindow().setAttributes(lp);
    }
    /**
     * 验证码倒计时
     */
    Disposable disposable;
    public void countDownTime(){
        tv_getcode.setBackgroundResource(R.drawable.bg_radius4_f2f2f2);
        tv_getcode.setTextColor(getResources().getColor(R.color._555555));
        disposable = Observable.intervalRange(0, 61, 0, 1, TimeUnit.SECONDS)
                .observeOn(AndroidSchedulers.mainThread())
                .doOnNext(new Consumer<Long>() {
                    @Override
                    public void accept(Long aLong) {
                        tv_getcode.setText("已发送("+(60-aLong)+")");
                    }
                })
                .doOnComplete(new Action() {
                    @Override
                    public void run() throws Exception {
                        isCountDown = false;
                        tv_getcode.setText("重新发送");
                        tv_getcode.setTextColor(getResources().getColor(R.color.white));
                        tv_getcode.setBackgroundResource(R.drawable.bg_radius4_428a6f);
                    }
                })
                .subscribe();
    }


    public interface OnChangeListener {
        void onchange(SlideRespose.Slide result);

        void onChangeFail();

        void onChangeSuccess();
        void onRefresh();
    }

    /**
     * 获取图像验证码
     */
    public void getCaptchaImage(){
        showDialog();
        uuid = System.currentTimeMillis()+"";
        Map<String,String> map = new HashMap<>();
        map.put("uuid",uuid);
        request(ApiProvider.getInstance(this).DFService.getSlideImage(map), new BaseSubscriber<SlideRespose>(this){
            @Override
            public void onCompleted() {
                super.onCompleted();
            }
            @Override
            public void onError(Throwable e) {
                super.onError(e);
                closeDialog();
            }
            @Override
            public void onNext(SlideRespose response) {
                super.onNext(response);
                closeDialog();
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        onChangeListener.onchange(response.getData());
                    }
                });
            }
        });
    }
    public static Bitmap Base64StringToImage(String strBase64) {
        try {
            byte[] arr = Base64.getDecoder().decode(strBase64);
            Bitmap bmp = BitmapFactory.decodeByteArray(arr, 0, arr.length);
            return bmp;
        } catch (Exception ex) {
            return null;
        }
    }

    private void checkSlide(int maveX) {
        Map<String,String> params = new HashMap<>();
        params.put("uuid",uuid);
        params.put("maveX", maveX+"");
        RequestBody body = SetParamsUtil.getRequestBodyfromParam(params);
        request(ApiProvider.getInstance(this).DFService.checkSlide(body), new BaseSubscriber<CheckSlideResponse>(this){
            @Override
            public void onCompleted() {
                super.onCompleted();
            }

            @Override
            public void onError(Throwable e) {
                super.onError(e);
            }

            @Override
            public void onNext(CheckSlideResponse response) {
                super.onNext(response);
                if (response.getData().getCode() == 20000){
                    getMsgCode(response.getData().getData());
                    onChangeListener.onChangeSuccess();
                }else{
                    showToast(response.getData().getMessage());
                    onChangeListener.onChangeFail();
                }
            }
        });
    }
    /**
     * 切换密码登录/验证码登录
     */
    public void changeLoginModel(){
        studentCode = "";
        if (loginType == Constants.USERNAMELOGIN){
            ll_namepwd_ui.setVisibility(View.VISIBLE);
            ll_phonecode_ui.setVisibility(View.GONE);
            ll_namepwd.setBackgroundResource(R.mipmap.btn_zuobian_h);
            ll_phonecode.setBackgroundResource(R.mipmap.btn_youbian_n);
            tv_namepwd.setTypeface(Typeface.DEFAULT_BOLD);
            tv_phonecode.setTypeface(Typeface.DEFAULT);
            tv_namepwd.setTextColor(getResources().getColor(R.color._428a6f));
            tv_phonecode.setTextColor(getResources().getColor(R.color._223843));
        }else if (loginType == Constants.PHONECODELOGIN){
            ll_namepwd_ui.setVisibility(View.GONE);
            ll_phonecode_ui.setVisibility(View.VISIBLE);
            ll_namepwd.setBackgroundResource(R.mipmap.btn_zuobian_n);
            ll_phonecode.setBackgroundResource(R.mipmap.btn_youbian_h);
            tv_namepwd.setTypeface(Typeface.DEFAULT);
            tv_phonecode.setTypeface(Typeface.DEFAULT_BOLD);
            tv_namepwd.setTextColor(getResources().getColor(R.color._223843));
            tv_phonecode.setTextColor(getResources().getColor(R.color._428a6f));
        }
    }

    /**
     * 校验用户输入信息
     */
    public void checkLoginInfo(){
        if (loginType == Constants.USERNAMELOGIN){
            if (TextUtils.isEmpty(ed_phoneandstnum.getText().toString())){
                showToast("请输入手机号");
                return;
            }
            if (ed_phoneandstnum.getText().toString().length()<11){
                showToast("手机号错误，请重新输入");
                return;
            }
            if (!RxRegTool.checkPhone(ed_phoneandstnum.getText().toString())) {
                showToast("手机号错误，请重新输入");
                return;
            }
            if (TextUtils.isEmpty(ed_pwd.getText().toString())){
                showToast("请输入登录密码");
                return;
            }
        }else if (loginType == Constants.PHONECODELOGIN){
            if (TextUtils.isEmpty(ed_phone.getText().toString())){
                showToast("请输入手机号");
                return;
            }
            if (ed_phone.getText().toString().length()<11){
                showToast("手机号错误，请重新输入");
                return;
            }
            if (!RxRegTool.checkPhone(ed_phone.getText().toString())) {
                showToast("手机号错误，请重新输入");
                return;
            }
            if (TextUtils.isEmpty(ed_phonecode.getText().toString())){
                showToast("验证码不可为空，请输入");
                return;
            }
        }
        login();
    }



    /**
     * 获取验证码
     */
    private void getMsgCode(String data) {
        showDialog();
        Map<String,String> params = new HashMap<>();
        params.put("code",data);
        params.put("uuid",uuid);
        request(ApiProvider.getInstance(this).DFService.getMsgCode(ed_phone.getText().toString(),params), new BaseSubscriber<BaseResponse>(this){
            @Override
            public void onCompleted() {
                super.onCompleted();
            }

            @Override
            public void onError(Throwable e) {
                super.onError(e);
                closeDialog();
            }

            @Override
            public void onNext(BaseResponse response) {
                super.onNext(response);
                closeDialog();
                if (response.getCode() == 20000){
                    if (!isCountDown){
                        popupWindow.dismiss();
                        isCountDown = true;
                        countDownTime();
                    }
                }
            }
        });
    }


    @Override
    public void onDestroy() {
        super.onDestroy();
        if (disposable!=null){
            disposable.dispose();
        }
    }
}