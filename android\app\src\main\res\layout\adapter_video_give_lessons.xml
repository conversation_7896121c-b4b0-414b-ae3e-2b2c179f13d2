<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="160dp"
    android:orientation="vertical"
    android:paddingBottom="14dp"
    android:paddingTop="14dp"
    android:paddingLeft="14dp"
    android:paddingRight="14dp"
    android:layout_marginBottom="10dp"
    android:background="@mipmap/pic_kapian">
    <TextView
        android:id="@+id/tv_coursename"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="14sp"
        android:textColor="@color/_333333"
        android:singleLine="true"
        android:textStyle="bold"
        android:text="注意力学习课"/>
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp">
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="14sp"
                android:textColor="@color/_555555"
                android:gravity="center"
                android:singleLine="true"
                android:text="学生姓名："/>
            <TextView
                android:id="@+id/tv_studentname"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="14sp"
                android:textColor="@color/_2c9e7c"
                android:gravity="center"
                android:textStyle="bold"
                android:singleLine="true"
                android:text="鼎学能"/>
        </LinearLayout>
    </RelativeLayout>
    <TextView
        android:id="@+id/tv_laststudytime"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="14sp"
        android:layout_marginTop="12dp"
        android:textColor="@color/_555555"
        android:singleLine="true"
        android:text="上次学习：2024-11-07 19:30:00"/>
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp">


        <TextView
            android:id="@+id/tv_gotostudy"
            android:layout_width="70dp"
            android:layout_height="24dp"
            android:textSize="12sp"
            android:layout_centerVertical="true"
            android:layout_alignParentRight="true"
            android:textColor="@color/white"
            android:gravity="center"
            android:background="@drawable/bg_radius20_428a6f"
            android:singleLine="true"
            android:layout_marginLeft="14dp"
            android:text="进入学习"/>
    </RelativeLayout>


</LinearLayout>