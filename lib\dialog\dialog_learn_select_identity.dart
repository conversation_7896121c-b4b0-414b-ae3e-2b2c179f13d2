// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:get/get.dart';
// import '../bean/base_bean/base_bean_entity.dart';
// import '../common/CommonUtil.dart';
// import 'dart:convert';
// import '../bean/student_app_bean_entity.dart';
// import '../common/Config.dart';
// import '../common/ne_user_option.dart';
// import '../common/user_option.dart';
// import '../utils/android_ios_plugin.dart';
// import '../utils/httpRequest.dart';
// import '../utils/ne_metting_utils.dart';
//
// enum UserType { student, patriarch } // 显示基础弹窗
//
// class GetXCustomDialog {
//   final String studentCode;
//   final String memberCode;
//   final String meetingNum;
//
//   const GetXCustomDialog({
//     required this.studentCode,
//     required this.memberCode,
//     required this.meetingNum,
//   });
//
//   void show() {
//     showSimpleSelectionDialog(
//       onConfirm: (userType) => handleClassStartDecision(userType!),
//     );
//   }
//
//   void handleClassStartDecision(UserType userType) {
//     userType == UserType.student
//         ? navigateToPermissionPage(userType)
//         : handleQueryMemberInMeeting(userType);
//   }
//
//   void navigateToPermissionPage(userType) {
//     NEMeetingUtil.logOut();
//
//     ENUserOption.getYunXinToken(studentCode, userType, (succeed) {
//       // if (!succeed) { /// 如果登录没成功 清除之前的登录数据
//       //   NEMeetingUtil.logOut();
//       // }else {
//       StudentAppBeanEntity studentAppBeanEntity = StudentAppBeanEntity();
//       studentAppBeanEntity.studentCode = studentCode;
//       studentAppBeanEntity.merchantCode = memberCode;
//       studentAppBeanEntity.meetingNum = meetingNum;
//       studentAppBeanEntity.identutyID =
//           userType == UserType.student ? "0" : "1";
//       studentAppBeanEntity.baseUrl = Config.URL;
//       AndroidIosPlugin.openLessonsDetail(jsonEncode(studentAppBeanEntity));
//       // }
//     });
//   }
//
//   void handleQueryMemberInMeeting(userType) {
//     final response = HttpUtil().getNoBaseBean(
//       '${Config.getQueryMemberInMeeting}?meetingNum=$meetingNum&userCode=${UserOption.userInfoBeanEntity?.mobile}&userType=1',
//     );
//     response.then((value) async {
//       BaseBeanEntity? baseBeanEntity = BaseBeanEntity.fromJson(value?.data);
//       if (baseBeanEntity.success) {
//         //true 家长已入会 提示弹窗
//         showBaseDialog(onConfirm: () {
//           navigateToPermissionPage(userType);
//         });
//       } else {
//         navigateToPermissionPage(userType);
//       }
//     });
//   }
// }
//
// void showBaseDialog({VoidCallback? onConfirm}) {
//   Get.dialog(
//     _buildAlertDialog(
//         content: "尊敬的家长，您的账号已在其他设备入会，当前状态不可重复入会，您可退出会议后重试",
//         confirmText: "确认",
//         onConfirm: onConfirm),
//     barrierDismissible: false,
//   );
// }
//
// Widget _buildAlertDialog(
//     {required String content,
//     required String confirmText,
//     required VoidCallback? onConfirm}) {
//   return AlertDialog(
//     shape: RoundedRectangleBorder(
//       borderRadius: BorderRadius.circular(10.r),
//     ),
//     backgroundColor: Colors.white,
//     content: Text(content, style: TextStyle(color: Colors.black87)),
//     actions: <Widget>[
//       Row(
//         mainAxisAlignment: MainAxisAlignment.center,
//         children: [
//           TextButton(
//             onPressed: () {
//               // 先执行回调再关闭弹窗
//               onConfirm?.call(); // 执行传入的回调
//               Get.back(); // 关闭弹窗
//             },
//             style: TextButton.styleFrom(
//               backgroundColor: Color(0xFF185D50),
//               foregroundColor: Colors.white,
//               minimumSize: Size(110, 32),
//               shape: RoundedRectangleBorder(
//                 borderRadius: BorderRadius.circular(2),
//               ),
//               padding: EdgeInsets.symmetric(horizontal: 6),
//             ),
//             child: Text(
//               "确认",
//               style: TextStyle(
//                 fontSize: 16,
//                 fontWeight: FontWeight.w600,
//                 color: Colors.white,
//               ),
//             ),
//           ),
//         ],
//       ),
//     ],
//   );
// }
//
// class SelectionDialogController extends GetxController {
//   UserType? selectedValue;
//   final List<String> options = ['我是学生', '我是家长'];
//   final List<UserType> enumOptions = [UserType.student, UserType.patriarch];
//   void select(int index) {
//     selectedValue = enumOptions[index];
//     update(); // 指定更新区域
//   }
//
//   // 新增：将枚举转换为对应字符串
//   String _getEnumString(UserType? type) {
//     switch (type) {
//       case UserType.student:
//         return '我是学生';
//       case UserType.patriarch:
//         return '我是家长';
//       default:
//         return '';
//     }
//   }
// }
//
// void showSimpleSelectionDialog({
//   required Function(UserType?) onConfirm,
// }) {
//   final controller = Get.put(SelectionDialogController());
//   Get.dialog(
//     Align(
//       alignment: Alignment.center,
//       child: SingleChildScrollView(
//         child: Container(
//           margin: EdgeInsets.only(left: 20.w, right: 20.w),
//           decoration: BoxDecoration(
//               borderRadius: BorderRadius.all(Radius.circular(15)),
//               color: Colors.white),
//           child: Column(
//             children: [
//               Padding(
//                 padding: const EdgeInsets.all(16),
//                 child: Text(
//                   '选择上课身份',
//                   style: TextStyle(
//                     fontSize: 18,
//                     color: HexColor('333333'),
//                     decoration: TextDecoration.none,
//                     fontWeight: FontWeight.bold,
//                   ),
//                 ),
//               ),
//               ClipRRect(
//                 child: GetBuilder<SelectionDialogController>(
//                   builder: (_) {
//                     return ListView.separated(
//                       itemCount: controller.enumOptions.length,
//                       shrinkWrap: true,
//                       physics: const NeverScrollableScrollPhysics(),
//                       separatorBuilder: (_, __) => const SizedBox(height: 8),
//                       itemBuilder: (context, index) {
//                         final enumValue = controller.enumOptions[index];
//                         final isSelected =
//                             controller.selectedValue == enumValue; // 直接比较枚举
//                         return Container(
//                           margin: const EdgeInsets.symmetric(horizontal: 14),
//                           decoration: BoxDecoration(
//                             color: isSelected
//                                 ? Colors.blue.withOpacity(0.05)
//                                 : Colors.white,
//                             border: Border.all(
//                               color: Colors.grey.shade300,
//                               width: 1,
//                             ),
//                             borderRadius: BorderRadius.circular(22),
//                           ),
//                           child: Material(
//                             color: Colors.transparent,
//                             child: InkWell(
//                               onTap: () => controller.select(index),
//                               borderRadius: BorderRadius.circular(22),
//                               child: Padding(
//                                   padding: const EdgeInsets.symmetric(
//                                     horizontal: 16,
//                                     vertical: 12,
//                                   ),
//                                   child: Align(
//                                     alignment: Alignment.center,
//                                     child: Text(
//                                       controller
//                                           ._getEnumString(enumValue), // 显示对应字符串
//                                       style: TextStyle(
//                                         fontSize: 16,
//                                         color: isSelected
//                                             ? Color(0xFF185D50)
//                                             : Colors.black,
//                                         fontWeight: isSelected
//                                             ? FontWeight.w600
//                                             : FontWeight.normal,
//                                       ),
//                                     ),
//                                   )),
//                             ),
//                           ),
//                         );
//                       },
//                     );
//                   },
//                 ),
//               ),
//               const SizedBox(height: 16),
//               Container(
//                 margin: EdgeInsets.only(bottom: 14.w, left: 14.w, right: 14.w),
//                 child: Row(
//                   children: [
//                     Expanded(
//                       child: Container(
//                         height: 48,
//                         decoration: BoxDecoration(
//                           color: HexColor("ffffff"),
//                           borderRadius: BorderRadius.circular(24),
//                           border:
//                               Border.all(color: HexColor("7BAEA0"), width: 1.w),
//                         ),
//                         child: TextButton(
//                           onPressed: () => Get.back(),
//                           child: Text(
//                             '取消',
//                             style: TextStyle(
//                               fontSize: 16,
//                               color: HexColor('287E66'),
//                             ),
//                           ),
//                         ),
//                       ),
//                     ),
//                     SizedBox(width: 14.w),
//                     Expanded(
//                       child: SizedBox(
//                         height: 48,
//                         child: TextButton(
//                           style: TextButton.styleFrom(
//                             backgroundColor: HexColor('287E66'),
//                             shape: const RoundedRectangleBorder(
//                               borderRadius:
//                                   BorderRadius.all(Radius.circular(24)),
//                             ),
//                           ),
//                           onPressed: () {
//                             if (controller.selectedValue == null) {
//                               Get.snackbar('', '请选择身份',
//                                   backgroundColor: Colors.grey);
//                               return;
//                             }
//                             Get.back(result: controller.selectedValue);
//                           },
//                           child: const Text(
//                             '确认',
//                             style: TextStyle(
//                               fontSize: 14,
//                               color: Colors.white,
//                             ),
//                           ),
//                         ),
//                       ),
//                     ),
//                   ],
//                 ),
//               ),
//             ],
//           ),
//         ),
//       ),
//     ),
//   ).then((value) {
//     if (value != null) {
//       Get.delete<SelectionDialogController>();
//       onConfirm(controller.selectedValue);
//     } else {
//       NEMeetingUtil.logOut();
//     }
//   });
// }
