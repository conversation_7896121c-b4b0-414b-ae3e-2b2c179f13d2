//
//  PayParam.swift
//  Runner
//
//  Created by 许江泉 on 2025/3/1.
//

import UIKit
struct PayParam {
    let cusid: String
    let appid: String
    let orgid: String
    let version: String
    let trxamt: String
    let reqsn: String
    let notify_url: String
    let body: String
    let remark: String
    let validtime: String
    let randomstr: String
    let paytype: String
    let signtype: String
    let sign: String
    let innerappid: String

}

extension PayParam {
    func toPath() -> String {
        let path = "pages/orderDetail/orderDetail"
        var components = URLComponents()
        components.queryItems = [
            URLQueryItem(name: "cusid", value: cusid),
            URLQueryItem(name: "appid", value: appid),
            URLQueryItem(name: "orgid", value: orgid),
            URLQueryItem(name: "version", value: version),
            URLQueryItem(name: "trxamt", value: trxamt),
            URLQueryItem(name: "reqsn", value: reqsn),
            URLQueryItem(name: "notify_url", value: notify_url),
            URLQueryItem(name: "randomstr", value: randomstr),
            URLQueryItem(name: "paytype", value: paytype),
            URLQueryItem(name: "body", value: body),
            URLQueryItem(name: "remark", value: remark),
            URLQueryItem(name: "validtime", value: validtime),
            URLQueryItem(name: "signtype", value: signtype),
            URLQueryItem(name: "sign", value: sign),
            URLQueryItem(name: "innerappid", value: innerappid)
        ]
        
        // 拼接路径和查询字符串
        if let queryString = components.query {
            return "\(path)?\(queryString)"
        } else {
            return path
        }
    }
}
