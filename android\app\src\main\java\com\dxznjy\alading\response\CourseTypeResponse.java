package com.dxznjy.alading.response;

import java.util.List;

public class CourseTypeResponse {

    private List<CourseType> data;

    public List<CourseType> getData() {
        return data;
    }

    public void setData(List<CourseType> data) {
        this.data = data;
    }

    public class  CourseType{
       private String id;
       private String enName;


        private Integer type=1;
        private boolean isCheck=false;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getEnName() {
            return enName;
        }

        public void setEnName(String enName) {
            this.enName = enName;
        }

        public Integer getType() {
            return type;
        }

        public void setType(Integer type) {
            this.type = type;
        }

        public boolean isCheck() {
            return isCheck;
        }

        public void setCheck(boolean check) {
            isCheck = check;
        }
    }
}
