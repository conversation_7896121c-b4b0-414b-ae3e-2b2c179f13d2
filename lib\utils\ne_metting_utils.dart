// import 'package:get/get.dart';
// import 'package:get/get_core/src/get_main.dart';
// import 'package:netease_meeting_kit/meeting_core.dart';
// import 'package:netease_meeting_kit/meeting_kit.dart';
// import 'package:netease_meeting_kit/meeting_ui.dart';
// import 'package:student_end_flutter/common/ne_user_option.dart';
// import '../components/toast_utils.dart';
// import '../common/sdk_config.dart';
// import '../dialog/dialog_learn_select_identity.dart';
//
// /*******************注意云信必须初始化成功 其他类才能用！！！************************/
//
// typedef NEMetCallback = Future<void> Function();
//
// class NEMeetingUtil {
//   static final _meetingKit = NEMeetingKit.instance;
//   static final _config = NEMeetingKitConfig(appKey: KSDKConfig.kNEMeetingkey);
//   static UserType userTypes = UserType.student;
//
//   /// 初始化配置，如果用户云信登录则尝试自动登录
//   static Future<void> commonInit() async {
//     if (_meetingKit.isInitialized) return;
//     try {
//       final initResult = await _meetingKit.initialize(_config);
//       if (initResult.code != 0) {
//         return;
//       }
//
//       if (ENUserOption.iSLogin) {
//         final autoLoginResult =
//             await _meetingKit.getAccountService().tryAutoLogin();
//         if (autoLoginResult.code != 0) {
//           _showError(autoLoginResult.msg ?? "");
//         }
//       }
//     } catch (e) {
//       _showError(e.toString());
//     }
//   }
//
//   /// 云信登录
//   static Future<bool> yxLogin(
//       String userUuid, String accountToken, UserType userType) async {
//     try {
//       if (!_meetingKit.isInitialized) {
//         final initResult = await _meetingKit.initialize(_config);
//         if (initResult.code != 0) {
//           return false;
//         }
//       }
//       userTypes = userType;
//       addListeners();
//       final result = await _meetingKit.getAccountService().loginByToken(
//             userUuid,
//             accountToken,
//           );
//       if (result.code == 0) {
//         saveData(userUuid, accountToken);
//       }
//       return result.code == 0;
//     } catch (e) {
//       return false;
//     }
//   }
//
//   /// 设置监听
//   static void addListeners() {
//     NEMeetingKit.instance.getAccountService().addListener(NEAccountService());
//     NEMeetingKit.instance
//         .getMeetingService()
//         .addMeetingStatusListener(NEMeetingService());
//   }
//
//   /// 保存登录数据
//   static void saveData(String userUuid, String accountToken) {
//     ENUserOption.saveData(userUuid, accountToken);
//   }
//
//   /// 退出云信登录
//   static void logOut() {
//     ENUserOption.cleanData();
//     _meetingKit.getAccountService().logout();
//   }
//
//   /// 退出云信登录
//   static void yxLogOut() {
//     ENUserOption.cleanData();
//     _meetingKit.getAccountService().logout();
//   }
//
//   /// 加入会议
//   static void onEnterMeeting(
//       {required String meetingNum, required String realName}) {
//     final params = NEJoinMeetingParams(
//       meetingNum: meetingNum,
//       displayName: realName,
//     );
//     _meetingKit
//         .getMeetingService()
//         .joinMeeting(Get.context!, params, NEMeetingOptions())
//         .then((result) {
//       if (result.code != 0) {
//         _showError(result.msg ?? "");
//       }
//     }).catchError((error) {
//       _showError('加入失败');
//     });
//   }
//
//   /// 创建会议配置选项
//   static NEMeetingOptions _createMeetingOptions() {
//     NEMeetingMenuItem participantsMenu = NEMenuItems.defaultToolbarMenuItems[3];
//     NEMeetingOptions neMeetingOptions = NEMeetingOptions()
//       ..showHandsUp = false
//       ..noWhiteBoard = false
//       ..noGallery = false
//       ..noSwitchCamera = false
//       ..showEmojiResponse = false
//       ..noAudio = true
//       ..noVideo = true
//       ..noInvite = true
//       ..noMinimize = true
//       ..defaultWindowMode = 2
//       ..meetingIdDisplayOption = 0
//       ..fullMoreMenuItems = [NEMenuItems.notifyCenter]
//       ..fullToolbarMenuItems = [
//         NEMenuItems.microphone,
//         NEMenuItems.camera,
//         participantsMenu,
//         NEMenuItems.chatroom,
//       ];
//     return neMeetingOptions;
//   }
//
//   /// 统一错误处理
//   static void _showError(String message) {
//     ToastUtil.showShortErrorToast('$message');
//   }
// }
//
// //MARK: 视频会议 当前正在从会议中断开，退出断开原因由<code>MeetingCode</code> 描述
// class NEMeetingService with NEMeetingStatusListener {
//   @override
//   void onMeetingStatusChanged(NEMeetingEvent event) {
//     if (event.arg == NEMeetingCode.loginOnOtherDevice) {
//       ToastUtil.showShortErrorToast('当前登录账号已在其他设备上登录');
//       NEMeetingUtil.logOut();
//     } else if (event.arg == NEMeetingCode.self) {
//       NEMeetingUtil.logOut();
//     }
//   }
// }
//
// //MARK:  视频会议 账号服务监听器，可监听登录状态变更、账号信息变更相关事件。
// class NEAccountService with NEAccountServiceListener {
//   /// 登录状态变更为未登录，原因为当前登录账号已在其他设备上重新登录
//   @override
//   void onKickOut() {
//     if (NEMeetingUtil.userTypes == UserType.student) {
//       ToastUtil.showShortErrorToast('当前登录账号已在其他设备上重新登录');
//     }
//     NEMeetingUtil.logOut();
//   }
//
//   /// 账号信息过期通知，原因为用户修改了密码，应用层随后应该重新登录
//   @override
//   void onAuthInfoExpired() {
//     ToastUtil.showShortErrorToast('登录状态已过期');
//     NEMeetingUtil.logOut();
//   }
//
//   /// 断线重连成功
//   @override
//   void onReconnected() {}
//
//   /// 账号信息更新通知
//   @override
//   void onAccountInfoUpdated(NEAccountInfo? accountInfo) {}
// }
