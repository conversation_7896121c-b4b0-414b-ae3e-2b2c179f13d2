package com.dxznjy.alading.methodchannel;

import android.content.Context;
import android.content.Intent;

import androidx.annotation.NonNull;

import com.dxznjy.alading.DXApplication;
import com.dxznjy.alading.common.Constants;
import com.dxznjy.alading.util.AppUtils;
import com.dxznjy.alading.widget.MySplashView;
import com.tencent.mm.opensdk.modelbiz.WXLaunchMiniProgram;

import java.net.URLEncoder;

import io.dcloud.feature.sdk.DCUniMPSDK;
import io.dcloud.feature.sdk.Interface.IUniMP;
import io.dcloud.feature.unimp.config.UniMPOpenConfiguration;
import io.flutter.embedding.engine.plugins.FlutterPlugin;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;

public class PayMethodchannel implements FlutterPlugin {
    String methodChannelName = Constants.openAndroidIosPay;
    Context mContext;
    @Override
    public void onAttachedToEngine(@NonNull FlutterPluginBinding binding) {
        mContext=binding.getApplicationContext();
        MethodChannel methodChannel=new MethodChannel(binding.getBinaryMessenger(),methodChannelName);
        methodChannel.setMethodCallHandler(new MethodChannel.MethodCallHandler() {
            @Override
            public void onMethodCall(@NonNull MethodCall call, @NonNull MethodChannel.Result result) {
                String methodCall = call.method;
                if (methodCall.equals(Constants.openUniAppWxPay)){
                    if(!AppUtils.isWxInstall(mContext)){//未安装微信
                        MethodChannel methodChannel = new MethodChannel(
                                DXApplication.app.flutterEngineInstance.getDartExecutor().getBinaryMessenger(),Constants.androidIosBackMeeting);
                        methodChannel.invokeMethod(Constants.initWeChatUnInstall,"");
                    }else{
                        WXLaunchMiniProgram.Req req = new WXLaunchMiniProgram.Req();
                        req.userName = "小程序原始ID"; //
                        req.path = call.arguments.toString();
                        req.miniprogramType = WXLaunchMiniProgram.Req.MINIPTOGRAM_TYPE_RELEASE; // 正式版
                        DXApplication.api.sendReq(req);
                    }
                }else if (methodCall.equals(Constants.openUniAppAliPay)){
                }
            }
        });
    }

    @Override
    public void onDetachedFromEngine(@NonNull FlutterPluginBinding binding) {

    }
}
