//
//  Config.swift
//  StuMetApp
//
//  Created by 许江泉 on 2024/11/11.
//

import Foundation
import UIKit


//分享配置
//        WXMiniProgramTypeRelease = 0,       //**< 正式版  */
//        WXMiniProgramTypeTest = 1,        //**< 开发版  */
//        WXMiniProgramTypePreview = 2,         //**< 体验版  */
var WxShareProgramType = 0

// MARK: - double类型width
/// - double类型width
var ScreenWidth: Double = Double(UIScreen.main.bounds.size.width)
// MARK: - double类型height
/// - double类型height
var ScreenHeight: Double = Double(UIScreen.main.bounds.size.height)
// MARK: - cgfloat类型width
/// - cgfloat类型width
let SRNW: CGFloat = UIScreen.main.bounds.size.width
// MARK: - cgfloat类型height
/// - cgfloat类型height
let SRNH: CGFloat = UIScreen.main.bounds.size.height

/// statusBar高度
let StatusBarHeight: CGFloat = getStatusBarHight()
/// - 简单的判断
let isiPhoneX: Bool = (StatusBarHeight != 20)
/// tabBar高度
let TabBarHeight: CGFloat = 49.0
let NewTabBarHeight: CGFloat = isiPhoneX ? 83.0 : 49.0
let appDelegate: AppDelegate = UIApplication.shared.delegate as! AppDelegate
let APP_PAY_CALLBACK = NSNotification.Name("APP_PAY_CALLBACK")


// MARK: - Key window
var appw: UIWindow? {
    return appDelegate.window
}

func getStatusBarHight() -> CGFloat {
    var statusBarHeight: CGFloat = 0
    if #available(iOS 13.0, *) {
        statusBarHeight = UIApplication.shared.windows.first?.windowScene?.statusBarManager?.statusBarFrame.size.height ?? 20
    } else {
        statusBarHeight = UIApplication.shared.statusBarFrame.size.height
    }
    return statusBarHeight
}


func manualLinkString(from parameters: [String: String]) -> String {
    var result = ""
    for (key, value) in parameters.sorted(by: { $0.key < $1.key }) {
        let encodedKey = key.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""
        let encodedValue = value.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""
        result += "\(encodedKey)=\(encodedValue)&"
    }
    return String(result.dropLast())
}

func getSafeAreaBottomHeight() -> CGFloat {
    if #available(iOS 13.0, *) {
        let scenes = UIApplication.shared.connectedScenes
        guard let windowScene = scenes.first as? UIWindowScene else {
            fatalError("Expected a UIWindowScene but didn't find one.")
        }
        let window = windowScene.windows.first!
        return window.safeAreaInsets.bottom
    } else {
        let window = UIApplication.shared.keyWindow!
        return window.safeAreaInsets.bottom
    }
}


func isChildOfTabBarController(controller: UIViewController?) -> Bool {
     return controller?.tabBarController != nil
}
// MARK: - GCD延时操作
/// GCD延时操作
///   - after: 延迟的时间
///   - handler: 事件
public func disPatchAfter(after: Double, handler:@escaping () -> Void) {
    DispatchQueue.main.asyncAfter(deadline: .now() + after) {
        handler()
    }
}
