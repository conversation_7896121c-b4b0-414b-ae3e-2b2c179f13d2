import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 自定义下拉选择组件
class CustomDropdown<T> extends StatefulWidget {
  /// 下拉框标题
  final String? title;

  /// 下拉框提示文本，未选择时显示
  final String hint;

  /// 下拉框选项列表
  final List<T> items;

  /// 选项构建方法，用于自定义每个选项的展示
  final Widget Function(T item) itemBuilder;

  /// 选择项改变回调
  final Function(T? selectedItem)? onChanged;

  /// 最大展示行数
  final int maxVisibleItems;

  /// 当前选中的值
  final T? value;

  /// 下拉框的装饰
  final BoxDecoration? decoration;

  /// 是否禁用
  final bool isDisabled;

  /// 下拉图标
  final Widget? dropdownIcon;

  const CustomDropdown({
    Key? key,
    this.title,
    required this.hint,
    required this.items,
    required this.itemBuilder,
    this.onChanged,
    this.maxVisibleItems = 3,
    this.value,
    this.decoration,
    this.isDisabled = false,
    this.dropdownIcon,
  }) : super(key: key);

  @override
  State<CustomDropdown<T>> createState() => _CustomDropdownState<T>();
}

class _CustomDropdownState<T> extends State<CustomDropdown<T>> {
  bool _isDropdownOpen = false; // 下拉状态标识
  T? _selectedItem; // 选中的 item
  final LayerLink _layerLink = LayerLink(); // 伴随标识
  OverlayEntry? _overlayEntry; // 下拉列表 Entry
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _selectedItem = widget.value;
    _focusNode.addListener(() {
      if (!_focusNode.hasFocus) {
        _closeDropdown();
      }
    });
  }

  @override
  void didUpdateWidget(covariant CustomDropdown<T> oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.value != widget.value) {
      _selectedItem = widget.value;
    }
  }

  @override
  void dispose() {
    _closeDropdown();
    _focusNode.dispose();
    super.dispose();
  }

  void _toggleDropdown() {
    if (widget.isDisabled) return;
    
    if (_isDropdownOpen) {
      _closeDropdown();
    } else {
      _openDropdown();
    }
  }

  void _openDropdown() {
    _focusNode.requestFocus();
    _overlayEntry = _createOverlayEntry();
    Overlay.of(context).insert(_overlayEntry!);
    setState(() {
      _isDropdownOpen = true;
    });
  }

  void _closeDropdown() {
    if (_overlayEntry != null) {
      _overlayEntry!.remove();
      _overlayEntry = null;
    }
    setState(() {
      _isDropdownOpen = false;
    });
  }

  /// 创建下拉列表 Entry
  OverlayEntry _createOverlayEntry() {
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;

    return OverlayEntry(
      builder: (context) => Positioned(
        width: size.width,
        child: CompositedTransformFollower(
          link: _layerLink,
          showWhenUnlinked: false,
          offset: Offset(0.0, size.height),
          child: Material(
            elevation: 4.0,
            borderRadius: BorderRadius.circular(8.w),
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8.w),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 5.0,
                    spreadRadius: 1.0,
                  ),
                ],
              ),
              constraints: BoxConstraints(
                maxHeight: widget.maxVisibleItems * 48.h,
              ),
              child: ListView.builder( // 构建下拉列表项 items
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                itemCount: widget.items.length,
                itemBuilder: (context, index) {
                  final item = widget.items[index];
                  return InkWell( // 下拉列表项
                    onTap: () {
                      setState(() {
                        _selectedItem = item;
                      });
                      // 有回调就执行回调
                      if (widget.onChanged != null) {
                        widget.onChanged!(item);
                      }
                      _closeDropdown();
                    },
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        vertical: 12.h,
                        horizontal: 16.w,
                      ),
                      child: widget.itemBuilder(item),
                    ),
                  );
                },
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建下拉框组件
  @override
  Widget build(BuildContext context) {
    final defaultDecoration = BoxDecoration(
      borderRadius: BorderRadius.circular(8.w),
      border: Border.all(
        width: 1.w,
        color: _isDropdownOpen 
            ? Theme.of(context).primaryColor 
            : Colors.grey.withOpacity(0.3),
      ),
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 如果传了 title，就在下拉框上面加一个标题，左对齐
        if (widget.title != null) ...[
          Text(
            widget.title!,
            style: TextStyle(
              fontSize: 15.sp,
              fontWeight: FontWeight.normal,
            ),
          ),
          SizedBox(height: 8.h),
        ],
        CompositedTransformTarget(
          link: _layerLink,
          child: Focus(
            focusNode: _focusNode,
            child: GestureDetector(
              onTap: _toggleDropdown,
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: 16.w,
                  vertical: 12.h,
                ),
                decoration: widget.decoration ?? defaultDecoration,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: _selectedItem == null
                        ? Text(
                          widget.hint,
                          style: TextStyle(
                            color: Colors.grey,
                            fontSize: 15.sp,
                          ),
                          overflow: TextOverflow.ellipsis,
                        )
                        : widget.itemBuilder(_selectedItem as T),
                    ),
                    widget.dropdownIcon ??
                        Icon(
                          _isDropdownOpen
                              ? Icons.keyboard_arrow_up
                              : Icons.keyboard_arrow_down,
                          color: Colors.grey,
                        ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}