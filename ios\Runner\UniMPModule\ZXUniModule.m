//
//  PayModuleBridge.m
//  Runner
//
//  Created by 许江泉 on 2025/2/17.
//

#import "ZXUniModule.h"
#import "Runner-Swift.h"

@implementation ZXUniModule
@synthesize weexInstance;
/* 小程序调用原生的方法 */

// 通过宏 WX_EXPORT_METHOD 将异步方法暴露给 js 端
WX_EXPORT_METHOD_SYNC(@selector(goZxGoods:))
WX_EXPORT_METHOD_SYNC(@selector(customerService:))
WX_EXPORT_METHOD_SYNC(@selector(appShare:shareType:callback:))
WX_EXPORT_METHOD_SYNC(@selector(appUseCoupon:evtName:callback:))
WX_EXPORT_METHOD_SYNC(@selector(openTLPayFunc:payType:callback:))


static NSString *nsNotificationName = @"APP_PAY_CALLBACK";
static NSString *eventName = @"appPayCallBack";

- (instancetype)init {
    self = [super init];
    if (self) {
        // 模块初始化逻辑（如注册通知监听）
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(handlePaymentResult:) name:nsNotificationName object:nil];
    }
    return self;
}

/// 跳甄选
- (NSString*)goZxGoods:(WXModuleCallback)callback {
    dispatch_async(dispatch_get_main_queue(), ^{
        MiniProgramBridge * obj = [[MiniProgramBridge alloc] init];
        [obj goZxGoods];
    });
    if (callback) {
        callback(@"success");
    }
    return  @"";
}


/// 去客服
- (NSString*)customerService:(WXModuleCallback) callback {
    dispatch_async(dispatch_get_main_queue(), ^{
        MiniProgramBridge * obj = [[MiniProgramBridge alloc] init];
        [obj customerService];
        if (callback) {
            callback(@"success");
        }
    });
    return  @"";
}


/// 回到app首页
- (NSString*)appUseCoupon:(NSString *)path evtName:(NSString*) evtName callback:(WXModuleCallback)callback {
    // options 为 js 端调用此方法时传递的参数
    dispatch_async(dispatch_get_main_queue(), ^{
        MiniProgramBridge * obj = [[MiniProgramBridge alloc] init];
        [obj appUseCoupon: path];
        if (callback) {
            callback(@"success");
        }
    });
    return  @"";
}

/// 微信支付
- (NSString*)openTLPayFunc:(NSString *)jsonString payType:(NSString*) payType callback:(WXModuleCallback)callback {
      // options 为 js 端调用此方法时传递的参数
     dispatch_async(dispatch_get_main_queue(), ^{
        MiniProgramBridge * obj = [[MiniProgramBridge alloc] init];
        [obj openTLPayFunc:jsonString payType:payType];
        if (callback) {
            callback(@"success");
         }
     });
    return  @"";
}


///  分享
- (NSString*)appShare:(NSDictionary *)params shareType:(NSNumber*) shareType callback:(WXModuleCallback)callback {
    NSLog(@"%@", params);
    dispatch_async(dispatch_get_main_queue(), ^{
        NSLog(@"%@", params);
        MiniProgramBridge * obj = [[MiniProgramBridge alloc] init];
        [obj shareApp:params shareType: shareType];
    });
    if (callback) {
        callback(@"success");
    }
    return  @"";
}


/// ios调用uniapp方法 必须在此类里面调用
/// - Parameter notification: 监听
- (void)handlePaymentResult:(NSNotification *)notification{
    NSDictionary *userInfo = notification.userInfo;
    [self.weexInstance fireGlobalEvent:eventName params:userInfo];
}



- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self name:nsNotificationName object:nil];
}


@end
