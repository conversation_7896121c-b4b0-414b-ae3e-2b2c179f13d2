package com.dxznjy.alading.activity.givelessons;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.blankj.rxbus.RxBus;
import com.dxznjy.alading.DXApplication;
import com.dxznjy.alading.R;
import com.dxznjy.alading.activity.BaseDxActivity;
import com.dxznjy.alading.activity.HomeActivity;
import com.dxznjy.alading.api.ApiProvider;
import com.dxznjy.alading.api.YxApiProvider;
import com.dxznjy.alading.common.Constants;
import com.dxznjy.alading.http.BaseSubscriber;
import com.dxznjy.alading.response.MeetingInfoResponse;
import com.dxznjy.alading.response.YXUserInfoResponse;
import com.dxznjy.alading.util.VolumeChecker;
import com.dxznjy.alading.widget.PermissonTipsPopWindow;
import com.hjq.permissions.OnPermissionCallback;
import com.hjq.permissions.Permission;
import com.hjq.permissions.XXPermissions;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import io.dcloud.uniplugin.TxMeetingUtil;
import io.flutter.plugin.common.MethodChannel;
import io.reactivex.disposables.Disposable;

public class MeetingPermissionActivity extends BaseDxActivity implements View.OnClickListener {

    @BindView(R.id.img_mic)
    ImageView img_mic;
    @BindView(R.id.img_voice)
    ImageView img_voice;
    @BindView(R.id.img_camear)
    ImageView img_camear;
    @BindView(R.id.img_sdcard)
    ImageView img_sdcard;
    @BindView(R.id.tv_joinmeeting)
    TextView tv_joinmeeting;
    @BindView(R.id.view_step1)
    View view_step1;
    @BindView(R.id.view_step2)
    View view_step2;
    @BindView(R.id.view_step3)
    View view_step3;
    private String courseId = "";
    PermissonTipsPopWindow permissonTipsPopWindow;

    boolean recordPermisoon = false,cameraPermisoon = false,sdCardPermisoon = false
            ,recordRefuse = false,cameraRefuse = false, sdCardRefuse = false;

    private String meetingParams = "";

    String meetingNum = "",identutyID = "";//会议号


    @Override
    public void beforeInflateNofit() {
        super.beforeInflateNofit();
        if (DXApplication.isTablet(this)){
            setNoFits();
        }
    }


    @Override
    public int getLayoutId() {
        return R.layout.activity_meeting_permission;
    }

    @Override
    public void initViewAndData() {
        inVisableDbTitleBar();
        meetingParams = getIntent().getStringExtra("meetingParams");
//        identutyID = getIntent().getStringExtra("identutyID");
        checkPermissionStatus();
    }



    @OnClick({R.id.img_back,R.id.tv_mic_check,R.id.tv_voice_check,R.id.tv_camear_check,R.id.tv_img_sdcard_check,R.id.tv_joinmeeting})
    public void onClick(View view) {
        switch (view.getId()){
            case R.id.img_back:
//                startActivity(new Intent(this, HomeActivity.class));
                finish();
                break;
            case R.id.tv_voice_check:
            case R.id.tv_mic_check:
                if(!XXPermissions.isGranted(MeetingPermissionActivity.this, Permission.RECORD_AUDIO)){
                    permissonTipsPopWindow = new PermissonTipsPopWindow(this, rootView, new PermissonTipsPopWindow.OnCheckPermissonListener() {
                        @Override
                        public void onSuccess() {
                            if(XXPermissions.isGranted(MeetingPermissionActivity.this, Permission.RECORD_AUDIO)){
                                img_mic.setVisibility(View.VISIBLE);
                                img_voice.setVisibility(View.VISIBLE);
                                view_step1.setVisibility(View.VISIBLE);
                            }else{
                                checkPermissionsRecordAudio();
                            }
                        }

                        @Override
                        public void onCancle() {

                        }
                    });
                    permissonTipsPopWindow.showQuanXianTips("我们需要您的扬声器/麦克风权限，将用于视频会议会话，是否同意?");
                }
                break;
            case R.id.tv_camear_check:
                if (!recordPermisoon){
                    showToast("请先完成扬声器/麦克风检测");
                    return;
                }
                if(!XXPermissions.isGranted(MeetingPermissionActivity.this, Permission.CAMERA)) {
                    permissonTipsPopWindow = new PermissonTipsPopWindow(this, rootView, new PermissonTipsPopWindow.OnCheckPermissonListener() {
                        @Override
                        public void onSuccess() {
                            if(XXPermissions.isGranted(MeetingPermissionActivity.this, Permission.CAMERA)){
                                img_camear.setVisibility(View.VISIBLE);
                            }else{
                                checkPermissionsCamear();
                            }
                        }

                        @Override
                        public void onCancle() {

                        }
                    });
                    permissonTipsPopWindow.showQuanXianTips("我们需要您的摄像头权限，将用于视频会议会话，是否同意?");
                }
                break;
            case R.id.tv_img_sdcard_check:
                if (!cameraPermisoon){
                    showToast("请先完成摄像头检测");
                    return;
                }
                if(!XXPermissions.isGranted(MeetingPermissionActivity.this, Permission.WRITE_EXTERNAL_STORAGE)
                        &&!XXPermissions.isGranted(MeetingPermissionActivity.this, Permission.READ_EXTERNAL_STORAGE)) {
                    permissonTipsPopWindow = new PermissonTipsPopWindow(this, rootView, new PermissonTipsPopWindow.OnCheckPermissonListener() {
                        @Override
                        public void onSuccess() {
                            if(XXPermissions.isGranted(MeetingPermissionActivity.this, Permission.WRITE_EXTERNAL_STORAGE) &&
                                    XXPermissions.isGranted(MeetingPermissionActivity.this, Permission.READ_EXTERNAL_STORAGE)){
                                img_sdcard.setVisibility(View.VISIBLE);
                            }else{
                                checkPermissionsSdCard();
                            }
                        }

                        @Override
                        public void onCancle() {

                        }
                    });
                    permissonTipsPopWindow.showQuanXianTips("我们需要您的存储权限，将用于会议中保存文件，是否同意?");
                }
                break;
       /*     case R.id.tv_xfk_check:
                if (!recordPermisoon){
                    showToast("请先完成扬声器/麦克风检测");
                    return;
                }
                if (!cameraPermisoon){
                    showToast("请先完成摄像头检测");
                    return;
                }
                if (!sdCardPermisoon){
                    showToast("请先完成文件存储检测");
                    return;
                }
                break;*/
            case R.id.tv_joinmeeting:
                if (recordPermisoon&&cameraPermisoon&&sdCardPermisoon){
                    if(!VolumeChecker.isVolumeSlightlyLow(MeetingPermissionActivity.this)){
                        showToast("为保证上课质量，请增大音量~");
//                    return;
                    }

                  /*  File audioFile = new File(getExternalFilesDir(null).getPath() + "/audio.pcm");
                    if (audioFile.exists()) {
                        boolean deleted = audioFile.delete();
                        if (deleted) {
                            Log.i("MeetingPermissionActivity", "Audio file deleted successfully.");
                        } else {
                            Log.e("MeetingPermissionActivity", "Failed to delete audio file.");
                        }
                    }*/
                    if (!TxMeetingUtil.isX5Init){
                        TxMeetingUtil.checkX5Init(this,meetingParams);
                    }else{
                        TxMeetingUtil.joinMeeting(this,meetingParams);
                    }
                }else{
                    showToast("请先进行权限检测");
                }
                break;
        }
    }



    public  void joinMeeting(){
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                String methodChannelName = "androidIosBackMeeting";
                MethodChannel methodChannel = new MethodChannel(DXApplication.app.flutterEngineInstance.getDartExecutor().getBinaryMessenger(),methodChannelName);
//                Map<String,String> map=new HashMap<>();
//                map.put("meetingNum",meetingNum);
                String json = "{\"meetingNum\":\"" + meetingNum + "\",\"identutyID\":\"" + identutyID + "\"}";
                methodChannel.invokeMethod("onEnterMeeting",json);
//                Log.i("zgj",json);
                finish();
            }
        });
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
//        startActivity(new Intent(this, HomeActivity.class));
        finish();

    }

/*    public  void joinTxMeeting() {
        Intent intent = new Intent(this, TCICClassActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_SINGLE_TOP);
        Bundle bundle = new Bundle();
        TCICClassConfig initConfig = new TCICClassConfig.Builder()
                .schoolId(3139658)
                .classId(373609441)
                .userId("30u4IDsP1bEyTpGyXv0hzvSuMHD")
                .token("eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************.0k2X8gpI9-EV5YOqVujDuB3vuLwNwXhnw8RaWH7Mp44")
                .build();
        bundle.putParcelable(TCICConstants.KEY_INIT_CONFIG, initConfig);
        intent.putExtras(bundle);
        startActivity(intent);
    }

     public  void checkX5Init(){
         if (TCICInitProvider.isMainProcess(this)) {
             //初始化X5内核
             TCICManager.getInstance().initX5Core("h2wRJQRkQ+7/uMmZR9qRRmWuMh02GN6xDsEHUbebnytCxN5XvZAJowp1TWsehW8tUUtstcG9BNwHvdVF9L7arNJemrkuSGiwcptE/lntMtcVh123zclVq1vPIvg/cy9f", new TBSSdkManageCallback() {

                 @Override
                 public void onCoreInitFinished() {
                 }

                 @Override
                 public void onViewInitFinished(boolean isX5Core) {
                     //X5内核初始化完成,可以进课堂
                 }
             });
         }
     }*/

    /**
     * 请求授权麦克风权限
     */
    public void checkPermissionsRecordAudio() {
        XXPermissions.with(this)
                // 申请单个权限
                .permission( Permission.RECORD_AUDIO)
                .request(new OnPermissionCallback() {
                    @Override
                    public void onGranted(@NonNull List<String> permissions, boolean allGranted) {
                        if (allGranted) {
                            img_mic.setVisibility(View.VISIBLE);
                            img_voice.setVisibility(View.VISIBLE);
                            view_step1.setVisibility(View.VISIBLE);
                            recordPermisoon = true;
                            checkPermissionStatus();
                        }
                    }

                    @Override
                    public void onDenied(@NonNull List<String> permissions, boolean doNotAskAgain) {
                        if (recordRefuse){
                            XXPermissions.startPermissionActivity(MeetingPermissionActivity.this);
                        }
                        recordRefuse = true;
                    }
                });
    }

    /**
     * 请求授权摄像头权限
     */
    public void checkPermissionsCamear() {
        XXPermissions.with(this)
                // 申请单个权限
                .permission( Permission.CAMERA)
                .request(new OnPermissionCallback() {
                    @Override
                    public void onGranted(@NonNull List<String> permissions, boolean allGranted) {
                        if (allGranted) {
                            img_camear.setVisibility(View.VISIBLE);
                            view_step2.setVisibility(View.VISIBLE);
                            cameraPermisoon = true;
                            checkPermissionStatus();
                        }
                    }

                    @Override
                    public void onDenied(@NonNull List<String> permissions, boolean doNotAskAgain) {
                        if (cameraRefuse){
                            XXPermissions.startPermissionActivity(MeetingPermissionActivity.this);
                        }
                        cameraRefuse = true;
                    }
                });
    }



    public void checkPermissionsSdCard() {
        XXPermissions.with(this)
                // 申请单个权限
                .permission( Permission.WRITE_EXTERNAL_STORAGE)
                .permission( Permission.READ_EXTERNAL_STORAGE)
                .request(new OnPermissionCallback() {
                    @Override
                    public void onGranted(@NonNull List<String> permissions, boolean allGranted) {
                        if (allGranted) {
                            img_sdcard.setVisibility(View.VISIBLE);
                            view_step3.setVisibility(View.VISIBLE);
                            sdCardPermisoon = true;
                            checkPermissionStatus();
                        }
                    }

                    @Override
                    public void onDenied(@NonNull List<String> permissions, boolean doNotAskAgain) {
                        if (sdCardRefuse){
                            XXPermissions.startPermissionActivity(MeetingPermissionActivity.this);
                        }
                        sdCardRefuse = true;
                    }
                });
    }

    @Override
    protected void onPostResume() {
        super.onPostResume();
        checkPermissionStatus();
    }

    /**
     * 校验是否授权麦克风与摄像头权限
     */
    public void checkPermissionStatus(){
        if(XXPermissions.isGranted(this, Permission.RECORD_AUDIO)){
            img_mic.setVisibility(View.VISIBLE);
            img_voice.setVisibility(View.VISIBLE);
            view_step1.setVisibility(View.VISIBLE);
            recordPermisoon = true;
        }else{
            recordRefuse = true;
        }
        if(XXPermissions.isGranted(this, Permission.CAMERA)){
            img_camear.setVisibility(View.VISIBLE);
            view_step2.setVisibility(View.VISIBLE);
            cameraPermisoon = true;
        }else{
            cameraRefuse = true;
        }
        if (XXPermissions.isGranted(this, Permission.WRITE_EXTERNAL_STORAGE)&&
                XXPermissions.isGranted(this, Permission.READ_EXTERNAL_STORAGE)){
            img_sdcard.setVisibility(View.VISIBLE);
            view_step3.setVisibility(View.VISIBLE);
            sdCardPermisoon = true;
        } else {
            sdCardPermisoon = false;
        }
    }

    /**
     * 获取课程下的会议号  并加入会议
     */
    private void getCourseMeetingInfo() {
        showDialog();
        Map<String,String> map = new HashMap<>();
        map.put("courseId",courseId);
        request(ApiProvider.getInstance(this).DFService.getCourseMeetingInfo(map), new BaseSubscriber<MeetingInfoResponse>(this){
            @Override
            public void onCompleted() {
                super.onCompleted();
            }

            @Override
            public void onError(Throwable e) {
                super.onError(e);
                closeDialog();
            }


            @Override
            public void onNext(MeetingInfoResponse response) {
                super.onNext(response);
                closeDialog();
                meetingNum = response.getData().getMeetingNum();
                joinMeeting();
            }
        });
    }

}