import 'package:student_end_flutter/generated/json/base/json_field.dart';
import 'package:student_end_flutter/generated/json/learn_tool_entity.g.dart';
import 'dart:convert';
export 'package:student_end_flutter/generated/json/learn_tool_entity.g.dart';

@JsonSerializable()
class LearnToolEntity {
	late String id;
	late String toolName;
	late int status;
	late String generalFlag;
	late int sort;
	late String iconAddress;
	late String ashIconAddress;
	late String jumpAddress;
	late String appJumpAddress;
	late String updateTime;
	late String createTime;
	late String bindId;
	late String remark;
	late String showChannel;
	late int deleted;
	late int showStatus;

	LearnToolEntity();

	factory LearnToolEntity.fromJson(Map<String, dynamic> json) => $LearnToolEntityFromJson(json);

	Map<String, dynamic> toJson() => $LearnToolEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}