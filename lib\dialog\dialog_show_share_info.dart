import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:student_end_flutter/components/toast_utils.dart';
import 'package:student_end_flutter/utils/share_utils.dart';

import '../bean/student_info_bean_entity.dart';
import '../common/CommonUtil.dart';
import '../common/user_option.dart';
import '../res/colors.dart';

class ShowShareInfoDialog extends StatefulWidget {
  @override
  _ShowShareInfoDialogState createState() => _ShowShareInfoDialogState();
}

class _ShowShareInfoDialogState extends State<ShowShareInfoDialog> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    double screenHeight = MediaQuery.of(context).size.height;
    return SingleChildScrollView(
      child: Column(
        children: [
          Container(
            alignment: Alignment.bottomCenter,
            height: screenHeight / 3.5,
            child: Image(image: AssetImage("assets/dialog_icon.png")),
          ),
          Container(
            width: double.infinity,
            margin: EdgeInsets.only(left: 20.w, right: 20.w),
            padding: EdgeInsets.all(10.w),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(15)),
                color: Colors.white),
            child: Column(
              children: [
                SizedBox(
                  height: 40.h,
                  width: double.infinity,
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      Text(
                        '温馨提示',
                        style: TextStyle(
                            decoration: TextDecoration.none,
                            fontSize: 16.sp,
                            fontFamily: "M",
                            color: Colors.black),
                      ),
                      Positioned(
                        top: 0,
                        right: 0,
                        child: GestureDetector(
                          onTap: () {
                            Navigator.pop(context);
                            ShareData.shareData = null;
                          },
                          child: Image.asset(
                            'assets/zx/close.png',
                            width: 26.w,
                            height: 26.w,
                          ),
                        ),
                      )
                    ],
                  ),
                ),
                SizedBox(
                  height: 20.w,
                ),
                Text(_getDialogText(),
                    textAlign: TextAlign.center,
                    style: TextStyle(
                        decoration: TextDecoration.none,
                        fontSize: 14.sp,
                        fontFamily: "R",
                        color: Colors.black)),
                SizedBox(
                  height: 20.w,
                ),
                GestureDetector(
                  onTap: () {
                    sureFunc();
                  },
                  child: Container(
                    alignment: Alignment.center,
                    margin: EdgeInsets.only(
                        left: 5.w, right: 5.w, top: 20.h, bottom: 10.h),
                    width: 200.w,
                    height: 50,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                          colors: [AppColors.f78d2aa, AppColors.f368e5c],
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter),
                      borderRadius: BorderRadius.all(Radius.circular(60)),
                    ),
                    child: Text(
                      '确定',
                      style: TextStyle(
                          decoration: TextDecoration.none,
                          fontSize: 14.sp,
                          fontFamily: "R",
                          color: Colors.white),
                    ),
                  ),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }

  /// 复用 scan_code_analysis.dart 的解析方法
  String _parsePrintId(String scanData) {
    try {
      if (scanData.isEmpty) {
        return '';
      }

      // 先进行URL解码
      String decodedData = Uri.decodeComponent(scanData);
      // 截取 printId= 的位置
      int printIdIndex = decodedData.indexOf('printId=');
      if (printIdIndex != -1) {
        String printIdPart = decodedData.substring(printIdIndex + 8);
        // 如果后面还有其他参数，取到 & 为止
        int endIndex = printIdPart.indexOf('&');
        if (endIndex != -1) {
          printIdPart = printIdPart.substring(0, endIndex);
        }
        // print("解析到的 printId: $printIdPart");
        return printIdPart;
      }

      return '';
    } catch (e) {
      return '';
    }
  }

  /// 根据二维码类型返回对应的文字提示
  String _getDialogText() {
    String shareUrl = ShareData.shareData ?? '';
    String printId = _parsePrintId(shareUrl);

    if (printId.isNotEmpty) {
      return "您有一条分享消息,\n点击确定进入题目解析页面";
    } else {
      return "您有一条分享消息,\n点击确定进入分享页面";
    }
  }

  sureFunc() {
    if (UserOption.token.isEmpty) {
      UserOption.toLoginPage();
      return;
    }
    print("-----${UserOption.userInfoBeanEntity?.toJson()}");
    if (UserOption.userInfoBeanEntity!.userId.isEmpty) {
      ToastUtil.showToastText("请求数据中~");
      return;
    }
    ShareUtils().getShard(ShareData.shareData);
    ShareData.shareData = null;
    Navigator.pop(context);
  }
}
