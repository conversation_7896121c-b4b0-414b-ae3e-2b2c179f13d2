// ignore_for_file: non_constant_identifier_names
// ignore_for_file: camel_case_types
// ignore_for_file: prefer_single_quotes

// This file is automatically generated. DO NOT EDIT, all your changes would be lost.
import 'package:flutter/material.dart' show debugPrint;
import 'package:student_end_flutter/bean/aiRead_member_bean_entity_entity.dart';
import 'package:student_end_flutter/bean/banner_bean_entity.dart';
import 'package:student_end_flutter/bean/course_detail_bean_entity.dart';
import 'package:student_end_flutter/bean/course_list_bean_entity.dart';
import 'package:student_end_flutter/bean/course_record_list_bean_entity.dart';
import 'package:student_end_flutter/bean/course_type_bean_entity.dart';
import 'package:student_end_flutter/bean/curriculum_entity.dart';
import 'package:student_end_flutter/bean/deliver_bean_entity.dart';
import 'package:student_end_flutter/bean/home_good_bean_entity.dart';
import 'package:student_end_flutter/bean/home_type_bean_entity.dart';
import 'package:student_end_flutter/bean/last_record_course_bean_entity.dart';
import 'package:student_end_flutter/bean/last_student_info_bean_entity.dart';
import 'package:student_end_flutter/bean/learn_tool_entity.dart';
import 'package:student_end_flutter/bean/leave_apply_info_entity.dart';
import 'package:student_end_flutter/bean/login_bean_entity.dart';
import 'package:student_end_flutter/bean/make_up_class_times_entity.dart';
import 'package:student_end_flutter/bean/pop_show_bean_entity.dart';
import 'package:student_end_flutter/bean/popup_model_entity.dart';
import 'package:student_end_flutter/bean/pyf_memberCode_bean_entity_entity.dart';
import 'package:student_end_flutter/bean/review_course_list_bean_entity.dart';
import 'package:student_end_flutter/bean/student_info_bean_entity.dart';
import 'package:student_end_flutter/bean/student_token_bean_entity.dart';
import 'package:student_end_flutter/bean/user_info_bean_entity.dart';
import 'package:student_end_flutter/bean/user_login_list_bean_entity.dart';
import 'package:student_end_flutter/bean/yunxin_userinfo_bean_entity.dart';
import 'package:student_end_flutter/bean/zx_course_entity.dart';
import 'package:student_end_flutter/bean/home_info_bean_entity.dart';

JsonConvert jsonConvert = JsonConvert();

typedef JsonConvertFunction<T> = T Function(Map<String, dynamic> json);
typedef EnumConvertFunction<T> = T Function(String value);
typedef ConvertExceptionHandler = void Function(Object error, StackTrace stackTrace);
extension MapSafeExt<K, V> on Map<K, V> {
  T? getOrNull<T>(K? key) {
    if (!containsKey(key) || key == null) {
      return null;
    } else {
      return this[key] as T?;
    }
  }
}

class JsonConvert {
  static ConvertExceptionHandler? onError;
  JsonConvertClassCollection convertFuncMap = JsonConvertClassCollection();

  /// When you are in the development, to generate a new model class, hot-reload doesn't find new generation model class, you can build on MaterialApp method called jsonConvert. ReassembleConvertFuncMap (); This method only works in a development environment
  /// https://flutter.cn/docs/development/tools/hot-reload
  /// class MyApp extends StatelessWidget {
  ///    const MyApp({Key? key})
  ///        : super(key: key);
  ///
  ///    @override
  ///    Widget build(BuildContext context) {
  ///      jsonConvert.reassembleConvertFuncMap();
  ///      return MaterialApp();
  ///    }
  /// }
  void reassembleConvertFuncMap() {
    bool isReleaseMode = const bool.fromEnvironment('dart.vm.product');
    if (!isReleaseMode) {
      convertFuncMap = JsonConvertClassCollection();
    }
  }

  T? convert<T>(dynamic value, {EnumConvertFunction? enumConvert}) {
    if (value == null) {
      return null;
    }
    if (value is T) {
      return value;
    }
    try {
      return _asT<T>(value, enumConvert: enumConvert);
    } catch (e, stackTrace) {
      debugPrint('asT<$T> $e $stackTrace');
      if (onError != null) {
        onError!(e, stackTrace);
      }
      return null;
    }
  }

  List<T?>? convertList<T>(List<dynamic>? value,
      {EnumConvertFunction? enumConvert}) {
    if (value == null) {
      return null;
    }
    try {
      return value.map((dynamic e) => _asT<T>(e, enumConvert: enumConvert))
          .toList();
    } catch (e, stackTrace) {
      debugPrint('asT<$T> $e $stackTrace');
      if (onError != null) {
        onError!(e, stackTrace);
      }
      return <T>[];
    }
  }

  List<T>? convertListNotNull<T>(dynamic value,
      {EnumConvertFunction? enumConvert}) {
    if (value == null) {
      return null;
    }
    try {
      return (value as List<dynamic>).map((dynamic e) =>
      _asT<T>(e, enumConvert: enumConvert)!).toList();
    } catch (e, stackTrace) {
      debugPrint('asT<$T> $e $stackTrace');
      if (onError != null) {
        onError!(e, stackTrace);
      }
      return <T>[];
    }
  }

  T? _asT<T extends Object?>(dynamic value,
      {EnumConvertFunction? enumConvert}) {
    final String type = T.toString();
    final String valueS = value.toString();
    if (enumConvert != null) {
      return enumConvert(valueS) as T;
    } else if (type == "String") {
      return valueS as T;
    } else if (type == "int") {
      final int? intValue = int.tryParse(valueS);
      if (intValue == null) {
        return double.tryParse(valueS)?.toInt() as T?;
      } else {
        return intValue as T;
      }
    } else if (type == "double") {
      return double.parse(valueS) as T;
    } else if (type == "DateTime") {
      return DateTime.parse(valueS) as T;
    } else if (type == "bool") {
      if (valueS == '0' || valueS == '1') {
        return (valueS == '1') as T;
      }
      return (valueS == 'true') as T;
    } else if (type == "Map" || type.startsWith("Map<")) {
      return value as T;
    } else {
      if (convertFuncMap.containsKey(type)) {
        if (value == null) {
          return null;
        }
        var covertFunc = convertFuncMap[type]!;
        if (covertFunc is Map<String, dynamic>) {
          return covertFunc(value as Map<String, dynamic>) as T;
        } else {
          return covertFunc(Map<String, dynamic>.from(value)) as T;
        }
      } else {
        throw UnimplementedError(
            '$type unimplemented,you can try running the app again');
      }
    }
  }

  //list is returned by type
  static M? _getListChildType<M>(List<Map<String, dynamic>> data) {
    if (<AiReadMemberBeanEntityEntity>[] is M) {
      return data.map<AiReadMemberBeanEntityEntity>((Map<String, dynamic> e) =>
          AiReadMemberBeanEntityEntity.fromJson(e)).toList() as M;
    }
    if (<BannerBeanEntity>[] is M) {
      return data.map<BannerBeanEntity>((Map<String, dynamic> e) =>
          BannerBeanEntity.fromJson(e)).toList() as M;
    }
    if (<BannerBeanData>[] is M) {
      return data.map<BannerBeanData>((Map<String, dynamic> e) =>
          BannerBeanData.fromJson(e)).toList() as M;
    }
    if (<CourseDetailBeanEntity>[] is M) {
      return data.map<CourseDetailBeanEntity>((Map<String, dynamic> e) =>
          CourseDetailBeanEntity.fromJson(e)).toList() as M;
    }
    if (<CourseListBeanEntity>[] is M) {
      return data.map<CourseListBeanEntity>((Map<String, dynamic> e) =>
          CourseListBeanEntity.fromJson(e)).toList() as M;
    }
    if (<CourseListBeanData>[] is M) {
      return data.map<CourseListBeanData>((Map<String, dynamic> e) =>
          CourseListBeanData.fromJson(e)).toList() as M;
    }
    if (<CourseRecordListBeanEntity>[] is M) {
      return data.map<CourseRecordListBeanEntity>((Map<String, dynamic> e) =>
          CourseRecordListBeanEntity.fromJson(e)).toList() as M;
    }
    if (<CourseRecordListBeanData>[] is M) {
      return data.map<CourseRecordListBeanData>((Map<String, dynamic> e) =>
          CourseRecordListBeanData.fromJson(e)).toList() as M;
    }
    if (<CourseTypeBeanEntity>[] is M) {
      return data.map<CourseTypeBeanEntity>((Map<String, dynamic> e) =>
          CourseTypeBeanEntity.fromJson(e)).toList() as M;
    }
    if (<CurriculumEntity>[] is M) {
      return data.map<CurriculumEntity>((Map<String, dynamic> e) =>
          CurriculumEntity.fromJson(e)).toList() as M;
    }
    if (<CurriculumCurriculumTool>[] is M) {
      return data.map<CurriculumCurriculumTool>((Map<String, dynamic> e) =>
          CurriculumCurriculumTool.fromJson(e)).toList() as M;
    }
    if (<DeliverBeanEntity>[] is M) {
      return data.map<DeliverBeanEntity>((Map<String, dynamic> e) =>
          DeliverBeanEntity.fromJson(e)).toList() as M;
    }
    if (<HomeGoodBeanEntity>[] is M) {
      return data.map<HomeGoodBeanEntity>((Map<String, dynamic> e) =>
          HomeGoodBeanEntity.fromJson(e)).toList() as M;
    }
    if (<HomeGoodBeanData>[] is M) {
      return data.map<HomeGoodBeanData>((Map<String, dynamic> e) =>
          HomeGoodBeanData.fromJson(e)).toList() as M;
    }
    if (<HomeGoodBeanDataData>[] is M) {
      return data.map<HomeGoodBeanDataData>((Map<String, dynamic> e) =>
          HomeGoodBeanDataData.fromJson(e)).toList() as M;
    }
    if (<HomeGoodBeanDataDataCoupon>[] is M) {
      return data.map<HomeGoodBeanDataDataCoupon>((Map<String, dynamic> e) =>
          HomeGoodBeanDataDataCoupon.fromJson(e)).toList() as M;
    }
    if (<HomeGoodBeanDataDataGoodsShareTextList>[] is M) {
      return data.map<HomeGoodBeanDataDataGoodsShareTextList>((
          Map<String, dynamic> e) =>
          HomeGoodBeanDataDataGoodsShareTextList.fromJson(e)).toList() as M;
    }
    if (<HomeTypeBeanEntity>[] is M) {
      return data.map<HomeTypeBeanEntity>((Map<String, dynamic> e) =>
          HomeTypeBeanEntity.fromJson(e)).toList() as M;
    }
    if (<HomeTypeBeanData>[] is M) {
      return data.map<HomeTypeBeanData>((Map<String, dynamic> e) =>
          HomeTypeBeanData.fromJson(e)).toList() as M;
    }
    if (<LastRecordCourseBeanEntity>[] is M) {
      return data.map<LastRecordCourseBeanEntity>((Map<String, dynamic> e) =>
          LastRecordCourseBeanEntity.fromJson(e)).toList() as M;
    }
    if (<LastStudentInfoBeanEntity>[] is M) {
      return data.map<LastStudentInfoBeanEntity>((Map<String, dynamic> e) =>
          LastStudentInfoBeanEntity.fromJson(e)).toList() as M;
    }
    if (<LastStudentInfoBeanMerchantVos>[] is M) {
      return data.map<LastStudentInfoBeanMerchantVos>((
          Map<String, dynamic> e) => LastStudentInfoBeanMerchantVos.fromJson(e))
          .toList() as M;
    }
    if (<LearnToolEntity>[] is M) {
      return data.map<LearnToolEntity>((Map<String, dynamic> e) =>
          LearnToolEntity.fromJson(e)).toList() as M;
    }
    if (<LeaveApplyInfoEntity>[] is M) {
      return data.map<LeaveApplyInfoEntity>((Map<String, dynamic> e) =>
          LeaveApplyInfoEntity.fromJson(e)).toList() as M;
    }
    if (<LoginBeanEntity>[] is M) {
      return data.map<LoginBeanEntity>((Map<String, dynamic> e) =>
          LoginBeanEntity.fromJson(e)).toList() as M;
    }
    if (<LoginBeanAdditionalParameters>[] is M) {
      return data.map<LoginBeanAdditionalParameters>((Map<String, dynamic> e) =>
          LoginBeanAdditionalParameters.fromJson(e)).toList() as M;
    }
    if (<MakeUpClassTimesEntity>[] is M) {
      return data.map<MakeUpClassTimesEntity>((Map<String, dynamic> e) =>
          MakeUpClassTimesEntity.fromJson(e)).toList() as M;
    }
    if (<MakeUpClassTimesResult>[] is M) {
      return data.map<MakeUpClassTimesResult>((Map<String, dynamic> e) =>
          MakeUpClassTimesResult.fromJson(e)).toList() as M;
    }
    if (<PopShowBeanEntity>[] is M) {
      return data.map<PopShowBeanEntity>((Map<String, dynamic> e) =>
          PopShowBeanEntity.fromJson(e)).toList() as M;
    }
    if (<PopupModelEntity>[] is M) {
      return data.map<PopupModelEntity>((Map<String, dynamic> e) =>
          PopupModelEntity.fromJson(e)).toList() as M;
    }
    if (<PopupModelData>[] is M) {
      return data.map<PopupModelData>((Map<String, dynamic> e) =>
          PopupModelData.fromJson(e)).toList() as M;
    }
    if (<PyfMemberCodeBeanEntityEntity>[] is M) {
      return data.map<PyfMemberCodeBeanEntityEntity>((Map<String, dynamic> e) =>
          PyfMemberCodeBeanEntityEntity.fromJson(e)).toList() as M;
    }
    if (<ReviewCourseListBeanEntity>[] is M) {
      return data.map<ReviewCourseListBeanEntity>((Map<String, dynamic> e) =>
          ReviewCourseListBeanEntity.fromJson(e)).toList() as M;
    }
    if (<ReviewCourseListBeanData>[] is M) {
      return data.map<ReviewCourseListBeanData>((Map<String, dynamic> e) =>
          ReviewCourseListBeanData.fromJson(e)).toList() as M;
    }
    if (<StudentInfoBeanEntity>[] is M) {
      return data.map<StudentInfoBeanEntity>((Map<String, dynamic> e) =>
          StudentInfoBeanEntity.fromJson(e)).toList() as M;
    }
    if (<StudentInfoBeanMerchantVos>[] is M) {
      return data.map<StudentInfoBeanMerchantVos>((Map<String, dynamic> e) =>
          StudentInfoBeanMerchantVos.fromJson(e)).toList() as M;
    }
    if (<StudentTokenBeanEntity>[] is M) {
      return data.map<StudentTokenBeanEntity>((Map<String, dynamic> e) =>
          StudentTokenBeanEntity.fromJson(e)).toList() as M;
    }
    if (<UserInfoBeanEntity>[] is M) {
      return data.map<UserInfoBeanEntity>((Map<String, dynamic> e) =>
          UserInfoBeanEntity.fromJson(e)).toList() as M;
    }
    if (<UserLoginListBeanEntity>[] is M) {
      return data.map<UserLoginListBeanEntity>((Map<String, dynamic> e) =>
          UserLoginListBeanEntity.fromJson(e)).toList() as M;
    }
    if (<YunxinUserinfoBeanEntity>[] is M) {
      return data.map<YunxinUserinfoBeanEntity>((Map<String, dynamic> e) =>
          YunxinUserinfoBeanEntity.fromJson(e)).toList() as M;
    }
    if (<YunxinUserinfoBeanData>[] is M) {
      return data.map<YunxinUserinfoBeanData>((Map<String, dynamic> e) =>
          YunxinUserinfoBeanData.fromJson(e)).toList() as M;
    }
    if (<YunxinUserinfoBeanDataUserInfos>[] is M) {
      return data.map<YunxinUserinfoBeanDataUserInfos>((
          Map<String, dynamic> e) =>
          YunxinUserinfoBeanDataUserInfos.fromJson(e)).toList() as M;
    }
    if (<ZxCourseEntity>[] is M) {
      return data.map<ZxCourseEntity>((Map<String, dynamic> e) =>
          ZxCourseEntity.fromJson(e)).toList() as M;
    }
    if (<ZxCourseData>[] is M) {
      return data.map<ZxCourseData>((Map<String, dynamic> e) =>
          ZxCourseData.fromJson(e)).toList() as M;
    }
    if (<ZxCourseDataData>[] is M) {
      return data.map<ZxCourseDataData>((Map<String, dynamic> e) =>
          ZxCourseDataData.fromJson(e)).toList() as M;
    }
    if (<ZxCourseDataDataCoupon>[] is M) {
      return data.map<ZxCourseDataDataCoupon>((Map<String, dynamic> e) =>
          ZxCourseDataDataCoupon.fromJson(e)).toList() as M;
    }
    if (<ZxCourseDataDataGoodsShareTextList>[] is M) {
      return data.map<ZxCourseDataDataGoodsShareTextList>((
          Map<String, dynamic> e) =>
          ZxCourseDataDataGoodsShareTextList.fromJson(e)).toList() as M;
    }

    debugPrint("$M not found");

    return null;
  }

  static M? fromJsonAsT<M>(dynamic json) {
    if (json is M) {
      return json;
    }
    if (json is List) {
      return _getListChildType<M>(
          json.map((dynamic e) => e as Map<String, dynamic>).toList());
    } else {
      return jsonConvert.convert<M>(json);
    }
  }
}

class JsonConvertClassCollection {
  Map<String, JsonConvertFunction> convertFuncMap = {
    (AiReadMemberBeanEntityEntity).toString(): AiReadMemberBeanEntityEntity
        .fromJson,
    (BannerBeanEntity).toString(): BannerBeanEntity.fromJson,
    (BannerBeanData).toString(): BannerBeanData.fromJson,
    (CourseDetailBeanEntity).toString(): CourseDetailBeanEntity.fromJson,
    (CourseListBeanEntity).toString(): CourseListBeanEntity.fromJson,
    (CourseListBeanData).toString(): CourseListBeanData.fromJson,
    (CourseRecordListBeanEntity).toString(): CourseRecordListBeanEntity
        .fromJson,
    (CourseRecordListBeanData).toString(): CourseRecordListBeanData.fromJson,
    (CourseTypeBeanEntity).toString(): CourseTypeBeanEntity.fromJson,
    (CurriculumEntity).toString(): CurriculumEntity.fromJson,
    (CurriculumCurriculumTool).toString(): CurriculumCurriculumTool.fromJson,
    (DeliverBeanEntity).toString(): DeliverBeanEntity.fromJson,
    (HomeGoodBeanEntity).toString(): HomeGoodBeanEntity.fromJson,
    (HomeGoodBeanData).toString(): HomeGoodBeanData.fromJson,
    (HomeGoodBeanDataData).toString(): HomeGoodBeanDataData.fromJson,
    (HomeGoodBeanDataDataCoupon).toString(): HomeGoodBeanDataDataCoupon
        .fromJson,
    (HomeGoodBeanDataDataGoodsShareTextList)
        .toString(): HomeGoodBeanDataDataGoodsShareTextList.fromJson,
    (HomeTypeBeanEntity).toString(): HomeTypeBeanEntity.fromJson,
    (HomeTypeBeanData).toString(): HomeTypeBeanData.fromJson,
    (LastRecordCourseBeanEntity).toString(): LastRecordCourseBeanEntity
        .fromJson,
    (LastStudentInfoBeanEntity).toString(): LastStudentInfoBeanEntity.fromJson,
    (LastStudentInfoBeanMerchantVos).toString(): LastStudentInfoBeanMerchantVos
        .fromJson,
    (LearnToolEntity).toString(): LearnToolEntity.fromJson,
    (LeaveApplyInfoEntity).toString(): LeaveApplyInfoEntity.fromJson,
    (LoginBeanEntity).toString(): LoginBeanEntity.fromJson,
    (LoginBeanAdditionalParameters).toString(): LoginBeanAdditionalParameters
        .fromJson,
    (MakeUpClassTimesEntity).toString(): MakeUpClassTimesEntity.fromJson,
    (MakeUpClassTimesResult).toString(): MakeUpClassTimesResult.fromJson,
    (PopShowBeanEntity).toString(): PopShowBeanEntity.fromJson,
    (PopupModelEntity).toString(): PopupModelEntity.fromJson,
    (PopupModelData).toString(): PopupModelData.fromJson,
    (PyfMemberCodeBeanEntityEntity).toString(): PyfMemberCodeBeanEntityEntity
        .fromJson,
    (ReviewCourseListBeanEntity).toString(): ReviewCourseListBeanEntity
        .fromJson,
    (ReviewCourseListBeanData).toString(): ReviewCourseListBeanData.fromJson,
    (StudentInfoBeanEntity).toString(): StudentInfoBeanEntity.fromJson,
    (StudentInfoBeanMerchantVos).toString(): StudentInfoBeanMerchantVos
        .fromJson,
    (StudentTokenBeanEntity).toString(): StudentTokenBeanEntity.fromJson,
    (UserInfoBeanEntity).toString(): UserInfoBeanEntity.fromJson,
    (UserLoginListBeanEntity).toString(): UserLoginListBeanEntity.fromJson,
    (YunxinUserinfoBeanEntity).toString(): YunxinUserinfoBeanEntity.fromJson,
    (YunxinUserinfoBeanData).toString(): YunxinUserinfoBeanData.fromJson,
    (YunxinUserinfoBeanDataUserInfos)
        .toString(): YunxinUserinfoBeanDataUserInfos.fromJson,
    (ZxCourseEntity).toString(): ZxCourseEntity.fromJson,
    (ZxCourseData).toString(): ZxCourseData.fromJson,
    (ZxCourseDataData).toString(): ZxCourseDataData.fromJson,
    (ZxCourseDataDataCoupon).toString(): ZxCourseDataDataCoupon.fromJson,
    (ZxCourseDataDataGoodsShareTextList)
        .toString(): ZxCourseDataDataGoodsShareTextList.fromJson,
    (HomePageEntity).toString(): HomePageEntity.fromJson,
    (HomePageData).toString(): HomePageData.fromJson,
    (SpecialItem).toString(): SpecialItem.fromJson,
    (BannerItem).toString(): BannerItem.fromJson,
    (GoodsShareText).toString(): GoodsShareText.fromJson,
    (HomePageGoodsItem).toString(): HomePageGoodsItem.fromJson,
  };
  // HomePageEntity: (Map<String, dynamic> json) => HomePageEntity.fromJson(json),
  // HomePageData: (Map<String, dynamic> json) => HomePageData.fromJson(json),
  // SpecialItem: (Map<String, dynamic> json) => SpecialItem.fromJson(json),
  // BannerItem: (Map<String, dynamic> json) => BannerItem.fromJson(json),
  // GoodsItem: (Map<String, dynamic> json) => GoodsItem.fromJson(json),
  // Coupon: (Map<String, dynamic> json) => Coupon.fromJson(json),
  // GoodsShareText: (Map<String, dynamic> json) => GoodsShareText.fromJson(json),
  // HomePageGoodsItem: (Map<String, dynamic> json) => HomePageGoodsItem.fromJson(json),

  bool containsKey(String type) {
    return convertFuncMap.containsKey(type);
  }

  JsonConvertFunction? operator [](String key) {
    return convertFuncMap[key];
  }
}