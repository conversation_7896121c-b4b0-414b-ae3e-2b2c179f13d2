package com.dxznjy.alading.adapter.givelessons;

import android.content.Context;
import android.text.TextUtils;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dxznjy.alading.R;
import com.dxznjy.alading.response.PayCourseDataResponse;

import java.util.List;
import java.util.Random;


public class PayGiveFinishLessonsAdapter extends BaseQuickAdapter<PayCourseDataResponse.PayCourseData, BaseViewHolder> {
    int[] headBg = new int[]{R.drawable.bg_circle_12c287,R.drawable.bg_circle_25c36d,R.drawable.bg_circle_6882f0,R.drawable.bg_circle_eaf3dd
            ,R.drawable.bg_circle_d6eef4,R.drawable.bg_circle_f47307,R.drawable.bg_circle_f9eaa0};
    public PayGiveFinishLessonsAdapter(Context context, List<PayCourseDataResponse.PayCourseData> data) {
        super(R.layout.adapter_pay_give_finish_lessons, data);
        this.mContext = context;
    }

    @Override
    protected void convert(final BaseViewHolder helper, PayCourseDataResponse.PayCourseData item) {
        helper.setText(R.id.tv_coursename,item.getCourseName());
        helper.setText(R.id.tv_coursetype, item.getCourseType());
        helper.setText(R.id.tv_coursetime,"上课时间："+item.getCourseTime());
        helper.setText(R.id.tv_teachername,"主讲师："+item.getTeacherName());
        helper.setText(R.id.tv_studentname,item.getStudentName());
        if (!TextUtils.isEmpty(item.getTeacherName())){
            helper.setText(R.id.tv_fristname,item.getTeacherName().substring(0,1));
        }
        if (TextUtils.isEmpty(item.getFeedback())){
            helper.setText(R.id.tv_feedback,"等待反馈");
            helper.setGone(R.id.tv_goonlearn,true);
            helper.addOnClickListener(R.id.tv_goonlearn);
        }else{
            helper.setGone(R.id.tv_goonlearn,false);
            helper.setText(R.id.tv_feedback,"查看反馈");
            helper.addOnClickListener(R.id.tv_feedback);
        }
        Random random = new Random();
        int index = random.nextInt(headBg.length);
        helper.setBackgroundRes(R.id.tv_fristname,headBg[index]);
    }
}
