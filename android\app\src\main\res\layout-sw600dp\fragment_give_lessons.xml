<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@mipmap/pic_beijin_pad"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true"
        android:background="@color/white"
        android:orientation="vertical">
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="50dp">
        <RelativeLayout
            android:id="@+id/rl_titlebar"
            android:layout_width="wrap_content"
            android:layout_height="36dp"
            >
            <ImageView
                android:id="@+id/img_back"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:paddingLeft="30dp"
                android:paddingRight="30dp"
                android:src="@mipmap/ic_back_black"
                />
        </RelativeLayout>
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="36dp"
            android:layout_gravity="center"
            android:background="@drawable/bg_radius4_f8f8f8"
            android:layout_centerInParent="true"
            android:gravity="center"
            android:orientation="horizontal"
            android:padding="2dp">

            <TextView
                android:id="@+id/tv_payclass"
                android:layout_width="110dp"
                android:layout_height="match_parent"
                android:background="@drawable/bg_radius4_white"
                android:gravity="center"
                android:text="交付课"
                android:textColor="@color/_428a6f"
                android:textSize="@dimen/sp_14"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv_videoclass"
                android:layout_width="110dp"
                android:layout_height="match_parent"
                android:gravity="center"
                android:text="录播课"
                android:textColor="@color/_a29d9d"
                android:textSize="@dimen/sp_14" />
        </LinearLayout>
    </RelativeLayout>
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginRight="30dp"
        android:layout_marginTop="14dp"
        android:layout_marginBottom="14dp"
        android:layout_marginLeft="30dp">

        <RelativeLayout
            android:id="@+id/rl_unclass"
            android:layout_width="50dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical">

            <View
                android:id="@+id/view_unclass"
                android:layout_width="wrap_content"
                android:layout_height="4dp"
                android:layout_below="@+id/tv_unclass"
                android:layout_marginTop="-6dp"
                android:background="@drawable/bg_green_line" />

            <TextView
                android:id="@+id/tv_unclass"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="未上课"
                android:textColor="@color/_555555"
                android:textSize="14sp" />
        </RelativeLayout>

        <View
            android:id="@+id/view_line"
            android:layout_width="0.5dp"
            android:layout_height="14dp"
            android:layout_centerVertical="true"
            android:layout_marginLeft="14dp"
            android:layout_marginRight="14dp"
            android:layout_toRightOf="@+id/rl_unclass"
            android:background="@color/_d7dde3" />

        <RelativeLayout
            android:id="@+id/rl_onclass"
            android:layout_width="50dp"
            android:layout_height="wrap_content"
            android:layout_toRightOf="@+id/view_line"
            android:gravity="center"
            android:orientation="vertical">

            <View
                android:id="@+id/view_onclass"
                android:layout_width="wrap_content"
                android:layout_height="4dp"
                android:layout_below="@+id/tv_onclass"
                android:layout_marginTop="-6dp"
                android:background="@drawable/bg_green_line"
                android:visibility="gone" />

            <TextView
                android:id="@+id/tv_onclass"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="已上课"
                android:textColor="@color/_555555"
                android:textSize="14sp" />
        </RelativeLayout>

        <LinearLayout
            android:id="@+id/ll_select_type"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_select_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="全部"
                android:textColor="@color/_555555"
                android:textSize="14sp" />

            <ImageView
                android:layout_width="14dp"
                android:layout_height="14dp"
                android:layout_marginLeft="4dp"
                android:src="@mipmap/ic_drop_black" />
        </LinearLayout>
    </RelativeLayout>
    </LinearLayout>
    <com.scwang.smartrefresh.layout.SmartRefreshLayout
        android:id="@+id/refreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_marginLeft="30dp"
            android:layout_marginRight="20dp"
            android:layout_marginTop="10dp"
            android:layout_height="match_parent"/>
    </com.scwang.smartrefresh.layout.SmartRefreshLayout>
</LinearLayout>