import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:url_launcher/url_launcher.dart';

class NetworkUtils {
  /// 检查当前网络是否可用（仅检测连接状态，不验证互联网访问）
  static Future<bool> get isConnected async {
    final connectivityResult = await Connectivity().checkConnectivity();
    return connectivityResult != ConnectivityResult.none;
  }

  /// 跳转到系统网络设置页面（跨平台适配）
  static Future<void> openSettings() async {
    const platformUrls = {
      'android': 'settings:',
      'ios': 'App-Prefs:',
    };

    final url = platformUrls[defaultTargetPlatform.name] ?? 'settings:';
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url));
    } else {
      throw '无法打开系统设置页面';
    }
  }
}
