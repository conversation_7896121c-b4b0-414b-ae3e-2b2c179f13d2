import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';
import 'package:dio/dio.dart';
// import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:student_end_flutter/components/toast_utils.dart';
import 'httpRequest.dart';

class Utils{
  static checkPhone(phone){
    final phoneRegex = RegExp(r'^1[3-9]\d{9}$');
    return phoneRegex.hasMatch(phone);
  }

  static handleLongStr(text,maxLength){
    return (text.length > maxLength) ? text.substring(0, maxLength) + "..." : text;
  }

  static getUuid() {
    final random = Random();
    final timestamp = DateTime.now().millisecondsSinceEpoch.toRadixString(16);
    final randomPart =
    List.generate(12, (_) => random.nextInt(16).toRadixString(16)).join();
    return '${timestamp.substring(0, 8)}-${timestamp.substring(8, 11)}-4${randomPart.substring(0, 3)}-${randomPart.substring(3, 7)}-${randomPart.substring(7, 12)}';
  }

  ///云信加密
  static String getCheckSum(String nonce, String curTime, String appSecret) {
    String plaintext = appSecret + nonce + curTime;
    var bytes = utf8.encode(plaintext);
    var digest = sha1.convert(bytes);
    return digest.toString();
  }


  /// 请求相册/存储权限
  static Future<bool> requestPermission() async {
    if (Platform.isIOS) {
      PermissionStatus status = await Permission.photos.request();
      return status.isGranted;
    } else if (Platform.isAndroid) {
      PermissionStatus status = await Permission.storage.request();
      return status.isGranted;
    }
    return false;
  }



  /// 保存图片到相册
  static saveImageToGallery(String imageUrl) async {
    bool hasPermission = await requestPermission();
    if (!hasPermission) {
      ToastUtil.showShortErrorToast("请先授权相册权限~");
      return;
    }
    if(imageUrl == null || imageUrl.isEmpty){
      return;
    }
    // try{
      var response = await Dio().get(
        imageUrl,
        options: Options(responseType: ResponseType.bytes),
      );
    print(response);
    Uint8List uint8List = Uint8List.fromList(response.data);
    print(uint8List);
      if(response != null){
        // await ImageGallerySaver.saveImage(
        //   uint8List,
        //   quality: 80,
        //   name: "zx_pic_${DateTime.now().millisecondsSinceEpoch}",
        // );
      }
    // }catch(e){
    //   ToastUtil.showShortErrorToast("下载失败~");
    // }
  }
}