import 'package:student_end_flutter/generated/json/base/json_convert_content.dart';
import 'package:student_end_flutter/bean/pop_show_bean_entity.dart';

PopShowBeanEntity $PopShowBeanEntityFromJson(Map<String, dynamic> json) {
  final PopShowBeanEntity popShowBeanEntity = PopShowBeanEntity();
  final String? todayShowData = jsonConvert.convert<String>(
      json['todayShowData']);
  if (todayShowData != null) {
    popShowBeanEntity.todayShowData = todayShowData;
  }
  final List<String>? popNoShowListId = (json['popNoShowListId'] as List<
      dynamic>?)?.map(
          (e) => jsonConvert.convert<String>(e) as String).toList();
  if (popNoShowListId != null) {
    popShowBeanEntity.popNoShowListId = popNoShowListId;
  }
  return popShowBeanEntity;
}

Map<String, dynamic> $PopShowBeanEntityToJson(PopShowBeanEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['todayShowData'] = entity.todayShowData;
  data['popNoShowListId'] = entity.popNoShowListId;
  return data;
}

extension PopShowBeanEntityExtension on PopShowBeanEntity {
  PopShowBeanEntity copyWith({
    String? todayShowData,
    List<String>? popNoShowListId,
  }) {
    return PopShowBeanEntity()
      ..todayShowData = todayShowData ?? this.todayShowData
      ..popNoShowListId = popNoShowListId ?? this.popNoShowListId;
  }
}