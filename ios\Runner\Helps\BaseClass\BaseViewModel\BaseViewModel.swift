//
//  BaseViewModel.swift
//  StuMetApp
//
//  Created by 许江泉 on 2024/11/19.
//

import UIKit
// 视图更新回调
protocol ViewModelDelegate: NSObjectProtocol {
    func onFetchCompleted()
    func onFetchFailed(with reason: String)
}

extension ViewModelDelegate {
    func onFetchCompleted() {}
    func onFetchFailed(with reason: String){}
}

class BaseViewModel {
    var pageNum = 1
    var pageSize = 10
    var hasMore = ResponseMoreArrDataType.nullData
  
}
