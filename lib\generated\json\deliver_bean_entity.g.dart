import 'package:student_end_flutter/generated/json/base/json_convert_content.dart';
import 'package:student_end_flutter/bean/deliver_bean_entity.dart';

DeliverBeanEntity $DeliverBeanEntityFromJson(Map<String, dynamic> json) {
  final DeliverBeanEntity deliverBeanEntity = DeliverBeanEntity();
  final String? courseId = jsonConvert.convert<String>(json['courseId']);
  if (courseId != null) {
    deliverBeanEntity.courseId = courseId;
  }
  final String? planId = jsonConvert.convert<String>(json['planId']);
  if (planId != null) {
    deliverBeanEntity.planId = planId;
  }
  final String? courseName = jsonConvert.convert<String>(json['courseName']);
  if (courseName != null) {
    deliverBeanEntity.courseName = courseName;
  }
  final String? courseType = jsonConvert.convert<String>(json['courseType']);
  if (courseType != null) {
    deliverBeanEntity.courseType = courseType;
  }
  final String? courseTime = jsonConvert.convert<String>(json['courseTime']);
  if (courseTime != null) {
    deliverBeanEntity.courseTime = courseTime;
  }
  final int? status = jsonConvert.convert<int>(json['status']);
  if (status != null) {
    deliverBeanEntity.status = status;
  }
  final String? teacherId = jsonConvert.convert<String>(json['teacherId']);
  if (teacherId != null) {
    deliverBeanEntity.teacherId = teacherId;
  }
  final String? teacherName = jsonConvert.convert<String>(json['teacherName']);
  if (teacherName != null) {
    deliverBeanEntity.teacherName = teacherName;
  }
  final String? teacherPhoto = jsonConvert.convert<String>(
      json['teacherPhoto']);
  if (teacherPhoto != null) {
    deliverBeanEntity.teacherPhoto = teacherPhoto;
  }
  final String? meetingId = jsonConvert.convert<String>(json['meetingId']);
  if (meetingId != null) {
    deliverBeanEntity.meetingId = meetingId;
  }
  final String? meetingNum = jsonConvert.convert<String>(json['meetingNum']);
  if (meetingNum != null) {
    deliverBeanEntity.meetingNum = meetingNum;
  }
  final String? feedback = jsonConvert.convert<String>(json['feedback']);
  if (feedback != null) {
    deliverBeanEntity.feedback = feedback;
  }
  final String? studentCode = jsonConvert.convert<String>(json['studentCode']);
  if (studentCode != null) {
    deliverBeanEntity.studentCode = studentCode;
  }
  final String? studentName = jsonConvert.convert<String>(json['studentName']);
  if (studentName != null) {
    deliverBeanEntity.studentName = studentName;
  }
  final String? oneToManyType = jsonConvert.convert<String>(
      json['oneToManyType']);
  if (oneToManyType != null) {
    deliverBeanEntity.oneToManyType = oneToManyType;
  }
  final String? classPlanStudyId = jsonConvert.convert<String>(
      json['classPlanStudyId']);
  if (classPlanStudyId != null) {
    deliverBeanEntity.classPlanStudyId = classPlanStudyId;
  }

  final int? leaveStatus = jsonConvert.convert<int>(
      json['leaveStatus']);
  if (leaveStatus != null) {
    deliverBeanEntity.leaveStatus = leaveStatus;
  }

  final int? lessonsFlag = jsonConvert.convert<int>(
      json['lessonsFlag']);
  if (lessonsFlag != null) {
    deliverBeanEntity.lessonsFlag = lessonsFlag;
  }
  return deliverBeanEntity;
}

Map<String, dynamic> $DeliverBeanEntityToJson(DeliverBeanEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['courseId'] = entity.courseId;
  data['planId'] = entity.planId;
  data['courseName'] = entity.courseName;
  data['courseType'] = entity.courseType;
  data['courseTime'] = entity.courseTime;
  data['status'] = entity.status;
  data['teacherId'] = entity.teacherId;
  data['teacherName'] = entity.teacherName;
  data['teacherPhoto'] = entity.teacherPhoto;
  data['meetingId'] = entity.meetingId;
  data['meetingNum'] = entity.meetingNum;
  data['feedback'] = entity.feedback;
  data['studentCode'] = entity.studentCode;
  data['studentName'] = entity.studentName;
  data['oneToManyType'] = entity.oneToManyType;
  data['classPlanStudyId'] = entity.classPlanStudyId;
  data['leaveStatus'] = entity.leaveStatus;
  data['lessonsFlag'] = entity.lessonsFlag;
  return data;
}

extension DeliverBeanEntityExtension on DeliverBeanEntity {
  DeliverBeanEntity copyWith({
    String? courseId,
    String? planId,
    String? courseName,
    String? courseType,
    String? courseTime,
    int? status,
    String? teacherId,
    String? teacherName,
    String? teacherPhoto,
    String? meetingId,
    String? meetingNum,
    String? feedback,
    String? studentCode,
    String? studentName,
    String? oneToManyType,
    String? classPlanStudyId,
    int? leaveStatus,
    int? lessonsFlag,
  }) {
    return DeliverBeanEntity()
      ..courseId = courseId ?? this.courseId
      ..planId = planId ?? this.planId
      ..courseName = courseName ?? this.courseName
      ..courseType = courseType ?? this.courseType
      ..courseTime = courseTime ?? this.courseTime
      ..status = status ?? this.status
      ..teacherId = teacherId ?? this.teacherId
      ..teacherName = teacherName ?? this.teacherName
      ..teacherPhoto = teacherPhoto ?? this.teacherPhoto
      ..meetingId = meetingId ?? this.meetingId
      ..meetingNum = meetingNum ?? this.meetingNum
      ..feedback = feedback ?? this.feedback
      ..studentCode = studentCode ?? this.studentCode
      ..studentName = studentName ?? this.studentName
      ..oneToManyType = oneToManyType ?? this.oneToManyType
      ..classPlanStudyId = classPlanStudyId ?? this.classPlanStudyId
      ..leaveStatus = leaveStatus ?? this.leaveStatus
      ..lessonsFlag = lessonsFlag ?? this.lessonsFlag;
  }
}