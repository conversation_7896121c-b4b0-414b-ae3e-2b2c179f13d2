import 'package:event_bus/event_bus.dart';

class EventUtils{
  String message;
  int? curIndex;
  int? preIndex;
  EventUtils(this.message, {this.curIndex = 0, this.preIndex = 0});

}
class EventBusUtils {
  static final EventBus _eventBus = EventBus();
  static EventBus get eventBus => _eventBus;

  static sendMsg(String name){
    eventBus.fire(EventUtils(name));
  }

  static sendPageSwitchMsg(String name, int curIndex, int preIndex){
    eventBus.fire(EventUtils(name, curIndex: curIndex, preIndex: preIndex));
  }
}

