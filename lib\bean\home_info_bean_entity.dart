import 'package:json_annotation/json_annotation.dart';
import 'package:student_end_flutter/generated/json/home_bean_entity.g.dart';
import 'dart:convert';
export 'package:student_end_flutter/generated/json/home_bean_entity.g.dart';
import 'package:student_end_flutter/bean/zx_course_entity.dart';
@JsonSerializable()
class HomePageEntity {
  @JsonKey(name: 'code')
  late int code = 0;

  @JsonKey(name: 'success')
  late bool success = false;

  @JsonKey(name: 'message')
  late String message = '';

  @JsonKey(name: 'data')
  late HomePageData data = HomePageData();

  @Json<PERSON>ey(name: 'total')
  late int? total = null;

  HomePageEntity();

  factory HomePageEntity.fromJson(Map<String, dynamic> json) => $HomePageEntityFromJson(json);

  Map<String, dynamic> toJson() => $HomePageEntityToJson(this);
}

@JsonSerializable()
class HomePageData {
  @JsonKey(name: 'specialList')
  late List<SpecialItem> specialList = [];

  @JsonKey(name: 'bannerList')
  late List<BannerItem> bannerList = [];

  @JsonKey(name: 'porcelain')
  late BannerItem? porcelain = BannerItem(); // 新增字段

  @JsonKey(name: 'homePageGoodsList')
  late List<dynamic> homePageGoodsList = [];

  HomePageData();

  factory HomePageData.fromJson(Map<String, dynamic> json) => $HomePageDataFromJson(json);

  Map<String, dynamic> toJson() => $HomePageDataToJson(this);
}

// 新增首页商品模块类
@JsonSerializable()
class HomePageGoodsItem {
  @JsonKey(name: 'moduleName')
  late String moduleName = '';

  @JsonKey(name: 'horzVert')
  late int horzVert = 0;
  @JsonKey(name: 'allGoodsDisplayed')
  late bool allGoodsDisplayed = false;
  @JsonKey(name: 'id')
  late String id = '';
  // allGoodsDisplayed
  @JsonKey(name: 'goodsList')
  late List<ZxCourseDataData> goodsList = [];

  HomePageGoodsItem();

  factory HomePageGoodsItem.fromJson(Map<String, dynamic> json) => $HomePageGoodsItemFromJson(json);

  Map<String, dynamic> toJson() => $HomePageGoodsItemToJson(this);
}
@JsonSerializable()
class SpecialItem {
  @JsonKey(name: 'specialName')
  late String specialName = '';
  @JsonKey(name: 'specialId')
  late String specialId = '';
  @JsonKey(name: 'picUrl')
  late String picUrl = '';

  @JsonKey(name: 'productIntroPicUrl')
  late String productIntroPicUrl = '';

  @JsonKey(name: 'backgroundColor')
  late String backgroundColor = '';

  @JsonKey(name: 'weight')
  late dynamic weight = null;

  @JsonKey(name: 'goodsList')
  late List<ZxCourseDataData> goodsList = [];

  @JsonKey(name: 'categoryList')
  late List<dynamic> categoryList = [];

  SpecialItem();

  factory SpecialItem.fromJson(Map<String, dynamic> json) => $SpecialItemFromJson(json);

  Map<String, dynamic> toJson() => $SpecialItemToJson(this);
}

@JsonSerializable()
class BannerItem {
  @JsonKey(name: 'id')
  late String id = '';

  @JsonKey(name: 'bannerName')
  late String bannerName = '';

  @JsonKey(name: 'bannerPicUrl')
  late String bannerPicUrl = '';

  @JsonKey(name: 'bannerPosition')
  late String bannerPosition = '';

  @JsonKey(name: 'bannerType')
  late int bannerType = 0;

  @JsonKey(name: 'goodsId')
  late String goodsId = '';

  @JsonKey(name: 'activityId')
  late String activityId = '';

  @JsonKey(name: 'goodsName')
  late String goodsName = '';

  @JsonKey(name: 'bannerLinkUrl')
  late String bannerLinkUrl = '';

  @JsonKey(name: 'weight')
  late int weight = 0;

  @JsonKey(name: 'bannerStatus')
  late int bannerStatus = 0;

  @JsonKey(name: 'effectiveStartTime')
  late String effectiveStartTime = '';

  @JsonKey(name: 'effectiveEndTime')
  late String effectiveEndTime = '';

  @JsonKey(name: 'needLogin')
  late int needLogin = 0;

  @JsonKey(name: 'createdTime')
  late String createdTime = '';

  @JsonKey(name: 'updatedTime')
  late String updatedTime = '';

  @JsonKey(name: 'goodsSharePoster')
  late String goodsSharePoster = '';

  @JsonKey(name: 'goodsShareTextList')
  late List<dynamic> goodsShareTextList = [];

  BannerItem();

  // factory BannerItem.fromJson(Map<String, dynamic> json) => $BannerItemFromJson(json);
  factory BannerItem.fromJson(Map<String, dynamic> json) => $BannerItemFromJson(json);

  Map<String, dynamic> toJson() => $BannerItemToJson(this);
}
@JsonSerializable()
class GoodsShareText {
  @JsonKey(name: 'id')
  late String id = '';

  @JsonKey(name: 'goodsId')
  late String goodsId = '';

  @JsonKey(name: 'shareText')
  late String shareText = '';

  GoodsShareText();

  factory GoodsShareText.fromJson(Map<String, dynamic> json) => $GoodsShareTextFromJson(json);

  Map<String, dynamic> toJson() => $GoodsShareTextToJson(this);
}