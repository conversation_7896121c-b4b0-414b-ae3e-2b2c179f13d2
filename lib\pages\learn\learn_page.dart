import 'package:bot_toast/bot_toast.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:student_end_flutter/bean/curriculum_entity.dart';
import 'package:student_end_flutter/common/CommonUtil.dart';
import 'package:get/get.dart';
import 'package:student_end_flutter/pages/learn/controller/learn_controller.dart';
import 'package:student_end_flutter/utils/utils.dart';
import '../../bean/learn_tool_entity.dart';

class LearnPage extends GetView<LearnController> {
  final List<Map> _coursesTypeList = [
    {'name': '伴学课', 'courseType': '4'},
    {'name': '录播课', 'courseType': '3'},
  ];

  final List<GlobalKey> _keys = List.generate(2, (index) => GlobalKey());

  @override
  Widget build(BuildContext context) {
    Get.put(LearnController(), permanent: true);
    final topPadding = MediaQuery.of(context).padding.top;
    return Scaffold(
      backgroundColor: HexColor('F3F8FC'),
      body: SingleChildScrollView(
        child: Container(
          padding: EdgeInsets.only(
              top: topPadding, left: 10.w, right: 10.w, bottom: 10.h),
          child: Column(
            // mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                height: 10.w,
              ),
              parentInfoCard(context),
              SizedBox(
                height: 10.w,
              ),
              Obx(() => courseSelectWidget()),
              SizedBox(
                height: 10.w,
              ),
              Obx(() => recentlyToolMenu()),
              Obx(() => generalToolsMenu()),
              Obx(() => Column(
                      children: controller.curriculumList
                          .asMap()
                          .entries
                          .map((value) {
                    int index = value.key;
                    CurriculumEntity curriculumEntity = value.value;
                    return curriculumToolsMenu(curriculumEntity, index);
                  }).toList())),
            ],
          ),
        ),
      ),
    );
  }

  /// 家长信息
  Widget parentInfoCard(BuildContext context) {
    return Stack(
      children: [
        Obx(() => Offstage(
              offstage: controller.studentsList.isNotEmpty,
              child: Image.asset('assets/learn/img_top_bg_icon.png',
                  width: double.infinity, fit: BoxFit.fill),
            )),
        Obx(() => Offstage(
              offstage: controller.studentsList.isEmpty,
              child: Image.asset('assets/learn/img_top_bg.png',
                  width: double.infinity, fit: BoxFit.fill),
            )),
        Positioned(
            top: 17.h,
            right: 0,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 3.h),
              decoration: BoxDecoration(
                image: DecorationImage(
                    // img_top_bg-icon
                    image: AssetImage("assets/learn/icon_role_bg.png")),
              ),
              alignment: Alignment.center,
              child: Text(
                '家长',
                style: TextStyle(
                    fontSize: 14.sp,
                    fontFamily: "R",
                    color: HexColor("2E896F")),
              ),
            )),
        Positioned(
            top: 40.h,
            right: 8.w,
            child: InkWell(
              onTap: () {
                controller.showSelectStudentDialog(true);
              },
              child: Image.asset(
                'assets/zx/icon_zx_qiehuan.png',
                width: 20.w,
                height: 20.h,
              ),
            )),
        Obx(
          () => Padding(
            padding: EdgeInsets.all(10.w),
            child: Column(
              children: [
                Row(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.all(Radius.circular(50)),
                      child: CachedNetworkImage(
                        fit: BoxFit.fill,
                        width: 50.h,
                        height: 50.h,
                        placeholder: (context, url) => Image.asset(
                          'assets/mine/home_avaUrl.png',
                          fit: BoxFit.cover,
                        ),
                        errorWidget: (context, url, error) => Image.asset(
                          'assets/mine/home_avaUrl.png',
                          fit: BoxFit.cover,
                        ),
                        imageUrl:
                            controller.userInfoBeanEntity.value.headPortrait,
                      ),
                    ),
                    SizedBox(
                      width: 10.w,
                    ),
                    Text(
                      controller.userInfoBeanEntity.value.nickName,
                      style: TextStyle(
                          fontSize: 16.sp,
                          fontFamily: "M",
                          color: Colors.white),
                    ),
                  ],
                ),
                Divider(
                  color: Color.fromRGBO(255, 255, 255, 0.2),
                  thickness: 1.0, // 分割线的厚度
                  indent: 0, // 左侧间距
                  endIndent: 0, // 右侧间距
                ),
                SizedBox(height: 3.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      controller.studentsList.isNotEmpty
                          ? '学员姓名：${controller.studentName}'
                          : "暂无学员",
                      style: TextStyle(
                          color: Colors.white,
                          fontSize: 14.sp,
                          fontFamily: "R",
                          overflow: TextOverflow.ellipsis),
                    ),
                    Expanded(
                      child: Text(
                        controller.studentsList.isNotEmpty
                            ? '门店编号：${controller.memberCode}'
                            : "",
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 14.sp,
                          fontFamily: "R",
                          overflow: TextOverflow.ellipsis,
                        ),
                        softWrap: true, // 启用自动换行
                        maxLines: 1,
                      ),
                    ),
                  ],
                ),
                Offstage(
                  offstage: controller.studentsList.isEmpty,
                  child: Container(
                    margin: EdgeInsets.only(top: 12.h),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(10), // 圆角半径
                      child: LinearProgressIndicator(
                        value: (controller.percentage ?? 0) > 0
                            ? (controller.percentage ?? 0) / 100
                            : 0, // 当前进度值（0.0~1.0）
                        backgroundColor: Colors.white, // 背景色
                        valueColor: AlwaysStoppedAnimation<Color>(
                            Color.fromARGB(255, 53, 117, 94)), // 进度条颜色
                        minHeight: 15, // 进度条高度
                      ),
                    ),
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(top: 12.h),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        controller.studentsList.isNotEmpty
                            ? '已用学时：${controller.useHours.value}'
                            : "",
                        style: TextStyle(
                            color: Colors.white,
                            fontSize: 14.sp,
                            fontFamily: "R",
                            overflow: TextOverflow.ellipsis),
                      ),
                      Text(
                        controller.studentsList.isNotEmpty
                            ? '剩余学时：${controller.haveHours.value}'
                            : '',
                        style: TextStyle(
                            color: Colors.white,
                            fontSize: 14.sp,
                            fontFamily: "R",
                            overflow: TextOverflow.ellipsis),
                      )
                    ],
                  ),
                )
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget courseSelectWidget() {
    return SizedBox(
      height: 190.h,
      child: _tabUI(),
    );
  }

  _tabUI() {
    return Stack(
      children: [
        Image(
            // fit: BoxFit.fitWidth,
            image: AssetImage("assets/learn/icon_green_bg.png")),
        Positioned(
          left: 15.w,
          top: 10.w,
          right: 0,
          child: SizedBox(
            width: double.infinity,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Image(
                    width: 100.w,
                    height: 24.w,
                    fit: BoxFit.fitWidth,
                    image: AssetImage("assets/learn/icon_my_course.png")),
                InkWell(
                  onTap: () {
                    controller.goToAndroidIosList();
                  },
                  child: Row(children: [
                    Text(
                      '更多',
                      style: TextStyle(
                        color: HexColor("555555"),
                        fontSize: 14.sp,
                        fontFamily: "R",
                      ),
                    ),
                    Icon(
                      Icons.chevron_right,
                      size: 20.w,
                      color: HexColor("555555"),
                    ),
                  ]),
                ),
              ],
            ),
          ),
        ),
        Positioned(
          top: 40.w,
          left: 10.w,
          child: Image(
              width: 200.w,
              // height: 28.h,
              // fit: BoxFit.contain,
              image: AssetImage("assets/learn/icon_yellow_bg.png")),
        ),
        Positioned(
          top: 48.w,
          left: 0,
          right: 0,
          child: Container(
            height: 340.w,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(10.w)),
              color: Colors.white,
            ),
            child: coursePage11(),
          ),
        ),
      ],
    );
  }

  /// 创建暂无上课数据的UI组件
  Widget _buildNoCourseDataWidget({double? height, bool fillParent = false}) {
    Widget content = Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Image(
            width: 50.w,
            height: 50.h,
            image: AssetImage("assets/icon_null.png")),
        SizedBox(
          height: 5.h,
        ),
        Text(
          "暂无上课数据",
          style: TextStyle(
              fontSize: 14.sp, color: HexColor("666666"), fontFamily: "R"),
        )
      ],
    );

    return Container(
      width: double.infinity,
      height: fillParent ? null : (height ?? 400.h),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(15))),
      child: fillParent ? Center(child: content) : content,
    );
  }

  Widget coursePage11() {
    // 检查是否有任何课程数据
    bool hasDeliverCourse =
        controller.deliverBeanEntity.value.courseId.isNotEmpty;
    bool hasReviewCourse =
        controller.reviewCourseListBeanData.value.courseId.isNotEmpty;
    bool hasRecordCourse =
        controller.courseRecordListBeanData.value.courseId.isNotEmpty;

    // 如果没有任何课程数据，显示暂无数据
    if (!hasDeliverCourse && !hasReviewCourse && !hasRecordCourse) {
      return _buildNoCourseDataWidget(useExpanded: true);
    }

    String courseTimeTitle = '';
    if (controller.deliverBeanEntity.value.courseTime.isNotEmpty) {
      courseTimeTitle = '上课时间';
    } else if (controller
        .reviewCourseListBeanData.value.courseTimeStr.isNotEmpty) {
      courseTimeTitle = '复习时间';
    } else if (controller
        .courseRecordListBeanData.value.lastStudyTime.isNotEmpty) {
      courseTimeTitle = '上次学习';
    }

    String courseTime = '';
    if (controller.deliverBeanEntity.value.courseTime.isNotEmpty == true) {
      courseTime = controller.deliverBeanEntity.value.courseTime;
    } else if (controller
        .reviewCourseListBeanData.value.courseTimeStr.isNotEmpty) {
      courseTime = controller.reviewCourseListBeanData.value.courseTimeStr;
    } else if (controller
        .courseRecordListBeanData.value.lastStudyTime.isNotEmpty) {
      courseTime = controller.courseRecordListBeanData.value.lastStudyTime;
    }

    String teacher = '';
    if (controller.deliverBeanEntity.value.teacherName.isNotEmpty) {
      teacher = controller.deliverBeanEntity.value.teacherName;
    } else if (controller
        .reviewCourseListBeanData.value.userNickName.isNotEmpty) {
      teacher = controller.reviewCourseListBeanData.value.userNickName;
    }

    String meetingId = '';
    if (controller.deliverBeanEntity.value.meetingId.isNotEmpty) {
      meetingId = controller.deliverBeanEntity.value.meetingId;
    } else if (controller.reviewCourseListBeanData.value.meetingId != 0) {
      meetingId =
          controller.reviewCourseListBeanData.value.meetingId.toString();
    }

    String courseName = '';
    if (controller.deliverBeanEntity.value.courseName.isNotEmpty) {
      courseName = controller.deliverBeanEntity.value.courseName;
    } else if (controller
        .reviewCourseListBeanData.value.courseTimeStr.isNotEmpty) {
      courseName = controller.reviewCourseListBeanData.value.courseTimeStr;
    } else if (controller
        .courseRecordListBeanData.value.courseName.isNotEmpty) {
      courseName = controller.courseRecordListBeanData.value.courseName;
    }

    return Container(
      // padding: EdgeInsets.all(15.h),
      width: double.infinity,
      height: 400.h,
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(15))),
      child: Stack(children: [
        if (controller.deliverBeanEntity.value.status == 0)
          Image(
              width: 80.w,
              height: 40.w,
              // fit: BoxFit.fitWidth,
              image: AssetImage("assets/learn/icon_not_attending_class.png")),
        Positioned(
          top: 0,
          right: 0,
          child: Image(
              width: 80.w,
              height: 40.w,
              // fit: BoxFit.fitWidth,
              image: AssetImage(
                  controller.deliverBeanEntity.value.meetingId.isNotEmpty
                      ? "assets/learn/icon_deliver_course.png"
                      : controller.courseRecordListBeanData.value.courseId
                              .isNotEmpty
                          ? "assets/learn/icon_record_course.png"
                          : "assets/learn/icon_review_course.png")),
        ),
        Positioned(
          left: 30.w,
          top: 30.w,
          right: 15.w,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                courseName,
                style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: HexColor("555555"),
                    fontFamily: "M"),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(
                height: 3.h,
              ),
              Text(
                '$courseTimeTitle：$courseTime',
                style: TextStyle(
                    fontSize: 14.sp,
                    fontFamily: "R",
                    color: HexColor("888888")),
              ),
              SizedBox(
                height: 3.h,
              ),
              if (teacher.isNotEmpty)
                Text(
                  '教练：$teacher',
                  style: TextStyle(
                      fontSize: 14.sp,
                      fontFamily: "R",
                      color: HexColor("888888")),
                ),
              SizedBox(
                height: 3.h,
              ),
              if (meetingId.isNotEmpty)
                Text(
                  '会议ID：$meetingId',
                  style: TextStyle(
                      fontSize: 14.sp,
                      fontFamily: "R",
                      color: HexColor("888888")),
                ),
              SizedBox(
                height: 10.h,
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 15.w),
                color: HexColor("F2F2F2"),
                height: 1.h,
              ),
              SizedBox(
                height: 10.h,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  if (controller.deliverBeanEntity.value.leaveStatus == 0 &&
                      controller.deliverBeanEntity.value.courseId.isNotEmpty)
                    InkWell(
                      onTap: () {},
                      child: Container(
                        alignment: Alignment.center,
                        height: 28.w,
                        padding: EdgeInsets.symmetric(horizontal: 25.w),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(14.w),
                          border: Border.all(
                            color: HexColor('CCCCCC'),
                            width: 1.0,
                          ),
                        ),
                        child: Text(
                          "请假",
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: HexColor('CCCCCC'),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                  SizedBox(width: 5.w),
                  Visibility(
                    visible:
                        controller.deliverBeanEntity.value.leaveStatus == 0,
                    child: InkWell(
                      onTap: () {
                        // 正常巡课 处理家长巡课按钮点击事件
                        controller.identutyID = '1';
                        controller.getCourseMeetingInfo();
                      },
                      child: Container(
                        alignment: Alignment.center,
                        height: 28.w,
                        padding: EdgeInsets.symmetric(horizontal: 15.w),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(14.w),
                          border: Border.all(
                            color: HexColor('009B55'),
                            width: 1.0,
                          ),
                        ),
                        child: Text(
                          "家长巡课",
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: HexColor('009B55'),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: 5.w),
                  Visibility(
                    visible:
                        controller.deliverBeanEntity.value.leaveStatus == 0,
                    child: InkWell(
                      onTap: () {
                        controller.identutyID = '6';
                        controller.getCourseMeetingInfo();
                      },
                      child: Container(
                        alignment: Alignment.center,
                        height: 28.w,
                        padding: EdgeInsets.symmetric(horizontal: 15.w),
                        decoration: BoxDecoration(
                          color: HexColor('009B55'),
                          borderRadius: BorderRadius.circular(14.w),
                          border: Border.all(
                            color: HexColor('009B55'),
                            width: 1.0,
                          ),
                        ),
                        child: Text(
                          "立即上课",
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ]),
    );
  }

  ///我的课程卡片
  Widget coursePage() {
    return controller.studentsList.isNotEmpty &&
            controller.deliverBeanEntity.value.studentCode.isNotEmpty
        ? Container(
            padding: EdgeInsets.all(15.h),
            width: double.infinity,
            decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.all(Radius.circular(15))),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        controller.deliverBeanEntity.value.courseName ?? '',
                        style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.bold,
                            fontFamily: "M"),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    SizedBox(width: 5.w),
                    Visibility(
                      // 请假课程不展示巡课按钮
                      visible:
                          controller.deliverBeanEntity.value.lessonsFlag == 1,
                      child: Container(
                        alignment: Alignment.center,
                        width: 20.w,
                        height: 20.w,
                        decoration: BoxDecoration(
                            color: HexColor('DBE436'),
                            borderRadius: BorderRadius.circular(4.w)),
                        child: Text(
                          '补',
                          style:
                              TextStyle(color: Colors.white, fontSize: 14.sp),
                        ),
                      ),
                    ),
                    Visibility(
                      visible:
                          controller.deliverBeanEntity.value.leaveStatus != 0,
                      child: Container(
                          padding: EdgeInsets.symmetric(
                              horizontal: 6.w, vertical: 2.h),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(4.w),
                          ),
                          child: Text(
                            "已请假",
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: HexColor('E57534'),
                              fontWeight: FontWeight.w500,
                            ),
                          )),
                    ),
                    SizedBox(width: 5.w),
                    Visibility(
                      visible:
                          controller.deliverBeanEntity.value.leaveStatus == 0,
                      child: InkWell(
                        onTap: () {
                          // 正常巡课 处理家长巡课按钮点击事件
                          controller.identutyID = '1';
                          controller.getCourseMeetingInfo();
                        },
                        child: Container(
                          padding: EdgeInsets.symmetric(
                              horizontal: 6.w, vertical: 2.h),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(4.w),
                          ),
                          child: Text(
                            "家长巡课 > ",
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: HexColor('2C9E7C'),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(
                  height: 10.h,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                        child: Text(
                      '学员姓名：${controller.deliverBeanEntity.value.studentName ?? ''}',
                      style: TextStyle(
                          fontSize: 14.sp,
                          fontFamily: "R",
                          color: HexColor("666666"),
                          overflow: TextOverflow.ellipsis),
                    )),
                    Text(
                      '课程类型：${controller.deliverBeanEntity.value?.courseType ?? ''}',
                      style: TextStyle(
                          fontSize: 14.sp,
                          fontFamily: "R",
                          color: HexColor("666666")),
                    ),
                  ],
                ),
                SizedBox(
                  height: 10.h,
                ),
                Text(
                  '上课时间：${controller.deliverBeanEntity.value.courseTime ?? ''}',
                  style: TextStyle(
                      fontSize: 14.sp,
                      fontFamily: "R",
                      color: HexColor("666666")),
                ),
                SizedBox(
                  height: 3.h,
                ),
                Divider(
                  color: Color.fromRGBO(181, 181, 181, 0.2),
                  thickness: 1.0, // 分割线的厚度
                  indent: 0, // 左侧间距
                  endIndent: 0, // 右侧间距
                ),
                Row(
                  children: [
                    Image(
                        width: 28.h,
                        height: 28.h,
                        image: AssetImage("assets/mine/home_avaUrl.png")),
                    SizedBox(
                      width: 10.w,
                    ),
                    Text(
                      Utils.handleLongStr(
                          '教练：${controller.deliverBeanEntity.value.teacherName ?? ''}',
                          10),
                      style: TextStyle(
                          fontSize: 14.sp,
                          fontFamily: "R",
                          color: HexColor("666666")),
                    ),
                    Spacer(),
                    Visibility(
                      visible:
                          controller.deliverBeanEntity.value.leaveStatus == 0,
                      child: InkWell(
                        onTap: () {
                          controller.identutyID = '6';
                          controller.getCourseMeetingInfo();
                        },
                        child: Container(
                          padding: EdgeInsets.only(
                              top: 2.h, bottom: 2.h, left: 10.w, right: 10.w),
                          decoration: BoxDecoration(
                            border: Border.all(
                                width: 1.0, color: HexColor("2E896F")),
                            borderRadius: BorderRadius.all(Radius.circular(15)),
                          ),
                          child: Text(
                            '立即上课',
                            style: TextStyle(
                                fontSize: 14.sp,
                                fontFamily: "R",
                                color: HexColor("2E896F")),
                          ),
                        ),
                      ),
                    ),
                  ],
                )
              ],
            ),
          )
        : _buildNoCourseDataWidget();
  }

  Widget recordPage() {
    return controller.recordBeanEntity.value!.studentCode.isNotEmpty
        ? Container(
            padding: EdgeInsets.all(15.h),
            width: double.infinity,
            decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.all(Radius.circular(15))),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        "${controller.recordBeanEntity.value?.courseName}" ??
                            '',
                        style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.bold,
                            fontFamily: "M",
                            overflow: TextOverflow.ellipsis),
                      ),
                    ),
                    Expanded(
                        child: Text(
                      '学员姓名：${controller.recordBeanEntity.value?.studentName ?? ''}',
                      style: TextStyle(
                          fontSize: 14.sp,
                          fontFamily: "R",
                          color: HexColor("666666"),
                          overflow: TextOverflow.ellipsis),
                    )),
                  ],
                ),
                SizedBox(
                  height: 10.h,
                ),
                Text(
                  '上课学习：${controller.recordBeanEntity.value?.lastStudyTime ?? ''}',
                  style: TextStyle(
                      fontSize: 14.sp,
                      fontFamily: "R",
                      color: HexColor("666666")),
                ),
                SizedBox(
                  height: 40,
                ),
                Divider(
                  color: Color.fromRGBO(181, 181, 181, 0.2),
                  thickness: 1.0, // 分割线的厚度
                  indent: 0, // 左侧间距
                  endIndent: 0, // 右侧间距
                ),
                SizedBox(
                  height: 5.h,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    InkWell(
                      onTap: () {
                        controller.goToAndroidIosVideoLessons();
                      },
                      child: Container(
                        margin: EdgeInsets.only(left: 5.w, right: 5.w),
                        padding: EdgeInsets.only(
                            top: 2.h, bottom: 2.h, left: 10.w, right: 10.w),
                        decoration: BoxDecoration(
                          border:
                              Border.all(width: 1.0, color: HexColor("2E896F")),
                          borderRadius: BorderRadius.all(Radius.circular(15)),
                        ),
                        child: Text(
                          controller.recordBeanEntity.value!.lastStudyTime
                                  .isNotEmpty
                              ? '继续学习'
                              : '进入学习',
                          style: TextStyle(
                              fontSize: 14.sp,
                              fontFamily: "R",
                              color: HexColor("2E896F")),
                        ),
                      ),
                    ),
                  ],
                )
              ],
            ),
          )
        : _buildNoCourseDataWidget();
  }

  // learnServiceMenu() {
  //   return Container(
  //     color: Colors.white,
  //     child: GridView.builder(
  //       padding: EdgeInsets.zero,
  //       shrinkWrap: true,
  //       physics: NeverScrollableScrollPhysics(),
  //       gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
  //           crossAxisCount: 4,
  //           mainAxisSpacing: 10.w,
  //           crossAxisSpacing: 0,
  //           childAspectRatio: 1),
  //       itemCount: controller.learnClickList.length,
  //       itemBuilder: (BuildContext context, int index) {
  //         return _titleBtnFromGrid(index);
  //       },
  //     ),
  //   );
  // }

  // _titleBtnFromGrid(int index) {
  //   Map map = controller.learnClickList[index];
  //   return InkWell(
  //     onTap: () {
  //       controller.learnClick(map);
  //     },
  //     child: Column(
  //       mainAxisAlignment: MainAxisAlignment.center,
  //       children: [
  //         Image.asset(
  //           width: 26.w,
  //           fit: BoxFit.fitWidth,
  //           'assets/learn/${map['icon']}',
  //         ),
  //         SizedBox(
  //           height: 8.h,
  //         ),
  //         Text(
  //           map['title'],
  //           style: TextStyle(
  //               fontSize: 12.sp, color: HexColor("666666"), fontFamily: "R"),
  //         ),
  //       ],
  //     ),
  //   );
  // }

  Widget recentlyToolMenu() {
    return controller.recentlyToolList.isNotEmpty == true
        ? Column(
            children: [
              Container(
                height: 20.w,
                color: HexColor('F3F8FC'),
              ),
              Container(
                padding: EdgeInsets.all(15.w),
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.all(Radius.circular(15)),
                    color: Colors.white),
                child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(left: 10.w, bottom: 10.w),
                        child: Text(
                          '最近工具',
                          style: TextStyle(
                              fontSize: 15.sp,
                              fontFamily: "M",
                              color: HexColor("333333")),
                        ),
                      ),
                      GridView.builder(
                        padding: EdgeInsets.zero,
                        shrinkWrap: true,
                        physics: NeverScrollableScrollPhysics(),
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 4,
                            mainAxisSpacing: 10.w,
                            crossAxisSpacing: 0,
                            childAspectRatio: 1.2),
                        itemCount: controller.recentlyToolList.length,
                        itemBuilder: (BuildContext context, int index) {
                          return _recentlyToolItem(index);
                        },
                      ),
                    ]),
              )
            ],
          )
        : Container();
  }

  Widget generalToolsMenu() {
    return controller.generalToolsList.isNotEmpty == true
        ? Column(
            children: [
              Container(
                height: 20.w,
                color: HexColor('F3F8FC'),
              ),
              Container(
                padding: EdgeInsets.all(10.w),
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.all(Radius.circular(15)),
                    color: Colors.white),
                child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(left: 10.w, bottom: 10.w),
                        child: Text(
                          '学习服务',
                          style: TextStyle(
                              fontSize: 15.sp,
                              fontFamily: "M",
                              color: HexColor("333333")),
                        ),
                      ),
                      GridView.builder(
                        padding: EdgeInsets.zero,
                        shrinkWrap: true,
                        physics: NeverScrollableScrollPhysics(),
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 4,
                            mainAxisSpacing: 10.w,
                            crossAxisSpacing: 0,
                            childAspectRatio: 1),
                        itemCount: controller.generalToolsList.length,
                        itemBuilder: (BuildContext context, int index) {
                          return _generalToolsItem(index);
                        },
                      ),
                    ]),
              )
            ],
          )
        : Container();
  }

  _generalToolsItem(int index) {
    LearnToolEntity learnToolEntity = controller.generalToolsList[index];
    return InkWell(
      onTap: () {
        controller.postToolClick(learnToolEntity.id);
        controller.learnClick(learnToolEntity);
      },
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          CachedNetworkImage(
            width: 25.w,
            height: 25.w,
            fit: BoxFit.fill,
            imageUrl: learnToolEntity.iconAddress,
          ),
          SizedBox(
            height: 8.h,
          ),
          Text(
            textAlign: TextAlign.center,
            learnToolEntity.toolName ?? '',
            style: TextStyle(
                fontSize: 10.sp, color: HexColor("666666"), fontFamily: "R"),
          ),
        ],
      ),
    );
  }

  _recentlyToolItem(int index) {
    CurriculumCurriculumTool curriculumCurriculumTool =
        controller.recentlyToolList[index];
    return InkWell(
      onTap: () {
        controller.postToolClick(curriculumCurriculumTool.id);
        controller.learnClick(curriculumCurriculumTool);
      },
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          CachedNetworkImage(
            width: 25.w,
            height: 25.w,
            fit: BoxFit.fill,
            imageUrl: curriculumCurriculumTool.iconAddress,
          ),
          SizedBox(
            height: 8.h,
          ),
          Text(
            textAlign: TextAlign.center,
            curriculumCurriculumTool.toolName ?? '',
            style: TextStyle(
                fontSize: 10.sp, color: HexColor("666666"), fontFamily: "R"),
          ),
        ],
      ),
    );
  }

  Widget curriculumToolsMenu(CurriculumEntity curriculumEntity, int index) {
    return curriculumEntity.curriculumTool.isNotEmpty == true
        ? Column(
            children: [
              Container(
                height: 20.w,
                color: HexColor('F3F8FC'),
              ),
              Container(
                padding: EdgeInsets.all(10.w),
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.all(Radius.circular(15)),
                    color: Colors.white),
                child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(left: 10.w, bottom: 10.w),
                        child: Row(
                          children: [
                            Text(
                              curriculumEntity.curriculumName,
                              style: TextStyle(
                                  fontSize: 15.sp,
                                  fontFamily: "M",
                                  color: HexColor("333333")),
                            ),
                            SizedBox(
                              width: 5.w,
                            ),
                            Visibility(
                                visible: !curriculumEntity.isPurchase,
                                child: Image.asset(
                                  'assets/learn/icon_lock.png',
                                  width: 15.w,
                                  height: 15.w,
                                )),
                          ],
                        ),
                      ),
                      GridView.builder(
                        padding: EdgeInsets.zero,
                        shrinkWrap: true,
                        physics: NeverScrollableScrollPhysics(),
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 4,
                            mainAxisSpacing: 10.w,
                            crossAxisSpacing: 0,
                            childAspectRatio: 1),
                        itemCount: curriculumEntity.curriculumTool.length,
                        itemBuilder: (BuildContext context, int index) {
                          return _curriculumToolItem(index, curriculumEntity);
                        },
                      ),
                    ]),
              )
            ],
          )
        : Container();
  }

  _curriculumToolItem(int index, CurriculumEntity curriculumEntity) {
    CurriculumCurriculumTool curriculumCurriculumTool =
        curriculumEntity.curriculumTool[index];
    return InkWell(
        onTap: () {
          if (curriculumEntity.isPurchase == true) {
            controller.postToolClick(curriculumCurriculumTool.id);
            controller.learnClick(curriculumCurriculumTool);
          } else {
            BotToast.showText(
                text: '此功能需购买【${curriculumEntity.curriculumName}】课程后体验');
          }
        },
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            CachedNetworkImage(
              width: 25.w,
              height: 25.w,
              fit: BoxFit.fill,
              imageUrl: curriculumEntity.isPurchase
                  ? curriculumCurriculumTool.iconAddress
                  : curriculumCurriculumTool.ashIconAddress,
            ),
            SizedBox(
              height: 8.h,
            ),
            Text(
              textAlign: TextAlign.center,
              curriculumCurriculumTool.toolName ?? '',
              style: TextStyle(
                  fontSize: 10.sp, color: HexColor("666666"), fontFamily: "R"),
            ),
          ],
        ));
  }
}
