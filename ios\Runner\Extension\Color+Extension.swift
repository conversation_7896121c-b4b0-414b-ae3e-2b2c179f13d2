//
//  Color+Extension.swift
//  LaiZhuanPro
//
//  Created by HyBoard on 2018/10/31.
//  Copyright © 2018 Jason. All rights reserved.
//

import UIKit

extension UIColor {

    /// 根据HEX返回UIColor，alpha = 1.0
    class  func hexColor(_ hexStr: String) -> UIColor {
        return self.hexColor(hexStr, withAlpha: CGFloat.init(1.0))
    }

    /// 根据HEX，alpha 返回UIColor
    class func hexColor(_ hexStr: String, withAlpha alpha: CGFloat) -> UIColor {
        // trim 空格
        let cStr = hexStr.trimmingCharacters(in: CharacterSet.whitespacesAndNewlines)
        guard cStr.hasPrefix("#") || cStr.count == 7 else {

            return UIColor.clear
        }

        let rstartIdx = cStr.index(cStr.startIndex, offsetBy: 1)
        let rendIdx = cStr.index(rstartIdx, offsetBy: 2)
        let rr: Range = rstartIdx..<rendIdx
        let rs = String(cStr[rr])

//        let gstartIdx = cStr.index(rendIdx, offsetBy: 1)
        let gendIdx = cStr.index(rendIdx, offsetBy: 2)
        let gr: Range = rendIdx..<gendIdx
        let gs = String(cStr[gr])

//        let bstartIdx = cStr.index(gendIdx, offsetBy: 1)
        let bendIdx  = cStr.index(gendIdx, offsetBy: 2)
        let br: Range = gendIdx..<bendIdx
        let bs = String(cStr[br])

        var red: CUnsignedInt = 0, ged: CUnsignedInt = 0, bed: CUnsignedInt = 0
        Scanner(string: rs).scanHexInt32(&red)
        Scanner(string: gs).scanHexInt32(&ged)
        Scanner(string: bs).scanHexInt32(&bed)

        return UIColor(red: CGFloat(red) / 255.0, green: CGFloat(ged) / 255.0, blue: CGFloat(bed) / 255.0, alpha: alpha )

    }

    /// 根据 rgb值返回对应的 UIColor
    class func RGBColor(_ rgb: CGFloat, _ grb: CGFloat, _ bgr: CGFloat, _ alpha: CGFloat = 1.0) -> UIColor {
        return UIColor.init(red: rgb/255.0, green: grb/255.0, blue: bgr/255.0, alpha: alpha)
    }

    /// 返回同一 rgb 值的灰色。
    class func RGBGrayColor(_ gray: CGFloat) -> UIColor {
        RGBColor(gray, gray, gray)
    }
}
//swift中是用extension来进行拓展

extension UIColor {

  convenience init(hexString: String) {
    let hex = hexString.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
    var int = UInt64()
    Scanner(string: hex).scanHexInt64(&int)
    let a, r, g, b: UInt64
    switch hex.count {
    case 3:
      (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
    case 6:
      (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
    case 8:
      (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
    default:
      (a, r, g, b) = (1, 1, 1, 0)
    }
    self.init(red: CGFloat(r) / 255, green: CGFloat(g) / 255, blue: CGFloat(b) / 255, alpha: CGFloat(a) / 255)
  }

}

extension UIColor {
    // 0
    class var hexFFFFFF: UIColor { return UIColor.hexColor("#FFFFFF") }
    class var hex333333: UIColor { return UIColor.hexColor("#333333") }
    class var hex555555: UIColor { return UIColor.hexColor("#555555") }
    class var hex999999: UIColor { return UIColor.hexColor("#999999") }
    class var hex737373: UIColor { return UIColor.hexColor("#737373") }
    class var hexbababa: UIColor { return UIColor.hexColor("#bababa") }
    class var hex04DB9C: UIColor { return UIColor.hexColor("#04DB9C") }
    class var hex05D87E: UIColor { return UIColor.hexColor("#05D87E") }
    class var hexF8F8F8: UIColor { return UIColor.hexColor("#F8F8F8") }
    class var hexF7F9FB: UIColor { return UIColor.hexColor("#F7F9FB") }
    class var hexD8D8D8: UIColor { return UIColor.hexColor("#D8D8D8") }
    class var hex000000: UIColor { return UIColor.hexColor("#000000") }
    class var hexA09E9E: UIColor { return UIColor.hexColor("#A09E9E") }
    
    
}
extension UIColor {
    // convenience 扩展遍历构造函数
    convenience init(red: CGFloat, green: CGFloat, blue: CGFloat) {
        self.init(red: red / 255.0, green: green / 255.0, blue: blue / 255.0, alpha: 1.0)
    }
    static func imageFromColor(color: UIColor, viewSize: CGSize) -> UIImage{
         let rect: CGRect = CGRect(x: 0, y: 0, width: viewSize.width, height: viewSize.height)
         
         UIGraphicsBeginImageContext(rect.size)
         
         let context: CGContext = UIGraphicsGetCurrentContext()!
         context.setFillColor(color.cgColor)
         context.fill(rect)
         let image = UIGraphicsGetImageFromCurrentImageContext()
         UIGraphicsGetCurrentContext()
         return image!
     }
    
    

}

// MARK: -  颜色处理类
 extension UIColor {

    // MARK: - extension 适配深色模式 浅色模式 非layer
    ///lightHex  浅色模式的颜色（十六进制）
    ///darkHex   深色模式的颜色（十六进制）
    ///return    返回一个颜色（UIColor）
    static func color(lightHex: String,
                      darkHex: String,
                      alpha: CGFloat = 1.0)
        -> UIColor {
        let light = UIColor(hex: lightHex, alpha) ?? UIColor.black
        let dark =  UIColor(hex: darkHex, alpha) ?? UIColor.white
            
        return color(lightColor: light, darkColor: dark)
    }

    // MARK: - extension 适配深色模式 浅色模式 非layer
    ///lightColor  浅色模式的颜色（UIColor）
    ///darkColor   深色模式的颜色（UIColor）
    ///return    返回一个颜色（UIColor）
   static func color(lightColor: UIColor,
                     darkColor: UIColor)
       -> UIColor {
       if #available(iOS 13.0, *) {
          return UIColor { (traitCollection) -> UIColor in
               if traitCollection.userInterfaceStyle == .dark {
                   return darkColor
               }else {
                   return lightColor
               }
           }
       } else {
          return lightColor
       }
   }

   
   // MARK: - 构造函数（十六进制）
    ///hex  颜色（十六进制）
    ///alpha   透明度
   convenience init?(hex : String,
                     _ alpha : CGFloat = 1.0) {
       var cHex = hex.trimmingCharacters(in: CharacterSet.whitespaces).uppercased()
       guard cHex.count >= 6 else {
           return nil
       }
       if cHex.hasPrefix("0X") {
           cHex = String(cHex[cHex.index(cHex.startIndex, offsetBy: 2)..<cHex.endIndex])
       }
       if cHex.hasPrefix("#") {
           cHex = String(cHex[cHex.index(cHex.startIndex, offsetBy: 1)..<cHex.endIndex])
       }

       var r : UInt64 = 0
       var g : UInt64  = 0
       var b : UInt64  = 0

       let rHex = cHex[cHex.startIndex..<cHex.index(cHex.startIndex, offsetBy: 2)]
       let gHex = cHex[cHex.index(cHex.startIndex, offsetBy: 2)..<cHex.index(cHex.startIndex, offsetBy: 4)]
       let bHex = cHex[cHex.index(cHex.startIndex, offsetBy: 4)..<cHex.index(cHex.startIndex, offsetBy: 6)]

       Scanner(string: String(rHex)).scanHexInt64(&r)
       Scanner(string: String(gHex)).scanHexInt64(&g)
       Scanner(string: String(bHex)).scanHexInt64(&b)

       self.init(red:CGFloat(r) / 255.0, green: CGFloat(g) / 255.0, blue: CGFloat(b) / 255.0, alpha: alpha)
   }
}
