import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:bot_toast/bot_toast.dart';
import 'package:dio/dio.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:student_end_flutter/utils/sharedPreferences_util.dart';

class AndroidIosPlugin {

  static final Dio _dio = Dio();

  ///通道名   openAndroidIos
  static final MethodChannel _openAppMethodChannel = const MethodChannel("openAndroidIos")
    ..setMethodCallHandler(_callHandler);
  static final MethodChannel _openAppPayMethodChannel = const MethodChannel("openAndroidIosPay")
    ..setMethodCallHandler(_callHandler);
  static final MethodChannel _meetMethodChannel = const MethodChannel("openAndroidIosMeet")
    ..setMethodCallHandler(_callHandler);
  static setWgtVersion ( String wgtVersion) {
    SharedPreferencesUtil.saveData(SharedPreferencesUtil.wgtVersion, wgtVersion);
  }

  static Future<String> getWgtVersion () async {
    return await SharedPreferencesUtil.getData(SharedPreferencesUtil.wgtVersion);
  }

  static setWgtAppId ( String wgtAppId) {
    SharedPreferencesUtil.saveData(SharedPreferencesUtil.wgtAppId, wgtAppId);
  }

  static Future<String> getWgtAppId () async {
    return await SharedPreferencesUtil.getData(SharedPreferencesUtil.wgtAppId);
  }
  ///  flutter 时的方法名和参数值
  static Future<dynamic> _callHandler(MethodCall call) async {
    log("method:${call.method}, arguments:${call.arguments}");
  }

  static Future<String> openUniApp(String jsonData) async {//打开小程序
    // if(jsonData.isEmpty){
    //   print("openUniAppData:$jsonData");
    //   return "";
    // }
    // print("openUniAppData:$jsonData");
    // /// String jsondata='Coursedetails/feedback/newIndex?app=1&token=********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************&data={"experience":false,"id":"1331853489575829504"}';
    // return await _openAppMethodChannel.invokeMethod("openUniApp", jsonData);
    try {
      Map<String,dynamic>loginInfo=jsonDecode(jsonData);
      if(Platform.isIOS) {
        loginInfo['tempDxSource']='DXX##IOS##APP';
      } else  {
        loginInfo['tempDxSource']='DXX##ANDROID##APP';
      }
      String jsonString =jsonEncode(loginInfo);
      String wgtAppId = await AndroidIosPlugin.getWgtAppId();
      List arguments = [jsonString, wgtAppId];
      return await _openAppMethodChannel.invokeMethod("openUniApp", arguments);
    } catch (e) {
      if(Platform.isIOS) {
        jsonData = jsonData + '&tempDxSource=DXZX##IOS##APP';
      } else  {
        jsonData = jsonData + '&tempDxSource=DXZX##ANDROID##APP';
      }
      String wgtAppId = await AndroidIosPlugin.getWgtAppId();
      List arguments = [jsonData, wgtAppId];
      return await _openAppMethodChannel.invokeMethod("openUniApp", arguments);
    }
  }
  static Future<String> exitApp() async {//关闭应用
    return await _openAppMethodChannel.invokeMethod("exitApp", "");
  }
  static Future<String> creatCalendar(String jsonData) async {//添加日历提醒
    return await _openAppMethodChannel.invokeMethod("creatCalendar", jsonData);
  }
  static Future<String> openDxnUniApp(String jsonData) async {//打开小程序
    print("openUniAppData:$jsonData");
    return await _openAppMethodChannel.invokeMethod("openDxnUniApp", jsonData);
  }

  static Future<String> isOpenNotificationSettings(String jsonData) async {//打开小程序
    return await _openAppMethodChannel.invokeMethod("isOpenNotificationSettings", jsonData);
  }

  static Future<String> twoBackFinish(canFinish) async {
    return await _openAppMethodChannel.invokeMethod("twoBackFinish",canFinish);
  }

  static Future<String> setUserIdForBugly(String jsonData) async {
    return await _meetMethodChannel.invokeMethod("setUserIdForBugly", jsonData);
  }
  /// 查找分享数据
  static Future<String> findShareData() async {
    return await _openAppMethodChannel.invokeMethod("getAppSharePath");
  }

  static Future<String> openUniAppAliPay(String jsonData) async {//打开支付宝小程序支付
    return await _openAppPayMethodChannel.invokeMethod("openUniAppAliPay", jsonData);
  }

  static Future<String> openUniAppWxPay(String jsonData) async {//打开微信小程序支付
    return await _openAppPayMethodChannel.invokeMethod("openUniAppWxPay", jsonData);
  }

  static Future<String> openMeeting(String jsonData) async {//打开到原生课程列表
    return await _meetMethodChannel.invokeMethod("openLessonsList", jsonData);
  }

  static Future<String> openLessonsDetail(String jsonData) async {//打开到原生课程权限检测
    return await _meetMethodChannel.invokeMethod("openLessonsDetail", jsonData);
  }
  static Future<String> openVideoLessonsLearn(String jsonData) async {//打开录播课学习详情界面
    return await _meetMethodChannel.invokeMethod("openVideoLessonsLearn", jsonData);
  }
  /// 分享小程序 {"miniProgramId":"","path":"","logo":"","title":""}
  static Future<String> openShareMiniProgram(String jsonData) async {//分享小程序
    print("openUniAppData:$jsonData");
    return await _openAppMethodChannel.invokeMethod("openShareMiniProgram", jsonData);
  }
  static Future<String> openNotificationSettings() async {// 打开应用通知
    return await _openAppMethodChannel.invokeMethod("openNotificationSettings","");
  }
  static Future<String> openShareApp(String jsonData) async {//打开小程序
    return await _openAppMethodChannel.invokeMethod("openShareApp", jsonData);
  }
  static Future<String> opneCustomerService() async {//打开到在线客服
    return await _openAppMethodChannel.invokeMethod("opneCustomerService", "");
  }
  static Future<String> shareImage(String jsonData) async {//打开小程序
    print("openUniAppData:$jsonData");
    return await _openAppMethodChannel.invokeMethod("openShareAppPic", jsonData);
  }
  static Future<String> openNetwork() async {//打开网络
    return await _openAppMethodChannel.invokeMethod("openNetwork", "");
  }


  /// 获取小程序运行基础路径
  static Future<String> getAppBasePath() async {
    try {
      final String? path = await  _openAppMethodChannel.invokeMethod('getAppBasePath');
      if (path == null) {
        throw Exception('无法获取应用基础路径');
      }
      return path;
    } on PlatformException catch (e) {
      throw Exception("获取路径失败: ${e.message}");
    }
  }

  /// 释放 wgt 资源包到运行路径
  static Future<void> releaseWgtToRunPath({
    required String wgtPath,
    required String appId,
  }) async {
    try {
      await _openAppMethodChannel.invokeMethod('releaseWgtToRunPath', {
        'wgtPath': wgtPath,
        'appId': appId,
      });
      return;
    } on PlatformException catch (e) {
      throw Exception("释放资源包失败: ${e.message}");
    }
  }


  /// 完整流程：下载远程 wgt文件
  static Future<void> updateWgt({
    required String appId,
    required String wgtUrl,
    required String version,
  }) async {
    try {
      final wgtPath = await _downloadUniWgt(url: wgtUrl,appId: appId, fileName:'$wgtUrl.wgt');
      await releaseWgtToRunPath(wgtPath: wgtPath, appId: appId);
      AndroidIosPlugin.setWgtVersion(version);
      AndroidIosPlugin.setWgtAppId(appId);
      BotToast.cleanAll();
    } catch (e) {
      print('updateWgt-----失败$e');
    }
  }


  /// 下载远程 wgt 文件
  static Future<String> _downloadUniWgt({
    required String url,
    required String appId,
    required String fileName,
    ProgressCallback? onDownloadProgress,
  }) async {
    try {
      // 请求存储权限
      final status = await Permission.manageExternalStorage.request();
      if (!status.isGranted) {
        throw Exception('存储权限被拒绝');
      }

      // 获取临时目录
      final tempDir = await getTemporaryDirectory();
      final savePath = '${tempDir.path}/$fileName';

      // 创建下载请求
      final response = await _dio.download(
        url,
        savePath,
        onReceiveProgress: onDownloadProgress,
        options: Options(responseType: ResponseType.bytes),
      );

      if (response.statusCode != 200) {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          error: '下载失败，状态码: ${response.statusCode}',
        );
      }
      return savePath;
    } on DioException catch (e) {
      throw Exception('下载失败: ${e.message}');
    }

  }

  Future<String> getAndroidDownloadPath() async {
    if (await Permission.storage.request().isGranted) {
      // 外部存储
      Directory? downloadsDir = await getExternalStorageDirectory();
      return downloadsDir?.path ?? (await getDownloadsDirectory())!.path;
    } else {
      // 应用私有存储
      return (await getApplicationDocumentsDirectory()).path;
    }
  }

  /// 检查小程序是否已安装
  static Future<bool> isAppInstalled(String appId) async {
    try {
      final basePath = await getAppBasePath();
      final appDir = Directory('$basePath/$appId');
      return await appDir.exists();
    } catch (e) {
      return false;
    }
  }

  /// 删除小程序
  static Future<void> deleteApp(String appId) async {
    try {
      final basePath = await getAppBasePath();
      final appDir = Directory('$basePath/$appId');
      if (await appDir.exists()) {
        await appDir.delete(recursive: true);
      }
    } catch (e) {
      throw Exception('删除失败: $e');
    }
  }
}