import 'package:bot_toast/bot_toast.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:student_end_flutter/bean/course_list_bean_entity.dart';
import 'package:student_end_flutter/dialog/dialog_learn_select_identity.dart';
import 'package:student_end_flutter/pages/learn/widgets/lesson_leave_button.dart';
import 'package:student_end_flutter/utils/utils.dart';

import '../../common/CommonUtil.dart';
import '../../common/ne_user_option.dart';
import '../../dialog/dialog_confirm.dart';
import 'controller/lessonlist_controller.dart';
import 'customer/CustomRefreshFooter.dart';
import 'customer/CustomRefreshHeader.dart';

class LessonListPage extends GetView<LessonListController> {
  final List<Map> _coursesTypeList = [
    {'name': '伴学课', 'courseType': '0'},
    {'name': '录播课', 'courseType': '1'},
    {'name': '复习课', 'courseType': '2'}
  ];
  final List<GlobalKey> _keys = List.generate(3, (index) => GlobalKey());
  final EasyRefreshController _easyRefreshController = EasyRefreshController();

  LessonListPage({super.key});

  @override
  Widget build(BuildContext context) {
    Get.put(LessonListController());
    final topPadding = MediaQuery.of(context).padding.top;
    return Scaffold(
      body: Container(
        height: double.infinity,
        color: Colors.white,
        padding: EdgeInsets.only(top: topPadding),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Container(
              width: double.infinity,
              margin: EdgeInsets.only(
                left: 10.w,
              ),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  Positioned(
                    left: 0,
                    child: GestureDetector(
                      onTap: () {
                        Get.back();
                      },
                      child: Image.asset(
                        width: 20.w,
                        height: 16.w,
                        fit: BoxFit.fill,
                        'assets/learn/ic_back_black.png',
                      ),
                    ),
                  ),
                  Container(
                    alignment: Alignment.center,
                    width: double.infinity,
                    margin: EdgeInsets.only(left: 40.w, right: 40.w),
                    padding: EdgeInsets.all(2.w),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(4.w),
                        color: HexColor('f8f8f8')),
                    height: 32.h,
                    child: _tabUI(),
                  ),
                ],
              ),
            ),
            _classStatus(),
            Expanded(
              child: Container(
                padding: EdgeInsets.only(top: 14.h),
                color: HexColor('fafafa'),
                child: TabBarView(
                  controller: controller.tabController,
                  children: [
                    _courseListPage(),
                    _courseListPage(),
                    _courseListPage(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  _tabUI() {
    return ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _coursesTypeList.length,
        itemBuilder: (BuildContext context, int index) {
          final listViewWidth = MediaQuery.of(context).size.width - 80.w;
          return _itemBuilder(index, listViewWidth);
        });
  }

  //已上课 未上课 全部
  _classStatus() {
    return Obx(
      () => Padding(
        padding: EdgeInsets.all(14.w),
        child: Row(
          children: [
            controller.currentTabIndex.value == 0 ||
                    controller.currentTabIndex.value == 2
                ? Row(
                    children: [
                      InkWell(
                        onTap: () {
                          controller.currentLessonStatusTabIndex.value = 0;
                          if (controller.currentTabIndex.value == 0) {
                            controller.getDeliverCoursePage(true);
                          } else {
                            controller.getReviewCourse(true);
                          }
                        },
                        child: Stack(
                          alignment: Alignment.center,
                          children: [
                            Positioned(
                              bottom: 0,
                              child: Container(
                                height: 4.h,
                                width: 44.w,
                                decoration: controller
                                            .currentLessonStatusTabIndex
                                            .value ==
                                        0
                                    ? BoxDecoration(
                                        borderRadius:
                                            BorderRadius.circular(4.w),
                                        color: HexColor('04DB9C'))
                                    : null, // 下划线的颜色
                              ),
                            ),
                            Text(
                              "未上课",
                              style: TextStyle(
                                color: controller.currentLessonStatusTabIndex
                                            .value ==
                                        0
                                    ? HexColor("428A6F")
                                    : HexColor("555555"),
                                fontSize: 15.sp,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.only(left: 14.w, right: 14.w),
                        width: 1.w, // 竖线的宽度
                        height: 16.h,
                        color: HexColor("F1EEEE"), // 竖线的颜色
                      ),
                      InkWell(
                        onTap: () {
                          controller.currentLessonStatusTabIndex.value = 1;
                          if (controller.currentTabIndex.value == 0) {
                            controller.getDeliverCoursePage(true);
                          } else {
                            controller.getReviewCourse(true);
                          }
                        },
                        child: Stack(
                          alignment: Alignment.center,
                          children: [
                            Positioned(
                              bottom: 0,
                              child: Container(
                                height: 4.h,
                                width: 44.w,
                                decoration: controller
                                            .currentLessonStatusTabIndex
                                            .value ==
                                        1
                                    ? BoxDecoration(
                                        borderRadius:
                                            BorderRadius.circular(4.w),
                                        color: HexColor('04DB9C'))
                                    : null, // 下划线的颜色
                              ),
                            ),
                            Text(
                              "已上课",
                              style: TextStyle(
                                color: controller.currentLessonStatusTabIndex
                                            .value ==
                                        1
                                    ? HexColor("428A6F")
                                    : HexColor("555555"),
                                fontSize: 15.sp,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  )
                : Container(),
            const Spacer(),
            // 日期选择 - 仅交付课(未上课)可见
            Visibility(
              visible: controller.currentTabIndex.value == 0 &&
                      controller.currentLessonStatusTabIndex.value == 0
                  ? true
                  : false,
              child: InkWell(
                onTap: () {
                  controller.showSelectDateDialog();
                },
                child: Row(
                  children: [
                    Text(
                      "日期",
                      style: TextStyle(
                        color: HexColor("555555"),
                        fontSize: 15.sp,
                      ),
                    ),
                    SizedBox(
                      width: 2.w,
                    ),
                    Image.asset(
                      width: 12.w,
                      height: 12.w,
                      fit: BoxFit.fill,
                      'assets/learn/ic_drop_black.png',
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(
              width: 8,
            ),
            Visibility(
              visible: controller.currentTabIndex.value == 0 ||
                      controller.currentTabIndex.value == 1
                  ? true
                  : false,
              child: InkWell(
                onTap: () {
                  controller.getCourseType();
                },
                child: Row(
                  children: [
                    Text(
                      "全部",
                      style: TextStyle(
                        color: HexColor("555555"),
                        fontSize: 15.sp,
                      ),
                    ),
                    SizedBox(
                      width: 2.w,
                    ),
                    Image.asset(
                      width: 12.w,
                      height: 12.w,
                      fit: BoxFit.fill,
                      'assets/learn/ic_drop_black.png',
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  //课程类型
  _itemBuilder(index, listViewWidth) {
    return Obx(
      () => InkWell(
        onTap: () {
          controller.changeTab(index);
        },
        child: Container(
          key: _keys[index],
          height: 30.h,
          width: listViewWidth / 3,
          alignment: Alignment.center,
          decoration: index == controller.currentTabIndex.value
              ? BoxDecoration(
                  borderRadius: BorderRadius.circular(4.w),
                  color: HexColor('ffffff'),
                )
              : null,
          child: Text(
            _coursesTypeList[index]['name'] ?? '',
            style: TextStyle(
              color: index == controller.currentTabIndex.value
                  ? HexColor('428A6F')
                  : HexColor('666666'),
              fontSize:
                  index == controller.currentTabIndex.value ? 16.sp : 14.sp,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }

  _courseListPage() {
    return Obx(
      () => EasyRefresh(
        controller: _easyRefreshController,
        header: CustomRefreshHeader(
            key: const Key('刷新'),
            alignment: Alignment.center,
            textColor: HexColor('999999'),
            infoColor: HexColor('999999')),
        footer: CustomRefreshFooter(
            key: const Key('加载更多'),
            alignment: Alignment.center,
            textColor: HexColor('999999'),
            infoColor: HexColor('999999')),
        onRefresh: () async {
          if (controller.currentTabIndex.value == 0) {
            controller.getDeliverCoursePage(true);
          } else if (controller.currentTabIndex.value == 1) {
            controller.getRecordCoursePage(true);
          } else if (controller.currentTabIndex.value == 2) {
            controller.getReviewCourse(true);
          }
          _easyRefreshController.finishRefresh();
          _easyRefreshController.resetLoadState();
        },
        onLoad: controller.isFootShow.value
            ? () async {
                if (controller.currentTabIndex.value == 0) {
                  controller.getDeliverCoursePage(false);
                } else if (controller.currentTabIndex.value == 1) {
                  controller.getRecordCoursePage(false);
                } else if (controller.currentTabIndex.value == 2) {
                  controller.getReviewCourse(false);
                }
                if (controller.canLoadMore.value) {
                  _easyRefreshController.finishLoad(success: true);
                } else {
                  _easyRefreshController.finishLoad(noMore: true);
                }
                controller.loadingBottomController();
              }
            : null,
        emptyWidget: (controller.currentTabIndex.value == 0 &&
                    controller.courseData.isEmpty) ||
                (controller.currentTabIndex.value == 1 &&
                    controller.courseRecordData.isEmpty) ||
                (controller.currentTabIndex == 2 &&
                    controller.reviewCourseData.isEmpty)
            ? Container(
                alignment: Alignment.center,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset(
                      width: 60.w,
                      height: 60.w,
                      fit: BoxFit.fill,
                      'assets/learn/icon_zanwu.png',
                    ),
                    Text(
                      "暂无数据",
                      style:
                          TextStyle(fontSize: 14.sp, color: HexColor('666666')),
                    ),
                  ],
                ),
              )
            : null,
        child: ListView.builder(
          itemCount: controller.currentTabIndex.value == 0
              ? controller.isSelectDate.value // 是否按日期筛选了伴学课
                  ? controller.datedCourseData.length
                  : controller.courseData.length
              : controller.currentTabIndex.value == 1
                  ? controller.courseRecordData.length
                  : controller.reviewCourseData.length,
          padding: EdgeInsets.zero,
          itemBuilder: (context, index) {
            return controller.currentTabIndex.value == 0
                ? buildLessonsContentList(context, index)
                : controller.currentTabIndex.value == 1
                    ? buildRecordLessonsContentList(context, index)
                    : buildReviewCourseContentList(context, index);
          },
        ),
      ),
    );
  }

  //  伴学课 增加家长巡课
  buildLessonsContentList(context, index) {
    final courseData = controller.isSelectDate.value
        ? controller.datedCourseData[index]
        : controller.courseData[index];
    return Container(
      width: double.infinity,
      height: 180.h,
      margin: EdgeInsets.only(left: 0.w, right: 14.w, bottom: 14.h),
      padding: EdgeInsets.only(top: 0.w, left: 0.w, right: 10.w, bottom: 10.w),
      decoration: const BoxDecoration(
        image: DecorationImage(
          image: AssetImage('assets/learn/pic_kapian.png'),
          fit: BoxFit.fill,
        ),
      ),
      child: Stack(
        children: [
          if (courseData.status == 0)
            Image(
                width: 80.w,
                height: 40.w,
                // fit: BoxFit.fitWidth,
                image: AssetImage("assets/learn/icon_not_attending_class.png")),
          Positioned(
            left: 30.w,
            top: 30.w,
            right: 0.w,
            bottom: 0.w,
            child: Column(
              children: [
                Container(
                  margin: EdgeInsets.only(bottom: 10.w),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Flexible(
                              child: Text(
                                courseData.courseName,
                                style: TextStyle(fontSize: 15.sp),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            SizedBox(
                              width: 4.w,
                            ),
                            Visibility(
                              visible: courseData.lessonsFlag == 1,
                              child: Container(
                                alignment: Alignment.center,
                                width: 20.w,
                                height: 20.w,
                                decoration: BoxDecoration(
                                    color: HexColor('DBE436'),
                                    borderRadius: BorderRadius.circular(4.w)),
                                child: Text(
                                  '补',
                                  style: TextStyle(
                                      color: Colors.white, fontSize: 14.sp),
                                ),
                              ),
                            )
                          ],
                        ),
                      ),
                      SizedBox(width: 5.w),
                    ],
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(top: 10.w),
                  alignment: Alignment.centerLeft,
                  child: Text(
                    "上课时间：${courseData.courseTime}",
                    style:
                        TextStyle(fontSize: 14.sp, color: HexColor('555555')),
                  ),
                ),
                // 伴学课meetingNum 会议ID

                Container(
                  margin: EdgeInsets.only(top: 10.w),
                  alignment: Alignment.centerLeft,
                  child: Text(
                    "教练：${courseData.teacherName}",
                    style:
                        TextStyle(fontSize: 14.sp, color: HexColor('555555')),
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(top: 10.w),
                  alignment: Alignment.centerLeft,
                  child: Text(
                    "会议ID：${courseData.meetingNum}",
                    style:
                        TextStyle(fontSize: 14.sp, color: HexColor('555555')),
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(top: 10.w, bottom: 10.w),
                  height: 0.5.h,
                  color: HexColor('F1EEEE'),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    LessonLeaveButton(courseData: courseData),
                    if (courseData.feedback.isEmpty)
                      InkWell(
                        onTap: () {
                          // 已请假的课程不响应点击事件
                          if (courseData.leaveStatus != 0) {
                            return;
                          }
                          // 已上课，正常巡课 处理家长巡课按钮点击事件
                          controller.getCourseMeetingInfo(
                              index, UserType.patriarch);
                        },
                        child: Container(
                          height: 30.w,
                          alignment: Alignment.center,
                          padding: EdgeInsets.symmetric(horizontal: 20.w),
                          decoration: BoxDecoration(
                            border: Border.all(
                                color: HexColor('009B55'), width: 1.w),
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(15.w),
                          ),
                          child: Text(
                            "家长巡课",
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: HexColor('009B55'),
                              fontWeight: FontWeight.w500, // 增加字体粗细
                            ),
                          ),
                        ),
                      ),
                    SizedBox(
                      width: 5,
                    ),
                    Visibility(
                      visible:
                          controller.currentLessonStatusTabIndex.value == 1 &&
                              courseData.feedback.isEmpty &&
                              courseData.leaveStatus == 0,
                      // 非请假学员才展示继续学习按钮
                      child: InkWell(
                        onTap: () {
                          controller.goOnStudy(index); // 原有逻辑 确认后的回调
                        },
                        child: Container(
                          alignment: Alignment.center,
                          height: 30.w,
                          padding: EdgeInsets.symmetric(horizontal: 20.w),
                          decoration: BoxDecoration(
                            border: Border.all(
                                color: HexColor('009B55'), width: 1.w),
                            // 边框样式
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            "继续学习",
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: HexColor('009B55'),
                              fontWeight: FontWeight.w500, // 增加字体粗细
                            ),
                            maxLines: 1,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(
                      width: 5,
                    ),
                    Visibility(
                      visible: courseData.leaveStatus == 0,
                      child: InkWell(
                        onTap: () {
                          controller.clickLessonBtn(index);
                        },
                        child: Container(
                          alignment: Alignment.center,
                          height: 30.w,
                          padding: EdgeInsets.symmetric(horizontal: 20.w),
                          decoration: BoxDecoration(
                            border: Border.all(
                                color: HexColor('009B55'), width: 1.w),
                            // 边框样式
                            borderRadius: BorderRadius.circular(15.w),
                            color: HexColor('009B55'),
                          ),
                          child: Text(
                            controller.currentLessonStatusTabIndex.value == 0
                                ? "立即上课"
                                : courseData.feedback.isNotEmpty
                                    ? '查看反馈'
                                    : '等待反馈',
                            style:
                                TextStyle(fontSize: 12.sp, color: Colors.white),
                          ),
                        ),
                      ),
                    ),
                    // const SizedBox(width: 8,),
                    // 请假按钮
                  ],
                ),
              ],
            ),
          )
        ],
      ),
    );
  }

  buildRecordLessonsContentList(context, index) {
    return IntrinsicWidth(
      child: Container(
        width: double.infinity,
        height: 120.h,
        margin: EdgeInsets.only(left: 10.w, right: 10.w, bottom: 10.w),
        padding:
            EdgeInsets.only(top: 10.w, left: 10.w, right: 10.w, bottom: 10.w),
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage('assets/learn/pic_kapian.png'),
            fit: BoxFit.fill,
          ),
        ),
        child: Column(
          children: [
            Container(
              margin: EdgeInsets.only(bottom: 10.w),
              alignment: Alignment.centerLeft,
              child: Text(
                controller.courseRecordData[index].createdTime,
                style: TextStyle(fontSize: 15.sp),
                textAlign: TextAlign.left,
              ),
            ),
            Container(
              margin: EdgeInsets.only(top: 10.w),
              alignment: Alignment.centerLeft,
              child: Text(
                controller.courseRecordData[index].lastStudyTime.isNotEmpty
                    ? "上次学习：${controller.courseRecordData[index].lastStudyTime}"
                    : "上次学习：~",
                style: TextStyle(fontSize: 15.sp, color: HexColor('555555')),
              ),
            ),
            Container(
              margin: EdgeInsets.only(top: 10.w, bottom: 10.w),
              height: 0.5.h,
            ),
            Row(
              children: [
                const Spacer(),
                Row(
                  children: [
                    InkWell(
                      onTap: () {
                        controller.clickLessonBtn(index); //  继续学习 原有逻辑，增加弹窗逻辑
                      },
                      child: Container(
                        alignment: Alignment.center,
                        height: 30.w,
                        padding: EdgeInsets.symmetric(horizontal: 20.w),
                        decoration: BoxDecoration(
                          border:
                              Border.all(color: HexColor('009B55'), width: 1.w),
                          // 边框样式
                          borderRadius: BorderRadius.circular(15.w),
                          color: HexColor('009B55'),
                        ),
                        child: Text(
                            controller.courseRecordData[index].lastStudyTime
                                    .isNotEmpty
                                ? "继续学习"
                                : '开始学习',
                            style: TextStyle(
                                fontSize: 12.sp, color: Colors.white)),
                      ),
                    ),
                  ],
                )
              ],
            ),
          ],
        ),
      ),
    );
  }

  // 复习课 增加家长巡课按钮
  buildReviewCourseContentList(context, index) {
    return Container(
        width: double.infinity,
        height: 180.h,
        margin: EdgeInsets.only(left: 0.w, right: 14.w, bottom: 14.h),
        padding:
            EdgeInsets.only(top: 0.w, left: 0.w, right: 10.w, bottom: 10.w),
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage('assets/learn/pic_kapian.png'),
            fit: BoxFit.fill,
          ),
        ),
        child: Stack(
          children: [
            // if (controller.reviewCourseData[index].state == 0)
            Image(
                width: 80.w,
                height: 40.w,
                // fit: BoxFit.fitWidth,
                image: AssetImage("assets/learn/icon_not_attending_class.png")),
            Positioned(
                left: 30.w,
                top: 30.w,
                right: 0.w,
                bottom: 0.w,
                child: Column(
                  children: [
                    Container(
                      margin: EdgeInsets.only(bottom: 10.w),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Flexible(
                                  child: Text(
                                    '22222',
                                    style: TextStyle(fontSize: 15.sp),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                                SizedBox(
                                  width: 4.w,
                                ),
                                // Visibility(
                                //   visible: courseData.lessonsFlag == 1,
                                //   child: Container(
                                //     alignment: Alignment.center,
                                //     width: 20.w,
                                //     height: 20.w,
                                //     decoration: BoxDecoration(
                                //         color: HexColor('DBE436'),
                                //         borderRadius: BorderRadius.circular(4.w)),
                                //     child: Text(
                                //       '补',
                                //       style: TextStyle(
                                //           color: Colors.white, fontSize: 14.sp),
                                //     ),
                                //   ),
                                // )
                              ],
                            ),
                          ),
                          SizedBox(width: 5.w),
                        ],
                      ),
                    ),
                    Container(
                      margin: EdgeInsets.only(top: 10.w),
                      alignment: Alignment.centerLeft,
                      child: Text(
                                  "复习时间：${controller.dateToString(controller.reviewCourseData[index].startTime)}",
                        style: TextStyle(
                            fontSize: 14.sp, color: HexColor('555555')),
                      ),
                    ),
                    // 伴学课meetingNum 会议ID

                    Container(
                      margin: EdgeInsets.only(top: 10.w),
                      alignment: Alignment.centerLeft,
                      child: Text(
                        "教练: ${controller.reviewCourseData[index].userNickName}",
                        style: TextStyle(
                            fontSize: 14.sp, color: HexColor('555555')),
                      ),
                    ),
                    Container(
                      margin: EdgeInsets.only(top: 10.w),
                      alignment: Alignment.centerLeft,
                      child: Text(
                        "会议ID${controller.reviewCourseData[index].meetingNum}",
                        style: TextStyle(
                            fontSize: 14.sp, color: HexColor('555555')),
                      ),
                    ),
                    Container(
                      margin: EdgeInsets.only(top: 10.w, bottom: 10.w),
                      height: 0.5.h,
                      color: HexColor('F1EEEE'),
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        // LessonLeaveButton(courseData: courseData),

                        InkWell(
                          onTap: () {
                            // 已上课，正常巡课 处理家长巡课按钮点击事件
                            controller.getCourseMeetingInfo(
                                index, UserType.patriarch);
                          },
                          child: Container(
                            height: 30.w,
                            alignment: Alignment.center,
                            padding: EdgeInsets.symmetric(horizontal: 20.w),
                            decoration: BoxDecoration(
                              border: Border.all(
                                  color: HexColor('009B55'), width: 1.w),
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(15.w),
                            ),
                            child: Text(
                              "家长巡课",
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: HexColor('009B55'),
                                fontWeight: FontWeight.w500, // 增加字体粗细
                              ),
                            ),
                          ),
                        ),
                        SizedBox(
                          width: 5,
                        ),
                        // Visibility(
                        //   visible:
                        //   controller.currentLessonStatusTabIndex.value == 1 &&
                        //       controller.reviewCourseData[index].feedback.isEmpty &&
                        //       courseData.leaveStatus == 0,
                        //   // 非请假学员才展示继续学习按钮
                        //   child: InkWell(
                        //     onTap: () {
                        //       controller.goOnStudy(index); // 原有逻辑 确认后的回调
                        //     },
                        //     child: Container(
                        //       alignment: Alignment.center,
                        //       height: 30.w,
                        //       padding: EdgeInsets.symmetric(horizontal: 20.w),
                        //       decoration: BoxDecoration(
                        //         border: Border.all(
                        //             color: HexColor('009B55'), width: 1.w),
                        //         // 边框样式
                        //         borderRadius: BorderRadius.circular(12),
                        //       ),
                        //       child: Text(
                        //         "继续学习",
                        //         style: TextStyle(
                        //           fontSize: 14.sp,
                        //           color: HexColor('009B55'),
                        //           fontWeight: FontWeight.w500, // 增加字体粗细
                        //         ),
                        //         maxLines: 1,
                        //       ),
                        //     ),
                        //   ),
                        // ),
                        SizedBox(
                          width: 5,
                        ),

                        InkWell(
                          onTap: () {
                            controller.clickLessonBtn(index);
                          },
                          child: Container(
                            alignment: Alignment.center,
                            height: 30.w,
                            padding: EdgeInsets.symmetric(horizontal: 20.w),
                            decoration: BoxDecoration(
                              border: Border.all(
                                  color: HexColor('009B55'), width: 1.w),
                              // 边框样式
                              borderRadius: BorderRadius.circular(15.w),
                              color: HexColor('009B55'),
                            ),
                            child: Text(
                              "立即上课",
                              style: TextStyle(
                                  fontSize: 12.sp, color: Colors.white),
                            ),
                          ),
                        ),

                        // const SizedBox(width: 8,),
                        // 请假按钮
                      ],
                    ),
                  ],
                ))
          ],
        ));
  }
}
