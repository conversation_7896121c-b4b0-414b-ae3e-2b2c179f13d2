import 'package:student_end_flutter/generated/json/base/json_convert_content.dart';
import 'package:student_end_flutter/bean/home_info_bean_entity.dart';
import 'package:student_end_flutter/bean/zx_course_entity.dart';
HomePageEntity $HomePageEntityFromJson(Map<String, dynamic> json) {
  final HomePageEntity homePageEntity = HomePageEntity();
  final int? code = jsonConvert.convert<int>(json['code']);
  if (code != null) {
    homePageEntity.code = code;
  }
  final bool? success = jsonConvert.convert<bool>(json['success']);
  if (success != null) {
    homePageEntity.success = success;
  }
  final String? message = jsonConvert.convert<String>(json['message']);
  if (message != null) {
    homePageEntity.message = message;
  }
  final HomePageData? data = jsonConvert.convert<HomePageData>(json['data']);
  if (data != null) {
    homePageEntity.data = data;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    homePageEntity.total = total;
  }
  return homePageEntity;
}

Map<String, dynamic> $HomePageEntityToJson(HomePageEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['code'] = entity.code;
  data['success'] = entity.success;
  data['message'] = entity.message;
  data['data'] = entity.data.toJson();
  data['total'] = entity.total;
  return data;
}

extension HomePageEntityExtension on HomePageEntity {
  HomePageEntity copyWith({
    int? code,
    bool? success,
    String? message,
    HomePageData? data,
    int? total,
  }) {
    return HomePageEntity()
      ..code = code ?? this.code
      ..success = success ?? this.success
      ..message = message ?? this.message
      ..data = data ?? this.data
      ..total = total ?? this.total;
  }
}

HomePageData $HomePageDataFromJson(Map<String, dynamic> json) {
  final HomePageData homePageData = HomePageData();
  final List<SpecialItem>? specialList = (json['specialList'] as List<dynamic>?)
      ?.map((e) => jsonConvert.convert<SpecialItem>(e) as SpecialItem)
      .toList();
  if (specialList != null) {
    homePageData.specialList = specialList;
  }

  // 新增porcelain字段解析
  final BannerItem? porcelain = jsonConvert.convert<BannerItem>(json['porcelain']);
  if (porcelain != null) {
    homePageData.porcelain = porcelain;
  }
  final List<BannerItem>? bannerList = (json['bannerList'] as List<dynamic>?)
      ?.map((e) => jsonConvert.convert<BannerItem>(e) as BannerItem)
      .toList();
  if (bannerList != null) {
    homePageData.bannerList = bannerList;
  }

  // 修改homePageGoodsList解析
  final List<HomePageGoodsItem>? homePageGoodsList = (json['homePageGoodsList'] as List<dynamic>?)
      ?.map((e) => jsonConvert.convert<HomePageGoodsItem>(e) as HomePageGoodsItem)
      .toList();
  if (homePageGoodsList != null) {
    homePageData.homePageGoodsList = homePageGoodsList;
  }

  return homePageData;
}
HomePageGoodsItem $HomePageGoodsItemFromJson(Map<String, dynamic> json) {
  final HomePageGoodsItem homePageGoodsItem = HomePageGoodsItem();
  final String? moduleName = jsonConvert.convert<String>(json['moduleName']);
  if (moduleName != null) {
    homePageGoodsItem.moduleName = moduleName;
  }
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    homePageGoodsItem.id = id;
  }
  final int? horzVert = jsonConvert.convert<int>(json['horzVert']);
  if (horzVert != null) {
    homePageGoodsItem.horzVert = horzVert;
  }
  final bool? allGoodsDisplayed = jsonConvert.convert<bool>(json['allGoodsDisplayed']);
  if (allGoodsDisplayed != null) {
    homePageGoodsItem.allGoodsDisplayed = allGoodsDisplayed;
  }
  final List<ZxCourseDataData>? goodsList = (json['goodsList'] as List<dynamic>?)
      ?.map((e) => jsonConvert.convert<ZxCourseDataData>(e) as ZxCourseDataData)
      .toList();
  if (goodsList != null) {
    homePageGoodsItem.goodsList = goodsList;
  }
  return homePageGoodsItem;
}

Map<String, dynamic> $HomePageGoodsItemToJson(HomePageGoodsItem entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['moduleName'] = entity.moduleName;
  data['horzVert'] = entity.horzVert;
  data['id'] = entity.id;
  data['goodsList'] = entity.goodsList.map((v) => v.toJson()).toList();
  return data;
}

extension HomePageGoodsItemExtension on HomePageGoodsItem {
  HomePageGoodsItem copyWith({
    String? moduleName,
    String? id,
    int? horzVert,
    bool? allGoodsDisplayed,
    List<ZxCourseDataData>? goodsList,
  }) {
    return HomePageGoodsItem()
      ..moduleName = moduleName ?? this.moduleName
      ..horzVert = horzVert ?? this.horzVert
      ..allGoodsDisplayed = allGoodsDisplayed ?? this.allGoodsDisplayed
      ..id = id ?? this.id
      ..goodsList = goodsList ?? this.goodsList;
  }
}
Map<String, dynamic> $HomePageDataToJson(HomePageData entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['specialList'] = entity.specialList.map((v) => v.toJson()).toList();
  data['bannerList'] = entity.bannerList.map((v) => v.toJson()).toList();
  data['porcelain'] = entity.porcelain?.toJson();
  data['homePageGoodsList'] = entity.homePageGoodsList.map((v) => v.toJson()).toList();
  return data;
}

extension HomePageDataExtension on HomePageData {
  HomePageData copyWith({
    List<SpecialItem>? specialList,
    List<BannerItem>? bannerList,
    List<dynamic>? homePageGoodsList,
  }) {
    return HomePageData()
      ..specialList = specialList ?? this.specialList
      ..bannerList = bannerList ?? this.bannerList
      ..homePageGoodsList = homePageGoodsList ?? this.homePageGoodsList;
  }
}

SpecialItem $SpecialItemFromJson(Map<String, dynamic> json) {
  final SpecialItem specialItem = SpecialItem();
  // 添加 SpecialItem 的所有字段解析
  final String? specialName = jsonConvert.convert<String>(json['specialName']);
  if (specialName != null) {
    specialItem.specialName = specialName;
  }
  final String? specialId = jsonConvert.convert<String>(json['specialId']);
  if (specialId != null) {
    specialItem.specialId = specialId;
  }
  final String? picUrl = jsonConvert.convert<String>(json['picUrl']);
  if (picUrl != null) {
    specialItem.picUrl = picUrl;
  }
  final String? productIntroPicUrl = jsonConvert.convert<String>(json['productIntroPicUrl']);
  if (productIntroPicUrl != null) {
    specialItem.productIntroPicUrl = productIntroPicUrl;
  }
  final String? backgroundColor = jsonConvert.convert<String>(json['backgroundColor']);
  if (backgroundColor != null) {
    specialItem.backgroundColor = backgroundColor;
  }
  final dynamic weight = json['weight'];
  if (weight != null) {
    specialItem.weight = weight;
  }
  final List<ZxCourseDataData>? goodsList = (json['goodsList'] as List<dynamic>?)
      ?.map((e) => jsonConvert.convert<ZxCourseDataData>(e) as ZxCourseDataData)
      .toList();
  if (goodsList != null) {
    specialItem.goodsList = goodsList;
  }
  final List<dynamic>? categoryList = json['categoryList'] as List<dynamic>?;
  if (categoryList != null) {
    specialItem.categoryList = categoryList;
  }
  return specialItem;
}

Map<String, dynamic> $SpecialItemToJson(SpecialItem entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['specialName'] = entity.specialName;
  data['specialId'] = entity.specialId;
  data['picUrl'] = entity.picUrl;
  data['productIntroPicUrl'] = entity.productIntroPicUrl;
  data['backgroundColor'] = entity.backgroundColor;
  data['weight'] = entity.weight;
  data['goodsList'] = entity.goodsList.map((v) => v.toJson()).toList();
  data['categoryList'] = entity.categoryList;
  return data;
}
extension SpecialItemExtension on SpecialItem {
  SpecialItem copyWith({
    String? specialName,
    String? picUrl,
    String? productIntroPicUrl,
    String? backgroundColor,
    dynamic weight,
    List<ZxCourseDataData>? goodsList,
    List<dynamic>? categoryList,
  }) {
    return SpecialItem()
      ..specialName = specialName ?? this.specialName
      ..picUrl = picUrl ?? this.picUrl
      ..productIntroPicUrl = productIntroPicUrl ?? this.productIntroPicUrl
      ..backgroundColor = backgroundColor ?? this.backgroundColor
      ..weight = weight ?? this.weight
      ..goodsList = goodsList ?? this.goodsList
      ..categoryList = categoryList ?? this.categoryList;
  }
}

BannerItem $BannerItemFromJson(Map<String, dynamic> json) {
  final BannerItem bannerItem = BannerItem();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    bannerItem.id = id;
  }
  final String? bannerName = jsonConvert.convert<String>(json['bannerName']);
  if (bannerName != null) {
    bannerItem.bannerName = bannerName;
  }
  final String? bannerPicUrl = jsonConvert.convert<String>(json['bannerPicUrl']);
  if (bannerPicUrl != null) {
    bannerItem.bannerPicUrl = bannerPicUrl;
  }
  final String? bannerPosition = jsonConvert.convert<String>(json['bannerPosition']);
  if (bannerPosition != null) {
    bannerItem.bannerPosition = bannerPosition;
  }
  final int? bannerType = jsonConvert.convert<int>(json['bannerType']);
  if (bannerType != null) {
    bannerItem.bannerType = bannerType;
  }
  final String? goodsId = jsonConvert.convert<String>(json['goodsId']);
  if (goodsId != null) {
    bannerItem.goodsId = goodsId;
  }
  final String? activityId = jsonConvert.convert<String>(json['activityId']);
  if (activityId != null) {
    bannerItem.activityId = activityId;
  }
  final String? goodsName = jsonConvert.convert<String>(json['goodsName']);
  if (goodsName != null) {
    bannerItem.goodsName = goodsName;
  }
  final String? bannerLinkUrl = jsonConvert.convert<String>(json['bannerLinkUrl']);
  if (bannerLinkUrl != null) {
    bannerItem.bannerLinkUrl = bannerLinkUrl;
  }
  final int? weight = jsonConvert.convert<int>(json['weight']);
  if (weight != null) {
    bannerItem.weight = weight;
  }
  final int? bannerStatus = jsonConvert.convert<int>(json['bannerStatus']);
  if (bannerStatus != null) {
    bannerItem.bannerStatus = bannerStatus;
  }
  final String? effectiveStartTime = jsonConvert.convert<String>(json['effectiveStartTime']);
  if (effectiveStartTime != null) {
    bannerItem.effectiveStartTime = effectiveStartTime;
  }
  final String? effectiveEndTime = jsonConvert.convert<String>(json['effectiveEndTime']);
  if (effectiveEndTime != null) {
    bannerItem.effectiveEndTime = effectiveEndTime;
  }
  final int? needLogin = jsonConvert.convert<int>(json['needLogin']);
  if (needLogin != null) {
    bannerItem.needLogin = needLogin;
  }
  final String? createdTime = jsonConvert.convert<String>(json['createdTime']);
  if (createdTime != null) {
    bannerItem.createdTime = createdTime;
  }
  final String? updatedTime = jsonConvert.convert<String>(json['updatedTime']);
  if (updatedTime != null) {
    bannerItem.updatedTime = updatedTime;
  }
  final String? goodsSharePoster = jsonConvert.convert<String>(json['goodsSharePoster']);
  if (goodsSharePoster != null) {
    bannerItem.goodsSharePoster = goodsSharePoster;
  }
  final List<dynamic>? goodsShareTextList = json['goodsShareTextList'] as List<dynamic>?;
  if (goodsShareTextList != null) {
    bannerItem.goodsShareTextList = goodsShareTextList;
  }
  return bannerItem;
}

Map<String, dynamic> $BannerItemToJson(BannerItem entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['bannerName'] = entity.bannerName;
  data['bannerPicUrl'] = entity.bannerPicUrl;
  data['bannerPosition'] = entity.bannerPosition;
  data['bannerType'] = entity.bannerType;
  data['goodsId'] = entity.goodsId;
  data['activityId'] = entity.activityId;
  data['goodsName'] = entity.goodsName;
  data['bannerLinkUrl'] = entity.bannerLinkUrl;
  data['weight'] = entity.weight;
  data['bannerStatus'] = entity.bannerStatus;
  data['effectiveStartTime'] = entity.effectiveStartTime;
  data['effectiveEndTime'] = entity.effectiveEndTime;
  data['needLogin'] = entity.needLogin;
  data['createdTime'] = entity.createdTime;
  data['updatedTime'] = entity.updatedTime;
  data['goodsSharePoster'] = entity.goodsSharePoster;
  data['goodsShareTextList'] = entity.goodsShareTextList;
  return data;
}

extension BannerItemExtension on BannerItem {
  BannerItem copyWith({
    String? id,
    String? bannerName,
    String? bannerPicUrl,
    String? bannerPosition,
    int? bannerType,
    String? goodsId,
    String? activityId,
    String? goodsName,
    String? bannerLinkUrl,
    int? weight,
    int? bannerStatus,
    String? effectiveStartTime,
    String? effectiveEndTime,
    int? needLogin,
    String? createdTime,
    String? updatedTime,
    String? goodsSharePoster,
    List<dynamic>? goodsShareTextList,
  }) {
    return BannerItem()
      ..id = id ?? this.id
      ..bannerName = bannerName ?? this.bannerName
      ..bannerPicUrl = bannerPicUrl ?? this.bannerPicUrl
      ..bannerPosition = bannerPosition ?? this.bannerPosition
      ..bannerType = bannerType ?? this.bannerType
      ..goodsId = goodsId ?? this.goodsId
      ..activityId = activityId ?? this.activityId
      ..goodsName = goodsName ?? this.goodsName
      ..bannerLinkUrl = bannerLinkUrl ?? this.bannerLinkUrl
      ..weight = weight ?? this.weight
      ..bannerStatus = bannerStatus ?? this.bannerStatus
      ..effectiveStartTime = effectiveStartTime ?? this.effectiveStartTime
      ..effectiveEndTime = effectiveEndTime ?? this.effectiveEndTime
      ..needLogin = needLogin ?? this.needLogin
      ..createdTime = createdTime ?? this.createdTime
      ..updatedTime = updatedTime ?? this.updatedTime
      ..goodsSharePoster = goodsSharePoster ?? this.goodsSharePoster
      ..goodsShareTextList = goodsShareTextList ?? this.goodsShareTextList;
  }
}

GoodsShareText $GoodsShareTextFromJson(Map<String, dynamic> json) {
  final GoodsShareText goodsShareText = GoodsShareText();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    goodsShareText.id = id;
  }
  final String? goodsId = jsonConvert.convert<String>(json['goodsId']);
  if (goodsId != null) {
    goodsShareText.goodsId = goodsId;
  }
  final String? shareText = jsonConvert.convert<String>(json['shareText']);
  if (shareText != null) {
    goodsShareText.shareText = shareText;
  }
  return goodsShareText;
}

Map<String, dynamic> $GoodsShareTextToJson(GoodsShareText entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['goodsId'] = entity.goodsId;
  data['shareText'] = entity.shareText;
  return data;
}

extension GoodsShareTextExtension on GoodsShareText {
  GoodsShareText copyWith({
    String? id,
    String? goodsId,
    String? shareText,
  }) {
    return GoodsShareText()
      ..id = id ?? this.id
      ..goodsId = goodsId ?? this.goodsId
      ..shareText = shareText ?? this.shareText;
  }
}
