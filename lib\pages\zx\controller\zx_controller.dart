import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:student_end_flutter/bean/zx_course_entity.dart';
import 'package:student_end_flutter/common/user_option.dart';
import 'package:student_end_flutter/components/toast_utils.dart';
import 'package:student_end_flutter/utils/httpRequest.dart';
import '../../../bean/banner_bean_entity.dart';
import '../../../bean/popup_model_entity.dart';
import '../../../common/Config.dart';
import '../../../common/pop_option.dart';
import '../../../dialog/dialog_activity_pop.dart';
import '../../../dialog/dialog_home_activity.dart';
import '../../../generated/json/base/json_convert_content.dart';
import 'package:get/get.dart';
import '../../../utils/app_version_util.dart';
import '../../../navigator/home_navigator.dart';
import '../../../utils/android_ios_plugin.dart';
import '../../../utils/event_bus_utils.dart';
import '../../../common/sensors_analytics_option.dart';

class ZxController extends GetxController
    with GetSingleTickerProviderStateMixin {
  RxList<ZxCourseDataData> coursesDataList = RxList<ZxCourseDataData>([]);
  RxList<ZxCourseDataData> goodsDataList = RxList<ZxCourseDataData>([]);

  RxList<BannerBeanData> bannerList = RxList<BannerBeanData>([]);

  final TextEditingController editingSearchController = TextEditingController();

  // TabController? tabController;

  var currentTabIndex = 0.obs;
  var tabIndex = 0.obs;
  double bottomPosW = 20.w;
  RxList<Map> buttonList = <Map>[].obs;
  RxString priceImg = "assets/zx/btn_price_normal.png".obs;
  RxString salesImg = "assets/zx/btn_price_normal.png".obs;
  RxString sales = "".obs; //asc  //desc
  RxString price = "".obs; //goodsOriginalPrice 价格  goodsSales 销量
  RxInt pageNum = 1.obs;
  int pageSize = 20;
  RxString courseId ='0'.obs;
  ScrollController scrollController = ScrollController();
  ScrollController scrollHorizontalController = ScrollController();
  ScrollController goodsController = ScrollController();
  int _totalCoursePages = 1;
  int _totalGoodsPages = 1;
  RxBool showTable = true.obs;
  final PopupController controller = Get.put(PopupController('ZxPage'));
  List<PopupModelData> items = [];
  DateTime _pageOpenTime = DateTime.now();
  DateTime _lastPauseTime = DateTime.now();
  @override
  void onInit() {
    UserOption.getUserInfoRequest(false);
    scrollController.addListener(scrollListener);
    scrollHorizontalController.addListener(scrollHorizontal);
    goodsController.addListener(scrollgoods);
    // tabController = TabController(vsync: this, length: buttonList.value.length)
    //   ..addListener(() {
    //     // if(!tabController!.indexIsChanging) {
    //     //   changeTab(tabController!.index);
    //     // }
    //   });
    // tabController?.animation?.addListener(() {
    //   currentTabIndex.value = tabController?.animation!.value.round() ?? 0;
    //   if (!tabController!.indexIsChanging) {
    //     changeTab(currentTabIndex.value);
    //   }
    // });
    super.onInit();
    getZxGoodCourse();
    getBannerList();
    getCourseData(0);
    // getGoodsData();
  }
  Future<void> onRefresh() async {
    getBannerList();
    pageNum.value = 1;
    getCourseData(0);
    // getGoodsData();
  }
getZxGoodCourse(){
  // zxGoodCourse\
  var coursesResponse = HttpUtil().get(
      "${Config.zxGoodCourse}?goodsTypeListStr=${'2,3,4,8'}&orderBy=$price&orderType=$sales&pageNum=$pageNum&pageSize=$pageSize&userId=${UserOption.userId}");
  coursesResponse.then((value) async {
    // buttonList.value=  // 转换为字符串映射
    List<Map<dynamic, dynamic>> convertedList = [];
    for (var item in value['data']) {
      if (item is Map) {
        convertedList.add(Map<dynamic, dynamic>.from(item));
      }
    }
    // 更新您的状态
    buttonList.value = convertedList;
    courseId.value= buttonList[0]['id'];
  });
}
  void scrollListener() {
    print('vvvvvvvvvvvvvv4444444444444444444444444vvvvvvvvvvvvvvvvvv');
    if(buttonList.length>5){
      if(scrollController.position.pixels>210){
        showTable.value=false;
      }else{
        showTable.value=true;
      }
    }else{
      if(scrollController.position.pixels>190){
        showTable.value=false;
      }else{
        showTable.value=true;
      }

    }
    int totalPages =_totalCoursePages;
    if (scrollController.position.pixels ==
        scrollController.position.maxScrollExtent) {
      print(pageNum);
      print(totalPages);
      print('cccccccccccccccccccccccccc333333333333333333333333333');
      if (pageNum < totalPages) {
        pageNum++;
        requestFunc();
      } else if (pageNum >= totalPages) {
        // ToastUtil.showToastText("已经是最后一页~");
      }
    }

  }
  void scrollHorizontal() {
    if(scrollHorizontalController.position.pixels==scrollHorizontalController.position.maxScrollExtent){
      tabIndex.value=1;
    }else{
      tabIndex.value=0;
    }

  }
  void scrollgoods(){

    if(goodsController.position.pixels==goodsController.position.maxScrollExtent){
     print('vddddddddddddddddddddddddddddddddddddddddddd');
    }
  }
  // 页面浏览埋点
  void sendPageViewEvent() {
    _pageOpenTime = DateTime.now();
    TrackingUtils.trackPageView(
      'APPViewScreen',
      {
        'url_path': 'zxPage',
        'refeere_path': '',
        'avitcity_id': '',
        'goods_id': '',
        'banner_id': '',
        'common_fields': ''
      },
    );
  }

  // 页面离开埋点
  void sendPageLeaveEvent() {
    _lastPauseTime = DateTime.now();
    final duration = _lastPauseTime.difference(_pageOpenTime).inMilliseconds;
    TrackingUtils.trackPageView(
      'AppPageLeave',
      {
        'url_path': 'zxPage',
        'refeere_path': '',
        'avitcity_id': '',
        'goods_id': '',
        'banner_id': '',
        'common_fields': '',
        'event_duration': duration.toString()
      },
    );
  }
  changeTab(index) {
    pageNum.value = 1;
    currentTabIndex.value = index;
    // tabController?.animateTo(index);
    initStatus();
    courseId.value =buttonList[index]['id']!;
    requestFunc();
  }
changeTabItem(item){
  tabSensors(item);
  courseId.value =item['id']!;
  currentTabIndex.value = buttonList.indexWhere((info)=>info['id']==item['id']);
  requestFunc();
}
tabSensors(item){
  TrackingUtils.trackEvent('TopCourseClick', {
    'banner_id': '',
    'common_fields':item['name'],
    'avitcity_id':'','goods_id':'','url_path': 'ZxPage'
  });
}
  /// 销量
  changeSales() {
    if(price.value!='goodsSales'){
      price.value = "goodsSales";
      priceImg.value = "assets/zx/btn_price_normal.png";
      sales.value ="";
    }

    if (sales.value == "") {
      sales.value = "asc";
      salesImg.value = "assets/zx/btn_shang_h.png";
    } else if (sales.value == "asc") {
      //向上
      sales.value = "desc";
      salesImg.value = "assets/zx/btn_xia_icon.png";
    } else if (sales.value == "desc") {
      //向下
      sales.value = "";
      salesImg.value = "assets/zx/btn_price_normal.png";
    }
    resetAndFetch();
  }

  /// 价格
  changePrice() {
    if( price.value != "goodsOriginalPrice"){
      price.value = "goodsOriginalPrice";
      salesImg.value='assets/zx/btn_price_normal.png';
      sales.value = "";
    }

    if (sales.value == "") {
      sales.value = "asc";
      priceImg.value = "assets/zx/btn_shang_h.png";
    } else if (sales.value == "asc") {
      //向上
      sales.value = "desc";
      priceImg.value = "assets/zx/btn_xia_icon.png";
    } else if (sales.value == "desc") {
      //向下
      sales.value = "";
      priceImg.value = "assets/zx/btn_price_normal.png";
    }
    print('${price.value}---------${sales.value}');
    resetAndFetch();
  }

  initStatus() {
    // showTable.value=true;
    sales.value = "";
    price.value = "";
    pageNum.value = 1;
  }

  void resetAndFetch() {
    pageNum.value = 1;
    requestFunc();
  }

  requestFunc() {
    if (currentTabIndex.value == 0) {
      getCourseData(0);
    }else{
       getCourseData(courseId.value);
    }
  }

  getCourseData(key) async {
    coursesDataList.clear();
    print('gggggggggggggggggggggggggggggggggggggggggggggggggggggggggg');
    ToastUtil.showLoadingDialog();
    var coursesResponse = HttpUtil().get(
        "${Config.zxCoursesList}?courseId=$key&orderBy=$price&orderType=$sales&pageNum=$pageNum&pageSize=$pageSize&userId=${UserOption.userId}");
   print(coursesResponse);
   print('vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv');
    coursesResponse.then((value) async {
      print(value);
      ToastUtil.closeLoadingDialog();
      ZxCourseData? entity = JsonConvert.fromJsonAsT<ZxCourseData>(value);
      if (pageNum.value == 1) {
        coursesDataList.clear();
      }
      if (pageNum.value == 1) {
        coursesDataList.value = entity!.data;
      } else {
        coursesDataList.addAll(entity!.data);
      }
      try {
        _totalCoursePages = int.parse(entity.totalPage);
      } catch (e) {
        _totalCoursePages = 1;
      }
    });
  }

  getGoodsData() {
    var goodsResponse = HttpUtil().get(
        "${Config.zxCoursesList}?goodsTypeListStr=${'1'}&orderBy=$price&orderType=$sales&pageNum=$pageNum&pageSize=$pageSize&userId=${UserOption.userId}");
    goodsResponse.then((value) async {
      ZxCourseData? entity = JsonConvert.fromJsonAsT<ZxCourseData>(value);
      if (pageNum.value == 1) {
        goodsDataList.clear();
      }
      if (pageNum.value == 1) {
        goodsDataList.value = entity!.data;
      } else {
        goodsDataList.addAll(entity!.data);
      }
      try {
        _totalGoodsPages = int.parse(entity.totalPage);
      } catch (e) {
        _totalGoodsPages = 1;
      }
    });
  }

  getBannerList() {
    var response = HttpUtil().get('${Config.bannerList}?bannerPosition=2');
    response.then((value) async {
      List<BannerBeanData>? data =
          jsonConvert.convertListNotNull<BannerBeanData>(value);
      if (data!.isNotEmpty) {
        bannerList.value = data;
      }
    });
  }

  onBannerListClick(index) {
    BannerBeanData bannerBeanData = bannerList[index];
    if (bannerBeanData.goodsId.isNotEmpty) {
      String path = HomeNavigator.goodsDetailsHaveBannerId(
              goodsId: bannerBeanData.goodsId, bannerId: bannerBeanData.id,title:'bannerId')
          .path;
      AndroidIosPlugin.openUniApp(path);
    } else {
      int? identityType = UserOption.userInfoBeanEntity?.identityType;
      if (bannerBeanData.bannerLinkUrl == 'Personalcenter/my/nomyEquity' &&
          identityType == 4) {
        String path = HomeNavigator.myEquity().path;
        AndroidIosPlugin.openUniApp(path);
      } else {
        if (bannerBeanData.bannerLinkUrl.isNotEmpty) {
          String path = "";
          // 含有指定字符串 不跳转
          if (bannerBeanData.bannerLinkUrl.contains('WWW')) {
            return;
          }
          if (bannerBeanData.bannerLinkUrl.startsWith("/")) {
            bannerBeanData.bannerLinkUrl =
                bannerBeanData.bannerLinkUrl.substring(1);
          }
          if (bannerBeanData.bannerLinkUrl.contains('?')) {
            path =
                '${bannerBeanData.bannerLinkUrl}&bannerId=${bannerBeanData.id}';
          } else {
            path =
                '${bannerBeanData.bannerLinkUrl}?bannerId=${bannerBeanData.id}';
          }
          path +=
              "&token=${UserOption.token}&app=2&nickName=${UserOption.userInfoBeanEntity?.nickName}&userCode=${UserOption.userInfoBeanEntity?.userCode}"
              "&identityType=${UserOption.userInfoBeanEntity?.identityType}&userId=${UserOption.userId}&phone=${UserOption.userInfoBeanEntity?.mobile}&appVersion=${AppVersionUtil.appVersion ?? ''}&distinctId=${AppVersionUtil.distinctId ?? ''}";
          if (path.isNotEmpty) {
            AndroidIosPlugin.openUniApp(path);
          }
        }
      }
    }
  }
  @override
  void onReady() {
    super.onReady();
    sendPageViewEvent();
  }

  @override
  void onClose() {
    sendPageLeaveEvent();
    // tabController?.removeListener(() {});
    // tabController?.dispose();
    super.onClose();
  }
}
