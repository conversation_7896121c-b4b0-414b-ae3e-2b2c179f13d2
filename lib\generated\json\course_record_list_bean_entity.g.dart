import 'package:student_end_flutter/generated/json/base/json_convert_content.dart';
import 'package:student_end_flutter/bean/course_record_list_bean_entity.dart';

CourseRecordListBeanEntity $CourseRecordListBeanEntityFromJson(
    Map<String, dynamic> json) {
  final CourseRecordListBeanEntity courseRecordListBeanEntity = CourseRecordListBeanEntity();
  final int? currentPage = jsonConvert.convert<int>(json['currentPage']);
  if (currentPage != null) {
    courseRecordListBeanEntity.currentPage = currentPage;
  }
  final int? totalPage = jsonConvert.convert<int>(json['totalPage']);
  if (totalPage != null) {
    courseRecordListBeanEntity.totalPage = totalPage;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    courseRecordListBeanEntity.size = size;
  }
  final List<CourseRecordListBeanData>? data = (json['data'] as List<dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<CourseRecordListBeanData>(
          e) as CourseRecordListBeanData)
      .toList();
  if (data != null) {
    courseRecordListBeanEntity.data = data;
  }
  final int? totalItems = jsonConvert.convert<int>(json['totalItems']);
  if (totalItems != null) {
    courseRecordListBeanEntity.totalItems = totalItems;
  }
  return courseRecordListBeanEntity;
}

Map<String, dynamic> $CourseRecordListBeanEntityToJson(
    CourseRecordListBeanEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['currentPage'] = entity.currentPage;
  data['totalPage'] = entity.totalPage;
  data['size'] = entity.size;
  data['data'] = entity.data.map((v) => v.toJson()).toList();
  data['totalItems'] = entity.totalItems;
  return data;
}

extension CourseRecordListBeanEntityExtension on CourseRecordListBeanEntity {
  CourseRecordListBeanEntity copyWith({
    int? currentPage,
    int? totalPage,
    int? size,
    List<CourseRecordListBeanData>? data,
    int? totalItems,
  }) {
    return CourseRecordListBeanEntity()
      ..currentPage = currentPage ?? this.currentPage
      ..totalPage = totalPage ?? this.totalPage
      ..size = size ?? this.size
      ..data = data ?? this.data
      ..totalItems = totalItems ?? this.totalItems;
  }
}

CourseRecordListBeanData $CourseRecordListBeanDataFromJson(
    Map<String, dynamic> json) {
  final CourseRecordListBeanData courseRecordListBeanData = CourseRecordListBeanData();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    courseRecordListBeanData.id = id;
  }
  final String? userId = jsonConvert.convert<String>(json['userId']);
  if (userId != null) {
    courseRecordListBeanData.userId = userId;
  }
  final String? studentCode = jsonConvert.convert<String>(json['studentCode']);
  if (studentCode != null) {
    courseRecordListBeanData.studentCode = studentCode;
  }
  final String? studentName = jsonConvert.convert<String>(json['studentName']);
  if (studentName != null) {
    courseRecordListBeanData.studentName = studentName;
  }
  final String? courseId = jsonConvert.convert<String>(json['courseId']);
  if (courseId != null) {
    courseRecordListBeanData.courseId = courseId;
  }
  final String? courseName = jsonConvert.convert<String>(json['courseName']);
  if (courseName != null) {
    courseRecordListBeanData.courseName = courseName;
  }
  final int? courseType = jsonConvert.convert<int>(json['courseType']);
  if (courseType != null) {
    courseRecordListBeanData.courseType = courseType;
  }
  final String? lastStudyTime = jsonConvert.convert<String>(
      json['lastStudyTime']);
  if (lastStudyTime != null) {
    courseRecordListBeanData.lastStudyTime = lastStudyTime;
  }
  final String? createdTime = jsonConvert.convert<String>(json['createdTime']);
  if (createdTime != null) {
    courseRecordListBeanData.createdTime = createdTime;
  }
  final String? updatedTime = jsonConvert.convert<String>(json['updatedTime']);
  if (updatedTime != null) {
    courseRecordListBeanData.updatedTime = updatedTime;
  }
  final int? refundStatus = jsonConvert.convert<int>(json['refundStatus']);
  if (refundStatus != null) {
    courseRecordListBeanData.refundStatus = refundStatus;
  }
  return courseRecordListBeanData;
}

Map<String, dynamic> $CourseRecordListBeanDataToJson(
    CourseRecordListBeanData entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['userId'] = entity.userId;
  data['studentCode'] = entity.studentCode;
  data['studentName'] = entity.studentName;
  data['courseId'] = entity.courseId;
  data['courseName'] = entity.courseName;
  data['courseType'] = entity.courseType;
  data['lastStudyTime'] = entity.lastStudyTime;
  data['createdTime'] = entity.createdTime;
  data['updatedTime'] = entity.updatedTime;
  data['refundStatus'] = entity.refundStatus;
  return data;
}

extension CourseRecordListBeanDataExtension on CourseRecordListBeanData {
  CourseRecordListBeanData copyWith({
    String? id,
    String? userId,
    String? studentCode,
    String? studentName,
    String? courseId,
    String? courseName,
    int? courseType,
    String? lastStudyTime,
    String? createdTime,
    String? updatedTime,
    int? refundStatus,
  }) {
    return CourseRecordListBeanData()
      ..id = id ?? this.id
      ..userId = userId ?? this.userId
      ..studentCode = studentCode ?? this.studentCode
      ..studentName = studentName ?? this.studentName
      ..courseId = courseId ?? this.courseId
      ..courseName = courseName ?? this.courseName
      ..courseType = courseType ?? this.courseType
      ..lastStudyTime = lastStudyTime ?? this.lastStudyTime
      ..createdTime = createdTime ?? this.createdTime
      ..updatedTime = updatedTime ?? this.updatedTime
      ..refundStatus = refundStatus ?? this.refundStatus;
  }
}