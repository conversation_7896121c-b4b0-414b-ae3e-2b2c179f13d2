<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>App Uses Non-ExemptEncryption</key>
	<false/>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>鼎校甄选</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleLocalizations</key>
	<array>
		<string>zh_CN</string>
		<string>en</string>
	</array>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>2.2.4</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>weixin</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>wx79465579ceea44ae</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleIdentifier</key>
			<string></string>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>com.studentmetting.dingxiao</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>zhengxuanUrlscheme</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>alipay</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>allinpaysdk</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>2</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>zhengxuanUrlscheme</string>
		<string>wechat</string>
		<string>tel</string>
		<string>alipay</string>
		<string>weixinURLParamsAPI</string>
		<string>weixinULAPI</string>
		<string>weixin</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>NSAppleMusicUsageDescription</key>
	<string>我们需要访问您的媒体库以便于课程学习，为您提供更好的服务</string>
	<key>NSCalendarsUsageDescription</key>
	<string>我们要访问你的日历，以便于添加课程提醒，精彩内容不会错过</string>
	<key>NSCameraUsageDescription</key>
	<string>我们需要访问您的相机以拍摄图片，您就能快速设置个人头像、背景图，或者便捷地上传图片参与活动/创作内容。丰富您的个人空间，从这里开始</string>
	<key>NSContactsUsageDescription</key>
	<string>我们需要添加联系人信息以便于添加好友，快速沟通</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>我们需要您的位置，以便于更新收货地址等信息，为您提供更便捷的服务</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>我们需要您的位置，以便于更新收货地址等信息，为您提供更便捷的服务</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>我们需要访问您的麦克风来录制音频，以便于更好的进行课程复习等</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>我们需要访问您的照片库，以便将编辑后的照片保存到您的设备中。</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>我们需要访问您的照片库，您就能快速设置个人头像、背景图，或者便捷地上传图片参与活动/创作内容。丰富您的个人空间，从这里开始</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>remote-notification</string>
	</array>
	<key>UILaunchScreen</key>
	<dict>
		<key>UIImageName</key>
		<string>LaunchImage</string>
	</dict>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen.storyboard</string>
	<key>UIRequiresFullScreen</key>
	<true/>
	<key>UIStatusBarHidden</key>
	<false/>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
	<key>UIUserInterfaceStyle</key>
	<string>Light</string>
</dict>
</plist>
