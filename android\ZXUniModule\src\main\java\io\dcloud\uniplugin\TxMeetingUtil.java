package io.dcloud.uniplugin;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.ActivityInfo;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.util.Log;
import android.widget.Toast;

import com.arthenica.mobileffmpeg.ExecuteCallback;
import com.arthenica.mobileffmpeg.FFmpeg;
import com.blankj.rxbus.RxBus;
import com.google.gson.Gson;
import com.tencent.tcic.TBSSdkManageCallback;
import com.tencent.tcic.TCICClassConfig;
import com.tencent.tcic.TCICConstants;
import com.tencent.tcic.TCICEventListener;
import com.tencent.tcic.TCICInitProvider;
import com.tencent.tcic.TCICManager;
import com.tencent.tcic.pages.TCICClassActivity;
import com.tencent.trtc.TRTCCloud;
import com.tencent.trtc.TRTCCloudDef;
import com.tencent.trtc.TRTCCloudListener;
import com.tencent.trtc.TRTCStatistics;
import com.tencent.ugc.videobase.utils.LocalBroadcastManager;

import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.ref.WeakReference;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class TxMeetingUtil {
    public  static String licenseKey = "h2wRJQRkQ+7/uMmZR9qRRmWuMh02GN6xDsEHUbebnytCxN5XvZAJowp1TWsehW8tUUtstcG9BNwHvdVF9L7arNJemrkuSGiwcptE/lntMtcVh123zclVq1vPIvg/cy9f";
    public static int schoolId = 3627194;
    public static boolean isX5Init = false;
    static List<byte[]> dataList = new ArrayList<>();
    public static boolean recordFinish = true;

    public static Context mContext;

    public static int countDown = 59;
    public static TCICEventListener tcicEventListener;
    public static JsCustomMsg jsCustomMsg;

    public static String userId = "";

    public static TCICClassConfig initConfig;
    public  static void checkX5Init(Context context, String meetingParams){
        mContext = context;
        if (TCICInitProvider.isMainProcess(context)) {
            //初始化X5内核
            TCICManager.getInstance().initX5Core(licenseKey, new TBSSdkManageCallback() {

                @Override
                public void onCoreInitFinished() {
                }

                @Override
                public void onViewInitFinished(boolean isX5Core) {
                    //X5内核初始化完成,可以进课堂
                    if (!isX5Init) {
                        joinMeeting(context,meetingParams);
                        isX5Init = true;
                    }
                }
            });
        }
    }

    public static void  joinMeeting(Context context,String meetingParams){
        Log.i("zgj", "joinMeeting: "+meetingParams);
        TxMeetingParams txMeetingParams = new Gson().fromJson(meetingParams,TxMeetingParams.class);
        if (txMeetingParams.getSchoolId().equals("") || txMeetingParams.getClassId().equals("") ||txMeetingParams.getUserId().equals("")|| txMeetingParams.getToken().equals("")) {
            Toast.makeText(context, "会议参数异常", Toast.LENGTH_SHORT).show();
            return;
        }
        userId = txMeetingParams.getUserId();
        Map<String,String> customParams = new HashMap<>();
        customParams.put("shebei","android");
        customParams.put("role",txMeetingParams.getRole());
        customParams.put("token_key",txMeetingParams.getToken_key());
        customParams.put("studentCode",txMeetingParams.getStudentCode());
        customParams.put("classId",txMeetingParams.getClassId());
        //X5内核初始化完成,可以进课堂
        tcicEventListener =new TCICEventListener() {
            @Override
            public void onClassExited() {

            }
            @Override
            public void onRecvCustomMessage(String msg) {
                Log.i("zgj", "收到消息:" +msg);
                if (msg.contains("stopRecording")) {
                    recordFinish = true;
                    jsCustomMsg = null;
                    jsCustomMsg = new Gson().fromJson(msg,JsCustomMsg.class);
                    if (userId.equals(jsCustomMsg.getCustomMsg().params.userUuid)){
                        stopRecord();
                    }
                } else if (msg.contains("startRecording")) {
                    jsCustomMsg = null;
                    jsCustomMsg = new Gson().fromJson(msg,JsCustomMsg.class);
                    if (userId.equals(jsCustomMsg.getCustomMsg().params.userUuid)){
                        startRecord();
                    }
                }else if (msg.contains("Joined_Class")){
//                    ToFlutterMsg toFlutterMsg = new ToFlutterMsg();
//                    toFlutterMsg.setRole(txMeetingParams.getRole());
//                    toFlutterMsg.setToken_key(txMeetingParams.getToken_key());
//                    toFlutterMsg.setStudentCode(txMeetingParams.getStudentCode());
//                    toFlutterMsg.setClassId(txMeetingParams.getClassId());
                    String sendMsg = new Gson().toJson(customParams).replaceAll("\\\\","");
                    TCICManager.getInstance().sendCustomMessage(sendMsg);
                    Log.i("zgj", sendMsg +"---------"+ new Gson().toJson(customParams) );
                }
            }
        };
        TRTCCloudDef.TRTCAudioFrameCallbackFormat  format=new TRTCCloudDef.TRTCAudioFrameCallbackFormat();
        format.sampleRate = 16000;
        format.channel = 1;
        TRTCCloud.sharedInstance(context).setCapturedAudioFrameCallbackFormat(format);
        TCICManager.getInstance().addTCICEventListener(tcicEventListener);
        TRTCCloud.sharedInstance(context).setAudioFrameListener(new TRTCCloudListener.TRTCAudioFrameListener() {
            @Override
            public void onCapturedAudioFrame(TRTCCloudDef.TRTCAudioFrame trtcAudioFrame) {
                if (recordFinish) {
                    return;
                }
                dataList.add(trtcAudioFrame.data);
            }

            @Override
            public void onLocalProcessedAudioFrame(TRTCCloudDef.TRTCAudioFrame trtcAudioFrame) {
            }

            @Override
            public void onRemoteUserAudioFrame(TRTCCloudDef.TRTCAudioFrame trtcAudioFrame, String s) {

            }

            @Override
            public void onMixedPlayAudioFrame(TRTCCloudDef.TRTCAudioFrame trtcAudioFrame) {
            }

            @Override
            public void onMixedAllAudioFrame(TRTCCloudDef.TRTCAudioFrame trtcAudioFrame) {
            }

            @Override
            public void onVoiceEarMonitorAudioFrame(TRTCCloudDef.TRTCAudioFrame trtcAudioFrame) {
            }
        });
        Intent intent = new Intent(context, TCICClassActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_SINGLE_TOP);
        Bundle bundle = new Bundle();
        initConfig = new TCICClassConfig.Builder()
                .schoolId(Integer.parseInt(txMeetingParams.getSchoolId()))
                .classId(Long.parseLong(txMeetingParams.getClassId()))
                .userId(txMeetingParams.getUserId())
                .token(txMeetingParams.getToken())
                .preferPortrait(true)
//                .schoolId( 3992451  )
//                .classId(355429553)
//                .userId("31LeB8fnwN16b2vSbFeWzLezyDr")
//                .token( "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTU5MTI0NDEsImlhdCI6MTc1NTMwNzY0MSwiaXNzIjoibVZxUjltUlptdDlLMlpsV2FaUzJwRFI2MWZRS1AxSk4iLCJzY2hvb2xfaWQiOjM5OTI0NTEsInVzZXJfaWQiOiIzMUxlQjhmbndOMTZiMnZTYkZlV3pMZXp5RHIifQ.1YtNIghVPQLfGfpWiwfsRqXmJWGjHYqI2S48UFmR24s")
                .customParams(customParams)
                .scene(txMeetingParams.getScene())
                .build();
        bundle.putParcelable(TCICConstants.KEY_INIT_CONFIG, initConfig);
        intent.putExtras(bundle);
        context.startActivity(intent);
//        startRecord();
    }
    public static void saveToPCMFile(Context context,List<byte[]> dataList) {
        String filePath = context.getExternalFilesDir(null).getPath()+"/audio.pcm";
        try (FileOutputStream fos = new FileOutputStream(filePath)) {
            for (byte[] data : dataList) {
                fos.write(data);
            }
            fos.flush();
            Log.i("zgj","保存成功"+dataList.size());
        } catch (IOException e) {
            e.printStackTrace();
        }
//        AudioConverter.resamplePcm(context.getExternalFilesDir(null).getPath()+"/audio.pcm", context.getExternalFilesDir(null).getPath()+"/16000audio.pcm",
//                44100,  // 原始采样率
//                2,      // 原始声道数（单声道）
//                16,     // 原始位深（16位）
//                16000,  // 新采样率
//                1       // 新声道数（单声道）
//        );
    }
    public static CountDownTimer countDownTimer;
    public static void startRecord(){
        recordFinish = false;
        dataList.clear();
        countDownTimer = new CountDownTimer(countDown*1000L, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                // 在这里执行每秒需要执行的操作
                Log.i("zgj", "计时器运行中: " + millisUntilFinished / 1000 + "秒");
            }

            @Override
            public void onFinish() {
                // 计时器结束后执行的操作
                if(!recordFinish){
                    recordFinish = true;
                    stopRecord();
                }
            }
        };
        countDownTimer.start();
    }


    public static void stopRecord(){
        countDown = 59;
        saveToPCMFile(mContext,dataList);
        countDownTimer.cancel();
        TxToFlutterParams txMeetingParams =jsCustomMsg.getCustomMsg();
        txMeetingParams.event = "audioRecord";
        txMeetingParams.params.filePath = mContext.getExternalFilesDir(null).getPath()+"/audio.pcm";
        RxBus.getDefault().post(txMeetingParams);
    }
}
