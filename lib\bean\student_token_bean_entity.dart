import 'package:student_end_flutter/generated/json/base/json_field.dart';
import 'package:student_end_flutter/generated/json/student_token_bean_entity.g.dart';
import 'dart:convert';
export 'package:student_end_flutter/generated/json/student_token_bean_entity.g.dart';

@JsonSerializable()
class StudentTokenBeanEntity {
	late String token;

	StudentTokenBeanEntity();

	factory StudentTokenBeanEntity.fromJson(Map<String, dynamic> json) => $StudentTokenBeanEntityFromJson(json);

	Map<String, dynamic> toJson() => $StudentTokenBeanEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}