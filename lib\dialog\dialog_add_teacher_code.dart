
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:student_end_flutter/utils/utils.dart';
import '../bean/student_info_bean_entity.dart';
import '../common/CommonUtil.dart';
import '../res/colors.dart';


class AddTeacherCodeDialog extends StatefulWidget {
  var codeData;
  AddTeacherCodeDialog(this.codeData);

  @override
  _AddTeacherCodeDialogState createState() =>
      _AddTeacherCodeDialogState();
}

class _AddTeacherCodeDialogState extends State<AddTeacherCodeDialog> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    double screenHeight = MediaQuery.of(context).size.height;
    return SingleChildScrollView(
      child: Column(
        children: [
          Container(
            alignment: Alignment.bottomCenter,
            height: screenHeight/3.5,
            child: Image(image: AssetImage("assets/dialog_icon.png")),
          ),
          Container(
            width: double.infinity,
            margin: EdgeInsets.only(left: 20.w, right: 20.w),
            padding: EdgeInsets.all(10.w),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(15)),
                color: Colors.white),
            child: Column(
              children: [
                SizedBox(
                  height: 40.h,
                  width:double.infinity,
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      Text(
                        '温馨提示',
                        style: TextStyle(
                            decoration: TextDecoration.none,
                            fontSize: 16.sp,
                            fontFamily: "M",
                            color: Colors.black),
                      ),
                      Positioned(
                        top: 0,
                        right: 0,
                        child: GestureDetector(
                          onTap: () {
                            Navigator.pop(context);
                          },
                          child: Image.asset(
                            'assets/zx/close.png',
                            width: 26.w,
                            height: 26.w,
                          ),
                        ),
                      )
                    ],
                  ),
                ),
                CachedNetworkImage(
                  imageUrl: widget.codeData["qrcode"],
                  height: 120.h,
                  width: 120.w,
                ),
                RichText(
                  textAlign:TextAlign.center,
                  text: TextSpan(
                    style: TextStyle(color: AppColors.c8c8c8, fontSize: 14.sp),
                    children: <TextSpan>[
                      TextSpan(text: '请用'),
                      TextSpan(
                        text: '微信',
                        style: TextStyle(color: Colors.red, fontSize: 14.sp),
                      ),
                      TextSpan(text: '扫码添加教练\n系统才会为'),
                      TextSpan(text: widget.codeData["studentName"] ?? "**"),
                      TextSpan(text: '学员建立专属体验群\n请务必添加哦~'),
                    ],
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    Utils.saveImageToGallery(widget.codeData["qrcode"]);
                  },
                  child: Container(
                    alignment: Alignment.center,
                    margin: EdgeInsets.only(left: 5.w,right: 5.w,top: 20.h,bottom: 10.h),
                    width: 180.w,
                    height: 30,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                          colors: [AppColors.f78d2aa, AppColors.f368e5c],
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter),
                      borderRadius: BorderRadius.all(Radius.circular(15)),
                    ),
                    child: Text(
                      '确定',
                      //保存图片
                      style: TextStyle(
                          decoration: TextDecoration.none,
                          fontSize: 14.sp,
                          fontFamily: "R",
                          color: Colors.white),
                    ),
                  ),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}
