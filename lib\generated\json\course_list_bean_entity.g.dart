import 'package:student_end_flutter/generated/json/base/json_convert_content.dart';
import 'package:student_end_flutter/bean/course_list_bean_entity.dart';

CourseListBeanEntity $CourseListBeanEntityFromJson(Map<String, dynamic> json) {
  final CourseListBeanEntity courseListBeanEntity = CourseListBeanEntity();
  final int? currentPage = jsonConvert.convert<int>(json['currentPage']);
  if (currentPage != null) {
    courseListBeanEntity.currentPage = currentPage;
  }
  final int? totalPage = jsonConvert.convert<int>(json['totalPage']);
  if (totalPage != null) {
    courseListBeanEntity.totalPage = totalPage;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    courseListBeanEntity.size = size;
  }
  final List<CourseListBeanData>? data = (json['data'] as List<dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<CourseListBeanData>(e) as CourseListBeanData)
      .toList();
  if (data != null) {
    courseListBeanEntity.data = data;
  }
  final int? totalItems = jsonConvert.convert<int>(json['totalItems']);
  if (totalItems != null) {
    courseListBeanEntity.totalItems = totalItems;
  }
  return courseListBeanEntity;
}

Map<String, dynamic> $CourseListBeanEntityToJson(CourseListBeanEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['currentPage'] = entity.currentPage;
  data['totalPage'] = entity.totalPage;
  data['size'] = entity.size;
  data['data'] = entity.data.map((v) => v.toJson()).toList();
  data['totalItems'] = entity.totalItems;
  return data;
}

extension CourseListBeanEntityExtension on CourseListBeanEntity {
  CourseListBeanEntity copyWith({
    int? currentPage,
    int? totalPage,
    int? size,
    List<CourseListBeanData>? data,
    int? totalItems,
  }) {
    return CourseListBeanEntity()
      ..currentPage = currentPage ?? this.currentPage
      ..totalPage = totalPage ?? this.totalPage
      ..size = size ?? this.size
      ..data = data ?? this.data
      ..totalItems = totalItems ?? this.totalItems;
  }
}

CourseListBeanData $CourseListBeanDataFromJson(Map<String, dynamic> json) {
  final CourseListBeanData courseListBeanData = CourseListBeanData();
  final String? courseId = jsonConvert.convert<String>(json['courseId']);
  if (courseId != null) {
    courseListBeanData.courseId = courseId;
  }
  final String? planId = jsonConvert.convert<String>(json['planId']);
  if (planId != null) {
    courseListBeanData.planId = planId;
  }
  final String? courseName = jsonConvert.convert<String>(json['courseName']);
  if (courseName != null) {
    courseListBeanData.courseName = courseName;
  }
  final String? courseType = jsonConvert.convert<String>(json['courseType']);
  if (courseType != null) {
    courseListBeanData.courseType = courseType;
  }
  final String? courseTime = jsonConvert.convert<String>(json['courseTime']);
  if (courseTime != null) {
    courseListBeanData.courseTime = courseTime;
  }
  final int? status = jsonConvert.convert<int>(json['status']);
  if (status != null) {
    courseListBeanData.status = status;
  }
  final String? teacherId = jsonConvert.convert<String>(json['teacherId']);
  if (teacherId != null) {
    courseListBeanData.teacherId = teacherId;
  }
  final String? teacherName = jsonConvert.convert<String>(json['teacherName']);
  if (teacherName != null) {
    courseListBeanData.teacherName = teacherName;
  }
  final String? teacherPhoto = jsonConvert.convert<String>(
      json['teacherPhoto']);
  if (teacherPhoto != null) {
    courseListBeanData.teacherPhoto = teacherPhoto;
  }
  final String? meetingId = jsonConvert.convert<String>(json['meetingId']);
  if (meetingId != null) {
    courseListBeanData.meetingId = meetingId;
  }
  final String? meetingNum = jsonConvert.convert<String>(json['meetingNum']);
  if (meetingNum != null) {
    courseListBeanData.meetingNum = meetingNum;
  }
  final String? feedback = jsonConvert.convert<String>(json['feedback']);
  if (feedback != null) {
    courseListBeanData.feedback = feedback;
  }
  final String? studentCode = jsonConvert.convert<String>(json['studentCode']);
  if (studentCode != null) {
    courseListBeanData.studentCode = studentCode;
  }
  final String? studentName = jsonConvert.convert<String>(json['studentName']);
  if (studentName != null) {
    courseListBeanData.studentName = studentName;
  }
  final String? classPlanStudyId = jsonConvert.convert<String>(
      json['classPlanStudyId']);
  if (classPlanStudyId != null) {
    courseListBeanData.classPlanStudyId = classPlanStudyId;
  }
  final int? oneToManyType = jsonConvert.convert<int>(
      json['oneToManyType']);
  if (oneToManyType != null) {
    courseListBeanData.oneToManyType = oneToManyType;
  }
  final int? leaveStatus = jsonConvert.convert<int>(json['leaveStatus']);
  if (leaveStatus != null) {
    courseListBeanData.leaveStatus = leaveStatus;
  }
  final bool? experience = jsonConvert.convert<bool>(json['experience']);
  if (experience != null) {
    courseListBeanData.experience = experience;
  }
  final int? lessonsFlag = jsonConvert.convert<int>(json['lessonsFlag']);
  if (lessonsFlag != null) {
    courseListBeanData.lessonsFlag = lessonsFlag;
  }

  final String? planStudyId = jsonConvert.convert<String>(
      json['planStudyId']);
  if (planStudyId != null) {
    courseListBeanData.planStudyId = planStudyId;
  }
  return courseListBeanData;
}

Map<String, dynamic> $CourseListBeanDataToJson(CourseListBeanData entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['courseId'] = entity.courseId;
  data['planId'] = entity.planId;
  data['courseName'] = entity.courseName;
  data['courseType'] = entity.courseType;
  data['courseTime'] = entity.courseTime;
  data['status'] = entity.status;
  data['teacherId'] = entity.teacherId;
  data['teacherName'] = entity.teacherName;
  data['teacherPhoto'] = entity.teacherPhoto;
  data['meetingId'] = entity.meetingId;
  data['meetingNum'] = entity.meetingNum;
  data['feedback'] = entity.feedback;
  data['studentCode'] = entity.studentCode;
  data['studentName'] = entity.studentName;
  data['classPlanStudyId'] = entity.classPlanStudyId;
  data['oneToManyType'] = entity.oneToManyType;
  data['leaveStatus'] = entity.leaveStatus;
  data['experience'] = entity.experience;
  data['lessonsFlag'] = entity.lessonsFlag;
  data['planStudyId'] = entity.planStudyId;
  return data;
}

extension CourseListBeanDataExtension on CourseListBeanData {
  CourseListBeanData copyWith({
    String? courseId,
    String? planId,
    String? courseName,
    String? courseType,
    String? courseTime,
    int? status,
    String? teacherId,
    String? teacherName,
    String? teacherPhoto,
    String? meetingId,
    String? meetingNum,
    String? feedback,
    String? studentCode,
    String? studentName,
    String? classPlanStudyId,
    int? oneToManyType,
    int? leaveStatus,
    bool? experience,
    int? lessonsFlag,
    String? planStudyId,
  }) {
    return CourseListBeanData()
      ..courseId = courseId ?? this.courseId
      ..planId = planId ?? this.planId
      ..courseName = courseName ?? this.courseName
      ..courseType = courseType ?? this.courseType
      ..courseTime = courseTime ?? this.courseTime
      ..status = status ?? this.status
      ..teacherId = teacherId ?? this.teacherId
      ..teacherName = teacherName ?? this.teacherName
      ..teacherPhoto = teacherPhoto ?? this.teacherPhoto
      ..meetingId = meetingId ?? this.meetingId
      ..meetingNum = meetingNum ?? this.meetingNum
      ..feedback = feedback ?? this.feedback
      ..studentCode = studentCode ?? this.studentCode
      ..studentName = studentName ?? this.studentName
      ..classPlanStudyId = classPlanStudyId ?? this.classPlanStudyId
      ..oneToManyType = oneToManyType ?? this.oneToManyType
      ..leaveStatus = leaveStatus ?? this.leaveStatus
      ..experience = experience ?? this.experience
      ..lessonsFlag = lessonsFlag ?? this.lessonsFlag
      ..planStudyId = planStudyId ?? this.planStudyId;
  }
}