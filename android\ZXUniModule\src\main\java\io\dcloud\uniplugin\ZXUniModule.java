package io.dcloud.uniplugin;

import android.app.Activity;
import android.content.Intent;
import android.util.Log;

import com.alibaba.fastjson.JSONObject;
import com.blankj.rxbus.RxBus;

import java.util.HashMap;
import java.util.Map;

import io.dcloud.feature.uniapp.annotation.UniJSMethod;
import io.dcloud.feature.uniapp.bridge.UniJSCallback;
import io.dcloud.feature.uniapp.common.UniModule;


public class ZXUniModule extends UniModule {
    String TAG = "ZXUniModule";


    //run ui thread
    @UniJSMethod(uiThread = true)
    public void testAsyncFunc(JSONObject options, UniJSCallback callback) {
        Log.e(TAG, "testAsyncFunc worked --"+ options);
        if(callback != null) {
            JSONObject data = new JSONObject();
            data.put("code", "success");
//            callback.invoke(data);
            callback.invokeAndKeepAlive(data);
        }
    }

    //run JS thread
    //payType  alipay 支付宝    wxpay微信
    @UniJSMethod (uiThread = false)
    public JSONObject openTLPayFunc(String jsonString,String payType){
        Log.e(TAG, "console openTLPayFunc jsonString --"+ jsonString+"---------payType============="+payType);
        JSONObject data = new JSONObject();
        data.put("code", "success");
        Intent intent = new Intent();
        intent.setClassName(mUniSDKInstance.getContext(), "com.dxznjy.alading.activity.PayCallBackActivity");
        intent.putExtra("payData",jsonString);
        intent.putExtra("payType",payType);
        ((Activity)mUniSDKInstance.getContext()).startActivityForResult(intent, REQUEST_CODE);
//        RxBus.getDefault().post(new PayEvent(payType,jsonString));
        return data;
    }

    //run JS thread
    //优惠券使用返回
    @UniJSMethod (uiThread = false)
    public JSONObject appUseCoupon(String path,String evtName){
        Log.e(TAG, "console appUsecoupon jsonString --"+ path+"---------evtName============="+evtName);
        JSONObject data = new JSONObject();
        data.put("code", "success");
        RxBus.getDefault().post(new JumpToFlutter(0));
        return data;
    }

    @UniJSMethod (uiThread = false)
    public JSONObject goZxGoods(){
        Log.e(TAG, "console goZxGoods jsonString --");
        JSONObject data = new JSONObject();
        data.put("code", "success");
        RxBus.getDefault().post(new JumpToFlutter(1));
        return data;
    }
    @UniJSMethod (uiThread = false)
    public JSONObject appShare(String params,int shareType){
        JSONObject data = new JSONObject();
        RxBus.getDefault().post(new AppShare(shareType,params));
        return data;
    }


    @UniJSMethod (uiThread = false)
    public JSONObject customerService(){
        JSONObject data = new JSONObject();
        RxBus.getDefault().post(new CustomerService());
        return data;
    }




    ///发送消息给uni
    public  void sendEvent(String errorCode){
        Map<String,Object> params=new HashMap<>();
        params.put("errorCode",errorCode);
        if (errorCode.isEmpty()){
            params.put("payType","alipay");
        }
        mUniSDKInstance.fireGlobalEventCallback("appPayCallBack", params);
    }
    public static int REQUEST_CODE = 1000; //数据返回标识code
    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        if(requestCode == REQUEST_CODE && data.hasExtra("errorCode")) {
            String errorCode=data.getStringExtra("errorCode");
            Log.e("fxiaoli", "原生页面返回----"+data.getStringExtra("errorCode"));
            sendEvent(errorCode);
        } else {
            super.onActivityResult(requestCode, resultCode, data);
        }
    }

}
