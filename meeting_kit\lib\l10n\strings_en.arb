{"globalAppName": "NetEase Meeting", "globalDelete": "delete", "globalNothing": "None", "globalCancel": "Cancel", "globalAdd": "Add", "globalClose": "Close", "globalOpen": "Open", "globalFail": "Failed", "globalYes": "Yes", "globalNo": "No", "globalSave": "Save", "globalDone": "Complete", "globalNotify": "Notification", "globalSure": "OK", "globalIKnow": "Got it", "globalCopy": "Copy", "globalCopySuccess": "<PERSON><PERSON> succeeded", "globalEdit": "Edit", "globalGotIt": "Got it", "globalNotWork": "Unable to use {permissionName}", "globalNeedPermissionTips": "The function requires {permissionName},  allow {title} to access your {permissionName} permission.", "globalToSetUp": "Go to", "globalNoPermission": "No permission", "globalDays": "days", "globalHours": "hours", "globalMinutes": "minutes", "globalViewMessage": "View Message", "globalNoLongerRemind": "Don't Remind Me Again", "globalOperationFail": "Operation failed", "globalOperationNotSupportedInMeeting": "This operation is not supported in the meeting.", "globalClear": "Clear", "globalSearch": "Search", "globalReject": "Reject", "globalCancelled": "cancelled", "globalClearAll": "Clear all", "globalStart": "Turn on", "globalTips": "Tips", "globalNetworkUnavailableCheck": "Network connection failed, please check your network connection!", "globalSubmit": "Submit", "globalGotoSettings": "Go to Settings", "globalPhotosPermissionRationale": "Apply for permission to upload pictures or modify your profile picture", "globalPhotosPermission": "Albums unavailable", "globalSend": "Send", "globalPause": "Pause", "globalNoContent": "No content", "meetingBeauty": "Beauty", "meetingBeautyLevel": "Level", "meetingJoinTips": "Joining...", "meetingQuit": "End", "meetingDefalutTitle": "Video Meeting", "meetingJoinFail": "Failed to join the meeting", "meetingHostKickedYou": "You are removed by HOST or switched to another device, you have left the meeting", "meetingMicphoneNotWorksDialogTitle": "Microphone unavailable", "meetingMicphoneNotWorksDialogMessage": "You are using the microphone. To speak,\n click Unmute and speak", "meetingFinish": "End", "meetingLeave": "Leave", "meetingLeaveFull": "Leave", "meetingSpeakingPrefix": "Speaking:", "meetingLockMeetingByHost": "The meeting is locked. New participants cannot join the meeting", "meetingLockMeetingByHostFail": "Failed to lock the meeting", "meetingUnLockMeetingByHost": "The meeting is unlocked. New participants can join the meeting", "meetingUnLockMeetingByHostFail": "Failed to unlock the meeting", "meetingLock": "Lock", "meetingMore": "More", "meetingPassword": "Meeting Password", "meetingEnterPassword": "Enter the meeting password", "meetingWrongPassword": "Incorrect code", "meetingNum": "Meeting ID", "meetingShortNum": "Short Meeting ID", "meetingInfoDesc": "The meeting is being encrypted and protected", "meetingAlreadyHandsUpTips": "You are raising hand, waiting for response", "meetingHandsUpApply": "Raise hand", "meetingRaiseHand": "Raise hand", "meetingCancelHandsUp": "Lower hand", "meetingCancelHandsUpConfirm": "Are you sure to put your hands down?", "meetingHandsUpDown": "Lower hand", "meetingHandsUpDownAll": "Lower all hands", "meetingHandsUpDownAllSuccess": "All participants' hands lowered", "meetingHandsUpDownAllFail": "Failed to lower all hands", "meetingInHandsUp": "Raising hand", "meetingHandsUpFail": "Failed to raise hand", "meetingHandsUpSuccess": "You are raising hand, waiting for response", "meetingCancelHandsUpFail": "Failed to lower hand", "meetingHostRejectAudioHandsUp": "The host rejected your request", "meetingHandsUpNumber": "{num} participants are raising hands", "meetingSip": "SIP", "meetingInviteUrl": "Meeting URL", "meetingInvitePageTitle": "Add Participants", "meetingSipNumber": "SIP Call", "meetingMobileDialInTitle": "Phone Call", "meetingMobileDialInMsg": "Dial {phoneNumber}", "meetingInputSipNumber": "Enter {sipNumber} to join the meeting", "meetingSipHost": "SIP Address", "meetingInvite": "Invite", "meetingInviteListTitle": "Invitation List", "meetingInvitationSendSuccess": "Invitation sent", "meetingInvitationSendFail": "Invitation failed", "meetingRemovedByHost": "You are removed", "meetingCloseByHost": "Meeting ended", "meetingWasInterrupted": "The meeting was interrupted", "meetingSyncDataError": "Failed to sync the room information", "meetingLeaveMeetingBySelf": "Leave", "meetingClosed": "Meeting is closed", "meetingConnectFail": "Connection failed", "meetingJoinTimeout": "Joining meeting timeout, try again later", "meetingEndOfLife": "Meeting is closed because the meeting duration reached the upper limit.", "meetingEndTip": "Remaining", "meetingReuseIMNotSupportAnonymousJoinMeeting": "IM reuse does not support anonymous login", "meetingInviteDialogTitle": "Meeting Invite", "meetingInviteContentCopySuccess": "Meeting invitation copied", "meetingInviteTitle": "Invite your to join the meeting", "meetingSubject": "Subject", "meetingTime": "Time", "meetingInvitationUrl": "Meeting URL", "meetingCopyInvite": "Copy invitation", "meetingInternalSpecial": "Internal", "loginOnOtherDevice": "Switched to another device", "authInfoExpired": "Authorization expired, please log in again", "meetingCamera": "Camera", "meetingMicrophone": "Microphone", "meetingBluetooth": "Bluetooth", "meetingPhoneState": "Phone", "meetingNeedRationaleAudioPermission": "Audio-video meeting needs to apply for {permission} permission for audio communication.", "meetingNeedRationaleVideoPermission": "Audio-video meeting needs to apply for {permission} permission for video communication.", "meetingNeedRationalePhotoPermission": "Need to apply for photo permission for the virtual background (adding and changing background images) function in the meeting", "meetingDisconnectAudio": "Disconnect Audio", "meetingReconnectAudio": "Audio", "meetingDisconnectAudioTips": "To turn off the conference sound, you can click on \"Disconnect Audio\" in More", "meetingNotificationContentTitle": "Video Meeting", "meetingNotificationContentText": "Meeting is ongoing", "meetingNotificationContentTicker": "Video meeting", "meetingNotificationChannelId": "ne_meeting_channel", "meetingNotificationChannelName": "Video meeting notification", "meetingNotificationChannelDesc": "Video meeting notification", "meetingUserJoin": "{user<PERSON><PERSON>} joined the meeting.", "meetingUserLeave": "{user<PERSON><PERSON>} left the meeting.", "meetingStartAudioShare": "Start Sharing system audio", "meetingStopAudioShare": "Stop sharing system audio", "meetingSwitchFcusView": "Switch to Focus view", "meetingSwitchGalleryView": "Switch to Gallery view", "meetingNoSupportSwitch": "This device does not support switching modes", "meetingFuncNotAvailableWhenInCallState": "Cannot use this feature while on a call", "meetingRejoining": "Rejoining", "meetingSecurity": "Security", "meetingManagement": "Meeting Management", "meetingWatermark": "Meeting Watermark", "meetingBeKickedOutByHost": "The host has removed you from the meeting", "meetingBeKickedOut": "Removed from the meeting", "meetingClickOkToClose": "Click OK and the page closes automatically", "meetingLeaveConfirm": "Are you sure you want to leave this meeting?", "meetingAppointNewHost": "Assign a new host", "meetingAppointAndLeave": "Assign and leave", "meetingWatermarkEnabled": "You have enabled watermark", "meetingWatermarkDisabled": "You have disabled watermark", "meetingInfo": "Meeting information", "meetingNickname": "Nickname", "meetingHostChangeYourMeetingName": "The host has changed your name", "meetingIsInCall": "Answering the phone now", "meetingPinView": "Lock video", "meetingPinViewTip": "The video is locked, tap unlock at {corner} to unlock", "meetingTopLeftCorner": "top left corner", "meetingBottomRightCorner": "bottom right corner", "meetingUnpinView": "Unlock video", "meetingUnpinViewTip": "Video unlocked", "meetingUnpin": "Unlock", "meetingPinFailedByFocus": "The host has set the focus video, and this operation is not supported.", "meetingBlacklist": "Meeting Blacklist", "meetingBlacklistDetail": "Once turned on, users marked \"No Re-joining\" will not be able to join the meeting.", "unableMeetingBlacklistTitle": "Are you sure to turn off the meeting blacklist?", "unableMeetingBlacklistTip": "After turned off, the blacklist will be cleared, and users marked \"No Re-joining\" can re-join the meeting.", "meetingNotAllowedToRejoin": "Not allowed to join the meeting again.", "meetingAllowMembersTo": "Allow participants to", "meetingEmojiResponse": "React", "meetingAllowEmojiResponse": "Allow emojis and reactions", "meetingRejectEmojiResponse": "Not allow emojis and reactions", "meetingChat": "Group and Private Chats", "meetingChatEnabled": "Chat has been enabled.", "meetingChatDisabled": "<PERSON><PERSON> has been disabled.", "meetingReclaimHost": "Reclaim Host", "meetingReclaimHostCancel": "Not Now", "meetingReclaimHostTip": "{user} is the host now, and withdrawing host privileges may interrupt screen sharing, etc", "meetingUserIsNowTheHost": "{user} is the host now.", "meetingGuestJoin": "Guest Mode", "meetingGuestJoinSecurityNotice": "Guest mode enabled, please pay attention to the security of meeting", "meetingGuestJoinEnableTip": "External visitors will be allowed to attend the meeting when enabled.", "meetingGuestJoinEnabled": "Guest mode has been enabled", "meetingGuestJoinDisabled": "Guest mode  has been closed", "meetingGuestJoinConfirm": "Are you sure to enable guest mode?", "meetingGuestJoinConfirmTip": "External visitors will be allowed to attend the meeting when enabled.", "meetingSearchNotFound": "No search results are available", "meetingGuestJoinSupported": "External visitors can join this meeting", "meetingGuest": "Guest", "meetingGuestJoinNamePlaceholder": "Enter the meeting nickname", "meetingAppInvite": "{userName} invites you to join", "meetingAudioJoinAction": "Voice", "meetingVideoJoinAction": "Video", "meetingMaxMembers": "Maximum participants", "speakerVolumeMuteTips": "The speaker device is silent. Please check whether the system speakers have been unmuted and adjusted to the appropriate volume.", "meetingAnnotationPermissionEnabled": "Annotate", "meetingMemberMaxTip": "The maximum number of participants has been reached", "meetingIsUnderGoing": "The meeting is still ongoing. Invalid operation", "unauthorized": "Login state expired, log in again", "meetingIdShouldNotBeEmpty": "Meeting ID is required", "meetingPasswordNotValid": "Invalid meeting password", "displayNameShouldNotBeEmpty": "Nickname is required", "meetingLogPathParamsError": "Parameter error, invalid log path or no permission", "meetingLocked": "Meeting is locked", "meetingLockedTip": "Sorry, the meeting is locked. Please contact the meeting organizer to unlock the meeting.", "meetingNotExist": "Meeting does not exist", "meetingSaySomeThing": "Type to chat", "meetingKeepSilence": "Block all chats", "reuseIMNotSupportAnonymousLogin": "IM reuse does not support anonymous login", "unmuteAudioBySelf": "Self-unmute", "updateNicknameBySelf": "<PERSON><PERSON>", "updateNicknameNoPermission": "Renaming is not allowed", "shareNoPermission": "Sharing failed, only the host can share", "localRecordPermission": "Local Recording Permissions", "localRecordOnlyHost": "Only the host can record", "localRecordAll": "Allow all members to record", "sharingStopByHost": "The host has terminated your sharing", "suspendParticipantActivities": "Suspend participant activities", "suspendParticipantActivitiesTips": "Everyone in the meeting will be muted and their videos and shared screen will stop. The meeting will be locked.", "alreadySuspendParticipantActivitiesByHost": "The host has suspended participant activities", "alreadySuspendParticipantActivities": "Participant activities have been suspended", "suspendAllParticipantActivities": "Suspend all participant activities?", "hideAvatarByHost": "The host has hidden all profile pictures", "hideAvatar": "Hide profile pictures", "screenShare": "Share", "screenShareStop": "Stop Share", "screenShareOverLimit": "Someone is sharing screen, you can not share your screen", "screenShareNoPermission": "No screen sharing permission", "screenShareTips": "All content on your screen will be captured.", "screenShareStopFail": "Failed to stop screen sharing", "screenShareStartFail": "Failed to start screen sharing", "screenShareLocalTips": "{userName} is Sharing screen", "screenShareUser": "Shared screen of {userName}", "screenShareMyself": "You are sharing your screen", "screenShareInteractionTip": "Pinch with 2 fingers to adjust zoom", "whiteBoardShareStopFail": "Failed to stop whiteboard sharing", "whiteBoardShareStartFail": "Failed to start whiteboard sharing", "whiteboardShare": "Whiteboard", "whiteBoardClose": "Stop Whiteboard", "whiteBoardInteractionTip": "You are granted the whiteboard permission", "whiteBoardUndoInteractionTip": "You are revoked the whiteboard permission", "whiteBoardNoAuthority": "Whiteboard is unactivated. contact sales for activating the service", "whiteBoardPackUp": "<PERSON>de", "meetingHasScreenShareShare": "Whiteboard cannot be shared while screen or computer audio is being shared", "meetingHasWhiteBoardShare": "Screen cannot be shared while whiteboard is being shared", "meetingStopSharing": "Stop sharing", "meetingStopSharingConfirm": "Are you sure you want to stop sharing in progress?", "screenShareWarning": "Recently, there are criminals posing as customer service, campus loans and public security fraud, please be vigilant. A security risk has been detected for your meeting and sharing has been disabled.", "backSharingView": "Switch to shared content", "screenSharingViewUserLabel": "Screen shared by {userName}", "whiteBoardSharingViewUserLabel": "Whiteboard shared by {userName}", "virtualBackground": "Background", "virtualBackgroundImageNotExist": "Custom background image does not exist", "virtualBackgroundImageFormatNotSupported": "Invalid background image format", "virtualBackgroundImageDeviceNotSupported": "The device is not supported", "virtualBackgroundImageLarge": "The custom background image exceeds 5MB in size", "virtualBackgroundImageMax": "The custom background images exceed the maximum number", "virtualBackgroundSelectTip": "Effective when the image is selected", "virtualDefaultBackground": "<PERSON><PERSON><PERSON>", "virtualCustom": "Custom", "virtualBackgroundPerfInadequate": "Insufficient device performance", "virtualBackgroundPerfInadequateTip": "Your device's performance is insufficient, enabling the virtual background function may result in a decrease in video quality or stuttering. Do you still want to try enabling it", "virtualBackgroundForce": "Continue", "live": "Live", "liveMeeting": "Live Meeting", "liveMeetingTitle": "Title", "liveMeetingUrl": "Live URL", "liveEnterLivePassword": "Enter the live code", "liveEnterLiveSixDigitPassword": "Enter a 6-digit code", "liveInteraction": "Interaction", "liveInteractionTips": "If enabled, messaging in the meeting room and live room is visible", "liveLevel": "Only staff participants can view", "liveLevelTip": "Non-staff participants are unable to view the live if enabled", "liveViewSetting": "View Settings", "liveViewSettingChange": "The view is changed", "liveViewPreviewTips": "Live streaming preview", "liveViewPreviewDesc": "Configure the view settings", "liveStart": "Start", "liveUpdate": "Update", "liveStop": "Stop", "liveGalleryView": "Gallery", "liveFocusView": "Focus", "liveScreenShareView": "Screen Sharing", "liveChooseView": "View Mode", "liveChooseCountTips": "Select up to 4 participants", "liveStartFail": "Failed to start live streaming, try again later", "liveStartSuccess": "Live streaming started", "livePickerCount": "Selected {length} participant(s)", "liveUpdateFail": "Failed to update the live streaming, try again later", "liveUpdateSuccess": "Live streaming updated", "liveStopFail": "Failed to stop the live streaming, try again later", "liveStopSuccess": "Live streaming stopped", "livePassword": "Live code", "liveDisableAuthLevel": "Editing live streaming permission is not allowed during streaming", "liveStreaming": "Live", "participants": "Participants", "participantsManager": "Participants", "participantAssignedHost": "You are assigned HOST", "participantAssignedCoHost": "You are assigned CO-HOST", "participantUnassignedCoHost": "You are unassigned CO-HOST", "participantAssignedActiveSpeaker": "You are assigned active speaker", "participantUnassignedActiveSpeaker": "You are unassigned active speaker", "participantMuteAudioAll": "Mute All", "participantMuteAudioAllDialogTips": "All and new participants are muted", "participantMuteVideoAllDialogTips": "All and new participants have video turned off", "participantUnmuteAll": "Unmute All", "participantMute": "Mute", "participantUnmute": "Unmute", "participantTurnOffVideos": "Turn off all videos", "participantTurnOnVideos": "Turn on all videos", "participantStopVideo": "Video Off", "participantStartVideo": "Video On", "participantTurnOffAudioAndVideo": "Turn off audio and video", "participantTurnOnAudioAndVideo": "Turn on audio and video", "participantHostStoppedShare": "Host stopped your screen sharing", "participantHostStopWhiteboard": "Host stopped your whiteboard sharing", "participantAssignActiveSpeaker": "Assign active speaker", "participantUnassignActiveSpeaker": "Unassign active speaker", "participantTransferHost": "Transfer HOST", "participantTransferHostConfirm": "Transfer HOST to {userName}", "participantRemove": "Remove", "participantRename": "<PERSON><PERSON>", "participantRenameDialogTitle": "Change Nickname", "participantAssignCoHost": "Assign CO-HOST", "participantUnassignCoHost": "Unassign CO-HOST", "participantRenameTips": "Enter a new nickname", "participantRenameSuccess": "Nickname edited", "participantRenameFail": "Renaming failed", "participantRemoveConfirm": "Remove", "participantCannotRemoveSelf": "You cannot remove yourself", "participantMuteAudioFail": "Muting failed", "participantUnMuteAudioFail": "Unmuting failed", "participantMuteVideoFail": "Failed to turn off camera", "participantUnMuteVideoFail": "Failed to turn on camera", "participantFailedToAssignActiveSpeaker": "Failed to assign active speaker", "participantFailedToUnassignActiveSpeaker": "Failed to unassign active speaker", "participantFailedToLowerHand": "Failed to lower hand", "participantFailedToTransferHost": "Failed to transfer HOST", "participantFailedToRemove": "Failed to remove the participant", "participantOpenCamera": "Cam <PERSON>", "participantOpenMicrophone": "Mic On", "participantHostOpenCameraTips": "Host requests to turn on your camera. Turn on the camera？", "participantHostOpenMicroTips": "Host requests to unmute your microphone. Unmute the microphone？", "participantMuteAllAudioTip": "Allow participants to unmute", "participantMuteAllVideoTip": "Allow participants to turn on videos", "participantMuteAllAudioSuccess": "All participants are muted", "participantMuteAllAudioFail": "Failed to mute all participants", "participantMuteAllVideoSuccess": "All participants have video turned off", "participantMuteAllVideoFail": "Failed to turn off videos of all participants", "participantUnMuteAllAudioSuccess": "All participants are unmuted", "participantUnMuteAllAudioFail": "Failed to unmute all participants", "participantUnMuteAllVideoSuccess": "All participants have video turned on", "participantUnMuteAllVideoFail": "Failed to turn on videos of all participants", "participantHostMuteVideo": "Your video is turned off", "participantHostMuteAudio": "You are muted", "participantHostMuteAllAudio": "Host muted all participants", "participantHostMuteAllVideo": "Host turned off videos of all participants", "participantMuteAudioHandsUpOnTips": "You are unmuted and can speak now", "participantOverRoleLimitCount": "The number of assigned roles exceeds the upper limit", "participantMe": "Me", "participantSearchMember": "Search", "participantHost": "HOST", "participantCoHost": "CO-HOST", "participantMuteAllHandsUpTips": "The host has muted all participants. You can raise your hand", "participantTurnOffAllVideoHandsUpTips": "The host has turned off all videos. You can raise your hand", "participantWhiteBoardInteract": "Grant the whiteboard permission", "participantWhiteBoardInteractFail": "Failed to grant the whiteboard permission", "participantUndoWhiteBoardInteract": "Revoke the whiteboard permission", "participantUndoWhiteBoardInteractFail": "Failed to revoke the whiteboard permission", "participantUserHasBeenAssignCoHostRole": "has been assigned CO-HOST", "participantUserHasBeenRevokeCoHostRole": "has been unassigned CO-HOST", "participantInMeeting": "In meeting", "participantNotJoined": "Not joined", "participantJoining": "Be joining", "participantAttendees": "Participants", "participantAdmit": "Admit", "participantWaitingTimePrefix": "Waiting", "participantPutInWaitingRoom": "Remove to waiting room", "participantDisallowMemberRejoinMeeting": "Do not allow users to join this meeting again", "participantVideoIsPinned": "The video is locked, tap unlock at {corner} to unlock", "participantVideoIsUnpinned": "Video unlocked", "participantNotFound": "No member found", "participantSetHost": "Set as host", "participantSetCoHost": "Set as co-host", "participantCancelCoHost": "<PERSON><PERSON> the co-host", "participantRemoveAttendee": "Delete", "participantUpperLimitWaitingRoomTip": "The number of participants has reached  the limit , it is recommended to use the waiting room.", "participantUpperLimitReleaseSeatsTip": "The number of participants has reached  the limit , and new participants will not be able to join the meeting.You can try removing not-joined members or releasing a seat in the meeting.", "participantUpperLimitTipAdmitOtherTip": "The number of participants has reached  the limit . Please remove an unjoined member or release a seat in the meeting before admitting a member to the waiting room.", "cloudRecordingEnabledTitle": "Are you sure you want to start a cloud recording?", "cloudRecordingEnabledMessage": "After the recording starts,  the meeting audio, video and shared screen view will be recorded to the cloud and all participants will be informed.", "cloudRecordingEnabledMessageWithoutNotice": "After the recording starts,  the meeting audio, video and shared screen view will be recorded to the cloud", "cloudRecordingTitle": "This meeting is being recorded", "cloudRecordingMessage": "The host has started a cloud recording and the meeting creator will receive the cloud recording file. You can contact the creator for the cloud recording file.", "cloudRecordingAgree": "By staying in the meeting, you agree to the recording.", "cloudRecordingWhetherEndedTitle": "End Cloud Recording", "cloudRecordingEndedMessage": "Go to 'Historical Meeting - Meeting Details' to check recorded files after meeting.", "cloudRecordingEndedTitle": "Cloud recording has ended", "cloudRecordingEndedAndGetUrl": "You can contact the meeting creator after the meeting to obtain a viewing link.", "cloudRecordingStart": "Cloud recording", "cloudRecordingStop": "stop recording", "cloudRecording": "Recording", "cloudRecordingStartFail": "Failed to start recording", "cloudRecordingStopFail": "Failed to stop recording", "cloudRecordingStarting": "Starting recording…", "cloudRecordingUnableToStart": "Unable to start cloud recording", "cloudRecordingUnableToStartTips": "No microphone or video is enabled in the meeting. To start recording, please unmute.", "cloudRecordingEnableAISummary": "Enable smart recording", "cloudRecordingEnableAISummaryTip": "When enabled, this meeting will generate smart AI minutes (including summary and to-do list))", "cloudRecordingAISummaryStarted": "This meeting has started smart recording and will generate smart AI minutes (including summary and to-do list)", "cloudRecordingEnableAISummaryFail": "Smart recording fails to be enabled. Please stop recording later and try again", "chat": "Cha<PERSON>", "chatInputMessageHint": "Entering...", "chatCannotSendBlankLetter": "Unable to send empty messages", "chatJoinFail": "Failed to join the chat room", "chatNewMessage": "New message", "chatUnsupportedFileExtension": "Unsupported file", "chatFileSizeExceedTheLimit": "File size cannot exceed 200MB", "chatImageSizeExceedTheLimit": "Image size cannot exceed 20MB", "chatImageMessageTip": "[Image]", "chatFileMessageTip": "[File]", "chatSaveToGallerySuccess": "Saved to Album", "chatOperationFailNoPermission": "No permission", "chatOpenFileFail": "Opening file failed", "chatOpenFileFailNoPermission": "Opening file failed: no permission", "chatOpenFileFailFileNotFound": "Opening file failed: no file exists", "chatOpenFileFailAppNotFound": "Opening file failed: no app installed to open the file", "chatRecall": "Recall", "chatAboveIsHistoryMessage": "Historical chat messages above", "chatYou": "You", "chatRecallAMessage": "recalled a message", "chatMessageRecalled": "Message recalled", "chatMessage": "Message", "chatSendTo": "Send to", "chatAllMembersInMeeting": "Everyone in the meeting", "chatAllMembersInWaitingRoom": "Everyone in the waiting room", "chatHistory": "Chat History", "chatMessageSendToWaitingRoom": "To the waiting room", "chatNoChatHistory": "No chat history", "chatAllMembers": "Everyone", "chatPrivate": "Private", "chatPrivateInWaitingRoom": "Waiting room-private", "chatPermission": "Chat permissions", "chatFree": "All chats allowed", "chatPublicOnly": "Group chats only", "chatPrivateHostOnly": "Chat with host only", "chatMuted": "Mute all participants", "chatPermissionInMeeting": "Chat permissions in the meeting", "chatPermissionInWaitingRoom": "Chat permissions in the waiting room", "chatWaitingRoomPrivateHostOnly": "Chat with the host", "chatHostMutedEveryone": "Block all chats", "chatHostLeft": "The host has left and cannot send private chat messages", "chatSaidToMe": "{user<PERSON><PERSON>} said to me", "chatISaidTo": "I said to{<PERSON><PERSON><PERSON>}", "chatSaidToWaitingRoom": "{userName} to everyone waiting", "chatISaidToWaitingRoom": "To Everyone waiting", "chatSendFailed": "Failed to send", "chatMemberLeft": "The participants have left the meeting", "chatWaitingRoomMuted": "The host has not opened the chat", "chatHistoryNotEnabled": "Chat history is not enabled. Please contact your administrator", "waitingRoomJoinMeeting": "Join", "waitingRoom": "Waiting Room", "waitingRoomJoinMeetingOption": "Meeting Settings", "waitingRoomWaitHostToInviteJoinMeeting": "Please wait. The host will let you into the meeting soon.", "waitingRoomWaitMeetingToStart": "Please wait. The meeting will begin soon", "waitingRoomTurnOnMicrophone": "Turn On Mic", "waitingRoomTurnOnVideo": "Turn On Video", "waitingRoomEnabledOnEntry": "You have enabled waiting room", "waitingRoomDisabledOnEntry": "You have disabled waiting room", "waitingRoomDisableDialogTitle": "Close the waiting room", "waitingRoomDisableDialogMessage": "After the waiting room closes, new members will join the meeting directly", "waitingRoomDisableDialogAdmitAll": "Allow all members of the waiting room to enter the meeting", "waitingRoomCloseRightNow": "Close", "waitingRoomCount": "{count}attendee(s) waiting", "waitingRoomAutoAdmit": "Admit to meeting automatically", "movedToWaitingRoom": "The host has moved you to the waiting room", "waitingRoomAdmitAll": "Admit All", "waitingRoomRemoveAll": "Remove All", "waitingRoomAdmitMember": "Admit Waiting Attendee", "waitingRoomAdmitAllMembersTip": "Do you want to admit all attendees in the waiting room to the meeting", "waitingRoomRemoveAllMemberTip": "Are you sure you want to remove all attendees from the waiting room ?", "waitingRoomExpelWaitingMember": "Remove waiting members", "waiting": "Waiting", "waitingRoomEnable": "Enable Waiting Room", "deviceSpeaker": "Speaker", "deviceReceiver": "Receiver", "deviceBluetooth": "Bluetooth", "deviceHeadphones": "Headphones", "deviceOutput": "Audio Device", "deviceHeadsetState": "You are using the earphone", "networkConnectionGood": "Network connection is good", "networkConnectionGeneral": "Network connection is fair", "networkConnectionPoor": "Network connection is poor", "networkConnectionUnknown": "Network connection is unknown", "networkLocalLatency": "Latency", "networkPacketLossRate": "Packet Loss Rate", "networkReconnectionSuccessful": "Network reconnection successful", "networkAbnormalityPleaseCheckYourNetwork": "Network abnormality, please check your network", "networkAbnormality": "Network abnormality", "networkDisconnectedPleaseCheckYourNetworkStatusOrTryToRejoin": "Network disconnected, please check your network status or try to rejoin.", "networkNotStable": "The network status is not good", "networkUnavailableCloseFail": "Ending meeting failed due to the network error", "networkDisconnectedTryingToReconnect": "Disconnected, trying to reconnect…", "networkUnavailableCheck": "Network connection failed, please check your network connection!", "networkUnstableTip": "The network is unstable, connecting...", "notifyCenter": "Notification", "notifyCenterAllClear": "Confirm to clear all notifications?", "notifyCenterNoMessage": "No news", "notifyCenterViewDetailsUnsupported": "The message does not support viewing details", "notifyCenterViewingDetails": "View details", "sipCallByNumber": "Phone", "sipCall": "Call", "sipContacts": "Contacts", "sipNumberPlaceholder": "Enter the phone number", "sipName": "Invitee name", "sipNamePlaceholder": "Names will be presented at the meeting", "sipCallNumber": "Dial out number:", "sipNumberError": "Phone number error", "sipCallIsCalling": "The number is already in a call", "sipLocalContacts": "Local contacts", "sipContactsClear": "Clear", "sipCalling": "Calling", "sipCallTerm": "Hang up", "sipCallOthers": "Call other members", "sipCallFailed": "Call failed", "sipCallBusy": "Line busy", "sipCallAgain": "Redial", "sipSearch": "Search", "sipSearchContacts": "Search and add participants", "sipCallPhone": "Phone call", "sipCallingNumber": "To join", "sipCallCancel": "Cancel call", "sipCallAgainEx": "Call again", "sipCallStatusCalling": "Calling", "callStatusCalling": "Calling", "sipCallStatusWaiting": "Waiting for call", "callStatusWaitingJoin": "To join", "sipCallStatusTermed": "Hung up", "sipCallStatusUnaccepted": "No answer", "sipCallStatusRejected": "Rejected", "sipCallStatusCanceled": "Cancelled", "sipCallStatusError": "Call exception", "sipPhoneNumber": "Phone number", "sipCallMemberSelected": "Selected: {count}", "sipContactsPrivacy": "Authorize access to your address book to call a contact to join a meeting by phone", "memberCountOutOfRange": "The maximum number of participants has been reached", "sipContactNoNumber": "User has no number.", "sipCallIsInMeeting": "The user is already in a meeting.", "callInWaitingMeeting": "The member is already in the waiting room", "sipCallIsInInviting": "The user is inviting.", "sipCallIsInBlacklist": "The member has been blocked. To invite, disable the meeting blacklist", "sipCallDeviceIsInBlacklist": "The device has been blocked. To invite, disable the meeting blacklist", "sipCallByPhone": "Phone", "sipKeypad": "Keypad", "sipBatchCall": "<PERSON><PERSON> Call", "sipLocalContactsEmpty": "Local address book is empty", "sipCallMaxCount": "Select at most {count} people at a time.", "sipInviteInfo": "Copy details", "sipAddressInvite": "Contacts", "sipJoinOtherMeetingTip": "Will leave the current meeting once you accept.", "sipRoom": "Conference room", "sipCallOutPhone": "Phone", "sipCallOutRoom": "Call SIP/H.323", "sipCallOutRoomInputTip": "IP address or SIP URI or registered device number", "sipCallOutRoomH323InputTip": "IP address or E.164 number", "sipDisplayName": "Name", "sipDeviceIsInCalling": "The device is already on the call", "sipDeviceIsInMeeting": "The device is already in the meeting", "sip": "SIP", "h323": "H.323", "sipProtocol": "Protocol", "roomSipCallIsInBlacklist": "The device has been blocked. To invite, disable the meeting blacklist", "roomSipCallIsInMeeting": "The device is already in the meeting", "roomSipCallrNetworkError": "Network abnormality, please check your network", "roomSipCallrNickNameLimit": "The  nickname is too long. Please reset it.", "monitoring": "Quality Monitoring", "overall": "Overall", "soundAndVideo": "Audiovisual", "cpu": "CPU", "memory": "Memory", "network": "Network", "bandwidth": "Bandwidth", "networkType": "Network Type", "networkState": "Network", "delay": "Latency", "packageLossRate": "Packet loss", "recently": "Last", "audio": "Audio", "microphone": "Mic", "speaker": "Speaker", "bitrate": "Bitrate", "speakerPlayback": "Speaker Playback", "microphoneAcquisition": "Mic Capture", "resolution": "Resolution", "frameRate": "<PERSON>ame", "moreMonitoring": "View More", "layoutSettings": "Layout setting", "galleryModeMaxCount": "Max participants per screen in gallery view", "galleryModeScreens": "{count}", "followGalleryLayout": "Follow the host's video sequence", "resetGalleryLayout": "Reset video sequence", "followGalleryLayoutTips": "The first 25 videos in host gallery mode are synchronized to all participants, and participants are not allowed to change themselves.", "followGalleryLayoutConfirm": "The host has set \"Follow the host's video sequence\" and cannot move the video.", "followGalleryLayoutResetConfirm": "The host has set \"Follow the host's video sequence\", and the video order cannot be reset.", "saveGalleryLayoutTitle": "Save video sequence", "saveGalleryLayoutContent": "Save the current video sequence to the scheduled meeting for subsequent meetings. Are you sure to save the video sequence?", "replaceGalleryLayoutContent": "The scheduled meeting already has an old video sequence. Do you want to replace it and save it to the new video sequence?", "loadGalleryLayoutTitle": "Load video sequence", "loadGalleryLayoutContent": "The scheduled meeting already has a video sequence. Do you want to load it?", "load": "Load", "noLoadGalleryLayout": "There is no video sequence to load", "loadSuccess": "Load successfully", "loadFail": "Failed to load", "globalUpdate": "Update", "globalLang": "Language", "globalView": "View", "interpretation": "Interpretation", "interpInterpreter": "Interpreter", "interpSelectInterpreter": "Select interpreter", "interpInterpreterAlreadyExists": "The user has been selected as a interpreter and cannot be selected again", "interpInfoIncompleteTitle": "Interpreter information is incomplete", "interpInfoIncompleteMsg": "Quitting will remove interpreters with incomplete information", "interpStart": "Start", "interpStartNotification": "The host has started simultaneous interpretation", "interpStop": "Stop interpretation", "interpStopNotification": "The host has turned off simultaneous interpretation", "interpConfirmStopMsg": "Turning off simultaneous interpretation will turn off all listening channels. Do you want to turn it off?", "interpConfirmUpdateMsg": "Update?", "interpConfirmCancelEditMsg": "Are you sure to cancel the settings ?", "interpSelectListenLanguage": "Please select a listening language", "interpSelectLanguage": "Select language", "interpAddLanguage": "Add", "interpInputLanguage": "Input", "interpLanguageAlreadyExists": "Language already exists", "interpListenMajorAudioMeanwhile": "Listen to the original sound", "interpManagement": "Manage interpretation", "interpSettings": "Set up", "interpMajorAudio": "Original sound", "interpMajorChannel": "Main", "interpMajorAudioVolume": "Original volume", "interpAddInterpreter": "Add interpreter", "interpJoinChannelErrorMsg": "Failed to join the interpretation channel. Do you want to rejoin?", "interpReJoinChannel": "Rejoin", "interpAssignInterpreter": "You have become the interpreter of this meeting", "interpAssignLanguage": "Language", "interpAssignInterpreterTip": "You can set the listening language and translation language in \"Interpretation\"", "interpUnassignInterpreter": "You have been removed from interpreters by the host", "interpLanguageRemoved": "The host has deleted the listening language \"{language}\"", "interpInterpreterOffline": "All the interpreters have left the channel you are listening to . Would you like to switch back to the original sound ?", "interpDontSwitch": "Not Now", "interpSwitchToMajorAudio": "Switch back", "interpAudioShareIsForbiddenDesktop": "As an interpreter, you will not be able to share your computer voice when sharing the screen", "interpAudioShareIsForbiddenMobile": "As an interpreter, you will not be able to share device audio when sharing the screen", "interpInterpreterInMeetingStatusChanged": "Interpreter participation status has changed", "interpSpeakerTip": "You are listening to {language1} and saying {language2}", "interpOutputLanguage": "Translation language", "interpRemoveInterpreterOnly": "Only delete interpreter", "interpRemoveInterpreterInMembers": "Delete from participants", "interpRemoveMemberInInterpreters": "The participant is also assigned as an interpreter. Deleting the participant will cancel the interpreter assignment at the same time.", "interpListeningChannelDisconnect": "The listening language channel has been disconnected, trying to reconnect", "interpSpeakingChannelDisconnect": "The interpreter language channel has been disconnected, trying to reconnect", "langChinese": "Chinese", "langEnglish": "English", "langJapanese": "Japanese", "langKorean": "Korean", "langFrench": "French", "langGerman": "German", "langSpanish": "Spanish", "langRussian": "Russian", "langPortuguese": "Portuguese", "langItalian": "Italian", "langTurkish": "Turkish", "langVietnamese": "Vietnamese", "langThai": "Thai", "langIndonesian": "Indonesian", "langMalay": "Malay", "langArabic": "Arabic", "langHindi": "Hindi", "annotation": "Annotate", "annotationEnabled": "Annotation enabled", "annotationDisabled": "Annotation disabled", "startAnnotation": "Annotate", "stopAnnotation": "Exit annotation", "inAnnotation": "Annotating", "saveAnnotation": "Save annotations", "cancelAnnotation": "Cancel annotation", "settings": "Settings", "settingAudio": "Audio", "settingVideo": "Video", "settingCommon": "General", "settingAudioAINS": "Smart Noise Reduction", "settingEnableTransparentWhiteboard": "Set the whiteboard to transparent", "settingEnableFrontCameraMirror": "Front camera mirroring", "settingShowMeetDuration": "Show Meeting Duration", "settingShowParticipationTime": "Show Connected Time", "settingShowElapsedTime": "Display Time", "settingShowNone": "Do not show", "settingShowMeetingElapsedTime": "Meeting duration", "settingShowParticipationElapsedTime": "Connected time", "settingSpeakerSpotlight": "Speaker Spotlight", "settingSpeakerSpotlightTip": "When turned on, the participants who are speaking will be displayed first.", "settingShowName": "Always display participant names on their video", "settingHideNotYetJoinedMembers": "Hide unjoined members", "settingChatMessageNotification": "Chat message notifications", "settingChatMessageNotificationBarrage": "Chat area", "settingChatMessageNotificationBubble": "Speech bubble", "settingChatMessageNotificationNoReminder": "Off", "settingHideVideoOffAttendees": "Hide non-video participants", "settingHideMyVideo": "Hide self view", "settingLeaveTheMeetingRequiresConfirmation": "Ask me to confirm when l leave a meeting", "usingComputerAudioInMeeting": "Use PC audio when joining a meeting", "settingEnterFullscreen": "Enter full screen automatically when starting or joining a meeting", "enterFullscreenTips": "Press ESC or click the button in the upper right corner to exit full screen mode", "joinMeetingSettings": "Join <PERSON><PERSON>", "memberJoinWithMute": "Mute participants upon entry", "ringWhenMemberJoinOrLeave": "Play sound when someone joins or leaves", "windowSizeWhenSharingTheScreen": "Window size when screen sharing", "sideBySideMode": "Side-by-side mode", "sideBySideModeTips": "While viewing others' shared screens automatically place the participants' videos to the right of the shared screen", "whenIShareMyScreenInMeeting": "When I share my screen in a meeting", "showAllSharingOptions": "Show all sharing options", "automaticDesktopSharing": "Automatically share the desktop", "automaticDesktopSharingTips": "When you have multiple monitors, your primary desktop will be shared automatically", "onlyShowTheEntireScreen": "Only display the entire screen", "sharedLimitFrameRate": "Limit your screen share to", "sharedLimitFrameRateTips": "When turned on, the screen sharing frame rate will not exceed the set value.", "sharedLimitFrameRateUnit": "frames-per-second", "preferMotionModel": "Fluency preferred", "preferMotionModelTips": "Reduce CPU and Bandwidth usage and prioritize the fluency if the shared video", "transcriptionEnableCaption": "Enable subtitles", "transcriptionEnableCaptionHint": "The current subtitles are only visible to you", "transcriptionDisableCaption": "Disable subtitles", "transcriptionDisableCaptionHint": "You have turned off subtitles", "transcriptionCaptionLoading": "Transcription enabled, The machine recognition results are only for reference.", "transcriptionDisclaimer": "Machine results for reference.", "transcriptionCaptionSettingsHint": "Click to enter subtitle settings.", "transcriptionCaptionSettings": "Subtitle settings", "transcriptionAllowEnableCaption": "Use subtitles", "transcriptionCanNotEnableCaption": "The subtitle function is unavailable. Please contact the host or administrator.", "transcriptionCaptionForbidden": "Participants are not allowed to use subtitles, and subtitles have been turned off.", "transcriptionCaptionNotAvailableInSubChannel": "Not listening to the original sound, subtitles are not available, if you need to use please listen to the original sound.", "transcriptionCaptionFontSize": "Text Size", "transcriptionCaptionSmall": "Small", "transcriptionCaptionBig": "Big", "transcriptionCaptionEnableWhenJoin": "Enable subtitles when joining a meeting", "transcriptionCaptionExampleSize": "Example of subtitle text size", "transcriptionCaptionTypeSize": "Text Size", "transcription": "Real-time transcription", "transcriptionStart": "Transcribe", "transcriptionStop": "End transcript", "transcriptionStartConfirmMsg": "Do you want to enable real-time translation?", "transcriptionStartedNotificationMsg": "The host has started real-time transcription, and all members can view the transcription content.", "transcriptionRunning": "Translating", "transcriptionStartedTip": "The host has enabled real-time transcription", "transcriptionStoppedTip": "The host has disabled real-time transcription", "transcriptionNotStarted": "Real-time transcription is not enabled. Contact the host to enable the transcription.", "transcriptionStopFailed": "Failed to disable subtitles", "transcriptionStartFailed": "Failed to enable subtitles", "transcriptionTranslationSettings": "Translation Settings", "transcriptionSettings": "Transcription Settings", "transcriptionTargetLang": "Translation Display", "transcriptionShowBilingual": "Bilingual Mode", "transcriptionNotTranslated": "Do Not Translate", "transcriptionMemberPermission": "Member viewing permissions", "transcriptionViewFullContent": "View full content", "transcriptionViewConferenceContent": "View the contents during the meeting", "feedbackInRoom": "<PERSON><PERSON><PERSON>", "feedbackProblemType": "Problem Type", "feedbackSuccess": "Submitted successfully", "feedbackFail": "Failed to submit", "feedbackAudioLatency": "A large delay", "feedbackAudioFreeze": "Stuck", "feedbackCannotHearOthers": "Can't hear the other's voice", "feedbackCannotHearMe": "The others can't hear me", "feedbackTitleExtras": "Additional Information", "feedbackTitleDate": "Occurrence Time", "feedbackContentEmpty": "None", "feedbackTitleSelectPicture": "Picture", "feedbackAudioMechanicalNoise": "Mechanical sound", "feedbackAudioNoise": "Noise", "feedbackAudioEcho": "Echo", "feedbackAudioVolumeSmall": "Low volume", "feedbackVideoFreeze": "Long time stuck", "feedbackVideoIntermittent": "Video is intermittent", "feedbackVideoTearing": "Tearing", "feedbackVideoTooBrightOrDark": "Picture too bright/too dark", "feedbackVideoBlurry": "Blurred image", "feedbackVideoNoise": "Obvious noise", "feedbackAudioVideoNotSync": "Sound and picture are not synchronized", "feedbackUnexpectedExit": "Unexpected exit", "feedbackOthers": "There are other problems", "feedbackTitleAudio": "Audio problem", "feedbackTitleVideo": "Video problem", "feedbackTitleOthers": "Other", "feedbackTitleDescription": "Description", "feedbackOtherTip": "Please describe your problem (when you select \"There are other problems\"), you need to fill in a specific description before submitting", "feedback": "<PERSON><PERSON><PERSON>"}