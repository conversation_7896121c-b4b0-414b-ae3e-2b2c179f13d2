import 'package:student_end_flutter/generated/json/base/json_convert_content.dart';
import 'package:student_end_flutter/bean/popup_model_entity.dart';

PopupModelEntity $PopupModelEntityFromJson(Map<String, dynamic> json) {
  final PopupModelEntity popupModelEntity = PopupModelEntity();
  final int? code = jsonConvert.convert<int>(json['code']);
  if (code != null) {
    popupModelEntity.code = code;
  }
  final bool? success = jsonConvert.convert<bool>(json['success']);
  if (success != null) {
    popupModelEntity.success = success;
  }
  final String? message = jsonConvert.convert<String>(json['message']);
  if (message != null) {
    popupModelEntity.message = message;
  }
  final List<PopupModelData>? data = (json['data'] as List<dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<PopupModelData>(e) as PopupModelData)
      .toList();
  if (data != null) {
    popupModelEntity.data = data;
  }
  final dynamic total = json['total'];
  if (total != null) {
    popupModelEntity.total = total;
  }
  return popupModelEntity;
}

Map<String, dynamic> $PopupModelEntityToJson(PopupModelEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['code'] = entity.code;
  data['success'] = entity.success;
  data['message'] = entity.message;
  data['data'] = entity.data.map((v) => v.toJson()).toList();
  data['total'] = entity.total;
  return data;
}

extension PopupModelEntityExtension on PopupModelEntity {
  PopupModelEntity copyWith({
    int? code,
    bool? success,
    String? message,
    List<PopupModelData>? data,
    dynamic total,
  }) {
    return PopupModelEntity()
      ..code = code ?? this.code
      ..success = success ?? this.success
      ..message = message ?? this.message
      ..data = data ?? this.data
      ..total = total ?? this.total;
  }
}

PopupModelData $PopupModelDataFromJson(Map<String, dynamic> json) {
  final PopupModelData popupModelData = PopupModelData();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    popupModelData.id = id;
  }
  final String? popupName = jsonConvert.convert<String>(json['popupName']);
  if (popupName != null) {
    popupModelData.popupName = popupName;
  }
  final String? popupPicUrl = jsonConvert.convert<String>(json['popupPicUrl']);
  if (popupPicUrl != null) {
    popupModelData.popupPicUrl = popupPicUrl;
  }
  final int? popupType = jsonConvert.convert<int>(json['popupType']);
  if (popupType != null) {
    popupModelData.popupType = popupType;
  }
  final String? goodsId = jsonConvert.convert<String>(json['goodsId']);
  if (goodsId != null) {
    popupModelData.goodsId = goodsId;
  }
  final String? color = jsonConvert.convert<String>(json['color']);
  if (color != null) {
    popupModelData.color = color;
  }
  final int? weight = jsonConvert.convert<int>(json['weight']);
  if (weight != null) {
    popupModelData.weight = weight;
  }
  final String? position = jsonConvert.convert<String>(json['position']);
  if (position != null) {
    popupModelData.position = position;
  }
  final int? upType = jsonConvert.convert<int>(json['upType']);
  if (upType != null) {
    popupModelData.upType = upType;
  }
  final String? activityId = jsonConvert.convert<String>(json['activityId']);
  if (activityId != null) {
    popupModelData.activityId = activityId;
  }
  final String? goodsName = jsonConvert.convert<String>(json['goodsName']);
  if (goodsName != null) {
    popupModelData.goodsName = goodsName;
  }
  final String? popupLinkUrl = jsonConvert.convert<String>(
      json['popupLinkUrl']);
  if (popupLinkUrl != null) {
    popupModelData.popupLinkUrl = popupLinkUrl;
  }
  final int? popupInterval = jsonConvert.convert<int>(json['popupInterval']);
  if (popupInterval != null) {
    popupModelData.popupInterval = popupInterval;
  }
  final int? popupStatus = jsonConvert.convert<int>(json['popupStatus']);
  if (popupStatus != null) {
    popupModelData.popupStatus = popupStatus;
  }
  final String? effectiveStartTime = jsonConvert.convert<String>(
      json['effectiveStartTime']);
  if (effectiveStartTime != null) {
    popupModelData.effectiveStartTime = effectiveStartTime;
  }
  final String? effectiveEndTime = jsonConvert.convert<String>(
      json['effectiveEndTime']);
  if (effectiveEndTime != null) {
    popupModelData.effectiveEndTime = effectiveEndTime;
  }
  final dynamic lastPopTime = json['lastPopTime'];
  if (lastPopTime != null) {
    popupModelData.lastPopTime = lastPopTime;
  }
  final int? needLogin = jsonConvert.convert<int>(json['needLogin']);
  if (needLogin != null) {
    popupModelData.needLogin = needLogin;
  }
  final String? createdTime = jsonConvert.convert<String>(json['createdTime']);
  if (createdTime != null) {
    popupModelData.createdTime = createdTime;
  }
  final String? updatedTime = jsonConvert.convert<String>(json['updatedTime']);
  if (updatedTime != null) {
    popupModelData.updatedTime = updatedTime;
  }
  return popupModelData;
}

Map<String, dynamic> $PopupModelDataToJson(PopupModelData entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['popupName'] = entity.popupName;
  data['popupPicUrl'] = entity.popupPicUrl;
  data['popupType'] = entity.popupType;
  data['goodsId'] = entity.goodsId;
  data['color'] = entity.color;
  data['weight'] = entity.weight;
  data['position'] = entity.position;
  data['upType'] = entity.upType;
  data['activityId'] = entity.activityId;
  data['goodsName'] = entity.goodsName;
  data['popupLinkUrl'] = entity.popupLinkUrl;
  data['popupInterval'] = entity.popupInterval;
  data['popupStatus'] = entity.popupStatus;
  data['effectiveStartTime'] = entity.effectiveStartTime;
  data['effectiveEndTime'] = entity.effectiveEndTime;
  data['lastPopTime'] = entity.lastPopTime;
  data['needLogin'] = entity.needLogin;
  data['createdTime'] = entity.createdTime;
  data['updatedTime'] = entity.updatedTime;
  return data;
}

extension PopupModelDataExtension on PopupModelData {
  PopupModelData copyWith({
    String? id,
    String? popupName,
    String? popupPicUrl,
    int? popupType,
    String? goodsId,
    String? color,
    int? weight,
    String? position,
    int? upType,
    String? activityId,
    String? goodsName,
    String? popupLinkUrl,
    int? popupInterval,
    int? popupStatus,
    String? effectiveStartTime,
    String? effectiveEndTime,
    dynamic lastPopTime,
    int? needLogin,
    String? createdTime,
    String? updatedTime,
  }) {
    return PopupModelData()
      ..id = id ?? this.id
      ..popupName = popupName ?? this.popupName
      ..popupPicUrl = popupPicUrl ?? this.popupPicUrl
      ..popupType = popupType ?? this.popupType
      ..goodsId = goodsId ?? this.goodsId
      ..color = color ?? this.color
      ..weight = weight ?? this.weight
      ..position = position ?? this.position
      ..upType = upType ?? this.upType
      ..activityId = activityId ?? this.activityId
      ..goodsName = goodsName ?? this.goodsName
      ..popupLinkUrl = popupLinkUrl ?? this.popupLinkUrl
      ..popupInterval = popupInterval ?? this.popupInterval
      ..popupStatus = popupStatus ?? this.popupStatus
      ..effectiveStartTime = effectiveStartTime ?? this.effectiveStartTime
      ..effectiveEndTime = effectiveEndTime ?? this.effectiveEndTime
      ..lastPopTime = lastPopTime ?? this.lastPopTime
      ..needLogin = needLogin ?? this.needLogin
      ..createdTime = createdTime ?? this.createdTime
      ..updatedTime = updatedTime ?? this.updatedTime;
  }
}