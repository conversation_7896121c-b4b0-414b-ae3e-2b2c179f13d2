import 'dart:io';

import 'package:student_end_flutter/common/user_option.dart';
import 'package:tx_im/dx_utils/http_config.dart';
import '../utils/app_version_util.dart';
import '../common/Config.dart';
import '../utils/config_service.dart';

/// 小程序跳转路径生成工具
class MiniProgramPathBuilder {
  const MiniProgramPathBuilder._();

  /// 核心路径构建方法
  static String buildMiniProgramPath({
    required List<String> pathComponents,
    Map<String, dynamic>? queryParams,
    bool isAddUserToken = true,
    bool isNeedUserId = true,
    bool isNeedParent = true,
                                                                                                                                                                                                                       }) {
    // 清理路径组件
    final cleanPath = pathComponents
        .where((component) => component.isNotEmpty)
        .map((component) => component.replaceAll(RegExp(r'^/|/$'), ''))
        .join('/');

    final params = queryParams ?? {};
    params.addAll({
      'app': 2,  // 固定参数
      'baseUrl': Config.URL,  // 固定参数
      'appShareType': ConfigService.zxShareType,  // 分享type
      // 版本
      'distinctId':AppVersionUtil.distinctId ?? '',
      'appVersion': AppVersionUtil.appVersion ?? '',
      'tempDxSource': Platform.isAndroid ? 'ZHENXUAN##ANDROID##APP' : 'ZHENXUAN##IOS##APP', // 平台标识
    });
    if (UserOption.token.isNotEmpty && isAddUserToken) {
       params.addAll({
        'token': UserOption.token, // token
      });
    }
    if (isAddUserToken && UserOption.userInfoBeanEntity?.userId != null) {
      if(params["identityType"] == null || params["identityType"] == ""){
        params.addAll({
          'identityType': UserOption.userInfoBeanEntity?.identityType,
        });
      }
      if((params["userId"] == null || params["userId"] == "") && isNeedUserId){
        params.addAll({
          'userId': UserOption.userInfoBeanEntity?.userId,
        });
      }
      if(params["userCode"] == null || params["userCode"] == ""){
        params.addAll({
          'userCode': UserOption.userInfoBeanEntity?.userCode,
        });
      }
      if(params["phone"] == null || params["phone"] == ""){
        params.addAll({
          'phone': UserOption.userInfoBeanEntity?.mobile,
        });
      }
      if((params["parentMemberType"] == null || params["parentMemberType"] == "") && isNeedParent){
        params.addAll({
          'parentMemberType': UserOption.parentMemberType,
        });
      }
      if(params["nickName"] == null || params["nickName"] == ""){
        params.addAll({
          'nickName': UserOption.userInfoBeanEntity?.nickName,
        });
      }
      if(params["headPortrait"] == null || params["headPortrait"] == ""){
        params.addAll({
          'headPortrait': UserOption.userInfoBeanEntity?.headPortrait,
        });
      }
    }

    // 处理查询参数
    final encodedParams = _encodeQueryParams(params);

    return encodedParams.isNotEmpty ? '$cleanPath?$encodedParams' : cleanPath;
  }

  /// 编码查询参数
  static String _encodeQueryParams(Map<String, dynamic>? params) {
    return params?.entries
        .where((entry) => entry.value != null)
        .map((entry) => '${entry.key}=${Uri.encodeComponent(entry.value.toString())}')
        .join('&') ?? '';
  }
}
