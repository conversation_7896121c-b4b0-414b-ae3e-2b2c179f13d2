<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_radius10_white"
    android:layout_marginLeft="46dp"
    android:layout_marginRight="46dp"
    android:orientation="vertical"
    >
    <TextView
        android:id="@+id/tv_tips"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="权限使用说明"
        android:layout_marginTop="14dp"
        android:gravity="center"
        android:textStyle="bold"
        android:textColor="@color/_333333"
        android:textSize="16sp"/>
    <TextView
        android:id="@+id/tv_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="14sp"
        android:gravity="center"
        android:layout_margin="20dp"
        android:textColor="@color/_999999"
        android:text=""/>
    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/_E3E1E1"/>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:orientation="horizontal">
        <TextView
            android:id="@+id/tv_cancel"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:text="拒绝"
            android:textSize="16sp"
            android:layout_weight="1"
            android:gravity="center"
            android:textColor="@color/_999999"/>
        <View
            android:layout_width="0.5dp"
            android:layout_height="match_parent"
            android:background="@color/_E3E1E1"/>
        <TextView
            android:id="@+id/tv_sure"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:text="同意"
            android:textSize="16sp"
            android:layout_weight="1"
            android:gravity="center"
            android:textColor="@color/_666666"/>
    </LinearLayout>
</LinearLayout>
