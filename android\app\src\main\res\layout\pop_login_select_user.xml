<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    >

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="440dp"
        android:layout_centerInParent="true"
        android:layout_marginLeft="14dp"
        android:layout_marginRight="14dp"
        android:background="@mipmap/pic_beijing"
        android:orientation="vertical"
        android:padding="20dp">

        <RelativeLayout
            android:id="@+id/rl_top"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="选择学员"
                android:textColor="@color/_333333"
                android:textSize="16sp"
                android:textStyle="bold" />

            <LinearLayout
                android:id="@+id/ll_close"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:gravity="center"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginLeft="14dp"
                    android:src="@mipmap/ic_close" />
            </LinearLayout>
        </RelativeLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@+id/ll_bottom"
            android:layout_below="@+id/rl_top"
            android:layout_marginLeft="30dp"
            android:layout_marginTop="30dp"
            android:layout_marginRight="30dp" />

        <LinearLayout
            android:id="@+id/ll_bottom"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="20dp"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_sure"
                android:layout_width="140dp"
                android:layout_height="40dp"
                android:background="@drawable/bg_radius20_428a6f"
                android:gravity="center"
                android:text="确定"
                android:textColor="@color/white"
                android:textSize="14sp" />
        </LinearLayout>
    </RelativeLayout>
</RelativeLayout>