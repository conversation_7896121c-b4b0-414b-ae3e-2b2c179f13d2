//
//  UIImageView+Extension.swift
//  StuMetApp
//
//  Created by 许江泉 on 2024/11/12.
//

import Foundation


extension UIImageView {
    
    class func creatWith(_ img: String = "defaultpic230px",
                         bgColor: UIColor = UIColor.clear,
                         radius: CGFloat = 0.0,
                         borderColor: UIColor = UIColor.clear,
                         borderWidth: CGFloat = 0.0

    ) -> UIImageView {
        let iv = UIImageView()
        iv.backgroundColor = bgColor
        iv.layer.cornerRadius = radius
        iv.layer.borderColor = borderColor.cgColor
        iv.layer.borderWidth = borderWidth
        iv.isUserInteractionEnabled = true
        if img.count > 0 {
            iv.image = UIImage.imgWithName(img)
        } else {
            iv.image = UIImage.imgWithName("defaultpic230px")
        }

        iv.contentMode = .scaleAspectFit
        return iv
    }

    func getRundedCornerImage(radius: CGFloat, rectSize: CGSize, fillColor: UIColor) -> UIImage? {
        UIGraphicsBeginImageContextWithOptions(rectSize, false, UIScreen.main.scale)
        if let currentContext = UIGraphicsGetCurrentContext() {
            let rect = CGRect(origin: .zero, size: rectSize)
            let outerPath = UIBezierPath(rect: rect)
            let innerPath = UIBezierPath(roundedRect: rect,
                                         byRoundingCorners: .allCorners,
                                         cornerRadii: CGSize(width: radius, height: radius))
            currentContext.setBlendMode(.normal)
            fillColor.setFill()
            outerPath.fill()

            currentContext.setBlendMode(.normal)
            innerPath.fill()
            let roundedCornerImage = UIGraphicsGetImageFromCurrentImageContext()
            UIGraphicsEndImageContext()
            return roundedCornerImage
        }
        return nil
    }

    /// 设置图片圆角
    func circleImage() {
        /// 建立上下文
        UIGraphicsBeginImageContextWithOptions(frame.size, false, 0)
        /// 获取当前上下文
        let ctx = UIGraphicsGetCurrentContext()
        /// 添加一个圆，并裁剪
        ctx?.addEllipse(in: bounds)
        ctx?.clip()
        /// 绘制图像
        draw(bounds)
        /// 获取绘制的图像
        let image = UIGraphicsGetImageFromCurrentImageContext()
        /// 关闭上下文
        UIGraphicsEndImageContext()
        DispatchQueue.global().async {
            self.image = image
        }
    }
}
