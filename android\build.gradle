buildscript {
    ext.kotlin_version = '1.8.22' // 使用你需要的Kotlin版本
    repositories {
        google()
        mavenCentral()
        maven { url 'https://developer.huawei.com/repo/' }
        maven { url 'https://developer.hihonor.com/repo' }
        maven {
            url = uri("https://storage.googleapis.com/r8-releases/raw")
        }
    }
    dependencies {
//        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath 'com.android.tools.build:gradle:7.3.1'
        classpath 'com.huawei.agconnect:agcp:1.8.0.300' // 主插件
        classpath 'com.hihonor.mcs:asplugin:2.0.1.300'
        classpath 'com.google.gms:google-services:4.3.15'
        classpath 'com.android.tools:r8:2.1.67'
    }

}

allprojects {
    repositories {
        google()
        mavenCentral()
        //导入aar需要的配置
        flatDir { dirs 'libs' }
//        maven { url "https://mirrors.tencent.com/nexus/repository/maven-public/" }
//        // 配置HMS Core SDK的Maven仓地址。
//        maven {url 'https://developer.huawei.com/repo/'}
//        maven {url 'https://developer.hihonor.com/repo'}
//        maven { url "https://mirrors.tencent.com/nexus/repository/maven-public/" }
        // 配置HMS Core SDK的Maven仓地址。
        maven { url "https://storage.googleapis.com/download.flutter.io" }
        maven { url 'https://developer.huawei.com/repo/' }
        maven { url 'https://developer.hihonor.com/repo' }
        maven {
            allowInsecureProtocol = true
            url 'https://maven.aliyun.com/nexus/content/groups/public/'
        }
        maven {
            allowInsecureProtocol = true
            url 'https://maven.aliyun.com/nexus/content/repositories/jcenter'
        }
        maven {
            allowInsecureProtocol = true
            url 'https://maven.aliyun.com/nexus/content/repositories/google'
        }
        maven {
            allowInsecureProtocol = true
            url 'https://maven.aliyun.com/nexus/content/repositories/gradle-plugin'
        }
        maven {
            allowInsecureProtocol = true
            url 'https://jitpack.io'
        }
        maven {
            allowInsecureProtocol = true
            url 'https://developer.huawei.com/repo/'
        }
        maven {
            allowInsecureProtocol = true
            url 'https://mirrors.huaweicloud.com/repository/maven/huaweicloudsdk/'
        }
        maven {
            allowInsecureProtocol = true
            url 'https://mirrors.huaweicloud.com/repository/maven/'
        }
        maven {
            allowInsecureProtocol = true
            url 'https://developer.huawei.com/repo/'
        }
        maven {
            allowInsecureProtocol = true
            url 'https://maven.aliyun.com/repository/releases'
        }
        maven {
            url "https://oss.sonatype.org/content/repositories/snapshots/"
        }

        google {
            content {
                includeGroupByRegex("com\\.android.*")
                includeGroupByRegex("com\\.google.*")
                includeGroupByRegex("androidx.*")
            }
        }
    }
}


rootProject.buildDir = "../build"
subprojects {
    afterEvaluate { project ->
        if (project.plugins.hasPlugin("com.android.application") ||
                project.plugins.hasPlugin("com.android.library")) {
            project.android {
                compileSdkVersion 34
                buildToolsVersion "34.0.0"
//                if (android.namespace == null) {
//                    namespace 'com.example.netease_roomkit'
//                }
            }
        }
    }
}
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}


subprojects {
    project.evaluationDependsOn(":app")
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}



