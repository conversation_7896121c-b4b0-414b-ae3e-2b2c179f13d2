package com.dxznjy.alading.common;

import com.tencent.mm.opensdk.modelbiz.WXLaunchMiniProgram;
/**
 */

public interface Constants {
    Integer VERTICAL = 0;//弹窗 列表 水平
    Integer HORIZONTAL = 1;//弹窗 列表 垂直
    Integer USERNAMELOGIN = 0;//账号密码登录
    Integer PHONECODELOGIN = 1;//账号验证码登录
    String STUDENT_INFO = "Student_Info";
    String STUDENT_NAME = "Student_Name";
    String STUDENT_CODE = "Student_Code";
    String MERCHANT_CODE = "MERCHANT_CODE";
    String SELECTED = "SELECTED";
    String PARENT_TOKEN = "Parent_Token";
    String STUDENT_TOKEN = "Student_TOKEN";
    String SHARE_PATH = "SHARE_PATH";
    String canBackFinish="canBackFinish";
    String STBG_UNIAPP_PAGE = "Trialclass/trialreport?app=2&type=1&trialname=";//试课报告
    String CHLJC_UNIAPP_PAGE = "parentEnd/vocabulary/vocabulary?app=1&studentCode="; //词汇量检测报告
    String CTB_UNIAPP_PAGE = "errorBook/index?app=2&studentCode=";//memberld=userCode  错题本
    String QWFX_UNIAPP_PAGE = "antiAmnesia/review/funReview?app=2&studentCode=";//studentCode=G00915&merchantCod=8221219595 趣味复习
    String XXNR_UNIAPP_PAGE = "Personalcenter/studyPrint/studyPrint?app=2&studentCode=";//学习内容打印
    String PYF_UNIAPP_PAGE = "PYFforget/forgetReview?app=1&studentCode=";//studentCode=6210118807   拼音法
    String KYW_UNIAPP_PAGE = "antiAmnesia/review/index?app=2&";//Buttonclick=6210118807&buttonclickName=陈    21天抗遗忘
    String TXJC_UNIAPP_PAGE = "parentEnd/dictation/dictationReport?app=2&&studentCode=";//听写检测报告
    //    String COURSE_STUDY="Coursedetails/study/courseDetail?app=1&courseId=";//进入学习
    String COURSE_FEEBACK = "Coursedetails/feedback/index?app=1&token=";//查看反馈
    String COURSE_VIDEO_DEATAIL = "Coursedetails/study/courseDetail?app=2";//录播课
    String COURSE_FEEBACK_ENGLISH = "Coursedetails/feedback/newIndex?app=1&token=";//查看鼎英语反馈
    //    String XCX_PKG_NAME="__UNI__E7982EF";//小程序正式包名
    String openUniAppWxPay = "wxpay";//打开微信支付小程序
    String openUniAppAliPay = "alipay";//打开支付宝小程序
    String openAndroidIos = "openAndroidIos";
    String openAndroidIosMeet = "openAndroidIosMeet";
    String openAndroidIosPay = "openAndroidIosPay";
    String openLessonsList = "openLessonsList";//打开交付课录播课列表
    String openLessonsDetail = "openLessonsDetail";//打开到课程权限界面
    String openVideoLessonsLearn = "openVideoLessonsLearn";//打开到录播课详情界面
    String initWeChatUnInstall = "initWeChatUnInstall";//微信未安装回传flutter
    String androidIosBackMeeting = "androidIosBackMeeting";//安卓ios回传通道
    String getAppSharePath = "getAppSharePath";//分享path方法
    String openShareApp = "openShareApp";

    String openShareMiniProgram = "openShareMiniProgram";

    String openNotificationSettings = "openNotificationSettings";

    String isOpenNotificationSettings = "isOpenNotificationSettings";


    String openShareAppPic = "openShareAppPic";

    String opneCustomerService = "opneCustomerService";
    String openDxnUniApp = "openDxnUniApp";
    String openUniApp = "openUniApp";//打开小程序

    String exitApp = "exitApp";//关闭应用
    String setUserIdForBugly="setUserIdForBugly";
    String twoBackFinish="twoBackFinish";

    String switchToHomeTab = "switchToHomeTab";


    String creatCalendar = "creatCalendar";//创建日历事件
    /**
     * 云信app 正式数据
     */
    String YX_APP_KEY = "9f920ca06eaf42806f7635fdb4e5e7eb";
    String YX_APP_SECRET = "d92a29f86e6c";
//   String SHARE_H5_URL = "http://**************:9020/#/home#/?";
    String SHARE_H5_URL = "https://zx-share.dxznjy.com/#/home/<USER>";
    /**
     * 云信app 测试数据
     */
//    String YX_APP_KEY="e39eb7ff1a808cf969ea9f7f52aed878";
//    String YX_APP_SECRET="eb2583cf500e";
//    String XCX_PKG_NAME="__UNI__9938909";//小程序测试名
//    String XCX_PKG_NAME = "__UNI__F0B6DB7";// xq小程序测试名
    String XCX_PKG_NAME="__UNI__D7DA9FC";//小程序测试名 公用名
//    String XCX_PKG_NAME="__UNI__A74DE53";//小程序测试名
    //1 正常 2 倍速 3 拖动视频 4 关闭视频
     Integer Normal = 1;
     Integer Speed = 2;
     Integer Drag = 3;
     Integer Close = 4;
     Integer useCoupons = 0;
     Integer goZxGoods = 1;

    //小程序支付id
    String MiniPayId="gh_e64a1a89a0ad";
    //鼎学能id
    String MiniDxnId="gh_32851b0f7b11";
//    int miniType= WXLaunchMiniProgram.Req.MINIPTOGRAM_TYPE_RELEASE;
     // 0-正式版，1-开发版，2-体验版
//    int miniType= WXLaunchMiniProgram.Req.MINIPROGRAM_TYPE_PREVIEW;
    int miniType= 0;
}