//
//  PermissionDetectionView.swift
//  StuMetApp
//
//  Created by yj9527 on 2024/12/14.
//

import UIKit
import QMUIKit
import SnapKit
import SnapKitExtend
import AVFoundation

class PermissionDetectionView: UIView {
    private var items = [UIView]()
    private var imageViews = [UIImageView]()
    private var isCamera = false
    private var isMicrophone = false
    private let cameraShapeLayer = CAShapeLayer()
    private let microphoneShapeLayer = CAShapeLayer()
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }

    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        setupPermission(permissionStr: "检测扬声器", type: .speaker)
        setupPermission(permissionStr: "检测麦克风", type: .microphone)
        setupPermission(permissionStr: "检测摄像头", type: .camera)
        items.snp.distributeViewsAlong(axisType: .vertical, fixedSpacing: LSize.normal, leadSpacing: 0, tailSpacing: 0)
        items.snp.makeConstraints{
            $0.width.left.equalToSuperview()
        }
        // 设置虚线属性
        microphoneShapeLayer.configureMicrophoneShapeLayer(strokeColor: UIColor.primary33, lineWidth: LSize.xSmall, lineDashPattern: [4, 4])
        layer.addSublayer(microphoneShapeLayer)

        cameraShapeLayer.configureMicrophoneShapeLayer(strokeColor: UIColor.primary33, lineWidth: LSize.xSmall, lineDashPattern: [4, 4])
        layer.addSublayer(cameraShapeLayer)
    }
    
    func setupPermission(permissionStr: String, type: PermissionType) {
        let speakerImage = UIImageView(image: R.image.icon_wancheng())
        speakerImage.isHidden = true
        let label = UILabel.createLabel(text: permissionStr, fontSize: SMFont.body2_1(), color: UIColor.hex555555)
        let btn = QMUIButton.createCustomButton(title: "检测", titleColor: UIColor.primary33, titleSize: SMFont.body2_1())
        btn.addTarget(self, action: #selector(buttonTapped), for: .touchUpInside)
        btn.tag = type.rawValue
        let pdView = UIView()
        addSubview(pdView)
        
        pdView.addSubview(speakerImage)
        speakerImage.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.centerY.equalToSuperview()
        }
        
        pdView.addSubview(label)
        label.snp.makeConstraints { make in
            make.left.equalTo(speakerImage.snp.right).offset(LSize.halfNormal)
            make.centerY.equalTo(speakerImage)
        }
        
        pdView.addSubview(btn)
        btn.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.right.equalToSuperview()
        }
        items.append(pdView)
        imageViews.append(speakerImage)
    }
    
    @objc private func buttonTapped(sender: QMUIButton) {
        let type = PermissionType(rawValue: sender.tag)
        switch type {
        case .speaker:
            speakerTap()
        case .microphone:
            microphoneTap()
        case .camera:
            cameraTap()
        case .none:
            break
        }
    }
    
    
    func permission()  {
        microphoneTap()
        cameraTap()
    }
    
    func speakerTap() {
        imageViews.first?.isHidden = false
    }

    func microphoneTap() {
        if isMicrophone {
            return
        }
        // 检查麦克风权限
        switch AVCaptureDevice.authorizationStatus(for: .audio) {
        case .authorized:
            DispatchQueue.main.async {
                self.isMicrophone = true
                self.imageViews.first?.isHidden = false
                self.imageViews[1].isHidden = false
                self.checkPermissionsAndDrawLine()
            }
        case .denied, .restricted:
            // 如果麦克风权限被拒绝或限制，跳转到设置页面
            DispatchQueue.main.async {
                self.isMicrophone = false
                self.showSettingsAlert(for: .audio)
            }
        case .notDetermined:
            AVCaptureDevice.requestAccess(for: .audio) { granted in
                if granted {
                    DispatchQueue.main.async {
                        self.isMicrophone = true
                        self.imageViews.first?.isHidden = false
                        self.imageViews[1].isHidden = false
                        self.checkPermissionsAndDrawLine()
                    }
                } else {
                    DispatchQueue.main.async {
                        self.isMicrophone = false
                        self.showSettingsAlert(for: .audio)
                    }
                }
            }
            break
        @unknown default: break
        }
    }

    func cameraTap() {
        if isCamera {
            return
        }
        switch AVCaptureDevice.authorizationStatus(for: .video) {
        case .authorized:
            DispatchQueue.main.async {
                self.isCamera = true
                self.imageViews.last?.isHidden = false
                self.checkPermissionsAndDrawLine()
            }
        case .denied, .restricted:
            // 如果摄像头权限被拒绝或限制，显示警告框并引导用户前往设置页面
            DispatchQueue.main.async {
                self.isCamera = false
                self.showSettingsAlert(for: .video)
            }

        case .notDetermined:
            // 如果摄像头权限状态未确定，请求权限
            AVCaptureDevice.requestAccess(for: .video) { granted in
                if granted {
                    DispatchQueue.main.async {
                        self.isCamera = true
                        self.imageViews.last?.isHidden = false
                        self.checkPermissionsAndDrawLine()
                    }

                } else {
                    // 如果用户拒绝授予摄像头权限，显示警告框并引导用户前往设置页面
                    DispatchQueue.main.async {
                        self.isCamera = false
                        self.showSettingsAlert(for: .video)
                    }
                }
            }
        @unknown default:
            // 处理未知的权限状态
            break
        }
    }

    func showSettingsAlert(for mediaType: AVMediaType) {
        let alertTitle = mediaType == .video ? "摄像头权限未开启" : "麦克风权限未开启"
        let alertMessage = mediaType == .video ? "请前往设置页面开启摄像头权限" : "请前往设置页面开启麦克风权限"
        let alertController = UIAlertController(title: alertTitle, message: alertMessage, preferredStyle: .alert)
        let settingsAction = UIAlertAction(title: "去设置", style: .default) { _ in
            // 跳转到设置页面
            if let settingsURL = URL(string: UIApplication.openSettingsURLString) {
                UIApplication.shared.open(settingsURL, options: [:], completionHandler: nil)
            }
        }
        let cancelAction = UIAlertAction(title: "取消", style: .cancel, handler: nil)
        alertController.addAction(settingsAction)
        alertController.addAction(cancelAction)
        currentViewController()?.present(alertController, animated: true, completion: nil)
    }

    func checkPermissionsAndDrawLine() {
        microphoneShapeLayer.path = nil
        cameraShapeLayer.path = nil
        let wh = (self.imageViews.first?.image?.size.width ?? 0) * PADDING_MIN
        // 如果麦克风权限已授予，从麦克风按钮底部中心开始绘制
        if isMicrophone {
            let speakerCenter = CGPoint(x: self.items.first!.frame.midX - self.items.first!.frame.size.width * 0.5 + wh, y: self.items.first!.frame.maxY)
            let micropCenter = CGPoint(x: self.items[1].frame.midX - self.items[1].frame.size.width * 0.5 + wh, y: self.items[1].frame.minY)
            microphoneShapeLayer.path = self.animateLine(startCenter: speakerCenter, endCenter: micropCenter)
            microphoneShapeLayer.addStrokeEndAnimation(fromValue: TIME, toValue: TIME, duration: LSize.xSmall, timingFunctionName: CAMediaTimingFunctionName.easeInEaseOut)
        }

        if isCamera {
            let micropCenter = CGPoint(x: self.items[1].frame.midX - self.items[1].frame.size.width * 0.5 + wh, y: self.items[1].frame.maxY)
            let cameraCenter = CGPoint(x: self.items.last!.frame.midX - self.items.last!.frame.size.width * 0.5 + wh, y: self.items.last!.frame.minY)
            cameraShapeLayer.path = self.animateLine(startCenter: micropCenter, endCenter: cameraCenter)
            cameraShapeLayer.addStrokeEndAnimation(fromValue: TIME, toValue: TIME, duration: LSize.xSmall, timingFunctionName: CAMediaTimingFunctionName.easeInEaseOut)
        }
    }
    
 

    func isMeeing() -> Bool {
        if !isCamera || !isMicrophone {
            appw?.l_showToastMessage(with: "请先点击检测来获取权限")
            return false
        }
        return true
    }
}
