//
//  MiniProgramBridge.swift
//  Runner
//
//  Created by 许江泉 on 2025/3/1.
//

import UIKit
import HandyJSON
import SwiftyJSON

enum PaymentMethod{
    case weChatpay
    case alipay
    case none
}

@objc class MiniProgramBridge: NSObject {
    func payType(typeString: String) -> PaymentMethod {
        var payType = PaymentMethod.none
        switch typeString {
        case "wxpay",
            "微信支付":
            payType = .weChatpay
        case "alipay",
            "支付宝支付":
            payType = .alipay
        default:
            payType = .none
        }
        return payType
    }
    
    @objc public func openTLPayFunc(_ jsonString: String, payType: String)  {
        switch self.payType(typeString: payType) {
        case .weChatpay:
            openWeChatpay(jsonString)
        case .alipay:
            openAlipay(jsonString)
            break
        case .none:
            break
        }
        
    }

  
    @objc public func appUseCoupon(_ path: String)  {
        appDelegate.flutterChannelManager?.callMethod(MethodNames.switchToHomeTab.rawValue, args: 0)
    }
    
    @objc public func goZxGoods()  {
        appDelegate.flutterChannelManager?.callMethod(MethodNames.switchToHomeTab.rawValue, args: 1)
    }
    
    @objc public func getPcmToFlutter()  {
        appDelegate.flutterChannelManager?.callMethod(MethodNames.nativeToFlutter.rawValue, args: 1)
    }
    
    
    @objc public func shareApp(_ params: [String: Any], shareType: NSNumber) {
//        UrlProtocolResolver.shareWechat(params)
        let intValue = shareType.intValue
        if intValue == 2 {
            UrlProtocolResolver.shareWechat(params, isMiniProgram:false)
        }else {
            UrlProtocolResolver.shareWechat(params, isMiniProgram:true)
        }
    }
    
    @objc public func customerService() {
        UrlProtocolResolver.contactCustomerService()
    }
    
    /// 微信支付
    func openWeChatpay(_ jsonStr: String)  {
        if let dic = jsonStr.toDictionary() {
            guard  let model =  PayModel.WechatPayModel.deserialize(from: dic) else {
                return
            }
            let order = PayParam(cusid: model.cusid, appid: model.appid, orgid: model.orgid, version: model.version, trxamt: model.trxamt, reqsn: model.reqsn, notify_url: model.notify_url, body: model.body, remark: model.remark, validtime: model.validtime, randomstr: model.randomstr, paytype: model.paytype, signtype: model.signtype, sign: model.sign, innerappid: model.innerappid)
            let req = WXLaunchMiniProgramReq()
            req.userName = SDKConfig.payweChatId
            req.path = order.toPath()
            req.miniProgramType = .release
            if WXApi.isWXAppInstalled() {// 检查用户是否安装微信
                WXApi.send(req) { resp in }
            }
        }
    }
 
    /// 支付宝支付
    func openAlipay(_ jsonStr: String) {
        // 尝试解析 JSON
        if let jsonData = jsonStr.data(using: .utf8),
           let json = try? JSONSerialization.jsonObject(with: jsonData) as? [String: Any]{
            // 如果解析成功，调用 generateAlipayURL
            _ = generateAlipayURL(jsonStr)
        } else {
            print("⚠️ 解析 JSON 失败或缺少 appid")
        }
    }
}

func generateAlipayURL(_ jsonString: String) -> String? {
    // 第一层编码：JSON字符串
    let encodedJson = urlEncode(jsonString)
    // 添加前缀并第二层编码
    let payInfo = "payinfo=\(encodedJson)"
    let finalQuery = urlEncode(payInfo)
    // 准备并编码thirdPartSchema
    let thirdPartSchema = "allinpaysdk://sdk/"
    let finalthirdPartSchema = urlEncode(thirdPartSchema)
    // 构造完整URL字符串
    let urlString = "alipays://platformapi/startapp?appId=2021001104615521&page=pages/orderDetail/orderDetail&thirdPartSchema=\(finalthirdPartSchema)&query=\(finalQuery)"
    // 验证URL
    guard let url = URL(string: urlString) else {
        print("URL格式错误: \(urlString)")
        return nil
    }
    // 跳转到支付宝
    UIApplication.shared.open(url, options: [:]) { success in
        if success {
            print("成功跳转到支付宝小程序支付")
        } else {
            appw?.l_showToastMessage(with: "请检查是否安装了支付宝应用")
        }
    }
    return urlString
}

func urlEncode(_ string: String) -> String {
    let allowedCharacters = CharacterSet(charactersIn: "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-_.*")
    let utf8Data = string.data(using: .utf8) ?? Data()
    var encodedString = ""
    for byte in utf8Data {
        let char = Character(UnicodeScalar(byte))
        if char == " " {
            encodedString.append("+")
        }
        else if String(char).rangeOfCharacter(from: allowedCharacters) != nil {
            encodedString.append(char)
        }else {
            encodedString.append(String(format: "%%%02X", byte))
        }
    }
    return encodedString
}
