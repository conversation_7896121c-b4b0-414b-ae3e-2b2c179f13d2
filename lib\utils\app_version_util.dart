import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:student_end_flutter/components/toast_utils.dart';

import '../bean/check_version_entity.dart';
import '../common/Config.dart';
import 'httpRequest.dart';
import 'package:sensors_analytics_flutter_plugin/sensors_analytics_flutter_plugin.dart';
class AppVersionUtil {
  static final String appName = "zhenxuanApp";
  static String device = "";
  static String? distinctId ='';
  static String? appVersion;

  static Future<void> getVersion() async {
    PackageInfo info = await PackageInfo.fromPlatform();
    appVersion = info.version;
  }
  static Future<void> getDistinctId() async{
    distinctId = await SensorsAnalyticsFlutterPlugin.getDistinctId;
  }
  ///请求接口返回最新版本
  static Future<bool> checkVersion({isShowTips = false}) async {
    try {
      // 获取设备 ID
      if (device.isEmpty) {
        await getDeviceId();
      }
      // 获取当前版本号
      if (appVersion == null || appVersion!.isEmpty) {
        await getVersion();
      }
      final response = await HttpUtil().get("${Config.checkVersion}?appName=$appName&device=${device.trim()}",);
      if (response == null) {
        _showLatestVersionToast(isShowTips);
        return false;
      }
      final CheckVersion? checkData = CheckVersion.fromMap(response);
      // 如果服务端返回的版本高于当前版本
      if (checkData != null
          && checkData.version != null
          && isLessThanManual(appVersion!, checkData.version!)) {
        if(isShowTips) ToastUtil.showToastText("您不是最新版本，最新版本 V: ${checkData.version}");
        // TODO: download
        return true;
      }
      _showLatestVersionToast(isShowTips);
      return false;
    } catch (e) {
      _showLatestVersionToast(isShowTips);
      return false;
    }
  }

  static void _showLatestVersionToast(isShowTips) {
    if(isShowTips) ToastUtil.showToastText("当前版本为最新版本 V: $appVersion");
  }

  /// 获取设备id
  static getDeviceId() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    if (Platform.isAndroid) {
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      device = androidInfo.device;  // 获取 Android 设备 ID
    } else if (Platform.isIOS) {
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      device = iosInfo.identifierForVendor!;
    }
  }

  /// 判断版本号 v1 是否“小于” 版本号 v2
  static bool isLessThanManual(String v1, String v2) {
    print("------");
    print(v1);
    print(v2);
    List<int> list1 = v1.split('.').map(int.parse).toList();
    List<int> list2 = v2.split('.').map(int.parse).toList();
    int len = list1.length > list2.length ? list1.length : list2.length;
    for (int i = 0; i < len; i++) {
      int a = i < list1.length ? list1[i] : 0;
      int b = i < list2.length ? list2[i] : 0;
      if (a < b) return true;
      if (a > b) return false;
    }
    return false;
  }

  /// 判断两个版本号是否“完全相等”。
  static bool isEqualManual(String v1, String v2) {
    List<int> list1 = v1.split('.').map(int.parse).toList();
    List<int> list2 = v2.split('.').map(int.parse).toList();
    int len = list1.length > list2.length ? list1.length : list2.length;
    for (int i = 0; i < len; i++) {
      int a = i < list1.length ? list1[i] : 0;
      int b = i < list2.length ? list2[i] : 0;
      if (a != b) return false;
    }
    return true;
  }
}
