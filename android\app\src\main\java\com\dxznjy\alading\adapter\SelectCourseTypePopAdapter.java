package com.dxznjy.alading.adapter;

import android.content.Context;
import android.graphics.Typeface;
import android.widget.RelativeLayout;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dxznjy.alading.R;
import com.dxznjy.alading.common.Constants;
import com.dxznjy.alading.response.CourseTypeResponse;

import java.util.List;


public class SelectCourseTypePopAdapter extends BaseQuickAdapter<CourseTypeResponse.CourseType, BaseViewHolder> {
    private Integer  mOrientation = Constants.VERTICAL;
    public SelectCourseTypePopAdapter(Context context, List<CourseTypeResponse.CourseType> data, Integer orientation) {
        super(R.layout.adapter_selectlist_pop, data);
        this.mContext = context;
        this.mOrientation = orientation;
    }

    @Override
    protected void convert(final BaseViewHolder helper, CourseTypeResponse.CourseType item) {
        RelativeLayout rl_bg = helper.getView(R.id.rl_bg);
        if (item.getType() != 0){
            if (item.isCheck()){
                rl_bg.setBackgroundResource(R.drawable.bg_radius4_f2faf7);
                helper.setTextColor(R.id.tv_title_vertical,mContext.getResources().getColor(R.color._428a6f));
                helper.setTextColor(R.id.tv_title_horizontal,mContext.getResources().getColor(R.color._428a6f));
                helper.setVisible(R.id.img_select,true);
                helper.setTypeface(R.id.tv_title_vertical, Typeface.DEFAULT_BOLD);

            }else{
                rl_bg.setBackgroundResource(R.drawable.bg_radius4_line_e1dede);
                helper.setTextColor(R.id.tv_title_vertical,mContext.getResources().getColor(R.color._555555));
                helper.setTextColor(R.id.tv_title_horizontal,mContext.getResources().getColor(R.color._555555));
                helper.setVisible(R.id.img_select,false);
                helper.setTypeface(R.id.tv_title_vertical, Typeface.DEFAULT);
            }
        }else{
            rl_bg.setBackgroundResource(R.drawable.bg_radius4_f7f7f7);
            helper.setTextColor(R.id.tv_title_vertical,mContext.getResources().getColor(R.color._b0aeae));
            helper.setTextColor(R.id.tv_title_horizontal,mContext.getResources().getColor(R.color._b0aeae));
            helper.setVisible(R.id.img_select,false);
            helper.setTypeface(R.id.tv_title_vertical, Typeface.DEFAULT);
        }
        if (mOrientation == Constants.VERTICAL){
            helper.setText(R.id.tv_title_vertical,item.getEnName());
            helper.setGone(R.id.tv_title_vertical,true);
            helper.setGone(R.id.tv_title_horizontal,false);
        }else{
            helper.setText(R.id.tv_title_horizontal,item.getEnName());
            helper.setGone(R.id.tv_title_vertical,false);
            helper.setGone(R.id.tv_title_horizontal,true);
        }
    }
}
