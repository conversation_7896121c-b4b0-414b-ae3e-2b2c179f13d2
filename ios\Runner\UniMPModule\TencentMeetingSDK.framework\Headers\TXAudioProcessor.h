#import <Foundation/Foundation.h>
#import "TRTCCloud.h"

// 前置声明
@class TXAudioProcessor;

// 新增代理协议
@protocol TXAudioProcessorDelegate <NSObject>
- (void)audioProcessor:(TXAudioProcessor *)processor didFinishRecordingWithFile:(NSString *)filePath sampleRate:(int)sampleRate channels:(int)channels;
@end

@interface TXAudioProcessor : NSObject

@property (nonatomic, weak) id<TXAudioProcessorDelegate> delegate; // 新增代理属性

// 可配置的音频参数 (在startAudioRecording前设置)
@property (nonatomic, assign) TRTCAudioSampleRate sampleRate; // 采样率
@property (nonatomic, assign) int channels;                  // 通道数
@property (nonatomic, strong) NSDictionary *jsData;          //用于缓存数据

// 单例访问
+ (instancetype)shared;
// 接收声音
- (void)onMixedAllAudioFrame:(TRTCAudioFrame *)frame;
// 开始录制
- (void)startAudioRecording;
// 开始定时录制（duration: 录制时长，单位秒）
- (void)startAudioRecordingWithDuration:(NSTimeInterval)duration;
// 停止录制
- (void)stopAudioRecording;
// 清理所有缓存文件
- (void)cleanupAllCacheFiles;
// 强行退出时停止录制并清理缓存
- (void)emergencyStopRecording;

@end
