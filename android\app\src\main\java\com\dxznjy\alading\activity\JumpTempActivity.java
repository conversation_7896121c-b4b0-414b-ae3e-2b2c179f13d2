package com.dxznjy.alading.activity;

import androidx.appcompat.app.AppCompatActivity;

import android.os.Bundle;

import com.dxznjy.alading.DXApplication;
import com.dxznjy.alading.common.Constants;

import io.flutter.plugin.common.MethodChannel;

public class JumpTempActivity extends AppCompatActivity {
     int jumpType=-1;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        jumpType = getIntent().getIntExtra("jumpType",-1);
        MethodChannel methodChannel = new MethodChannel(DXApplication.app.flutterEngineInstance.getDartExecutor().getBinaryMessenger(), Constants.androidIosBackMeeting);
        if (jumpType == Constants.useCoupons){
            methodChannel.invokeMethod(Constants.switchToHomeTab,Constants.useCoupons);
        }else if (jumpType == Constants.goZxGoods){
            methodChannel.invokeMethod(Constants.switchToHomeTab,Constants.goZxGoods);
        }
        finish();
    }
}