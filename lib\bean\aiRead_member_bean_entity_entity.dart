import 'package:student_end_flutter/generated/json/base/json_field.dart';
import 'package:student_end_flutter/generated/json/aiRead_member_bean_entity_entity.g.dart';
import 'dart:convert';
export 'package:student_end_flutter/generated/json/aiRead_member_bean_entity_entity.g.dart';

@JsonSerializable()
class AiReadMemberBeanEntityEntity {
	String merchantCode = '';
	String merchantName = '';

	AiReadMemberBeanEntityEntity();

	factory AiReadMemberBeanEntityEntity.fromJson(Map<String, dynamic> json) => $AiReadMemberBeanEntityEntityFromJson(json);

	Map<String, dynamic> toJson() => $AiReadMemberBeanEntityEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}