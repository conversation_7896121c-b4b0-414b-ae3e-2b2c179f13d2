//
//  BaseTableView.swift
//  StuMetApp
//
//  Created by 许江泉 on 2025/1/21.
//

import EmptyDataSet_Swift
import UIKit

enum EmptyDataState: Int {
    /// 状态0 - 暂无数据
    case stateNoData = 0
    /// 状态1 -网络请求错误
    case stateNetWorkError
    /// 状态2(网络不可用,请检查网络设置)
    case statenetworkDisable
    /// 状态3 - other
    case stateOhter
}
private let kNoDataStr = "暂无数据"
private let kNetWorkErrorStr = "网络不给力,点击重新加载"
var titleEmptyNoData: String = ""
var descriptionEmptyNoData: String = ""
protocol BaseTableViewDelegate: NSObjectProtocol {
    func updateTabListData()
}
extension BaseTableViewDelegate {
    func updateTabListData() {}
}

class BaseTableView: UITableView {
    
    weak var updateDelegate: BaseTableViewDelegate?
    var emptyData = EmptyDataState.stateOhter
    override init(frame: CGRect, style: UITableView.Style) {
        super.init(frame: frame, style: style)
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        fatalError("init(coder:) has not been implemented")
    }

    func setUpEmptyDataSet(_ type: EmptyDataState) {
        switch type {
        case .stateNoData:
            titleEmptyNoData = kNoDataStr
            descriptionEmptyNoData = "努力为您查找..."
        case .stateNetWorkError:
            titleEmptyNoData = kNoDataStr
            descriptionEmptyNoData = "网络不给力,点击重新加载"
        case .statenetworkDisable:
            titleEmptyNoData = kNoDataStr
            descriptionEmptyNoData = "打开网络"
        case .stateOhter:
            titleEmptyNoData = ""
            descriptionEmptyNoData = ""
        }
        emptyData = type
        initMethod()
        /// self.reloadEmptyDataSet()
    }

    private func initMethod() {
        configIOS11()
        emptyDataSetSource = self
        emptyDataSetDelegate = self
    }

    func serverErrorEmptyAction() {
        guard let updateDelegate = updateDelegate else {
            return
        }
        updateDelegate.updateTabListData()
    }

    func customEmptyAction() {
        guard let updateDelegate = updateDelegate else {
            return
        }
        updateDelegate.updateTabListData()
    }

    // MARK: - action

    private func emptyBtnAction() {
        switch emptyData {
        case .stateNetWorkError:
            serverErrorEmptyAction()
        case .statenetworkDisable:
            openLocationAction()
        default:
            customEmptyAction()
        }
    }

    private func openLocationAction() {
        if let url = URL(string: UIApplication.openSettingsURLString), UIApplication.shared.canOpenURL(url) {
            UIApplication.shared.open(url, options: [:], completionHandler: nil)
        }
    }

    // MARK: - iOS 适配

    private func configIOS11() {
        /// 适配 iOS 11.0 ,iOS11以后，控制器的automaticallyAdjustsScrollViewInsets已经废弃，所以默认就会是YES
        /// iOS 11新增：adjustContentInset 和 contentInsetAdjustmentBehavior 来处理滚动区域
        if #available(iOS 11.0, *) {
            self.estimatedRowHeight = 0
            self.estimatedSectionHeaderHeight = 0
            self.estimatedSectionFooterHeight = 0
            // 防止列表/页面偏移
            self.contentInsetAdjustmentBehavior = .never
        }
    }
}

extension BaseTableView: EmptyDataSetSource {
    func title(forEmptyDataSet scrollView: UIScrollView) -> NSAttributedString? {
        let text = titleEmptyNoData
        let attributes = [NSAttributedString.Key.font: UIFont.systemFont(ofSize: 16), NSAttributedString.Key.foregroundColor: UIColor.darkGray]
        return NSAttributedString(string: text, attributes: attributes)
    }

    func description(forEmptyDataSet scrollView: UIScrollView) -> NSAttributedString? {
        let text = descriptionEmptyNoData
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.lineBreakMode = .byWordWrapping
        paragraphStyle.alignment = .center
        let attributes = [NSAttributedString.Key.font: UIFont.systemFont(ofSize: 18), NSAttributedString.Key.foregroundColor: UIColor.lightGray, NSAttributedString.Key.paragraphStyle: paragraphStyle]
        return NSAttributedString(string: text, attributes: attributes)
    }

    func buttonTitle(forEmptyDataSet scrollView: UIScrollView, for state: UIControl.State) -> NSAttributedString? {
        return NSAttributedString(string: "重新加载")
    }

    func buttonImage(forEmptyDataSet scrollView: UIScrollView, for state: UIControl.State) -> UIImage? {
        return R.image.notData()
    }

    func verticalOffset(forEmptyDataSet scrollView: UIScrollView) -> CGFloat {
        return 10
    }

    func image(forEmptyDataSet scrollView: UIScrollView) -> UIImage? {
        return R.image.notData()
    }

    func imageAnimation(forEmptyDataSet scrollView: UIScrollView) -> CAAnimation? {
        let animation = CABasicAnimation(keyPath: "transform.rotation.z")
        animation.fromValue = 0
        animation.toValue = Double.pi * 2
        animation.duration = 5
        animation.autoreverses = false
        animation.fillMode = .forwards
        animation.repeatCount = MAXFLOAT
        animation.isRemovedOnCompletion = false
        return animation
    }
}

extension BaseTableView: EmptyDataSetDelegate {
    func emptyDataSetShouldDisplay(_ scrollView: UIScrollView) -> Bool {
        return numberOfRows(inSection: 0) == 0
    }

    func emptyDataSet(_ scrollView: UIScrollView, didTapButton button: UIButton) {
        emptyBtnAction()
    }

    func emptyDataSetShouldAnimateImageView(_ scrollView: UIScrollView) -> Bool {
        return true
    }
}
