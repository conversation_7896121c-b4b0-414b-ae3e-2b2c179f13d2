package com.dxznjy.alading.response;

public class YXAddUserResponse {

    private Integer code;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    private AddUserInfo data;

    public AddUserInfo getData() {
        return data;
    }

    public void setData(AddUserInfo data) {
        this.data = data;
    }

    public class AddUserInfo{
        private String   name;
        private String userUuid;
        private String      privateMeetingNum;
        private String      state;
        private String      userToken;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getUserUuid() {
            return userUuid;
        }

        public void setUserUuid(String userUuid) {
            this.userUuid = userUuid;
        }

        public String getPrivateMeetingNum() {
            return privateMeetingNum;
        }

        public void setPrivateMeetingNum(String privateMeetingNum) {
            this.privateMeetingNum = privateMeetingNum;
        }

        public String getState() {
            return state;
        }

        public void setState(String state) {
            this.state = state;
        }

        public String getUserToken() {
            return userToken;
        }

        public void setUserToken(String userToken) {
            this.userToken = userToken;
        }
    }

}

