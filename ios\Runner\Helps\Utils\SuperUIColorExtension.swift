//
//  SuperUIColorExtension.swift
//  颜色配置
// 定义颜色，主要是定义项目中的颜色，然后大部分情况用定义号的动态颜色，或者项目中直接引用
// 颜色不太好取名，例如：灰色，到底多少才是灰色，每个人，每个项目可能定义不一样
// 所以这里就不在按颜色名称取名，而是把要用到的颜色，按照黑色到白色（如果是其他颜色，例如：警告位置颜色，那可以定义为警告名称）定义出来
//
//  Created by smile on 2022/7/5.
//

import UIKit

import DynamicColor

//iOS中也提供了命名颜色，例如：.systemBackground，但无法更改他的颜色，Android中就可以根据浅色，深色修改命名的颜色，更方便
extension UIColor{
    /// 主色调
    static var primaryColor: UIColor { return DynamicColor(hex: 0xd6271c)}
    /// 暗一点 主色调
    static var primary30 : UIColor {return DynamicColor(hex: 0x28a781)}
    
    static var primary31 : UIColor {return DynamicColor(hex: 0x339378)}
    
    static var primary32 : UIColor {return DynamicColor(hex: 0x12c287)}
    
    static var primary33 : UIColor {return DynamicColor(hex: 0x428a6f)}
    
    static var primary34 : UIColor {return DynamicColor(hex: 0xf2faf7)}
    
    static var primary35 : UIColor {return DynamicColor(hex: 0x3f8970)}
    
    static var primary36 : UIColor {return DynamicColor(hex: 0xecf9f4)}
    
    static var lineColor : UIColor {return DynamicColor(hex: 0xf2f2f2)}
   
    static var borderColor : UIColor {return DynamicColor(hex: 0xe1dede)}
    
    static var btnColor : UIColor {return DynamicColor(hex: 0xf7f6f6)}
    
    static var black25 : UIColor {return DynamicColor(hex: 0x191919)}
    
    static var colorBackground: UIColor {return DynamicColor(hex: 0xfafafa)}
    
    static var black130 : UIColor {return DynamicColor(hex: 0xc8c8c8)}

//    #pragma mark - 黑色到白色
    //颜色命名也是很大的问题，关于颜色命名方式讨论：https://www.zhihu.com/question/301985702
    //以下命名后面的数字，没有实际意思
    //后面两位是透明度
    static var transparent88 : UIColor {return DynamicColor(hex: 0x88888888,useAlpha: true)}
    static var black11 : UIColor {return DynamicColor(hex: 0x435A70)}
    static var black15 : UIColor {return DynamicColor(hex: 0xa29d9d)}
    static var black17 : UIColor {return DynamicColor(hex: 0x223843)}
    static var notDataF: UIColor { return DynamicColor(hex: 0xB0AEAE) }
    /// 链接颜色
    static var link : UIColor {return DynamicColor(hex: 0x2440b3)}
    
    /// 亮灰色，例如：设置item右侧图标，右侧更多文本颜色
    static var lightGray : UIColor {return DynamicColor(hex: 0xf8f8f8)}
}

