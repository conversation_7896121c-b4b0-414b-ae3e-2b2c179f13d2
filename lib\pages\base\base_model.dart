import 'dart:convert';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';
import 'package:hex/hex.dart';
import 'package:http/http.dart' as http;

class TencentCloudAuth {
  final String secretId;
  final String secretKey;
  final String service;
  final String region;

  TencentCloudAuth({
    required this.secretId,
    required this.secretKey,
    required this.service,
    required this.region,
  });

  Map<String, String> getHeaders(int timestamp) {
    // 1. 计算日期
    final date = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000)
        .toUtc()
        .toString()
        .substring(0, 10);

    // 2. 分层计算签名密钥（关键步骤！）
    final kDate = _hmacSha256(utf8.encode('TC3$secretKey'), utf8.encode(date));
    final kService = _hmacSha256(kDate, utf8.encode(service));
    final kSigning = _hmacSha256(kService, utf8.encode('tc3_request'));

    // 3. 构造规范请求
    final canonicalRequest = '''
POST
/

content-type:application/json
host:asr.tencentcloudapi.com

content-type;host
${sha256.convert(utf8.encode('{}')).toString()}
''';

    // 4. 构造待签字符串
    final credentialScope = '$date/$service/tc3_request';
    final stringToSign = '''
TC3-HMAC-SHA256
$timestamp
$credentialScope
${sha256.convert(utf8.encode(canonicalRequest)).toString()}
''';

    // 5. 计算签名
    final signature = _hmacSha256(kSigning, utf8.encode(stringToSign));
    final signatureHex = HEX.encode(signature);

    // 6. 构造Authorization头
    final authorization =
        'TC3-HMAC-SHA256 Credential=$secretId/$credentialScope, '
        'SignedHeaders=content-type;host, Signature=$signatureHex';

    return {
      'Authorization': authorization,
      'Content-Type': 'application/json',
      'Host': 'asr.tencentcloudapi.com',
      'X-TC-Action': 'SentenceRecognition',
      'X-TC-Timestamp': timestamp.toString(),
      'X-TC-Version': '2019-06-14',
      'X-TC-Region': region,
    };
  }

  Uint8List _hmacSha256(Uint8List key, List<int> data) {
    final hmac = Hmac(sha256, key);
    return Uint8List.fromList(hmac.convert(data).bytes);
  }
}