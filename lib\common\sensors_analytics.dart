import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:sensors_analytics_flutter_plugin/sensors_analytics_flutter_plugin.dart';
import './Config.dart';
import 'package:student_end_flutter/pages/main/home_page.dart';
import 'package:student_end_flutter/pages/mine/mine_page.dart';
import 'package:student_end_flutter/pages/learn/learn_page.dart';
import 'package:student_end_flutter/pages/zx/zx_page.dart';
import 'package:tx_im/im_home_page.dart';
void initSensorsAnalytics() {
  // 获取服务器地址（从配置文件中读取）
  final platform = Platform.isAndroid ? "/log/do/sensors" : "/log/do/sensors/ios";
  final serverUrl = Config.formalURL + platform;
  WidgetsFlutterBinding.ensureInitialized();
  SensorsAnalyticsFlutterPlugin.init(
      serverUrl: serverUrl,
      enableLog: false,
      autoTrackTypes: <SAAutoTrackType>{
        // SAAutoTrackType.APP_START,
        SAAutoTrackType.APP_VIEW_SCREEN, // 确保包含此项
        SAAutoTrackType.APP_CLICK,
        // SAAutoTrackType.APP_END,
      },
      // autoTrackConfig: SAAutoTrackConfig(
      //   pageConfigs: [
      //     SAAutoTrackPageConfig<HomePage>(title: '首页', screenName: 'HomePage'),
      //     SAAutoTrackPageConfig<MinePage>(title: '我的', screenName: 'MinePage'),
      //     SAAutoTrackPageConfig<ZxPage>(title: '甄选', screenName: 'ZxPage'),
      //     SAAutoTrackPageConfig<LearnPage>(title: '学习', screenName: 'LearnPage'),
      //     SAAutoTrackPageConfig<IMModuleHomePage>(title: '消息', screenName: 'IMModuleHomePage'),
      //   ],
      // ),
      android: AndroidConfig(
          maxCacheSize: 32 * 1024 * 1024,
          jellybean: true,
          subProcessFlush: true),
      ios: IOSConfig(maxCacheSize: 10000));
  // print(SensorsAnalyticsFlutterPlugin.getDistinctId());
}
