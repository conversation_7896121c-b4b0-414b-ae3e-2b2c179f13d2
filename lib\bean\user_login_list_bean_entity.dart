import 'package:student_end_flutter/generated/json/base/json_field.dart';
import 'package:student_end_flutter/generated/json/user_login_list_bean_entity.g.dart';
import 'dart:convert';
export 'package:student_end_flutter/generated/json/user_login_list_bean_entity.g.dart';

@JsonSerializable()
class UserLoginListBeanEntity {
	late bool isCur = false;
	late String id = '';
	late String userCode = '';
	late String userType = '';
	late String waitAllocateFunds = '';
	late String totalMoney = '';
	late String availableCashAmount = '';
	late double status;
	late String unifiedId = '';
	late String paymentIn = '';
	late String paymentOut = '';

	UserLoginListBeanEntity();

	factory UserLoginListBeanEntity.fromJson(Map<String, dynamic> json) => $UserLoginListBeanEntityFromJson(json);

	Map<String, dynamic> toJson() => $UserLoginListBeanEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}