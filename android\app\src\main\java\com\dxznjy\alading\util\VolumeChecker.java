package com.dxznjy.alading.util;

import android.content.Context;
import android.media.AudioManager;

public class VolumeChecker {
    public static boolean isVolumeSlightlyLow(Context context) {
        AudioManager audioManager = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
        int maxVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC);
        int currentVolume = audioManager.getStreamVolume(AudioManager.STREAM_MUSIC);
        // 音量大于60% 允许入会
        return currentVolume > maxVolume *3/ 5;
    }
}
