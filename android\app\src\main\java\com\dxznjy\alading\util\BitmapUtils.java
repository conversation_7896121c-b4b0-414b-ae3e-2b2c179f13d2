package com.dxznjy.alading.util;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;

public class BitmapUtils {
    /**
     * 将Bitmap的字节流压缩为目标大小
     *
     * @param src
     * @param targetSize 单位B
     * @return
     */
    public static byte[] compressBitmapBytes2TargetSize(byte[] src, int targetSize) {
        // 将字节数据转换成临时bitmap对象，为压缩做准备
        Bitmap bmp = BitmapFactory.decodeByteArray(src, 0, src.length);
        byte[] result = getBytesFromCompressBitmap(bmp, targetSize);
        // 回收不用的Bitmap
        if (!bmp.isRecycled()) {
            bmp.recycle();
        }
        return result;
    }
    /**
     * 压缩bitmap的字节数据，quality每次减少5
     * @param bitmap
     * @param targetSize
     * @return
     */
    public static byte[] getBytesFromCompressBitmap(Bitmap bitmap, int targetSize) {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        // 默认quality为100，quality取值范围[0, 100]
        int quality = 100;
        bitmap.compress(Bitmap.CompressFormat.PNG, quality, baos);
        byte[] bytes = baos.toByteArray();
        while (bytes.length > targetSize && quality >= 5) {
            quality -= 5;
            if (quality < 0) {
                quality = 0;
            }
            // 重置，不然会累加
            baos.reset();
            // 将数据写入ByteArrayOutputStream对象中
            bitmap.compress(Bitmap.CompressFormat.JPEG, quality, baos);
            // 将流转换成字节数组
            bytes = baos.toByteArray();
        }
        // 关闭流
        try {
            baos.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return bytes;
    }


}
