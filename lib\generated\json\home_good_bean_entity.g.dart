import 'package:student_end_flutter/generated/json/base/json_convert_content.dart';
import 'package:student_end_flutter/bean/home_good_bean_entity.dart';

HomeGoodBeanEntity $HomeGoodBeanEntityFromJson(Map<String, dynamic> json) {
  final HomeGoodBeanEntity homeGoodBeanEntity = HomeGoodBeanEntity();
  final int? code = jsonConvert.convert<int>(json['code']);
  if (code != null) {
    homeGoodBeanEntity.code = code;
  }
  final bool? success = jsonConvert.convert<bool>(json['success']);
  if (success != null) {
    homeGoodBeanEntity.success = success;
  }
  final String? message = jsonConvert.convert<String>(json['message']);
  if (message != null) {
    homeGoodBeanEntity.message = message;
  }
  final HomeGoodBeanData? data = jsonConvert.convert<HomeGoodBeanData>(
      json['data']);
  if (data != null) {
    homeGoodBeanEntity.data = data;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    homeGoodBeanEntity.total = total;
  }
  return homeGoodBeanEntity;
}

Map<String, dynamic> $HomeGoodBeanEntityToJson(HomeGoodBeanEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['code'] = entity.code;
  data['success'] = entity.success;
  data['message'] = entity.message;
  data['data'] = entity.data.toJson();
  data['total'] = entity.total;
  return data;
}

extension HomeGoodBeanEntityExtension on HomeGoodBeanEntity {
  HomeGoodBeanEntity copyWith({
    int? code,
    bool? success,
    String? message,
    HomeGoodBeanData? data,
    int? total,
  }) {
    return HomeGoodBeanEntity()
      ..code = code ?? this.code
      ..success = success ?? this.success
      ..message = message ?? this.message
      ..data = data ?? this.data
      ..total = total ?? this.total;
  }
}

HomeGoodBeanData $HomeGoodBeanDataFromJson(Map<String, dynamic> json) {
  final HomeGoodBeanData homeGoodBeanData = HomeGoodBeanData();
  final String? currentPage = jsonConvert.convert<String>(json['currentPage']);
  if (currentPage != null) {
    homeGoodBeanData.currentPage = currentPage;
  }
  final String? totalPage = jsonConvert.convert<String>(json['totalPage']);
  if (totalPage != null) {
    homeGoodBeanData.totalPage = totalPage;
  }
  final String? size = jsonConvert.convert<String>(json['size']);
  if (size != null) {
    homeGoodBeanData.size = size;
  }
  final List<HomeGoodBeanDataData>? data = (json['data'] as List<dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<HomeGoodBeanDataData>(e) as HomeGoodBeanDataData)
      .toList();
  if (data != null) {
    homeGoodBeanData.data = data;
  }
  final String? totalItems = jsonConvert.convert<String>(json['totalItems']);
  if (totalItems != null) {
    homeGoodBeanData.totalItems = totalItems;
  }
  return homeGoodBeanData;
}

Map<String, dynamic> $HomeGoodBeanDataToJson(HomeGoodBeanData entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['currentPage'] = entity.currentPage;
  data['totalPage'] = entity.totalPage;
  data['size'] = entity.size;
  data['data'] = entity.data.map((v) => v.toJson()).toList();
  data['totalItems'] = entity.totalItems;
  return data;
}

extension HomeGoodBeanDataExtension on HomeGoodBeanData {
  HomeGoodBeanData copyWith({
    String? currentPage,
    String? totalPage,
    String? size,
    List<HomeGoodBeanDataData>? data,
    String? totalItems,
  }) {
    return HomeGoodBeanData()
      ..currentPage = currentPage ?? this.currentPage
      ..totalPage = totalPage ?? this.totalPage
      ..size = size ?? this.size
      ..data = data ?? this.data
      ..totalItems = totalItems ?? this.totalItems;
  }
}

HomeGoodBeanDataData $HomeGoodBeanDataDataFromJson(Map<String, dynamic> json) {
  final HomeGoodBeanDataData homeGoodBeanDataData = HomeGoodBeanDataData();
  final String? goodsId = jsonConvert.convert<String>(json['goodsId']);
  if (goodsId != null) {
    homeGoodBeanDataData.goodsId = goodsId;
  }
  final String? goodsName = jsonConvert.convert<String>(json['goodsName']);
  if (goodsName != null) {
    homeGoodBeanDataData.goodsName = goodsName;
  }
  final String? goodsDesc = jsonConvert.convert<String>(json['goodsDesc']);
  if (goodsDesc != null) {
    homeGoodBeanDataData.goodsDesc = goodsDesc;
  }
  final String? usageInstruction = jsonConvert.convert<String>(
      json['usageInstruction']);
  if (usageInstruction != null) {
    homeGoodBeanDataData.usageInstruction = usageInstruction;
  }
  final String? goodsCategoryId = jsonConvert.convert<String>(
      json['goodsCategoryId']);
  if (goodsCategoryId != null) {
    homeGoodBeanDataData.goodsCategoryId = goodsCategoryId;
  }
  final String? goodsCategoryName = jsonConvert.convert<String>(
      json['goodsCategoryName']);
  if (goodsCategoryName != null) {
    homeGoodBeanDataData.goodsCategoryName = goodsCategoryName;
  }
  final int? goodsType = jsonConvert.convert<int>(json['goodsType']);
  if (goodsType != null) {
    homeGoodBeanDataData.goodsType = goodsType;
  }
  final String? goodsPicUrl = jsonConvert.convert<String>(json['goodsPicUrl']);
  if (goodsPicUrl != null) {
    homeGoodBeanDataData.goodsPicUrl = goodsPicUrl;
  }
  final String? goodsSpecialPicUrl = jsonConvert.convert<String>(json['goodsSpecialPicUrl']);
  if (goodsSpecialPicUrl != null) {
    homeGoodBeanDataData.goodsSpecialPicUrl = goodsSpecialPicUrl;
  }
  // goodsSpecialPicUrl
  final String? goodsVideoUrl = jsonConvert.convert<String>(
      json['goodsVideoUrl']);
  if (goodsVideoUrl != null) {
    homeGoodBeanDataData.goodsVideoUrl = goodsVideoUrl;
  }
  final int? goodsStock = jsonConvert.convert<int>(json['goodsStock']);
  if (goodsStock != null) {
    homeGoodBeanDataData.goodsStock = goodsStock;
  }
  final int? goodsSales = jsonConvert.convert<int>(json['goodsSales']);
  if (goodsSales != null) {
    homeGoodBeanDataData.goodsSales = goodsSales;
  }
  final double? goodsCostPrice = jsonConvert.convert<double>(
      json['goodsCostPrice']);
  if (goodsCostPrice != null) {
    homeGoodBeanDataData.goodsCostPrice = goodsCostPrice;
  }
  final double? goodsMaxCostPrice = jsonConvert.convert<double>(
      json['goodsMaxCostPrice']);
  if (goodsMaxCostPrice != null) {
    homeGoodBeanDataData.goodsMaxCostPrice = goodsMaxCostPrice;
  }
  final double? goodsOriginalPrice = jsonConvert.convert<double>(
      json['goodsOriginalPrice']);
  if (goodsOriginalPrice != null) {
    homeGoodBeanDataData.goodsOriginalPrice = goodsOriginalPrice;
  }
  final double? goodsVipPrice = jsonConvert.convert<double>(
      json['goodsVipPrice']);
  if (goodsVipPrice != null) {
    homeGoodBeanDataData.goodsVipPrice = goodsVipPrice;
  }
  final double? goodsOriginalDiscountPrice = jsonConvert.convert<double>(
      json['goodsOriginalDiscountPrice']);
  if (goodsOriginalDiscountPrice != null) {
    homeGoodBeanDataData.goodsOriginalDiscountPrice =
        goodsOriginalDiscountPrice;
  }
  final double? goodsVipDiscountPrice = jsonConvert.convert<double>(
      json['goodsVipDiscountPrice']);
  if (goodsVipDiscountPrice != null) {
    homeGoodBeanDataData.goodsVipDiscountPrice = goodsVipDiscountPrice;
  }
  final String? goodsCurrencyUnit = jsonConvert.convert<String>(
      json['goodsCurrencyUnit']);
  if (goodsCurrencyUnit != null) {
    homeGoodBeanDataData.goodsCurrencyUnit = goodsCurrencyUnit;
  }
  final String? goodsCurrencyDesc = jsonConvert.convert<String>(
      json['goodsCurrencyDesc']);
  if (goodsCurrencyDesc != null) {
    homeGoodBeanDataData.goodsCurrencyDesc = goodsCurrencyDesc;
  }
  final int? goodsCommission = jsonConvert.convert<int>(
      json['goodsCommission']);
  if (goodsCommission != null) {
    homeGoodBeanDataData.goodsCommission = goodsCommission;
  }
  final double? goodsMaxOriginalPrice = jsonConvert.convert<double>(
      json['goodsMaxOriginalPrice']);
  if (goodsMaxOriginalPrice != null) {
    homeGoodBeanDataData.goodsMaxOriginalPrice = goodsMaxOriginalPrice;
  }
  final int? goodsMaxOriginalDiscountPrice = jsonConvert.convert<int>(
      json['goodsMaxOriginalDiscountPrice']);
  if (goodsMaxOriginalDiscountPrice != null) {
    homeGoodBeanDataData.goodsMaxOriginalDiscountPrice =
        goodsMaxOriginalDiscountPrice;
  }
  final double? goodsMaxVipPrice = jsonConvert.convert<double>(
      json['goodsMaxVipPrice']);
  if (goodsMaxVipPrice != null) {
    homeGoodBeanDataData.goodsMaxVipPrice = goodsMaxVipPrice;
  }
  final double? goodsShowPrice = jsonConvert.convert<double>(
      json['goodsShowPrice']);
  if (goodsShowPrice != null) {
    homeGoodBeanDataData.goodsShowPrice = goodsShowPrice;
  }
  final int? goodsMaxVipDiscountPrice = jsonConvert.convert<int>(
      json['goodsMaxVipDiscountPrice']);
  if (goodsMaxVipDiscountPrice != null) {
    homeGoodBeanDataData.goodsMaxVipDiscountPrice = goodsMaxVipDiscountPrice;
  }
  final int? goodsCollect = jsonConvert.convert<int>(json['goodsCollect']);
  if (goodsCollect != null) {
    homeGoodBeanDataData.goodsCollect = goodsCollect;
  }
  final int? goodsWeight = jsonConvert.convert<int>(json['goodsWeight']);
  if (goodsWeight != null) {
    homeGoodBeanDataData.goodsWeight = goodsWeight;
  }
  final String? goodsTagOne = jsonConvert.convert<String>(json['goodsTagOne']);
  if (goodsTagOne != null) {
    homeGoodBeanDataData.goodsTagOne = goodsTagOne;
  }
  final String? goodsTagTwo = jsonConvert.convert<String>(json['goodsTagTwo']);
  if (goodsTagTwo != null) {
    homeGoodBeanDataData.goodsTagTwo = goodsTagTwo;
  }
  final String? goodsTagThree = jsonConvert.convert<String>(
      json['goodsTagThree']);
  if (goodsTagThree != null) {
    homeGoodBeanDataData.goodsTagThree = goodsTagThree;
  }
  final int? goodsStatus = jsonConvert.convert<int>(json['goodsStatus']);
  if (goodsStatus != null) {
    homeGoodBeanDataData.goodsStatus = goodsStatus;
  }
  final String? onSaleTime = jsonConvert.convert<String>(json['onSaleTime']);
  if (onSaleTime != null) {
    homeGoodBeanDataData.onSaleTime = onSaleTime;
  }
  final String? curriculumId = jsonConvert.convert<String>(
      json['curriculumId']);
  if (curriculumId != null) {
    homeGoodBeanDataData.curriculumId = curriculumId;
  }
  final String? curriculumName = jsonConvert.convert<String>(
      json['curriculumName']);
  if (curriculumName != null) {
    homeGoodBeanDataData.curriculumName = curriculumName;
  }
  final HomeGoodBeanDataDataCoupon? coupon = jsonConvert.convert<
      HomeGoodBeanDataDataCoupon>(json['coupon']);
  if (coupon != null) {
    homeGoodBeanDataData.coupon = coupon;
  }
  final int? whetherCollect = jsonConvert.convert<int>(json['whetherCollect']);
  if (whetherCollect != null) {
    homeGoodBeanDataData.whetherCollect = whetherCollect;
  }
  final String? goodsSharePoster = jsonConvert.convert<String>(
      json['goodsSharePoster']);
  if (goodsSharePoster != null) {
    homeGoodBeanDataData.goodsSharePoster = goodsSharePoster;
  }
  final String? goodsSharePosterTwo = jsonConvert.convert<String>(
      json['goodsSharePosterTwo']);
  if (goodsSharePosterTwo != null) {
    homeGoodBeanDataData.goodsSharePosterTwo = goodsSharePosterTwo;
  }
  final String? goodsSharePosterThree = jsonConvert.convert<String>(
      json['goodsSharePosterThree']);
  if (goodsSharePosterThree != null) {
    homeGoodBeanDataData.goodsSharePosterThree = goodsSharePosterThree;
  }
  final int? goodsRecommendationWeight = jsonConvert.convert<int>(
      json['goodsRecommendationWeight']);
  if (goodsRecommendationWeight != null) {
    homeGoodBeanDataData.goodsRecommendationWeight = goodsRecommendationWeight;
  }
  final int? goodsRecommendationWhetherTop = jsonConvert.convert<int>(
      json['goodsRecommendationWhetherTop']);
  if (goodsRecommendationWhetherTop != null) {
    homeGoodBeanDataData.goodsRecommendationWhetherTop =
        goodsRecommendationWhetherTop;
  }
  final dynamic studentCourse = json['studentCourse'];
  if (studentCourse != null) {
    homeGoodBeanDataData.studentCourse = studentCourse;
  }
  final List<dynamic>? goodsSpecList = (json['goodsSpecList'] as List<dynamic>?)
      ?.map(
          (e) => e)
      .toList();
  if (goodsSpecList != null) {
    homeGoodBeanDataData.goodsSpecList = goodsSpecList;
  }
  final List<dynamic>? goodsSpecPriceList = (json['goodsSpecPriceList'] as List<
      dynamic>?)?.map(
          (e) => e).toList();
  if (goodsSpecPriceList != null) {
    homeGoodBeanDataData.goodsSpecPriceList = goodsSpecPriceList;
  }
  final List<dynamic>? goodsCarouselList = (json['goodsCarouselList'] as List<
      dynamic>?)?.map(
          (e) => e).toList();
  if (goodsCarouselList != null) {
    homeGoodBeanDataData.goodsCarouselList = goodsCarouselList;
  }
  final List<
      HomeGoodBeanDataDataGoodsShareTextList>? goodsShareTextList = (json['goodsShareTextList'] as List<
      dynamic>?)?.map(
          (e) =>
      jsonConvert.convert<HomeGoodBeanDataDataGoodsShareTextList>(
          e) as HomeGoodBeanDataDataGoodsShareTextList).toList();
  if (goodsShareTextList != null) {
    homeGoodBeanDataData.goodsShareTextList = goodsShareTextList;
  }
  final List<dynamic>? goodsCatalogueList = (json['goodsCatalogueList'] as List<
      dynamic>?)?.map(
          (e) => e).toList();
  if (goodsCatalogueList != null) {
    homeGoodBeanDataData.goodsCatalogueList = goodsCatalogueList;
  }
  final List<
      dynamic>? piGoodsExchangeCardList = (json['piGoodsExchangeCardList'] as List<
      dynamic>?)?.map(
          (e) => e).toList();
  if (piGoodsExchangeCardList != null) {
    homeGoodBeanDataData.piGoodsExchangeCardList = piGoodsExchangeCardList;
  }
  return homeGoodBeanDataData;
}

Map<String, dynamic> $HomeGoodBeanDataDataToJson(HomeGoodBeanDataData entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['goodsId'] = entity.goodsId;
  data['goodsName'] = entity.goodsName;
  data['goodsDesc'] = entity.goodsDesc;
  data['usageInstruction'] = entity.usageInstruction;
  data['goodsCategoryId'] = entity.goodsCategoryId;
  data['goodsCategoryName'] = entity.goodsCategoryName;
  data['goodsType'] = entity.goodsType;
  data['goodsPicUrl'] = entity.goodsPicUrl;
  data['goodsSpecialPicUrl'] = entity.goodsSpecialPicUrl;
  data['goodsVideoUrl'] = entity.goodsVideoUrl;
  data['goodsStock'] = entity.goodsStock;
  data['goodsSales'] = entity.goodsSales;
  data['goodsCostPrice'] = entity.goodsCostPrice;
  data['goodsMaxCostPrice'] = entity.goodsMaxCostPrice;
  data['goodsOriginalPrice'] = entity.goodsOriginalPrice;
  data['goodsVipPrice'] = entity.goodsVipPrice;
  data['goodsOriginalDiscountPrice'] = entity.goodsOriginalDiscountPrice;
  data['goodsVipDiscountPrice'] = entity.goodsVipDiscountPrice;
  data['goodsCurrencyUnit'] = entity.goodsCurrencyUnit;
  data['goodsCurrencyDesc'] = entity.goodsCurrencyDesc;
  data['goodsCommission'] = entity.goodsCommission;
  data['goodsMaxOriginalPrice'] = entity.goodsMaxOriginalPrice;
  data['goodsMaxOriginalDiscountPrice'] = entity.goodsMaxOriginalDiscountPrice;
  data['goodsMaxVipPrice'] = entity.goodsMaxVipPrice;
  data['goodsShowPrice'] = entity.goodsShowPrice;
  data['goodsMaxVipDiscountPrice'] = entity.goodsMaxVipDiscountPrice;
  data['goodsCollect'] = entity.goodsCollect;
  data['goodsWeight'] = entity.goodsWeight;
  data['goodsTagOne'] = entity.goodsTagOne;
  data['goodsTagTwo'] = entity.goodsTagTwo;
  data['goodsTagThree'] = entity.goodsTagThree;
  data['goodsStatus'] = entity.goodsStatus;
  data['onSaleTime'] = entity.onSaleTime;
  data['curriculumId'] = entity.curriculumId;
  data['curriculumName'] = entity.curriculumName;
  data['coupon'] = entity.coupon.toJson();
  data['whetherCollect'] = entity.whetherCollect;
  data['goodsSharePoster'] = entity.goodsSharePoster;
  data['goodsSharePosterTwo'] = entity.goodsSharePosterTwo;
  data['goodsSharePosterThree'] = entity.goodsSharePosterThree;
  data['goodsRecommendationWeight'] = entity.goodsRecommendationWeight;
  data['goodsRecommendationWhetherTop'] = entity.goodsRecommendationWhetherTop;
  data['studentCourse'] = entity.studentCourse;
  data['goodsSpecList'] = entity.goodsSpecList;
  data['goodsSpecPriceList'] = entity.goodsSpecPriceList;
  data['goodsCarouselList'] = entity.goodsCarouselList;
  data['goodsShareTextList'] =
      entity.goodsShareTextList.map((v) => v.toJson()).toList();
  data['goodsCatalogueList'] = entity.goodsCatalogueList;
  data['piGoodsExchangeCardList'] = entity.piGoodsExchangeCardList;
  return data;
}

extension HomeGoodBeanDataDataExtension on HomeGoodBeanDataData {
  HomeGoodBeanDataData copyWith({
    String? goodsId,
    String? goodsName,
    String? goodsDesc,
    String? usageInstruction,
    String? goodsCategoryId,
    String? goodsCategoryName,
    int? goodsType,
    String? goodsPicUrl,
    String? goodsSpecialPicUrl,
    String? goodsVideoUrl,
    int? goodsStock,
    int? goodsSales,
    double? goodsCostPrice,
    double? goodsMaxCostPrice,
    double? goodsOriginalPrice,
    double? goodsVipPrice,
    double? goodsOriginalDiscountPrice,
    double? goodsVipDiscountPrice,
    String? goodsCurrencyUnit,
    String? goodsCurrencyDesc,
    int? goodsCommission,
    double? goodsMaxOriginalPrice,
    int? goodsMaxOriginalDiscountPrice,
    double? goodsMaxVipPrice,
    double? goodsShowPrice,
    int? goodsMaxVipDiscountPrice,
    int? goodsCollect,
    int? goodsWeight,
    String? goodsTagOne,
    String? goodsTagTwo,
    String? goodsTagThree,
    int? goodsStatus,
    String? onSaleTime,
    String? curriculumId,
    String? curriculumName,
    HomeGoodBeanDataDataCoupon? coupon,
    int? whetherCollect,
    String? goodsSharePoster,
    String? goodsSharePosterTwo,
    String? goodsSharePosterThree,
    int? goodsRecommendationWeight,
    int? goodsRecommendationWhetherTop,
    dynamic studentCourse,
    List<dynamic>? goodsSpecList,
    List<dynamic>? goodsSpecPriceList,
    List<dynamic>? goodsCarouselList,
    List<HomeGoodBeanDataDataGoodsShareTextList>? goodsShareTextList,
    List<dynamic>? goodsCatalogueList,
    List<dynamic>? piGoodsExchangeCardList,
  }) {
    return HomeGoodBeanDataData()
      ..goodsId = goodsId ?? this.goodsId
      ..goodsName = goodsName ?? this.goodsName
      ..goodsDesc = goodsDesc ?? this.goodsDesc
      ..usageInstruction = usageInstruction ?? this.usageInstruction
      ..goodsCategoryId = goodsCategoryId ?? this.goodsCategoryId
      ..goodsCategoryName = goodsCategoryName ?? this.goodsCategoryName
      ..goodsType = goodsType ?? this.goodsType
      ..goodsPicUrl = goodsPicUrl ?? this.goodsPicUrl
      ..goodsSpecialPicUrl = goodsSpecialPicUrl ?? this.goodsSpecialPicUrl
      ..goodsVideoUrl = goodsVideoUrl ?? this.goodsVideoUrl
      ..goodsStock = goodsStock ?? this.goodsStock
      ..goodsSales = goodsSales ?? this.goodsSales
      ..goodsCostPrice = goodsCostPrice ?? this.goodsCostPrice
      ..goodsMaxCostPrice = goodsMaxCostPrice ?? this.goodsMaxCostPrice
      ..goodsOriginalPrice = goodsOriginalPrice ?? this.goodsOriginalPrice
      ..goodsVipPrice = goodsVipPrice ?? this.goodsVipPrice
      ..goodsOriginalDiscountPrice = goodsOriginalDiscountPrice ??
          this.goodsOriginalDiscountPrice
      ..goodsVipDiscountPrice = goodsVipDiscountPrice ??
          this.goodsVipDiscountPrice
      ..goodsCurrencyUnit = goodsCurrencyUnit ?? this.goodsCurrencyUnit
      ..goodsCurrencyDesc = goodsCurrencyDesc ?? this.goodsCurrencyDesc
      ..goodsCommission = goodsCommission ?? this.goodsCommission
      ..goodsMaxOriginalPrice = goodsMaxOriginalPrice ??
          this.goodsMaxOriginalPrice
      ..goodsMaxOriginalDiscountPrice = goodsMaxOriginalDiscountPrice ??
          this.goodsMaxOriginalDiscountPrice
      ..goodsMaxVipPrice = goodsMaxVipPrice ?? this.goodsMaxVipPrice
      ..goodsShowPrice = goodsShowPrice ?? this.goodsShowPrice
      ..goodsMaxVipDiscountPrice = goodsMaxVipDiscountPrice ??
          this.goodsMaxVipDiscountPrice
      ..goodsCollect = goodsCollect ?? this.goodsCollect
      ..goodsWeight = goodsWeight ?? this.goodsWeight
      ..goodsTagOne = goodsTagOne ?? this.goodsTagOne
      ..goodsTagTwo = goodsTagTwo ?? this.goodsTagTwo
      ..goodsTagThree = goodsTagThree ?? this.goodsTagThree
      ..goodsStatus = goodsStatus ?? this.goodsStatus
      ..onSaleTime = onSaleTime ?? this.onSaleTime
      ..curriculumId = curriculumId ?? this.curriculumId
      ..curriculumName = curriculumName ?? this.curriculumName
      ..coupon = coupon ?? this.coupon
      ..whetherCollect = whetherCollect ?? this.whetherCollect
      ..goodsSharePoster = goodsSharePoster ?? this.goodsSharePoster
      ..goodsSharePosterTwo = goodsSharePosterTwo ?? this.goodsSharePosterTwo
      ..goodsSharePosterThree = goodsSharePosterThree ??
          this.goodsSharePosterThree
      ..goodsRecommendationWeight = goodsRecommendationWeight ??
          this.goodsRecommendationWeight
      ..goodsRecommendationWhetherTop = goodsRecommendationWhetherTop ??
          this.goodsRecommendationWhetherTop
      ..studentCourse = studentCourse ?? this.studentCourse
      ..goodsSpecList = goodsSpecList ?? this.goodsSpecList
      ..goodsSpecPriceList = goodsSpecPriceList ?? this.goodsSpecPriceList
      ..goodsCarouselList = goodsCarouselList ?? this.goodsCarouselList
      ..goodsShareTextList = goodsShareTextList ?? this.goodsShareTextList
      ..goodsCatalogueList = goodsCatalogueList ?? this.goodsCatalogueList
      ..piGoodsExchangeCardList = piGoodsExchangeCardList ??
          this.piGoodsExchangeCardList;
  }
}

HomeGoodBeanDataDataCoupon $HomeGoodBeanDataDataCouponFromJson(
    Map<String, dynamic> json) {
  final HomeGoodBeanDataDataCoupon homeGoodBeanDataDataCoupon = HomeGoodBeanDataDataCoupon();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    homeGoodBeanDataDataCoupon.id = id;
  }
  final String? couponName = jsonConvert.convert<String>(json['couponName']);
  if (couponName != null) {
    homeGoodBeanDataDataCoupon.couponName = couponName;
  }
  final String? couponStartTime = jsonConvert.convert<String>(
      json['couponStartTime']);
  if (couponStartTime != null) {
    homeGoodBeanDataDataCoupon.couponStartTime = couponStartTime;
  }
  final String? couponEndTime = jsonConvert.convert<String>(
      json['couponEndTime']);
  if (couponEndTime != null) {
    homeGoodBeanDataDataCoupon.couponEndTime = couponEndTime;
  }
  final int? couponEffectiveDays = jsonConvert.convert<int>(
      json['couponEffectiveDays']);
  if (couponEffectiveDays != null) {
    homeGoodBeanDataDataCoupon.couponEffectiveDays = couponEffectiveDays;
  }
  final int? couponHasCondition = jsonConvert.convert<int>(
      json['couponHasCondition']);
  if (couponHasCondition != null) {
    homeGoodBeanDataDataCoupon.couponHasCondition = couponHasCondition;
  }
  final int? couponCondition = jsonConvert.convert<int>(
      json['couponCondition']);
  if (couponCondition != null) {
    homeGoodBeanDataDataCoupon.couponCondition = couponCondition;
  }
  final double? couponDiscount = jsonConvert.convert<double>(
      json['couponDiscount']);
  if (couponDiscount != null) {
    homeGoodBeanDataDataCoupon.couponDiscount = couponDiscount;
  }
  final int? couponLimitType = jsonConvert.convert<int>(
      json['couponLimitType']);
  if (couponLimitType != null) {
    homeGoodBeanDataDataCoupon.couponLimitType = couponLimitType;
  }
  final int? couponLimitQuantity = jsonConvert.convert<int>(
      json['couponLimitQuantity']);
  if (couponLimitQuantity != null) {
    homeGoodBeanDataDataCoupon.couponLimitQuantity = couponLimitQuantity;
  }
  final int? couponQuantity = jsonConvert.convert<int>(json['couponQuantity']);
  if (couponQuantity != null) {
    homeGoodBeanDataDataCoupon.couponQuantity = couponQuantity;
  }
  final int? couponReceivedQuantity = jsonConvert.convert<int>(
      json['couponReceivedQuantity']);
  if (couponReceivedQuantity != null) {
    homeGoodBeanDataDataCoupon.couponReceivedQuantity = couponReceivedQuantity;
  }
  final int? couponType = jsonConvert.convert<int>(json['couponType']);
  if (couponType != null) {
    homeGoodBeanDataDataCoupon.couponType = couponType;
  }
  final int? couponStatus = jsonConvert.convert<int>(json['couponStatus']);
  if (couponStatus != null) {
    homeGoodBeanDataDataCoupon.couponStatus = couponStatus;
  }
  final String? createdTime = jsonConvert.convert<String>(json['createdTime']);
  if (createdTime != null) {
    homeGoodBeanDataDataCoupon.createdTime = createdTime;
  }
  final String? updatedTime = jsonConvert.convert<String>(json['updatedTime']);
  if (updatedTime != null) {
    homeGoodBeanDataDataCoupon.updatedTime = updatedTime;
  }
  final int? personLimitType = jsonConvert.convert<int>(
      json['personLimitType']);
  if (personLimitType != null) {
    homeGoodBeanDataDataCoupon.personLimitType = personLimitType;
  }
  final int? couponUserType = jsonConvert.convert<int>(json['couponUserType']);
  if (couponUserType != null) {
    homeGoodBeanDataDataCoupon.couponUserType = couponUserType;
  }
  final int? couponReceivedType = jsonConvert.convert<int>(
      json['couponReceivedType']);
  if (couponReceivedType != null) {
    homeGoodBeanDataDataCoupon.couponReceivedType = couponReceivedType;
  }
  final int? couponTaskType = jsonConvert.convert<int>(json['couponTaskType']);
  if (couponTaskType != null) {
    homeGoodBeanDataDataCoupon.couponTaskType = couponTaskType;
  }
  final List<dynamic>? couponVoList = (json['couponVoList'] as List<dynamic>?)
      ?.map(
          (e) => e)
      .toList();
  if (couponVoList != null) {
    homeGoodBeanDataDataCoupon.couponVoList = couponVoList;
  }
  final int? showApp = jsonConvert.convert<int>(json['showApp']);
  if (showApp != null) {
    homeGoodBeanDataDataCoupon.showApp = showApp;
  }
  final String? gainWay = jsonConvert.convert<String>(json['gainWay']);
  if (gainWay != null) {
    homeGoodBeanDataDataCoupon.gainWay = gainWay;
  }
  final int? gainPersonLimitType = jsonConvert.convert<int>(
      json['gainPersonLimitType']);
  if (gainPersonLimitType != null) {
    homeGoodBeanDataDataCoupon.gainPersonLimitType = gainPersonLimitType;
  }
  final int? shareable = jsonConvert.convert<int>(json['shareable']);
  if (shareable != null) {
    homeGoodBeanDataDataCoupon.shareable = shareable;
  }
  final int? createRedemptionCode = jsonConvert.convert<int>(
      json['createRedemptionCode']);
  if (createRedemptionCode != null) {
    homeGoodBeanDataDataCoupon.createRedemptionCode = createRedemptionCode;
  }
  final int? useSharePersonLimitType = jsonConvert.convert<int>(
      json['useSharePersonLimitType']);
  if (useSharePersonLimitType != null) {
    homeGoodBeanDataDataCoupon.useSharePersonLimitType =
        useSharePersonLimitType;
  }
  return homeGoodBeanDataDataCoupon;
}

Map<String, dynamic> $HomeGoodBeanDataDataCouponToJson(
    HomeGoodBeanDataDataCoupon entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['couponName'] = entity.couponName;
  data['couponStartTime'] = entity.couponStartTime;
  data['couponEndTime'] = entity.couponEndTime;
  data['couponEffectiveDays'] = entity.couponEffectiveDays;
  data['couponHasCondition'] = entity.couponHasCondition;
  data['couponCondition'] = entity.couponCondition;
  data['couponDiscount'] = entity.couponDiscount;
  data['couponLimitType'] = entity.couponLimitType;
  data['couponLimitQuantity'] = entity.couponLimitQuantity;
  data['couponQuantity'] = entity.couponQuantity;
  data['couponReceivedQuantity'] = entity.couponReceivedQuantity;
  data['couponType'] = entity.couponType;
  data['couponStatus'] = entity.couponStatus;
  data['createdTime'] = entity.createdTime;
  data['updatedTime'] = entity.updatedTime;
  data['personLimitType'] = entity.personLimitType;
  data['couponUserType'] = entity.couponUserType;
  data['couponReceivedType'] = entity.couponReceivedType;
  data['couponTaskType'] = entity.couponTaskType;
  data['couponVoList'] = entity.couponVoList;
  data['showApp'] = entity.showApp;
  data['gainWay'] = entity.gainWay;
  data['gainPersonLimitType'] = entity.gainPersonLimitType;
  data['shareable'] = entity.shareable;
  data['createRedemptionCode'] = entity.createRedemptionCode;
  data['useSharePersonLimitType'] = entity.useSharePersonLimitType;
  return data;
}

extension HomeGoodBeanDataDataCouponExtension on HomeGoodBeanDataDataCoupon {
  HomeGoodBeanDataDataCoupon copyWith({
    String? id,
    String? couponName,
    String? couponStartTime,
    String? couponEndTime,
    int? couponEffectiveDays,
    int? couponHasCondition,
    int? couponCondition,
    double? couponDiscount,
    int? couponLimitType,
    int? couponLimitQuantity,
    int? couponQuantity,
    int? couponReceivedQuantity,
    int? couponType,
    int? couponStatus,
    String? createdTime,
    String? updatedTime,
    int? personLimitType,
    int? couponUserType,
    int? couponReceivedType,
    int? couponTaskType,
    List<dynamic>? couponVoList,
    int? showApp,
    String? gainWay,
    int? gainPersonLimitType,
    int? shareable,
    int? createRedemptionCode,
    int? useSharePersonLimitType,
  }) {
    return HomeGoodBeanDataDataCoupon()
      ..id = id ?? this.id
      ..couponName = couponName ?? this.couponName
      ..couponStartTime = couponStartTime ?? this.couponStartTime
      ..couponEndTime = couponEndTime ?? this.couponEndTime
      ..couponEffectiveDays = couponEffectiveDays ?? this.couponEffectiveDays
      ..couponHasCondition = couponHasCondition ?? this.couponHasCondition
      ..couponCondition = couponCondition ?? this.couponCondition
      ..couponDiscount = couponDiscount ?? this.couponDiscount
      ..couponLimitType = couponLimitType ?? this.couponLimitType
      ..couponLimitQuantity = couponLimitQuantity ?? this.couponLimitQuantity
      ..couponQuantity = couponQuantity ?? this.couponQuantity
      ..couponReceivedQuantity = couponReceivedQuantity ??
          this.couponReceivedQuantity
      ..couponType = couponType ?? this.couponType
      ..couponStatus = couponStatus ?? this.couponStatus
      ..createdTime = createdTime ?? this.createdTime
      ..updatedTime = updatedTime ?? this.updatedTime
      ..personLimitType = personLimitType ?? this.personLimitType
      ..couponUserType = couponUserType ?? this.couponUserType
      ..couponReceivedType = couponReceivedType ?? this.couponReceivedType
      ..couponTaskType = couponTaskType ?? this.couponTaskType
      ..couponVoList = couponVoList ?? this.couponVoList
      ..showApp = showApp ?? this.showApp
      ..gainWay = gainWay ?? this.gainWay
      ..gainPersonLimitType = gainPersonLimitType ?? this.gainPersonLimitType
      ..shareable = shareable ?? this.shareable
      ..createRedemptionCode = createRedemptionCode ?? this.createRedemptionCode
      ..useSharePersonLimitType = useSharePersonLimitType ??
          this.useSharePersonLimitType;
  }
}

HomeGoodBeanDataDataGoodsShareTextList $HomeGoodBeanDataDataGoodsShareTextListFromJson(
    Map<String, dynamic> json) {
  final HomeGoodBeanDataDataGoodsShareTextList homeGoodBeanDataDataGoodsShareTextList = HomeGoodBeanDataDataGoodsShareTextList();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    homeGoodBeanDataDataGoodsShareTextList.id = id;
  }
  final String? goodsId = jsonConvert.convert<String>(json['goodsId']);
  if (goodsId != null) {
    homeGoodBeanDataDataGoodsShareTextList.goodsId = goodsId;
  }
  final String? shareText = jsonConvert.convert<String>(json['shareText']);
  if (shareText != null) {
    homeGoodBeanDataDataGoodsShareTextList.shareText = shareText;
  }
  return homeGoodBeanDataDataGoodsShareTextList;
}

Map<String, dynamic> $HomeGoodBeanDataDataGoodsShareTextListToJson(
    HomeGoodBeanDataDataGoodsShareTextList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['goodsId'] = entity.goodsId;
  data['shareText'] = entity.shareText;
  return data;
}

extension HomeGoodBeanDataDataGoodsShareTextListExtension on HomeGoodBeanDataDataGoodsShareTextList {
  HomeGoodBeanDataDataGoodsShareTextList copyWith({
    String? id,
    String? goodsId,
    String? shareText,
  }) {
    return HomeGoodBeanDataDataGoodsShareTextList()
      ..id = id ?? this.id
      ..goodsId = goodsId ?? this.goodsId
      ..shareText = shareText ?? this.shareText;
  }
}