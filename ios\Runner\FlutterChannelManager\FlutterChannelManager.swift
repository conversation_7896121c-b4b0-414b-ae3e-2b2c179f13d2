//
//  FlutterChannelManager.swift
//  Runner
//
//  Created by 许江泉 on 2025/2/10.
//
import Flutter
import HandyJSON
import SwiftyJSON
import EventKit
import Bugly
import TencentMeetingSDK


class FlutterChannelManager {
    var mainChannel: FlutterMethodChannel?
    var openMeetingChannel: FlutterMethodChannel?
    var onEnterMeetingChannel: FlutterMethodChannel?
    var nativeToFlutterChannel: FlutterMethodChannel?
    var isProcessing = false
    
    init(controller: FlutterViewController) {
        // 初始化主通道
        mainChannel = FlutterMethodChannel(name: ChannelNames.openAndroidIos.rawValue, binaryMessenger: controller.binaryMessenger)
        mainChannel?.setMethodCallHandler(handleMethodCall)
        
        openMeetingChannel = FlutterMethodChannel(name: ChannelNames.openAndroidIosMeet.rawValue, binaryMessenger: controller.binaryMessenger)
        openMeetingChannel?.setMethodCallHandler(handleMethodCall)
        
        onEnterMeetingChannel = FlutterMethodChannel(name: ChannelNames.androidIosBackMeeting.rawValue, binaryMessenger: controller.binaryMessenger)
        onEnterMeetingChannel?.setMethodCallHandler(handleMethodCall)
        
        // 初始化原生到Flutter的通道
        nativeToFlutterChannel = FlutterMethodChannel(name: "nativeToFlutter", binaryMessenger: controller.binaryMessenger)
    }
    
    /// 注册方法给flutter调用
    /// - Parameters:
    ///   - call: 通道
    ///   - result: 回调
    private func handleMethodCall(call: FlutterMethodCall, result: @escaping FlutterResult) {
        switch call.method {
        case MethodNames.openShareMiniProgram.rawValue:  // 分享小程序
            if let data = call.arguments as? String  {
                guard let jsonData = data.data(using: .utf8) else { return }
                do {
                    let json = try JSON(data: jsonData)
                    if let dictionary = json.dictionaryObject {
                        UrlProtocolResolver.shareWechat(dictionary, isMiniProgram: true)
                    }
                } catch {
                   
                }
            }
            result("")
        case MethodNames.openUniApp.rawValue: // 打开小程序
            if let arguments = call.arguments as? Array<String> {
                if let data = arguments.first, let appId = arguments.last {
                    if let jsonData = data.data(using: .utf8) {
                        do {
                            // 尝试解析为 JSON 对象
                            let jsonObject = try JSONSerialization.jsonObject(with: jsonData, options: [])
                            if jsonObject is [String: Any] || jsonObject is [Any] {
                                // 符合 JSON 字典或数组结构
                                let json = try JSON(data: jsonData)
                                if let dictionary = json.dictionaryObject {
                                    UrlProtocolResolver.openUniMP(extraData: dictionary,appId: appId)
                                }
                            } else {
                                // 虽然是有效 JSON 语法，但不符合业务预期（如纯数字、布尔值）
                                UrlProtocolResolver.openUniMP(path: data,appId: appId)
                            }
                        } catch {
                            // 解析失败，视为普通字符串
                            UrlProtocolResolver.openUniMP(path: data,appId: appId)
                        }
                    } else {
                        // 无法转换为 Data，直接作为普通字符串处理
                        UrlProtocolResolver.openUniMP(path: data,appId: appId)
                    }
                }
            }
            result("")
        case MethodNames.openDxnUniApp.rawValue: // 打开鼎学能小程序
            if let path = call.arguments as? String {
                UrlProtocolResolver.openWechatMiniProgram(path: path)
            }
            result("")
        case MethodNames.creatCalendar.rawValue:
            guard let jsonString = call.arguments as? String,
                  let jsonData = jsonString.data(using: .utf8),
                  let arguments = try? JSONSerialization.jsonObject(with: jsonData) as? [String: Any],
                  let title = arguments["remindName"] as? String,
                  let startTime = arguments["beginTimestamp"],
                  let endTime = arguments["endTimestamp"] else {
                result(FlutterError(code: "INVALID_ARGUMENTS",
                                    message: "参数必须包含 remindName, beginTimestamp 和 endTimestamp",
                                    details: nil))
                return
            }
            func parseTimestamp(_ time: Any) -> Date? {
                if let string = time as? String, let millis = Double(string) {
                    return Date(timeIntervalSince1970: millis / 1000.0)
                } else if let number = time as? NSNumber {
                    return Date(timeIntervalSince1970: number.doubleValue / 1000.0)
                }
                return nil
            }
            guard let startDate = parseTimestamp(startTime),
                      let endDate = parseTimestamp(endTime) else {
                    result(FlutterError(code: "INVALID_TIMESTAMP",
                                        message: "时间戳格式错误，需为秒级或毫秒级数值/字符串",
                                        details: nil))
                    return
            }
            let eventStore = EKEventStore()
            eventStore.requestAccess(to: .event) { granted, error in
                if (granted==false){
                    self.showPermissionAlert()
                    return
                }
                if let error = error {
//                    appw?.l_showToastMessage(with: "访问日历失败，请检查权限设置")
                    self.showPermissionAlert()
                } else {
                    let event = EKEvent(eventStore: eventStore)
                    event.title = title
                    event.startDate = startDate
                    event.endDate = endDate
                    
                    // 设置日历
                    if let defaultCalendar = eventStore.defaultCalendarForNewEvents {
                        event.calendar = defaultCalendar
                    }
                    
                    // 添加两个提醒（提前30分钟 + 到点）
                    let alarm30min = EKAlarm(relativeOffset: -1800) // 提前30分钟（1800秒）
                    let alarm0min = EKAlarm(relativeOffset: 0)      // 事件开始时
                    event.addAlarm(alarm30min)
                    event.addAlarm(alarm0min)
                    
                    do {
                        try eventStore.save(event, span: .thisEvent)
                        DispatchQueue.main.async {
                            if let window = UIApplication.shared.windows.first(where: { $0.isKeyWindow }) {
                                window.l_showToastMessage(with: "日历提醒保存成功", time: 2.0, postion: .center)
                            }
                        }
//                        appw?.l_showToastMessage(with: "日历提醒保存成功")
                    } catch {
                        DispatchQueue.main.async {
                            if let window = UIApplication.shared.windows.first(where: { $0.isKeyWindow }) {
                                window.l_showToastMessage(with: "日历提醒保存失败，请检查权限设置", time: 2.0, postion: .center)
                            }
                        }
//                        appw?.l_showToastMessage(with: "日历提醒保存失败，请检查权限设置")
                    }
                }
            }
            result("")
        case MethodNames.openVideoLessonsLearn.rawValue: // 打开小程序录播课详情
            if let data = call.arguments as? String {
                if let model = QYParam.deserialize(from: data) {
                    let userId = model.userId ?? ""
                    let courseId = model.courseId ?? ""
                    let studentCode = model.studentCode ?? ""
                    let memberCode = model.memberCode ?? ""
                    let studentToken = model.parentToken ?? ""
                    let path = UniMPSDK.growthReport(token: studentToken, courseId: courseId, studentCode: studentCode, memberCode: memberCode, userId: userId).path
                    UrlProtocolResolver.openUniMP(path: data,appId: APPID)
                }
            }
            result("")
        case MethodNames.openLessonsDetail.rawValue: // 打开权限页面
            if let data = call.arguments as? String {
                if let model = QYParam.deserialize(from: data) {
                    let vc = EnterMettingController()
                    let dict = (try? JSONSerialization.jsonObject(with: Data(data.utf8), options: [])) as? [String: Any] ?? [:]
                    vc.setMeetingNum(meetingNum: model.meetingNum ?? "", identutyID: model.identutyID ?? "0", isPresent: true, txData: dict)
                    let nav = BaseNavViewController(rootViewController: vc)
                    currentViewController()?.presentTo(nav)
                }
            }
            result("")
        case MethodNames.openShareApp.rawValue: // 小程序去分享
            if let data = call.arguments as? String {
                guard let jsonData = data.data(using: .utf8) else { return }
                do {
                    let json = try JSON(data: jsonData)
                    if let dictionary = json.dictionaryObject {
                        UrlProtocolResolver.shareWechat(dictionary, isMiniProgram: false)
                    }
                } catch {}
            }
            result("")
        case MethodNames.setUserIdForBugly.rawValue:
            if let userId = call.arguments as? String {
                Bugly.setUserIdentifier(userId)
            }
            result("")
        case MethodNames.opneCustomerService.rawValue: // 打开在线客服
            UrlProtocolResolver.contactCustomerService()
            result("")
        case MethodNames.openNetwork.rawValue:
            UrlProtocolResolver.openNetwork()
            result("")
        case MethodNames.getAppSharePath.rawValue:
            if let path = appDelegate.paths {
               if path != "" {
                  result(path)
                  appDelegate.paths = ""
               }
           }
           result("")
        case MethodNames.exitApp.rawValue:
            exit(0)
            result("")
        case MethodNames.openShareAppPic.rawValue:
            if let data = call.arguments as? String {
                LShareManger.manager.shareImage(to: .wechatSession, image: data)
            }
        case MethodNames.releaseWgtToRunPath.rawValue:
            
                guard let args = call.arguments as? [String: Any],
                      let wgtPath = args["wgtPath"] as? String,
                      let appId = args["appId"] as? String else {
                    result(FlutterError(code: "INVALID_ARGUMENT", message: "wgtPath or appId is null", details: nil))
                    return
                }
            
            try? DCUniMPSDKEngine.installUniMPResource(withAppid: appId, resourceFilePath: wgtPath, password: nil)
            result("")
            
        default:
            result(FlutterMethodNotImplemented)
        }
    }


    /// 调用flutter方法
    /// - Parameters:
    ///   - method: 方法
    ///   - args: 参数
    func callMethod(_ method: String, args: Any? = nil) {
        if let onEnterMeetingChannel = self.onEnterMeetingChannel {
            onEnterMeetingChannel.invokeMethod(method, arguments: args)
        }
    }
    
    // MARK: - 新增方法：向Flutter发送消息（简单版）
    func notifyFlutter(message: String) {
        if let nativeToFlutterChannel = self.onEnterMeetingChannel {
            nativeToFlutterChannel.invokeMethod("nativeToFlutter", arguments: message)
        }
    }
    
    func showPermissionAlert() {
        let alert = UIAlertController(
            title: "需要日历权限",
            message: "请在设置中开启日历权限以创建提醒",
            preferredStyle: .alert
        )
        alert.addAction(UIAlertAction(title: "取消", style: .cancel, handler: nil))
        alert.addAction(UIAlertAction(title: "去设置", style: .default) { [weak self] _ in
            self?.openAppSettings()
        })
        
        // present方法需要在主线程调用
        DispatchQueue.main.async {
            if let viewController = self as? UIViewController {
                viewController.present(alert, animated: true, completion: nil)
            } else {
                // 如果self不是ViewController，使用rootViewController
                if let rootVC = UIApplication.shared.keyWindow?.rootViewController {
                    rootVC.present(alert, animated: true, completion: nil)
                }
            }
        }
    }
    
    // 跳转到应用设置页面的方法
    func openAppSettings() {
        DispatchQueue.main.async {
            if let settingsUrl = URL(string: UIApplication.openSettingsURLString),
               UIApplication.shared.canOpenURL(settingsUrl) {
                UIApplication.shared.open(settingsUrl, options: [:], completionHandler: nil)
            }
        }
    }
}
