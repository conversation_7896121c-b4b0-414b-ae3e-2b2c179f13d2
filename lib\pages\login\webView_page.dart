import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_state_manager/src/simple/get_view.dart';
import 'package:student_end_flutter/pages/login/controllers/webView_controller.dart';
import 'package:webview_flutter/webview_flutter.dart';
import '../../res/colors.dart';

class WebViewPage extends GetView<WebViewXController> {

  @override
  Widget build(BuildContext context) {
    Get.put(WebViewXController());
    return Scaffold(
        backgroundColor: AppColors.white,
        body: Column(
          children: [
            getTitleUI(),
            Expanded(child: WebViewWidget(controller: controller.webViewController,)),
          ],
        ));
  }

  getTitleUI() {
    return Container(
      margin: EdgeInsets.only(bottom: 10.h),
      height: 60.h,
      width: double.infinity,
      child:
      Stack(
        alignment: Alignment.bottomCenter,
        children: [
          Positioned(
              left: 10.w,
              child: GestureDetector(
                onTap: (){
                  Get.back();
                },
                child:  Icon(Icons.chevron_left,size: 35,),),),
          Text(controller.titleName,
              style: TextStyle(
                  color: AppColors.f333333, fontSize: 18.sp, fontFamily: "M"))
        ],
      ),
    );
  }
}
