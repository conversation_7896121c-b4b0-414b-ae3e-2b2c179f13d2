import 'package:student_end_flutter/generated/json/base/json_field.dart';
import 'package:student_end_flutter/generated/json/login_bean_entity.g.dart';
import 'dart:convert';
export 'package:student_end_flutter/generated/json/login_bean_entity.g.dart';

@JsonSerializable()
class LoginBeanEntity {
	late String tokenType = '';
	late String tokenHeader = '';
	late String token = '';
	late String issuedAt = '';
	late String expiresAt = '';
	late String principalName = '';
	late String refreshToken = '';
	late String refreshIssuedAt = '';
	dynamic callbackData;
	dynamic deviceInfo;
	late LoginBeanAdditionalParameters additionalParameters;

	LoginBeanEntity();

	factory LoginBeanEntity.fromJson(Map<String, dynamic> json) => $LoginBeanEntityFromJson(json);

	Map<String, dynamic> toJson() => $LoginBeanEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class LoginBeanAdditionalParameters {


	LoginBeanAdditionalParameters();

	factory LoginBeanAdditionalParameters.fromJson(Map<String, dynamic> json) => $LoginBeanAdditionalParametersFromJson(json);

	Map<String, dynamic> toJson() => $LoginBeanAdditionalParametersToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}