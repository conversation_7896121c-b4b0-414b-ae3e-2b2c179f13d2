package com.dxznjy.alading.response;


import com.dxznjy.alading.http.BaseResponse;

import java.util.List;

public class LoginResponse extends BaseResponse {

    private  Login data;

    public Login getData() {
        return data;
    }

    public void setData(Login data) {
        this.data = data;
    }

    public class Login{
        private String tokenType;
        private String      tokenHeader;
        private String      token;
        private String      issuedAt;
        private String     expiresAt;
        private String     principalName;
        private String     refreshToken;
        private String   refreshIssuedAt;
        private String      deviceInfo;
        private List<CallbackData> callbackData;


        public String getTokenType() {
            return tokenType;
        }

        public void setTokenType(String tokenType) {
            this.tokenType = tokenType;
        }

        public String getTokenHeader() {
            return tokenHeader;
        }

        public void setTokenHeader(String tokenHeader) {
            this.tokenHeader = tokenHeader;
        }

        public String getToken() {
            return token;
        }

        public void setToken(String token) {
            this.token = token;
        }

        public String getIssuedAt() {
            return issuedAt;
        }

        public void setIssuedAt(String issuedAt) {
            this.issuedAt = issuedAt;
        }

        public String getExpiresAt() {
            return expiresAt;
        }

        public void setExpiresAt(String expiresAt) {
            this.expiresAt = expiresAt;
        }

        public String getPrincipalName() {
            return principalName;
        }

        public void setPrincipalName(String principalName) {
            this.principalName = principalName;
        }

        public String getRefreshToken() {
            return refreshToken;
        }

        public void setRefreshToken(String refreshToken) {
            this.refreshToken = refreshToken;
        }

        public String getRefreshIssuedAt() {
            return refreshIssuedAt;
        }

        public void setRefreshIssuedAt(String refreshIssuedAt) {
            this.refreshIssuedAt = refreshIssuedAt;
        }

        public String getDeviceInfo() {
            return deviceInfo;
        }

        public void setDeviceInfo(String deviceInfo) {
            this.deviceInfo = deviceInfo;
        }


        public List<CallbackData> getCallbackData() {
            return callbackData;
        }

        public void setCallbackData(List<CallbackData> callbackData) {
            this.callbackData = callbackData;
        }
    }

    public static class CallbackData{

        private String loginName;
        private String studentCode;

        private String     realName;
        private String     memberCode;
        private Integer      grade;

        private Integer type=1;
        private boolean isCheck=false;

        public Integer getType() {
            return type;
        }

        public void setType(Integer type) {
            this.type = type;
        }

        public boolean isCheck() {
            return isCheck;
        }

        public void setCheck(boolean check) {
            isCheck = check;
        }

        public String getLoginName() {
            return loginName;
        }

        public void setLoginName(String loginName) {
            this.loginName = loginName;
        }

        public String getStudentCode() {
            return studentCode;
        }

        public void setStudentCode(String studentCode) {
            this.studentCode = studentCode;
        }

        public String getRealName() {
            return realName;
        }

        public void setRealName(String realName) {
            this.realName = realName;
        }

        public String getMemberCode() {
            return memberCode;
        }

        public void setMemberCode(String memberCode) {
            this.memberCode = memberCode;
        }

        public Integer getGrade() {
            return grade;
        }

        public void setGrade(Integer grade) {
            this.grade = grade;
        }
    }
}
