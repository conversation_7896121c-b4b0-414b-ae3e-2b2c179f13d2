import 'package:student_end_flutter/generated/json/base/json_convert_content.dart';
import 'package:student_end_flutter/bean/course_detail_bean_entity.dart';

CourseDetailBeanEntity $CourseDetailBeanEntityFromJson(
    Map<String, dynamic> json) {
  final CourseDetailBeanEntity courseDetailBeanEntity = CourseDetailBeanEntity();
  final String? studentName = jsonConvert.convert<String>(json['studentName']);
  if (studentName != null) {
    courseDetailBeanEntity.studentName = studentName;
  }
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    courseDetailBeanEntity.id = id;
  }
  final String? planId = jsonConvert.convert<String>(json['planId']);
  if (planId != null) {
    courseDetailBeanEntity.planId = planId;
  }
  final int? type = jsonConvert.convert<int>(json['type']);
  if (type != null) {
    courseDetailBeanEntity.type = type;
  }
  final String? date = jsonConvert.convert<String>(json['date']);
  if (date != null) {
    courseDetailBeanEntity.date = date;
  }
  final String? startTime = jsonConvert.convert<String>(json['startTime']);
  if (startTime != null) {
    courseDetailBeanEntity.startTime = startTime;
  }
  final String? endTime = jsonConvert.convert<String>(json['endTime']);
  if (endTime != null) {
    courseDetailBeanEntity.endTime = endTime;
  }
  final String? courseType = jsonConvert.convert<String>(json['courseType']);
  if (courseType != null) {
    courseDetailBeanEntity.courseType = courseType;
  }
  final String? teachingType = jsonConvert.convert<String>(
      json['teachingType']);
  if (teachingType != null) {
    courseDetailBeanEntity.teachingType = teachingType;
  }
  final String? teacher = jsonConvert.convert<String>(json['teacher']);
  if (teacher != null) {
    courseDetailBeanEntity.teacher = teacher;
  }
  final bool? isFeedback = jsonConvert.convert<bool>(json['isFeedback']);
  if (isFeedback != null) {
    courseDetailBeanEntity.isFeedback = isFeedback;
  }
  final bool? experience = jsonConvert.convert<bool>(json['experience']);
  if (experience != null) {
    courseDetailBeanEntity.experience = experience;
  }
  final String? dateTime = jsonConvert.convert<String>(json['dateTime']);
  if (dateTime != null) {
    courseDetailBeanEntity.dateTime = dateTime;
  }
  final String? curriculumName = jsonConvert.convert<String>(
      json['curriculumName']);
  if (curriculumName != null) {
    courseDetailBeanEntity.curriculumName = curriculumName;
  }
  return courseDetailBeanEntity;
}

Map<String, dynamic> $CourseDetailBeanEntityToJson(
    CourseDetailBeanEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['studentName'] = entity.studentName;
  data['id'] = entity.id;
  data['planId'] = entity.planId;
  data['type'] = entity.type;
  data['date'] = entity.date;
  data['startTime'] = entity.startTime;
  data['endTime'] = entity.endTime;
  data['courseType'] = entity.courseType;
  data['teachingType'] = entity.teachingType;
  data['teacher'] = entity.teacher;
  data['isFeedback'] = entity.isFeedback;
  data['experience'] = entity.experience;
  data['dateTime'] = entity.dateTime;
  data['curriculumName'] = entity.curriculumName;
  return data;
}

extension CourseDetailBeanEntityExtension on CourseDetailBeanEntity {
  CourseDetailBeanEntity copyWith({
    String? studentName,
    String? id,
    String? planId,
    int? type,
    String? date,
    String? startTime,
    String? endTime,
    String? courseType,
    String? teachingType,
    String? teacher,
    bool? isFeedback,
    bool? experience,
    String? dateTime,
    String? curriculumName,
  }) {
    return CourseDetailBeanEntity()
      ..studentName = studentName ?? this.studentName
      ..id = id ?? this.id
      ..planId = planId ?? this.planId
      ..type = type ?? this.type
      ..date = date ?? this.date
      ..startTime = startTime ?? this.startTime
      ..endTime = endTime ?? this.endTime
      ..courseType = courseType ?? this.courseType
      ..teachingType = teachingType ?? this.teachingType
      ..teacher = teacher ?? this.teacher
      ..isFeedback = isFeedback ?? this.isFeedback
      ..experience = experience ?? this.experience
      ..dateTime = dateTime ?? this.dateTime
      ..curriculumName = curriculumName ?? this.curriculumName;
  }
}