<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">
    <RelativeLayout
        android:id="@+id/rl_titlebar"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:background="@color/white">
        <ImageView
            android:id="@+id/img_base_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:padding="10dp"
            android:src="@mipmap/ic_back_black"
           />
        <TextView
            android:visibility="gone"
            android:layout_gravity="right"
            android:textColor="#666666"
            android:textSize="12sp"
            android:layout_alignParentRight="true"
            android:layout_margin="14dp"
            android:layout_width="wrap_content"
            android:gravity="center"
            android:layout_height="match_parent"/>
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:layout_centerVertical="true"
            android:layout_toRightOf="@+id/img_base_back"
            android:textSize="18sp"
            android:text="默认标题"
            android:textStyle="bold"
            android:singleLine="true"
            />
        <RelativeLayout
            android:layout_width="100dp"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_alignParentRight="true">
            <ImageView
                android:layout_gravity="right"
                android:scaleType="centerInside"
                android:layout_marginRight="4dp"
                android:layout_centerVertical="true"
                android:layout_toLeftOf="@+id/tvBtnRight"
                android:layout_width="22dp"
                android:gravity="center"
                android:visibility="gone"
                android:layout_height="22dp"/>
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:id="@+id/tvBtnRight"
                android:layout_gravity="right"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:textSize="12sp"
                android:visibility="invisible"
                android:layout_marginRight="14dp"
                android:gravity="center"/>

        </RelativeLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginRight="120dp"
            android:orientation="horizontal">
            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="6dp"
                android:layout_gravity="center"
                android:visibility="gone"/>
        </LinearLayout>
    </RelativeLayout>
    <RelativeLayout
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:background="@color/white"
        android:layout_height="match_parent">
        <!--加载失败-->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="gone">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                android:text="加载失败,点击重试"
                android:textSize="15sp" />
        </LinearLayout>

        <!--加载中..-->
        <ViewStub
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:inflatedId="@+id/panel_import"
             />

    </RelativeLayout>
</LinearLayout>
