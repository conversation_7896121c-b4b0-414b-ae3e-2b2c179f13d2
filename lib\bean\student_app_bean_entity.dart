
class StudentAppBeanEntity {
	late String studentCode = "";
	late String studentToken = "";
	late String parentToken = "";
	late String merchantCode = "";
	late String courseId = "";
	late String userId = "";
	late String meetingNum = "";
	late int selected = 0;
	late String identutyID = "";
	late String baseUrl="";
	Map toJson() => {
		"studentCode": studentCode,
		"studentToken": studentToken,
		"parentToken": parentToken,
		"merchantCode": merchantCode,
		"courseId": courseId,
		"userId": userId,
		'selected': selected,
		"meetingNum": meetingNum,
		"identutyID": identutyID,
		"baseUrl":baseUrl
	};
}