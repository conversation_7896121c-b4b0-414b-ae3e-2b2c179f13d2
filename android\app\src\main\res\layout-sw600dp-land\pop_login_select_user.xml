<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    >

    <RelativeLayout
        android:layout_width="360dp"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_marginTop="50dp"
        android:layout_marginBottom="100dp"
        android:background="@mipmap/pic_beijing"
        android:orientation="vertical"
        android:padding="20dp">


        <RelativeLayout
            android:id="@+id/rl_top"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="选择学员"
                android:textColor="@color/_333333"
                android:textSize="16sp"
                android:textStyle="bold" />

            <LinearLayout
                android:id="@+id/ll_close"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:gravity="center"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:layout_marginLeft="14dp"
                    android:src="@mipmap/ic_close" />
            </LinearLayout>
        </RelativeLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/rl_top"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="20dp"
            android:layout_marginRight="10dp" />

        <LinearLayout
            android:id="@+id/ll_bottom"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/recyclerView"
            android:layout_marginTop="40dp"
            android:layout_marginBottom="20dp"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_sure"
                android:layout_width="200dp"
                android:layout_height="40dp"
                android:background="@drawable/bg_radius20_428a6f"
                android:gravity="center"
                android:text="确定"
                android:textColor="@color/white"
                android:textSize="10sp" />
        </LinearLayout>
    </RelativeLayout>
</RelativeLayout>