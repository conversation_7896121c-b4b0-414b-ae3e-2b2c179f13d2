//
//  UIImage+Extension.swift
//  LUI_Example
//
//  Created by yj9527 on 2023/2/12.
//  Copyright © 2023 CocoaPods. All rights reserved.
//

import Foundation
import UIKit

extension UIImage {
    
    class func x_imageNamed(named: String) -> UIImage? {
        return UIImage(named: named)
    }
    /// 返回UIImage
    class func imgWithName(_ name: String) -> UIImage? {
        let img = UIImage.init(named: name)
        guard let _ = img else {

            return UIImage.init(named: "default")
        }
        return img
    }
    /// 颜色返回图片
    class func imageWithColor(_ color: UIColor) -> UIImage {

        let rect = CGRect(x: 0, y: 0, width: 1.0, height: 1.0)
        UIGraphicsBeginImageContext(rect.size)
        let context = UIGraphicsGetCurrentContext()
        context?.setFillColor(color.cgColor)
        context?.fill(rect)
        let image = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()

        return image!
    }
}


extension UIImage {
    func scaleImageToFitScreen() -> UIImage? {
        let screenWidth = UIScreen.main.bounds.width
        let screenHeight = UIScreen.main.bounds.height
        let imageAspectRatio = self.size.width / self.size.height
        let screenAspectRatio = screenWidth / screenHeight
        var scale: CGFloat
        if imageAspectRatio > screenAspectRatio {
            // Image is wider than screen; width should be scaled to fit
            scale = screenWidth / self.size.width
        } else {
            // Image is taller than screen or similar aspect ratio; height should be scaled to fit
            scale = screenHeight / self.size.height
        }
        
        // Create a scaled image that fits the screen
        let scaledSize = CGSize(width: self.size.width * scale, height: self.size.height * scale)
        UIGraphicsBeginImageContextWithOptions(scaledSize, false, 0.0)
        self.draw(in: CGRect(origin: .zero, size: scaledSize))
        let scaledImage = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        return scaledImage
    }

    /// 截屏使用
    func screenShotWithoutMask(shotView: UIView) -> UIImage {
        UIGraphicsBeginImageContextWithOptions(shotView.frame.size, true, 0.0)
        shotView.drawHierarchy(in: CGRect(origin: CGPoint(x: 0, y: 0), size: CGSize(width: ScreenWidth, height: ScreenHeight)), afterScreenUpdates: false)
        let image = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        return image!
    }
    
    /// 拉伸按钮背景图片
    class func resizableImage(_ image: UIImage) -> UIImage {
        let w: CGFloat = image.size.width * 0.5
        let h: CGFloat = image.size.height * 0.5
        return image.resizableImage(withCapInsets: UIEdgeInsets(top: w, left: h, bottom: w, right: h))
    }
}

extension UIImage {
    
    func scaleSuitableSize() -> UIImage? {
        var imageSize = self.size
        while imageSize.width * imageSize.height > 3 * 1000 * 1000 {
            imageSize.width *= 0.5
            imageSize.height *= 0.5
        }
        return self.scaleToFillSize(size: imageSize)
    }
    func scaleToFillSize(size: CGSize) -> UIImage? {
        if __CGSizeEqualToSize(self.size, size) {
            return self
        }
        UIGraphicsBeginImageContextWithOptions(size, false, self.scale)
        self.draw(in: CGRect(x: 0, y: 0, width: size.width, height: size.height))
        let image = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        return image
    }
    

}

extension UIImage {
    /// 深色图片和浅色图片切换 （深色模式适配）
     ///
     /// - Parameters:
     ///   - light: 浅色图片
     ///   - dark: 深色图片
    public static func image(lightStr: String, darkStr: String) -> UIImage {
     
         let light = UIImage(named: lightStr)
         let dark = UIImage(named: lightStr)

     if light != nil && dark != nil {
             return image(light: light!, dark: dark!)
         }else {
             return light ?? dark ?? UIImage()
         }
    }

   /// 深色图片和浅色图片切换 （深色模式适配）
    ///
    /// - Parameters:
    ///   - light: 浅色图片
    ///   - dark:  深色图片
    public static func image(light: UIImage, dark: UIImage) -> UIImage {
         if #available(iOS 13.0, *) {
             guard let config = light.configuration else { return light }
             let lightImage = light.withConfiguration(config.withTraitCollection(UITraitCollection.init(userInterfaceStyle: UIUserInterfaceStyle.light)))
             lightImage.imageAsset?.register(dark, with: config.withTraitCollection(UITraitCollection.init(userInterfaceStyle: UIUserInterfaceStyle.dark)))
             return lightImage.imageAsset?.image(with: UITraitCollection.current) ?? light
         } else {
             return light
         }
     }
}
