//
//  TXAudioUpload.h
//  Runner
//
//  Created by <PERSON><PERSON><PERSON>密码0000 on 2025/8/15.
//

#import <Foundation/Foundation.h>

typedef void(^DXCallback)(NSString *jsonStr);
@interface DXMeeting : NSObject
+ (instancetype)shared;
@property (nonatomic, copy) DXCallback callback;

//是否需要音频录制，请在joinMeetingFromArgs之前设置
@property (nonatomic, assign) BOOL needAudioRecording;

//加入会议，args为flutter传入
-(void)joinMeetingFromArgs:(NSDictionary *)args;
@property (nonatomic, strong) NSDictionary *args;

@end


