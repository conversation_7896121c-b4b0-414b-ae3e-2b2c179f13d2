import 'dart:io';

import 'package:flutter/services.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:path_provider/path_provider.dart';
///接收消息
typedef NativeHandler = Future<dynamic> Function(dynamic params);
/* **************** 具体业务方法名称 **************** */
class NativeMethods {
  // 会议相关
  static const String onEnterMeeting = 'onEnterMeeting';
  // 分享打开app
  static const String navigateToFlutterPage = 'navigateToFlutterPage';
  // 微信未安装提示
  static const String initWeChatUnInstall = 'initWeChatUnInstall';
  // 切换到首页
  static const String switchToHomeTab = 'switchToHomeTab';
  // 录音
  static const String nativeToFlutter = 'nativeToFlutter';
}
/* **************** 注册监听原生的方法 **************** */

class NativeBridge {
  static const String _channelName = 'androidIosBackMeeting';
  static final NativeBridge _instance = NativeBridge._internal();
  factory NativeBridge() => _instance;
  NativeBridge._internal() {
    _channel.setMethodCallHandler(_handleMethodCall);
  }

  final MethodChannel _channel = const MethodChannel(_channelName);
  final Map<String, NativeHandler> _handlers = {};
  // 注册方法处理器
  static void registerHandler(String methodName, NativeHandler handler) {
    _instance._handlers[methodName] = handler;
  }

  // 统一处理方法调用
  Future<dynamic> _handleMethodCall(MethodCall call) async {
    final handler = _handlers[call.method];
    if (handler == null) {
      throw MissingHandlerException('未注册方法: ${call.method}');
    }
    try {
      return await handler(call.arguments);
    } catch (e) {
      throw PlatformException(
        code: 'EXECUTION_ERROR',
        message: '方法执行失败: ${call.method}',
        details: e.toString(),
      );
    }
  }

  // 主动调用原生方法（可选）
  static Future<T?> invokeNative<T>(String method, String params) async {
    return await _instance._channel.invokeMethod<T>(method, params);
  }
}




class MissingHandlerException implements Exception {
  final String message;
  MissingHandlerException(this.message);
}



class PcmToWavConverter {
  /// 将 PCM 数据转换为 WAV 格式
  /// [pcmData]：原始 PCM 字节数据
  /// [sampleRate]：采样率（如 8000, 16000, 44100）
  /// [bitDepth]：位深（如 8, 16, 24）
  /// [channels]：声道数（1 或 2）
  /// 返回转换后的 WAV 字节数据
  static Uint8List convert({
    required Uint8List pcmData,
    required int sampleRate,
    required int bitDepth,
    required int channels,
  }) {
    // 参数校验
    _validateParameters(
      pcmData: pcmData,
      sampleRate: sampleRate,
      bitDepth: bitDepth,
      channels: channels,
    );

    // 计算 WAV 头部所需参数
    final int bytesPerSample = bitDepth ~/ 8;
    final int byteRate = sampleRate * channels * bytesPerSample;
    final int blockAlign = channels * bytesPerSample;
    final int dataSize = pcmData.length;
    final int totalFileSize = 36 + dataSize; // 标准 WAV 头部大小 + 数据大小

    // 创建 WAV 头部（44 字节）
    final Uint8List header = Uint8List(44);

    // RIFF 块
    header.setAll(0, 'RIFF'.codeUnits); // 0-3: RIFF 标识
    header.setAll(4, _intToBytes(totalFileSize, 4)); // 4-7: 文件总大小
    header.setAll(8, 'WAVE'.codeUnits); // 8-11: WAVE 标识

    // fmt 子块
    header.setAll(12, 'fmt '.codeUnits); // 12-15: fmt 标识
    header.setAll(16, _intToBytes(16, 4)); // 16-19: 子块大小（16 表示 PCM）
    header.setAll(20, _intToBytes(1, 2)); // 20-21: 音频格式（1 表示 PCM）
    header.setAll(22, _intToBytes(channels, 2)); // 22-23: 声道数
    header.setAll(24, _intToBytes(sampleRate, 4)); // 24-27: 采样率
    header.setAll(28, _intToBytes(byteRate, 4)); // 28-31: 字节率
    header.setAll(32, _intToBytes(blockAlign, 2)); // 32-33: 块对齐
    header.setAll(34, _intToBytes(bitDepth, 2)); // 34-35: 位深

    // data 子块
    header.setAll(36, 'data'.codeUnits); // 36-39: data 标识
    header.setAll(40, _intToBytes(dataSize, 4)); // 40-43: 数据大小

    // 拼接头部和 PCM 数据
    final Uint8List wavData = Uint8List(header.length + dataSize);
    wavData.setAll(0, header);
    wavData.setAll(header.length, pcmData);

    return wavData;
  }

  /// 参数校验，确保转换的合法性
  static void _validateParameters({
    required Uint8List pcmData,
    required int sampleRate,
    required int bitDepth,
    required int channels,
  }) {
    if (pcmData.isEmpty) {
      throw ArgumentError('PCM 数据不能为空');
    }

    if (sampleRate <= 0) {
      throw ArgumentError('采样率必须为正数，当前值: $sampleRate');
    }

    if (![8, 16, 24, 32].contains(bitDepth)) {
      throw ArgumentError('不支持的位深: $bitDepth，支持的值为 8, 16, 24, 32');
    }

    if (![1, 2].contains(channels)) {
      throw ArgumentError('不支持的声道数: $channels，仅支持单声道(1)和立体声(2)');
    }

    final int bytesPerSample = bitDepth ~/ 8;
    if (pcmData.length % (channels * bytesPerSample) != 0) {
      throw ArgumentError('PCM 数据长度不符合声道数和位深的要求，可能是数据损坏');
    }
  }

  /// 将整数转换为指定长度的小端字节序
  static Uint8List _intToBytes(int value, int bytesLength) {
    final Uint8List bytes = Uint8List(bytesLength);
    for (int i = 0; i < bytesLength; i++) {
      bytes[i] = (value >> (8 * i)) & 0xff;
    }
    return bytes;
  }
}




class WavFileSaver {
  /// 保存 WAV 数据到本地存储
  /// [wavData]：转换后的 WAV 字节数据
  /// [fileName]：保存的文件名（默认带时间戳，避免重复）
  /// 返回保存的文件路径
  static Future<String> saveWavToLocal({
    required Uint8List wavData,
    String? fileName,
  }) async {
    // 1. 申请文件写入权限（Android 13+ 需申请）
    if (Platform.isAndroid) {
      final status = await Permission.storage.request();
      if (!status.isGranted) {
        throw Exception('未获得文件写入权限，请在设置中开启');
      }
    }

    // 2. 获取本地存储路径
    final Directory directory;
    if (Platform.isAndroid) {
      // Android：保存到外部存储的音频目录（可被媒体库扫描到）
      directory = Directory('/storage/emulated/0/Music/FlutterWav');
    } else if (Platform.isIOS) {
      // iOS：保存到应用沙盒的文档目录
      directory = await getApplicationDocumentsDirectory();
    } else {
      // 桌面平台：保存到用户文档目录
      directory = await getApplicationDocumentsDirectory();
    }

    // 确保目录存在
    if (!await directory.exists()) {
      await directory.create(recursive: true);
    }

    // 3. 生成文件名（默认格式：wav_20240520_153045.wav）
    final String timeStamp =
    DateTime.now().toString().replaceAll(RegExp(r'[:.]'), '_');
    final String saveFileName = fileName ?? 'wav_$timeStamp.wav';
    final File file = File('${directory.path}/$saveFileName');

    // 4. 写入 WAV 数据到文件
    await file.writeAsBytes(wavData);
    print('WAV 文件保存成功：${file.path}');
    return file.path;
  }
}




