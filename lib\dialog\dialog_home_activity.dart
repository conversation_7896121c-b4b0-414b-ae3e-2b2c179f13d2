import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../bean/popup_model_entity.dart';
import '../common/CommonUtil.dart';
import '../common/user_option.dart';
import '../navigator/home_navigator.dart';
import '../utils/android_ios_plugin.dart';
import '../common/sensors_analytics_option.dart';
import '../utils/app_version_util.dart';
class PopupController extends GetxController {
  String title;
  PopupController(this.title);
  late SharedPreferences _prefs;
  static const _storagePrefix = 'popup_expire_';
  final RxList<PopupModelData> popupQueue = RxList<PopupModelData>([]);
  String? currentImageUrl; // 当前显示的图片 URL
  String? currentBtnBgc; // 当前显示的活动弹窗按钮颜色
  String? currentPopupLinkUrl; // 跳转的link

  @override
  void onInit() async {
    super.onInit();
    _prefs = await SharedPreferences.getInstance();
    _cleanExpiredKeys(); // 初始化时清理过期数据
  }

  /// 显示弹窗队列（传入 URL 数组）
  void showPopups(List<PopupModelData> urls) {
    final currentTime = DateTime.now().millisecondsSinceEpoch;
    final validPopups = urls.where((item) {
      final expireTime = _prefs.getInt('$_storagePrefix${item.id}') ?? 0;
      return currentTime > expireTime; // 过期校验逻辑
    }).toList();

    popupQueue.addAll(validPopups);
    if (popupQueue.isNotEmpty) _showNextPopup();
  }

  /// 显示下一个弹窗
  void _showNextPopup() {
    if (popupQueue.isEmpty) return;
    final currentPopup = popupQueue.first;
    currentImageUrl = currentPopup.popupPicUrl;
    currentBtnBgc =
        currentPopup.color.trim().isEmpty ? '#339378' : currentPopup.color;
    currentPopupLinkUrl = currentPopup.popupLinkUrl;
    TrackingUtils.trackPageView('OpenPrePopupEv', {
      'common_fields': '预热活动弹框曝光',
      'avitcity_id': currentPopup.id,
      'goods_id':'','banner_id':'','url_path': title
    });
    showDialog(
      context: Get.context!,
      builder: (context) => CustomPopup(
        title:title,
        activityId: currentPopup.id,
        imageUrl: currentImageUrl!,
        color: currentBtnBgc!,
        onClose: () => _handleClose(),
        onConfirm: (isChecked) => _handleConfirm(isChecked, currentPopup),
      ),
    );
  }

  /// 处理关闭弹窗
  void _handleClose() {
    if (popupQueue.isEmpty) return;
    Navigator.pop(Get.context!);
    currentImageUrl = null;
    currentBtnBgc = null;
    currentPopupLinkUrl = null;
    popupQueue.removeAt(0);
    _showNextPopup(); // 显示下一个弹窗
  }

  /// 弹窗确认处理
  void _handleConfirm(bool isChecked, PopupModelData popupModelData) {
    if (UserOption.checkNoTokenJump()) return;
    final popupId = popupModelData.id;
    TrackingUtils.trackEvent(
        ' PopupClick', {'common_fields': '弹框确认按钮点击','avitcity_id': popupId,'goods_id':'','banner_id':'','url_path': title});
    if (isChecked) {
      final now = DateTime.now();
      final endTime = DateTime(now.year, now.month, now.day, 23, 59, 59);
      _prefs.setInt('$_storagePrefix$popupId',
          endTime.millisecondsSinceEpoch); // 精确时间存储[6](@ref)
    }

    Navigator.pop(Get.context!);
    popupQueue.removeAt(0);
    currentBtnBgc = null;
    currentImageUrl = null;
    _showNextPopup();

    /// popupType 1商品 2链接 3页面
    String path = '';
    if (popupModelData.goodsId.isNotEmpty && popupModelData.popupType == 1) {
      path = HomeNavigator.goodsDetailsActivity(
              goodsId: popupModelData.goodsId, activityId: popupId)
          .path;
    } else {
      path = popupModelData.popupLinkUrl;
      if (popupModelData.popupLinkUrl.isNotEmpty) {
        if (popupModelData.popupLinkUrl.startsWith("/")) {
          popupModelData.popupLinkUrl =
              popupModelData.popupLinkUrl.substring(1);
        }
        if (popupModelData.popupLinkUrl.contains('?')) {
          path =
              '${popupModelData.popupLinkUrl}&activityId=${popupModelData.id}';
        } else {
          path =
              '${popupModelData.popupLinkUrl}?activityId=${popupModelData.id}';
        }
        if (popupModelData.popupLinkUrl.contains('groupActivityId')) {
          path = popupModelData.popupLinkUrl;
        }
      }
    }
    path += "&token=${UserOption.token}&app=2&options=${title}&appVersion=${AppVersionUtil.appVersion ?? ''}&distinctId=${AppVersionUtil.distinctId ?? ''}";
    print(path);
    print(popupModelData.popupLinkUrl);
    print('kkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkk');
    if (path.isNotEmpty) {
      AndroidIosPlugin.openUniApp(path);
    }
  }

  /// 清理过期数据
  void _cleanExpiredKeys() {
    final currentTime = DateTime.now().millisecondsSinceEpoch;
    _prefs.getKeys().where((k) => k.startsWith(_storagePrefix)).forEach((key) {
      if ((_prefs.getInt(key) ?? 0) < currentTime) {
        _prefs.remove(key);
      }
    });
  }
}

class CustomPopup extends StatefulWidget {
  final String title;
  final String activityId;
  final String imageUrl;
  final String color;
  final Function(bool) onConfirm;
  final Function() onClose;

  const CustomPopup({
    super.key,
    required this.title,
    required this.activityId,
    required this.imageUrl,
    required this.color,
    required this.onConfirm,
    required this.onClose,
  });

  @override
  State<CustomPopup> createState() => _CustomPopupState();
}

class _CustomPopupState extends State<CustomPopup> {
  bool _isChecked = false;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: EdgeInsets.zero,
      backgroundColor: Colors.transparent,
      child: WillPopScope(
        onWillPop: () async => false,
        child: Stack(
          children: [
            // 主内容区域
            Container(
              width: MediaQuery.of(context).size.width * 0.9,
              height: MediaQuery.of(context).size.height * 0.9,
              child: Column(
                children: [
                  // 图片占80%高度
                  Expanded(
                    flex: 8, // 占80%
                    child: ClipRRect(
                      child: Image.network(
                        widget.imageUrl,
                        fit: BoxFit.contain,
                        errorBuilder: (_, __, ___) => Container(
                          color: Colors.grey,
                          child: const Center(child: Text('图片加载失败')),
                        ),
                      ),
                    ),
                  ),
                  // 底部按钮区域占20%
                  Expanded(
                    flex: 2,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          // 确认按钮
                          SizedBox(
                            width: 160,
                            height: 44,
                            child: ElevatedButton(
                              onPressed: () => widget.onConfirm(_isChecked),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: HexColor(widget.color),
                                foregroundColor: Colors.white,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(22),
                                ),
                                padding:
                                    const EdgeInsets.symmetric(vertical: 6),
                              ),
                              child: const Text('确认'),
                            ),
                          ),
                          // 复选框
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Checkbox(
                                  value: _isChecked,
                                  onChanged: (value) {
                                    _isChecked = value ?? false;
                                    TrackingUtils.trackEvent(
                                        'PopupNeverShowClick', {
                                      'common_fields': '选择今日不再提醒',
                                      'avitcity_id': widget.activityId,
                                      'goods_id':'','banner_id':'','url_path': widget.title
                                    });
                                  },
                                  checkColor: Colors.white,
                                  shape: CircleBorder(),
                                  fillColor:
                                      MaterialStateProperty.resolveWith<Color?>(
                                          (states) => Colors.transparent),
                                  side: MaterialStateBorderSide.resolveWith(
                                      (Set<MaterialState> states) {
                                    if (states
                                        .contains(MaterialState.selected)) {
                                      return BorderSide(
                                          color: Colors.white,
                                          width: 1); // 选中时白色边框
                                    }
                                    return BorderSide(
                                        color: Colors.white54,
                                        width: 1); // 未选中状态的淡白边框
                                  })),
                              const Text(
                                '今日不再提醒',
                                style: TextStyle(color: Colors.white),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            // 关闭按钮
            Positioned(
              top: 20,
              right: 20,
              child: GestureDetector(
                onTap: widget.onClose,
                child: Container(
                  padding: const EdgeInsets.all(4),
                  decoration: const BoxDecoration(
                    color: Colors.black54,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(Icons.close, color: Colors.white, size: 24),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
