import 'dart:convert';

import 'package:bot_toast/bot_toast.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:dio/dio.dart';
import 'package:intl/intl.dart';
import 'package:student_end_flutter/bean/leave_apply_info_entity.dart';
import 'package:student_end_flutter/bean/make_up_class_times_entity.dart';
import 'package:student_end_flutter/bean/review_course_list_bean_entity.dart';
import 'package:student_end_flutter/components/toast_utils.dart';
import 'package:student_end_flutter/dialog/dialog_ask_for_leave.dart';
import 'package:student_end_flutter/dialog/dialog_select_date.dart';
import 'package:student_end_flutter/navigator/learn_navigator.dart';
import 'package:student_end_flutter/pages/tx_meeting/web_page/tx_meeting_web_page.dart';
import 'package:student_end_flutter/utils/android_ios_plugin.dart';
import '../../../bean/course_detail_bean_entity.dart';
import '../../../bean/course_list_bean_entity.dart';
import '../../../bean/course_record_list_bean_entity.dart';
import '../../../bean/course_type_bean_entity.dart';
import '../../../bean/student_app_bean_entity.dart';
import '../../../common/Config.dart';
import '../../../common/ne_user_option.dart';
import '../../../common/user_option.dart';
import '../../../dialog/dialog_learn_select_identity.dart';
import '../../../dialog/dialog_select_lesson_type.dart';
import '../../../generated/json/base/json_convert_content.dart';
import '../../../utils/httpRequest.dart';
import '../../../utils/ne_metting_utils.dart';

class LessonListController extends GetxController
    with GetSingleTickerProviderStateMixin {
  RxInt currentTabIndex = 0.obs; //交付课   录播课
  RxInt currentLessonStatusTabIndex = 0.obs; //未上课 已上课
  RxInt pageNum = 1.obs;
  RxString curriculumTypeId = ''.obs;
  String merchantCode = '';
  String studentCode = '';
  String studentToken = '';
  RxBool canLoadMore = true.obs;
  TabController? tabController;
  RxBool isFootShow = true.obs;
  RxList<CourseListBeanData> courseData = RxList<CourseListBeanData>([]); //
  RxList<CourseRecordListBeanData> courseRecordData =
      RxList<CourseRecordListBeanData>([]);
  RxList<ReviewCourseListBeanData> reviewCourseData =
      RxList<ReviewCourseListBeanData>([]);

  int selectCourseTypeIndex = 0; //课程类型选择下标
  List<CourseTypeBeanEntity> courseTypeList = [];
  bool isDisable = false;

  /// 按日期筛选课程列表相关变量
  /// 是否根据日期筛选了课程列表
  RxBool isSelectDate = false.obs;
  /// 按日期筛选后的课程列表 (伴学课)
  RxList<CourseListBeanData> datedCourseData = RxList<CourseListBeanData>([]);
  /// 日期筛选组件当前选中的日期
  RxString selectedDate = ''.obs;

  /// 是否正在加载请假弹窗回显数据
  bool isLoadingLeaveInfo = false;

  @override
  Future<void> onInit() async {
    super.onInit();
    currentTabIndex.value = Get.arguments["currentTabIndex"] ?? 0;
    merchantCode = Get.arguments["merchantCode"] ?? '';
    studentCode = Get.arguments["studentCode"] ?? '';
    studentToken = Get.arguments["studentToken"] ?? '';
    tabController = TabController(vsync: this, length: 3)..addListener(() {});
    tabController?.animation?.addListener(() {
      currentTabIndex.value = tabController?.animation!.value.round() ?? 0;
      if (tabController?.index == tabController?.animation!.value) {
        changeTab(currentTabIndex.value);
      }
    });
    if (currentTabIndex.value == 0) {
      //交付课
      getDeliverCoursePage(true);
    } else if (currentTabIndex.value == 1) {
      //录播课
      getRecordCoursePage(true);
    } else if (currentTabIndex.value == 2) {
      //复习课
      getReviewCourse(true);
    }
  }

  //切换 交付课 录播课
  changeTab(index) {
    currentTabIndex.value = index;
    currentLessonStatusTabIndex.value = 0;
    curriculumTypeId.value = "";
    selectCourseTypeIndex = 0;
    tabController?.animateTo(index);
    if (index == 0) {
      //交付课
      getDeliverCoursePage(true);
    } else if (index == 1) {
      //录播课
      getRecordCoursePage(true);
    } else if (index == 2) {
      //复习课
      getReviewCourse(true);
    }
  }

  //获取交付课列表   未上课 已上课
  getDeliverCoursePage(refresh) async {
    if (refresh) {
      pageNum.value = 1;
      canLoadMore.value = true;
    } else {
      pageNum.value++;
    }
    var response = HttpUtil(url: Config.getDeliverCoursePage).get(
        "${Config.getDeliverCoursePage}?merchantCode=$merchantCode&courseStatus=${currentLessonStatusTabIndex.value}&pageNum=${pageNum.value}&pageSize=10&curriculumTypeId=${curriculumTypeId.value}");
    ToastUtil.showLoadingDialog();
    response.then((value) async {
      ToastUtil.closeLoadingDialog();
      CourseListBeanEntity? courseListBeanEntity =
          JsonConvert.fromJsonAsT<CourseListBeanEntity>(value)!;
      if (refresh) {
        courseData.clear();
        courseData.addAll(courseListBeanEntity.data);
        if (courseListBeanEntity.data.length < 10) {
          canLoadMore.value = false;
        }
      } else {
        if (canLoadMore.value) {
          courseData.addAll(courseListBeanEntity.data);
          if (courseListBeanEntity.data.length < 10) {
            canLoadMore.value = false;
          }
        }
      }
    });
  }

  //获取录播课列表
  getRecordCoursePage(refresh) async {
    if (refresh) {
      pageNum.value = 1;
      canLoadMore.value = true;
    } else {
      pageNum.value++;
    }
    var response = HttpUtil(url: Config.getRecordCoursePage).get(
        "${Config.getRecordCoursePage}?pageNum=${pageNum.value}&pageSize=10&curriculumTypeId=${curriculumTypeId.value}");
    ToastUtil.showLoadingDialog();
    response.then((value) async {
      ToastUtil.closeLoadingDialog();
      if (value == null || value.isEmpty) {
        return;
      }
      CourseRecordListBeanEntity? courseRecordListBeanEntity =
          JsonConvert.fromJsonAsT<CourseRecordListBeanEntity>(value)!;
      if (refresh) {
        courseRecordData.clear();
        courseRecordData.addAll(courseRecordListBeanEntity.data);
        if (courseRecordListBeanEntity.data.length < 10) {
          canLoadMore.value = false;
        }
      } else {
        if (canLoadMore.value) {
          courseRecordData.addAll(courseRecordListBeanEntity.data);
          if (courseRecordListBeanEntity.data.length < 10) {
            canLoadMore.value = false;
          }
        }
      }
    });
  }

  //获取复习课列表 已上课
  getReviewCourse(refresh) async {
    if (refresh) {
      pageNum.value = 1;
      canLoadMore.value = true;
    } else {
      pageNum.value++;
    }
    var response = HttpUtil(url: Config.getReviewCourse).get(
        "${Config.getReviewCourse}?studentCode=$studentCode&courseStatus=${currentLessonStatusTabIndex.value}&pageNum=${pageNum.value}&pageSize=10");
    ToastUtil.showLoadingDialog();
    response.then((value) async {
      ToastUtil.closeLoadingDialog();
      if (value == null || value.isEmpty) {
        reviewCourseData.clear();
        return;
      }
      ReviewCourseListBeanEntity? reviewCourseListBeanEntity =
          JsonConvert.fromJsonAsT<ReviewCourseListBeanEntity>(value)!;
      if (refresh) {
        reviewCourseData.clear();
        reviewCourseData.addAll(reviewCourseListBeanEntity.data);
        if (reviewCourseListBeanEntity.data.length < 10) {
          canLoadMore.value = false;
        }
      } else {
        if (canLoadMore.value) {
          reviewCourseData.addAll(reviewCourseListBeanEntity.data);
          if (reviewCourseListBeanEntity.data.length < 10) {
            canLoadMore.value = false;
          }
        }
      }
    });
  }
  // void navigateToPermissionPage(index) {
  //   NEMeetingUtil.logOut();
  //
  //   ENUserOption.getYunXinToken(studentCode, UserType.patriarch, (succeed) {
  //     // if (!succeed) { /// 如果登录没成功 清除之前的登录数据
  //     //   NEMeetingUtil.logOut();
  //     // }else {
  //     StudentAppBeanEntity studentAppBeanEntity = StudentAppBeanEntity();
  //     studentAppBeanEntity.studentCode = studentCode;
  //     studentAppBeanEntity.merchantCode = merchantCode;
  //     studentAppBeanEntity.meetingNum = index;
  //     studentAppBeanEntity.identutyID = "1";
  //     // userType == UserType.student ? "0" : "1";
  //     studentAppBeanEntity.baseUrl = Config.URL;
  //     AndroidIosPlugin.openLessonsDetail(jsonEncode(studentAppBeanEntity));
  //     // }
  //   });
  // }
  //底部加载更多
  loadingBottomController() {
    Future.delayed(const Duration(milliseconds: 800), () {
      isFootShow.value = false;
      Future.delayed(const Duration(milliseconds: 100), () {
        isFootShow.value = true;
      });
    });
  }

  //列表点击事件
  clickLessonBtn(index) {
    if (currentTabIndex.value == 0) {
      //交付课
      if (currentLessonStatusTabIndex.value == 0) {
        //未上课  立即上课
        getCourseMeetingInfo(index,UserType.student);
      } else {
        //已上课
        String path = "";
        CourseListBeanData courseListBeanData = courseData[index];
        if (courseListBeanData.feedback.isNotEmpty) {
          //已反馈查看反馈
          if (courseListBeanData.courseType == "鼎英语") {
            getCourseById(index);
          } else {
            path = LearnNavigator.feedback(studentCode: studentCode).path;
            AndroidIosPlugin.openUniApp(path);
          }
        }
      }
    } else if (currentTabIndex.value == 1) {
      //录播课
      CourseRecordListBeanData courseRecordListBeanData =
          courseRecordData[index];
      String path = LearnNavigator.courseDetail(
              studentCode: studentCode,
              courseId: courseRecordListBeanData.courseId,
              userId: courseRecordListBeanData.userId,
              memberCode: merchantCode)
          .path;
      AndroidIosPlugin.openUniApp(path);
    } else if (currentTabIndex.value == 2) {
      //复习课
      getReviewMeetingInfo(index,UserType.student);
    }
  }

  goOnStudy(index) {
    getCourseMeetingInfo(index,UserType.student); //继续学习
  }

  //获取上课数据 课程是否结束 是否可以入会
  getCourseMeetingInfo(index,userType) {
    BotToast.showLoading();  // 家长巡课 增加loading
    ENUserOption.studyId = courseData[index].courseId ?? "";
    ENUserOption.curriculum = courseData[index].courseType ?? "";
    if (courseData[index].oneToManyType == "1") {
      ENUserOption.studyId = courseData[index].classPlanStudyId ?? "";
    }
    print("-----ENUserOption.curriculum---");
    print(ENUserOption.curriculum);

    var response = HttpUtil()
        .get('${Config.getCourseMeetingInfo}?courseId=${courseData[index].courseId}');
    response.then((value) async {
      // BotToast.cleanAll(); // 家长巡课 去除loading
      // if (value != null) {
        String meetingNum = value["meetingNum"];
        String userTypeString = userType == UserType.student ? '6':'1';

        String phone = UserOption.mobile;
        if (userType == UserType.student) {
          phone = studentCode;
        }
        var response = HttpUtil().get('${Config.getAccountByType}?phone=$phone&userType=$userTypeString');
        response.then((value) async {
          String  userUuid = '';
          String  userToken = '';
          String  schoolId = '';
          String  tempUserType = '';
          String  scene = '';
          if (value is Map ) {
            if (value.containsKey('userUuid')) {
              userUuid =  value["userUuid"];
            }
            if (value.containsKey('userToken')) {
              userToken =  value["userToken"];
            }
            if (value.containsKey('appId')) {
              schoolId =  value["appId"];
            }
            if (value.containsKey('userType')) {
              tempUserType =  value["userType"].toString();
            }
            if (value.containsKey('scene')) {
              scene =  value["scene"].toString();
            }
            var saveTokenResponse =  HttpUtil().post('${Config.saveToken}?token=${UserOption.token}');
            saveTokenResponse.then((response) async {
              if (response!.data is Map) {
                Map dataMap = response.data as Map;
                if (dataMap.containsKey('data')) {
                  String tokenKey = dataMap['data'];
                  Map params = {
                    'schoolId': schoolId,
                    'classId': meetingNum,
                    'userId': userUuid,
                    'token': userToken,
                    'role': tempUserType,
                    'studentCode': studentCode,
                    'token_key': tokenKey,
                    'scene':scene,
                  };
                  // if (userType == UserType.patriarch) {
                  //   Get.to(() => TxMeetingWebPage(url: 'https://class.qcloudclass.com/latest/index.html?userid=$userUuid&token=$userToken&classid=$meetingNum&role=supervisor&scene=supervisor&appName=dxx'));
                  // } else {
                    AndroidIosPlugin.openLessonsDetail(jsonEncode(params));
                  // }
                }
              }
            });
          }
        });


        // HiCache.getInstance().setString(HiConstants.tx_userId, dataMap["userUuid"]);
        // HiCache.getInstance().setString(HiConstants.tx_token, dataMap["userToken"]);

        /*   StudentAppBeanEntity studentAppBeanEntity = StudentAppBeanEntity();
        studentAppBeanEntity.studentToken = studentToken;
        studentAppBeanEntity.parentToken = UserOption.token;
        studentAppBeanEntity.studentCode = studentCode;
        studentAppBeanEntity.courseId = courseData[index].courseId;
        studentAppBeanEntity.merchantCode = merchantCode;
        studentAppBeanEntity.meetingNum = meetingNum;
        AndroidIosPlugin.openLessonsDetail(jsonEncode(studentAppBeanEntity));*/
        // GetXCustomDialog(
        //   studentCode: studentCode,
        //   memberCode: merchantCode,
        //   meetingNum: meetingNum,
        // ).show();

        // NEMeetingUtil.logOut();
        // ENUserOption.getYunXinToken(studentCode, userType, (succeed) {
        //   StudentAppBeanEntity studentAppBeanEntity = StudentAppBeanEntity();
        //   studentAppBeanEntity.studentCode = studentCode;
        //   studentAppBeanEntity.merchantCode = merchantCode;
        //   studentAppBeanEntity.meetingNum = meetingNum;
        //   studentAppBeanEntity.identutyID =
        //   userType ==UserType.student ? "0" :"1";
        //   studentAppBeanEntity.baseUrl = Config.URL;
        //   AndroidIosPlugin.openLessonsDetail(jsonEncode(studentAppBeanEntity));
        // });
      // } else {
      //   // ToastUtil.showShortErrorToast("获取上课数据失败~");
      // }
    });
  }

  //获取复习课上课数据 课程是否结束 是否可以入会
  getReviewMeetingInfo(index,userType) {
    BotToast.showLoading();
    var response = HttpUtil().get(
        '${Config.getReviewMeetingInfo}?meetingId=${reviewCourseData[index].meetingId}');
    response.then((value) async {
      if (value != null) {
        String meetingNum = value["meetingNum"];
          // BotToast.cleanAll(); // 家长巡课 去除loading
          String userTypeString = userType == UserType.student ? '6':'1';
          String phone = UserOption.mobile;
          if (userType == UserType.student) {
            phone = studentCode;
          }

          var response = HttpUtil().get('${Config.getAccountByType}?phone=$phone&userType=$userTypeString');
          response.then((value) async {
            if(value == null) {
              return;
            }
            String userUuid = '';
            String userToken = '';
            String schoolId = '';
            String tempUserType = '';
            String scene = '';
            if (value is Map) {
              if (value.containsKey('userUuid')) {
                userUuid = value["userUuid"];
              }
              if (value.containsKey('userToken')) {
                userToken = value["userToken"];
              }
              if (value.containsKey('appId')) {
                schoolId = value["appId"];
              }
              if (value.containsKey('userType')) {
                tempUserType = value["userType"].toString();
              }
              if (value.containsKey('scene')) {
                scene = value["scene"].toString();
              }
            }
            var saveTokenResponse =  HttpUtil().post('${Config.saveToken}?token=${UserOption.token}');
            saveTokenResponse.then((response) async {
              if (response!.data is Map) {
                Map dataMap = response.data as Map;
                if (dataMap.containsKey('data')) {
                  String tokenKey = dataMap['data'];
                  Map params = {
                    'schoolId': schoolId,
                    'classId': meetingNum,
                    'userId': userUuid,
                    'token': userToken,
                    'role': tempUserType,
                    'token_key': tokenKey,
                    'studentCode':studentCode,
                    'scene':scene,
                  };
                  // if (userType == UserType.patriarch) {
                  //   Get.to(() => TxMeetingWebPage(url: 'https://class.qcloudclass.com/latest/index.html?userid=$userUuid&token=$userToken&classid=$meetingNum&role=supervisor&scene=supervisor&appName=dxx'));
                  // } else {
                    AndroidIosPlugin.openLessonsDetail(jsonEncode(params));
                  // }
                }
              }
            });

        });

        // GetXCustomDialog(
        //   studentCode: studentCode,
        //   memberCode: merchantCode,
        //   meetingNum: meetingNum,
        // ).show();
        // NEMeetingUtil.logOut();
        // ENUserOption.getYunXinToken(studentCode, userType, (succeed) {
        //   StudentAppBeanEntity studentAppBeanEntity = StudentAppBeanEntity();
        //   studentAppBeanEntity.studentCode = studentCode;
        //   studentAppBeanEntity.merchantCode = merchantCode;
        //   studentAppBeanEntity.meetingNum = meetingNum;
        //   studentAppBeanEntity.identutyID =
        //   userType ==UserType.student ? "0" :"1";
        //   studentAppBeanEntity.baseUrl = Config.URL;
        //   AndroidIosPlugin.openLessonsDetail(jsonEncode(studentAppBeanEntity));
        // });
      } else {
        // ToastUtil.showShortErrorToast("获取上课数据失败~");
      }
    });
  }

  //获取交付课列表   已上课 查看反馈 鼎英语课程数据
  getCourseById(index) async {
    String id = courseData[index].courseId ?? "";
    if (courseData[index].oneToManyType == "1") {
      id = courseData[index].classPlanStudyId ?? "";
    }
    var response = HttpUtil(url: Config.getCourseById)
        .get("${Config.getCourseById}?courseId=$id");
    response.then((value) async {
      CourseDetailBeanEntity controlsDetails =
          JsonConvert.fromJsonAsT<CourseDetailBeanEntity>(value)!;
      Map<String, dynamic> feedBackMap = {};
      feedBackMap.putIfAbsent('experience', () => controlsDetails.experience);
      feedBackMap.putIfAbsent('id', () => controlsDetails.id);
      String path =
          LearnNavigator.feedbackDyx(data: json.encode(feedBackMap)).path;
      AndroidIosPlugin.openUniApp(path);
    });
  }

  /// 筛选指定日期的课程列表
  /// [date] - DateTime 类型
  getCourseListByDate(DateTime date) {
    // 月份补零
    final month = date.month < 10 ? "0${date.month}" : "${date.month}";
    // 日期补零
    final day = date.day < 10 ? "0${date.day}" : "${date.day}";
    final formattedDate = "${date.year}-$month-$day";
    datedCourseData.value = courseData.where((course) {
      return course.courseTime.split(' ')[0] == formattedDate;
    }).toList();
    debugPrint('筛选的课程：${datedCourseData.toString()}');
  }

  /// 打开日期选择弹窗
  showSelectDateDialog() {
    final initDate = DateTime.tryParse(selectedDate.value) ?? DateTime.now();
    if(isSelectDate.value) {
      getCourseListByDate(initDate);
    }
    Get.dialog(SelectDateDialog(
      initDate: initDate,
      onChange: (dateList) {
        // 仅保存选择的日期，但不立即筛选
        if (dateList.isNotEmpty) {
          final date = dateList[0];
          selectedDate.value = date.toString();
        }
      },
      onConfirm: (dateList) {
        // 点击确认按钮时才执行筛选
        if (dateList.isNotEmpty) {
          isSelectDate.value = true;
          final date = dateList[0];
          selectedDate.value = date.toString();
          // 筛选课程
          getCourseListByDate(date);
        }
      },
      onCancel: () {
        // 取消筛选，重置状态
        isSelectDate.value = false;
        // 重置选中日期为空，这样下次打开默认是今天
        selectedDate.value = '';
      },
    ));
  }

  //获取课程类型筛选项
  getCourseType() async {
    if (isDisable) {
      return;
    }
    if (courseTypeList.isNotEmpty) {
      showSelectLessonTypeDialog(courseTypeList);
      return;
    } else {
      isDisable = true;
      var response =
          HttpUtil(url: Config.getCourseType).get(Config.getCourseType);
      response.then((value) async {
        isDisable = false;
        courseTypeList =
            jsonConvert.convertListNotNull<CourseTypeBeanEntity>(value)!;
        showSelectLessonTypeDialog(courseTypeList);
      });
    }
  }

  //选择课程类型弹窗
  showSelectLessonTypeDialog(data) {
    Get.dialog(
      SelectLessonTypeDialog(data, selectLessonType, selectCourseTypeIndex),
      barrierDismissible: false,
    );
  }

  //选择课程类型数据
  void selectLessonType(String id, int index) {
    curriculumTypeId.value = id;
    selectCourseTypeIndex = index;
    if (currentTabIndex.value == 0) {
      //交付课
      getDeliverCoursePage(true);
    } else if (currentTabIndex.value == 1) {
      //录播课
      getRecordCoursePage(true);
    } else if (currentTabIndex.value == 2) {
      //复习课
      getReviewCourse(true);
    }
  }

  String dateToString(timestamp) {
    // 创建一个DateTime对象
    DateTime dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp);

    // 定义日期格式
    var formatter = DateFormat('yyyy-MM-dd HH:mm:ss');
    return formatter.format(dateTime);
  }

  /// 打开请假弹窗
  /// [type] - 弹窗类型，1 为请假，2 为填写补课时间
  Future<void> showAskForLeaveDialog({
    required CourseListBeanData courseData,
    required int type,
    bool isTestClassLeave = false
  }) async {
    // 防重复请求
    if(isLoadingLeaveInfo) return;
    ToastUtil.showLoadingDialog();
    isLoadingLeaveInfo = true;

    try {
      // 获取请假回显数据
      final leaveInfo = await getLeaveApplyInfo(courseData.planStudyId ?? "", type);

      // 检查课程是否支持请假
      if(leaveInfo.configLeaveNum == 0 && type == 1) {
        ToastUtil.closeLoadingDialog();
        BotToast.showText(text: '该课程暂不支持请假');
        isLoadingLeaveInfo = false;
        return;
      }

      // 获取可供补课的时间列表
      final makeUpClassTimes = await getMakeUpClassTimes(
          type: courseData.experience == true ? '1' : '2',
          grade: leaveInfo.grade.toString(),
          curriculumId: leaveInfo.curriculumId.toString()
      );

      // 关闭加载提示
      ToastUtil.closeLoadingDialog();

      // 传入回显数据打开请假弹窗
      Get.dialog(AskForLeaveDialog(
        courseData: courseData,
        leaveApplyInfo: leaveInfo,
        makeupClassTimes: makeUpClassTimes,
        type: type,
        testClassLeave: isTestClassLeave,
        onConfirm: () => getDeliverCoursePage(true),
      ));
    } catch (e) {
      debugPrint('打开请假弹窗失败: $e');
      ToastUtil.showShortErrorToast('获取请假信息失败，请稍后重试');
    } finally {
      ToastUtil.closeLoadingDialog();
      isLoadingLeaveInfo = false;
    }
  }

  /// 获取请假与补课弹窗回显数据
  /// [type] - 数据类型，1 为请假，2 为填写补课时间
  Future<LeaveApplyInfoEntity> getLeaveApplyInfo(String planStudyId, int type) async {
    try {
      var response = await HttpUtil(url: Config.leaveApplyInfo, sendLoginToken: true)
          .get("${Config.leaveApplyInfo}?planStudyId=$planStudyId&type=$type");
      
      if (response != null) {
        final leaveApplyInfo = JsonConvert.fromJsonAsT<LeaveApplyInfoEntity>(response)!;
        return leaveApplyInfo;
      } else {
        throw Exception('获取请假数据失败，服务器返回空数据');
      }
    } catch (e) {
      debugPrint("获取请假回显数据失败: $e");
      throw Exception('获取请假数据失败: ${e.toString()}');
    }
  }

  /// 获取期望补课时间列表
  /// [type] - 试课传 1，正课传 2
  Future<List<MakeUpClassTimesEntity>> getMakeUpClassTimes({
    required String type,
    required String curriculumId,
    required String grade
  }) async {
    try {
      final response = await HttpUtil(url: Config.makeUpClassTimes)
          .get("${Config.makeUpClassTimes}?type=$type&curriculumId=$curriculumId&grade=$grade&week=");
      
      if(response != null) {
        final makeUpClassTimes = JsonConvert.fromJsonAsT<List<MakeUpClassTimesEntity>>(response)!;
        return makeUpClassTimes;
      } else {
        throw Exception('获取补课时间列表失败，服务器返回空数据');
      }
    } catch(e) {
      debugPrint('获取补课时间列表失败: $e');
      throw Exception('获取补课时间列表失败: ${e.toString()}');
    }
  }

  @override
  onClose() {
    UserOption.studentToken = '';
    tabController?.removeListener(() {});
    tabController?.dispose();
    super.onClose();
  }
}
