import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class VerificationCodeInput extends StatefulWidget {
  final String title;
  final String buttonText;
  final Color buttonColor;
  final String? initialErrorText;
  final Future<bool> Function(String code) onVerify;
  final Function()? onCancel;
  final bool autoFocusFirstField;

  const VerificationCodeInput({
    super.key,
    this.title = '输入验证码',
    this.buttonText = '确认',
    this.buttonColor = Colors.green,
    this.initialErrorText,
    required this.onVerify,
    this.onCancel,
    this.autoFocusFirstField = true,
  });

  static Future<void> show(
    BuildContext context, {
    String title = '输入验证码',
    String buttonText = '确认',
    Color buttonColor = Colors.green,
    String? initialErrorText,
    required final Future<bool> Function(String code) onVerify,
    Function()? onCancel,
    bool autoFocusFirstField = true,
  }) {
    return showDialog(
      context: context,
      builder: (context) => VerificationCodeInput(
        title: title,
        buttonText: buttonText,
        buttonColor: buttonColor,
        initialErrorText: initialErrorText,
        onVerify: onVerify,
        onCancel: onCancel,
        autoFocusFirstField: autoFocusFirstField,
      ),
    );
  }

  @override
  State<VerificationCodeInput> createState() => _VerificationCodeInputState();
}

class _VerificationCodeInputState extends State<VerificationCodeInput> {
  final int _codeLength = 6;
  final List<FocusNode> _focusNodes = [];
  final List<TextEditingController> _controllers = [];
  String? _errorText;
  bool _isVerifying = false;

  @override
  void initState() {
    super.initState();
    for (int i = 0; i < _codeLength; i++) {
      _focusNodes.add(FocusNode());
      _controllers.add(TextEditingController());
    }
    _errorText = widget.initialErrorText;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (widget.autoFocusFirstField) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        FocusScope.of(context).requestFocus(_focusNodes[0]);
      });
    }
  }

  @override
  void dispose() {
    for (int i = 0; i < _codeLength; i++) {
      _focusNodes[i].dispose();
      _controllers[i].dispose();
    }
    super.dispose();
  }

  Future<void> _verifyCode() async {
    if (_isVerifying) return;

    final code = _controllers.map((c) => c.text).join();
    if (code.length != _codeLength) return;

    setState(() {
      _isVerifying = true;
      _errorText = null;
    });

    try {
      final isValid = await widget.onVerify(code);
      if (!isValid) {
        setState(() {
          _errorText = '请输入正确的营销码';
        });
      } else {
        Navigator.of(context).pop();
      }
    } finally {
      if (mounted) {
        setState(() => _isVerifying = false);
      }
    }
  }

  Widget _buildCodeField(int index, double width) {
    return SizedBox(
      width: width,
      height: width,
      child: TextField(
        controller: _controllers[index],
        focusNode: _focusNodes[index],
        textAlign: TextAlign.center,
        keyboardType: TextInputType.number,
        inputFormatters: [
          FilteringTextInputFormatter.digitsOnly,
          LengthLimitingTextInputFormatter(1),
        ],
        style: TextStyle(
            fontSize: width * 0.48, // 动态调整字体大小
            fontWeight: FontWeight.bold),
        decoration: InputDecoration(
          border: const OutlineInputBorder(),
          counterText: '',
          contentPadding: EdgeInsets.zero,
        ),
        onChanged: (value) {
          if (value.isNotEmpty) {
            if (index < _codeLength - 1) {
              FocusScope.of(context).requestFocus(_focusNodes[index + 1]);
            }
          } else {
            if (index > 0) {
              FocusScope.of(context).requestFocus(_focusNodes[index - 1]);
            }
          }
        },
        onTap: () {
          _controllers[index].selection = TextSelection(
            baseOffset: 0,
            extentOffset: _controllers[index].text.length,
          );
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: const EdgeInsets.symmetric(horizontal: 20),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: SingleChildScrollView(
        child: Container(
          width: double.infinity,
          constraints: const BoxConstraints(maxWidth: 400),
          padding: const EdgeInsets.fromLTRB(20, 20, 20, 15),
          child: Stack(
            clipBehavior: Clip.none,
            children: [
              Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(top: 10),
                    child: Text(
                      widget.title,
                      style: const TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: 20,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  const SizedBox(height: 20),
                  LayoutBuilder(
                    builder: (context, constraints) {
                      // 计算输入框的间距
                      final spacing = 8.0;
                      // 计算总的可用宽度（减去输入框之间的间距）
                      final totalAvailableWidth =
                          constraints.maxWidth - (spacing * (_codeLength - 1));
                      // 计算每个输入框的宽度
                      final inputWidth =
                          (totalAvailableWidth / _codeLength).clamp(30.0, 50.0);

                      return Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: List.generate(_codeLength, (index) {
                          return Padding(
                            padding: EdgeInsets.only(
                              right: index < _codeLength - 1 ? spacing : 0,
                            ),
                            child: _buildCodeField(index, inputWidth),
                          );
                        }),
                      );
                    },
                  ),
                  const SizedBox(height: 20),
                  if (_errorText != null)
                    Padding(
                      padding: const EdgeInsets.only(top: 4),
                      child: Text(
                        _errorText!,
                        textAlign: TextAlign.center,
                        style: const TextStyle(color: Colors.red, fontSize: 12),
                      ),
                    ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: widget.buttonColor,
                      shape: const StadiumBorder(),
                      minimumSize: const Size(double.infinity, 45),
                    ),
                    onPressed: _isVerifying ? null : _verifyCode,
                    child: _isVerifying
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: Colors.white,
                            ),
                          )
                        : Text(
                            widget.buttonText,
                            style: const TextStyle(color: Colors.white),
                          ),
                  ),
                ],
              ),
              Positioned(
                top: -10,
                right: -10,
                child: IconButton(
                  icon: const Icon(Icons.close, size: 20),
                  color: Colors.grey[600],
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  onPressed: () {
                    if (widget.onCancel != null) {
                      widget.onCancel!();
                    }
                    Navigator.of(context).pop();
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
