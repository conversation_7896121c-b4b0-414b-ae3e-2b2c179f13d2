import 'dart:convert';
import '../../generated/json/base/json_field.dart';
import 'base_bean_entity.g.dart';

/// 请求基类model
class BaseBeanEntity<T>{
  late int code = 0;
  late bool success = false;
  late String message = '';
  late T? data;
  late int total = 0;
  late int status = 0;

  BaseBeanEntity();

  factory BaseBeanEntity.fromJson(Map<String, dynamic> json) => $BaseBeanEntityFromJson(json);

  Map<String, dynamic> toJson() => $BaseBeanEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}