import 'package:student_end_flutter/generated/json/base/json_convert_content.dart';
import 'package:student_end_flutter/bean/pyf_memberCode_bean_entity_entity.dart';

PyfMemberCodeBeanEntityEntity $PyfMemberCodeBeanEntityEntityFromJson(
    Map<String, dynamic> json) {
  final PyfMemberCodeBeanEntityEntity pyfMemberCodeBeanEntityEntity = PyfMemberCodeBeanEntityEntity();
  final String? merchantCode = jsonConvert.convert<String>(
      json['merchantCode']);
  if (merchantCode != null) {
    pyfMemberCodeBeanEntityEntity.merchantCode = merchantCode;
  }
  final String? studentCode = jsonConvert.convert<String>(json['studentCode']);
  if (studentCode != null) {
    pyfMemberCodeBeanEntityEntity.studentCode = studentCode;
  }
  final String? studentName = jsonConvert.convert<String>(json['studentName']);
  if (studentName != null) {
    pyfMemberCodeBeanEntityEntity.studentName = studentName;
  }
  final int? curriculumId = jsonConvert.convert<int>(json['curriculumId']);
  if (curriculumId != null) {
    pyfMemberCodeBeanEntityEntity.curriculumId = curriculumId;
  }
  final String? curriculumName = jsonConvert.convert<String>(
      json['curriculumName']);
  if (curriculumName != null) {
    pyfMemberCodeBeanEntityEntity.curriculumName = curriculumName;
  }
  final int? totalCourseHours = jsonConvert.convert<int>(
      json['totalCourseHours']);
  if (totalCourseHours != null) {
    pyfMemberCodeBeanEntityEntity.totalCourseHours = totalCourseHours;
  }
  final int? haveCourseHours = jsonConvert.convert<int>(
      json['haveCourseHours']);
  if (haveCourseHours != null) {
    pyfMemberCodeBeanEntityEntity.haveCourseHours = haveCourseHours;
  }
  final int? haveDeliverHours = jsonConvert.convert<int>(
      json['haveDeliverHours']);
  if (haveDeliverHours != null) {
    pyfMemberCodeBeanEntityEntity.haveDeliverHours = haveDeliverHours;
  }
  final int? relievedDeliverHours = jsonConvert.convert<int>(
      json['relievedDeliverHours']);
  if (relievedDeliverHours != null) {
    pyfMemberCodeBeanEntityEntity.relievedDeliverHours = relievedDeliverHours;
  }
  final bool? isFirstBuyDyy = jsonConvert.convert<bool>(json['isFirstBuyDyy']);
  if (isFirstBuyDyy != null) {
    pyfMemberCodeBeanEntityEntity.isFirstBuyDyy = isFirstBuyDyy;
  }
  return pyfMemberCodeBeanEntityEntity;
}

Map<String, dynamic> $PyfMemberCodeBeanEntityEntityToJson(
    PyfMemberCodeBeanEntityEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['merchantCode'] = entity.merchantCode;
  data['studentCode'] = entity.studentCode;
  data['studentName'] = entity.studentName;
  data['curriculumId'] = entity.curriculumId;
  data['curriculumName'] = entity.curriculumName;
  data['totalCourseHours'] = entity.totalCourseHours;
  data['haveCourseHours'] = entity.haveCourseHours;
  data['haveDeliverHours'] = entity.haveDeliverHours;
  data['relievedDeliverHours'] = entity.relievedDeliverHours;
  data['isFirstBuyDyy'] = entity.isFirstBuyDyy;
  return data;
}

extension PyfMemberCodeBeanEntityEntityExtension on PyfMemberCodeBeanEntityEntity {
  PyfMemberCodeBeanEntityEntity copyWith({
    String? merchantCode,
    String? studentCode,
    String? studentName,
    int? curriculumId,
    String? curriculumName,
    int? totalCourseHours,
    int? haveCourseHours,
    int? haveDeliverHours,
    int? relievedDeliverHours,
    bool? isFirstBuyDyy,
  }) {
    return PyfMemberCodeBeanEntityEntity()
      ..merchantCode = merchantCode ?? this.merchantCode
      ..studentCode = studentCode ?? this.studentCode
      ..studentName = studentName ?? this.studentName
      ..curriculumId = curriculumId ?? this.curriculumId
      ..curriculumName = curriculumName ?? this.curriculumName
      ..totalCourseHours = totalCourseHours ?? this.totalCourseHours
      ..haveCourseHours = haveCourseHours ?? this.haveCourseHours
      ..haveDeliverHours = haveDeliverHours ?? this.haveDeliverHours
      ..relievedDeliverHours = relievedDeliverHours ?? this.relievedDeliverHours
      ..isFirstBuyDyy = isFirstBuyDyy ?? this.isFirstBuyDyy;
  }
}