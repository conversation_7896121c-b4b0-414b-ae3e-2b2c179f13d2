package com.dxznjy.alading.api.service;


import com.dxznjy.alading.http.BaseResponse;
import com.dxznjy.alading.response.CheckSlideResponse;
import com.dxznjy.alading.response.CourseInfoResponse;
import com.dxznjy.alading.response.CourseTypeResponse;
import com.dxznjy.alading.response.LoginResponse;
import com.dxznjy.alading.response.MeetingInfoResponse;
import com.dxznjy.alading.response.PayCourseDataResponse;
import com.dxznjy.alading.response.PlayVideoResponse;
import com.dxznjy.alading.response.SlideRespose;
import com.dxznjy.alading.response.StudentMerchantResponse;
import com.dxznjy.alading.response.UserInfoResponse;
import com.dxznjy.alading.response.VideoCourseDataResponse;
import com.dxznjy.alading.response.YXAddUserResponse;
import com.dxznjy.alading.response.YXUserInfoResponse;

import java.util.Map;

import okhttp3.RequestBody;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Path;
import retrofit2.http.Query;
import retrofit2.http.QueryMap;
import rx.Observable;

/**
 * Created by Administrator on 2016/9/3.
 */
public interface DFService {
    //云信账号注册
    @POST("new/security/v2/add-user")
    Observable<YXAddUserResponse> addUser(@Body RequestBody bean);
    //云信账号查询信息
    @GET("batch-get-user?")
    Observable<YXUserInfoResponse> getYxUserInfo(@Query("userUuids")String  userUuids);

    //账号 密码登录
    @GET("new/security/v2/login/student/app")
    Observable<LoginResponse> loginPwd(@QueryMap Map<String, String> map);
    //获取验证码
    @POST("new/security/v2/sms/{mobile}?")
    Observable<BaseResponse> getMsgCode(@Path("mobile") String mobile,@QueryMap Map<String, String> map);
    /**
     * 获取滑动验证码
     * @param
     * @return
     */
    @GET("new/security/captcha/image/slide")
    Observable<SlideRespose> getSlideImage(@QueryMap Map<String, String> map);

    /**
     * 验证滑动验证码
     * @param bean
     * @return
     */
    @POST("new/security/captcha/check/slide")
    Observable<CheckSlideResponse> checkSlide(@Body RequestBody bean);

    //获取用户信息
    @GET("zx/student/getUserInfo")
    Observable<UserInfoResponse> getUserInfo();
    //获取课程筛选项
    @GET("zx/student/course/getCourseType")
    Observable<CourseTypeResponse> getCourseType();
    //获取交付课列表
    @GET("zx/student/course/getDeliverCoursePage")
    Observable<PayCourseDataResponse> getDeliverCoursePage(@QueryMap Map<String, String> map);
    //获取录播课列表
    @GET("zx/student/course/getRecordCoursePage")
    Observable<VideoCourseDataResponse> getRecordCoursePage(@QueryMap Map<String, String> map);
    //获取交付课会议id
    @GET("zx/student/course/getCourseMeetingInfo")
    Observable<MeetingInfoResponse> getCourseMeetingInfo(@QueryMap Map<String, String> map);
    //获取交付课老师播放视频url
    @GET("zx/student/course/getPlayVideo")
    Observable<PlayVideoResponse> getPlayVideo(@QueryMap Map<String, String> map);
    //获取学生所在门店
    @GET("v2/mall/getStudentMerchantList")
    Observable<StudentMerchantResponse> getStudentMerchantList(@QueryMap Map<String, String> map);
    //
    @GET("deliver/app/parent/getCourseById")
    Observable<CourseInfoResponse> getCourseById(@QueryMap Map<String, String> map);
}
