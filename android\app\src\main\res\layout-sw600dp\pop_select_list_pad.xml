<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="MissingDefaultResource">

    <LinearLayout
        android:layout_width="360dp"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:background="@mipmap/pic_beijing"
        android:orientation="vertical"
        android:paddingLeft="20dp"
        android:paddingTop="20dp"
        android:paddingRight="10dp">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_above="@+id/recyclerView">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginRight="10dp"
                android:orientation="vertical">

                <View
                    android:layout_width="36dp"
                    android:layout_height="4dp"
                    android:layout_below="@+id/tv_title"
                    android:layout_marginTop="-6dp"
                    android:background="@drawable/bg_green_line"
                    android:visibility="invisible" />

                <TextView
                    android:id="@+id/tv_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="下载列表"
                    android:textColor="@color/_333333"
                    android:textSize="16sp"
                    android:textStyle="bold" />
            </RelativeLayout>

            <LinearLayout
                android:id="@+id/ll_close"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:gravity="center"
                android:layout_marginRight="10dp"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_marginLeft="14dp"
                    android:src="@mipmap/ic_close_pad" />
            </LinearLayout>
        </RelativeLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp" />

        <LinearLayout
            android:id="@+id/ll_bottom"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="30dp"
            android:layout_marginRight="10dp"
            android:layout_marginBottom="20dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_cancle"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:layout_weight="1"
                android:background="@drawable/bg_radius20_428a6f_line"
                android:gravity="center"
                android:text="取消"
                android:textColor="@color/_428a6f"
                android:textSize="12sp" />

            <TextView
                android:id="@+id/tv_sure"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:layout_marginLeft="20dp"
                android:layout_weight="1"
                android:background="@drawable/bg_radius20_428a6f"
                android:gravity="center"
                android:text="下载"
                android:textColor="@color/white"
                android:textSize="12sp" />
        </LinearLayout>
    </LinearLayout>
</RelativeLayout>