//
//  BaseTableViewCell.swift
//  XappTest
//
//  Created by yj9527 on 2023/2/16.
//

import UIKit


class BaseTableViewCell: UITableViewCell {

    class var identifier: String {
        return String(describing: self)
    }
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        self.selectionStyle = .none
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
}
