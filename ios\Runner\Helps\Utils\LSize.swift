//
//  LSize.swift
//  StuMetApp
//
//  Created by 许江泉 on 2024/11/12.
//

import Foundation
public struct LSize {
    /// 0.0
    public static let zero = 0.0
    /// 1.0
    public static let xSmall = 1.0
    /// 4.0
    public static let small = 4.0
    /// 8.0
    public static let halfNormal = 8.0
    /// 12.0
    public static let normalSmall = 12.0
    /// 16.0
    public static let normal = 16.0
    /// 20.0
    public static let large = 20.0
    /// 24.0
    public static let xLarge = 24.0
    /// 32.0
    public static let xxLarge = 32.0
    /// 36.0
    public static let xxxLarge = 36.0
    /// 40.0
    public static let xxxxLarge = 40.0
    /// 44.0
    public static let xxxxxLarge = 44.0
    /// 48.0
    public static let ultLarge = 48.0
    /// 60.0
    public static let ultraLarge = 60.0
    /// 64.0
    public static let ultraXLarge = 64.0
}
