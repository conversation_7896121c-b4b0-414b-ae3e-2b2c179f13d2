package com.dxznjy.alading.fragment;

import android.os.Bundle;

import androidx.fragment.app.Fragment;

public abstract class LazyFragment extends Fragment {
    // 是否第一次可见
    private boolean isFirstVisible = true;
    // 确保在第一次可见时只走onFirstUserVisible方法，而不走onUserVisible
    private boolean isFirstResume = true;
    // 是否已经初始化完毕
    private boolean isPrepared;

    @Override
    public void onDetach() {
        super.onDetach();
        // for bug ---> java.lang.IllegalStateException: Activity has been destroyed
     /*   try {
            Field childFragmentManager = Fragment.class.getDeclaredField("mChildFragmentManager");
            childFragmentManager.setAccessible(true);
            childFragmentManager.set(this, null);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }*/
    }

    @Override
    public void onActivityCreated(Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        initPrepare();
    }

    @Override
    public void onResume() {
        super.onResume();
        if (isFirstResume) {
            isFirstResume = false;
            return;
        }
        if (getUserVisibleHint()) {
            onUserVisible();
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        if (getUserVisibleHint()) {
            onUserInvisible();
        }
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (isVisibleToUser) {
            if (isFirstVisible) {
                initPrepare();
            } else {
                onUserVisible();
            }
        } else {
            onUserInvisible();
        }
    }

    private synchronized void initPrepare() {
        if (isPrepared) {
            if (!isFirstVisible) {
                // 切换viewpager页面时,fragment会经常走onCreateView到onDestroyView的生命周期方法
                // 这个判断可以防止多次走onFirstUserVisible()方法
                // 如果viewPager.setOffScreenLimited()缓存了所有的fragment（建议设置），切换页面时不会走生命周期方法
                return;
            }
            isFirstVisible = false;
            onFirstUserVisible();
        } else {
            isPrepared = true;
        }
    }

    /**
     * 仅在第一次可见时调用
     */
    protected abstract void onFirstUserVisible();

    /**
     * 除第一次之外的可见时调用
     */
    protected abstract void onUserVisible();

    /**
     * 不可见时调用，相当于onPause()
     */
    protected abstract void onUserInvisible();

}