import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../bean/student_info_bean_entity.dart';
import '../common/CommonUtil.dart';

class CourseProgressStudentDialog extends StatefulWidget {
  final List<StudentInfoBeanEntity> studentList;
  final Function(int) onConfirm;
  final VoidCallback onCancel;

  CourseProgressStudentDialog({
    required this.studentList,
    required this.onConfirm,
    required this.onCancel,
  });

  @override
  _CourseProgressStudentDialogState createState() =>
      _CourseProgressStudentDialogState();
}

class _CourseProgressStudentDialogState
    extends State<CourseProgressStudentDialog> {
  int? selectedIndex;

  @override
  void initState() {
    super.initState();
    // 默认选中第一个学员
    if (widget.studentList.isNotEmpty) {
      selectedIndex = 0;
    }
  }

  @override
  Widget build(BuildContext context) {
    double screenHeight = MediaQuery.of(context).size.height;
    return SingleChildScrollView(
      child: Column(
        children: [
          Container(
            alignment: Alignment.bottomCenter,
            height: screenHeight / 3.5,
            child: Image(image: AssetImage("assets/dialog_icon.png")),
          ),
          Container(
            width: double.infinity,
            margin: EdgeInsets.only(left: 20.w, right: 20.w),
            padding: EdgeInsets.all(10.w),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(15)),
                color: Colors.white),
            child: Column(
              children: [
                SizedBox(
                  height: 40.h,
                  width: double.infinity,
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      Text(
                        '选择学员',
                        style: TextStyle(
                            decoration: TextDecoration.none,
                            fontSize: 16.sp,
                            fontFamily: "M",
                            color: Colors.black),
                      ),
                      Positioned(
                        top: 0,
                        right: 0,
                        child: GestureDetector(
                          onTap: () {
                            widget.onCancel();
                            Get.back();
                          },
                          child: Image.asset(
                            'assets/zx/close.png',
                            width: 26.w,
                            height: 26.w,
                          ),
                        ),
                      )
                    ],
                  ),
                ),
                SizedBox(height: 10.w),
                SizedBox(
                  height: 220.h,
                  child: ListView.builder(
                    shrinkWrap: true,
                    itemCount: widget.studentList.length,
                    itemBuilder: (context, index) {
                      return _buildStudentItem(index);
                    },
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    TextButton(
                      onPressed: () {
                        widget.onCancel();
                        Get.back();
                      },
                      style: TextButton.styleFrom(
                        backgroundColor: Colors.grey[200],
                        minimumSize: Size(120.w, 40.h),
                      ),
                      child: Text(
                        '取消',
                        style: TextStyle(
                          color: Colors.black87,
                          fontSize: 16.sp,
                        ),
                      ),
                    ),
                    TextButton(
                      onPressed: () {
                        if (selectedIndex != null) {
                          Get.back();
                          widget.onConfirm(selectedIndex!);
                        }
                      },
                      style: TextButton.styleFrom(
                        backgroundColor: HexColor('2E896F'),
                        minimumSize: Size(120.w, 40.h),
                      ),
                      child: Text(
                        '确定',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16.sp,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStudentItem(int index) {
    bool isSelected = selectedIndex == index;
    return GestureDetector(
      onTap: () {
        setState(() {
          selectedIndex = index;
        });
      },
      child: Container(
        height: 40.w,
        margin: EdgeInsets.symmetric(vertical: 5.h),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(
            width: 1,
            color: isSelected ? HexColor('2E896F') : HexColor('EFEFEF'),
          ),
          borderRadius: BorderRadius.all(Radius.circular(25.w)),
        ),
        alignment: Alignment.center,
        child: Text(
          widget.studentList[index].studentName,
          style: TextStyle(
            decoration: TextDecoration.none,
            fontSize: 15.sp,
            fontFamily: "R",
            color: HexColor('555555'),
          ),
        ),
      ),
    );
  }
}
