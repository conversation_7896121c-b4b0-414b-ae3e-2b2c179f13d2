import 'package:student_end_flutter/generated/json/base/json_convert_content.dart';
import 'package:student_end_flutter/bean/zx_course_entity.dart';

ZxCourseEntity $ZxCourseEntityFromJson(Map<String, dynamic> json) {
  final ZxCourseEntity zxCourseEntity = ZxCourseEntity();
  final int? code = jsonConvert.convert<int>(json['code']);
  if (code != null) {
    zxCourseEntity.code = code;
  }
  final bool? success = jsonConvert.convert<bool>(json['success']);
  if (success != null) {
    zxCourseEntity.success = success;
  }
  final String? message = jsonConvert.convert<String>(json['message']);
  if (message != null) {
    zxCourseEntity.message = message;
  }
  final ZxCourseData? data = jsonConvert.convert<ZxCourseData>(json['data']);
  if (data != null) {
    zxCourseEntity.data = data;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    zxCourseEntity.total = total;
  }
  return zxCourseEntity;
}

Map<String, dynamic> $ZxCourseEntityToJson(ZxCourseEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['code'] = entity.code;
  data['success'] = entity.success;
  data['message'] = entity.message;
  data['data'] = entity.data.toJson();
  data['total'] = entity.total;
  return data;
}

extension ZxCourseEntityExtension on ZxCourseEntity {
  ZxCourseEntity copyWith({
    int? code,
    bool? success,
    String? message,
    ZxCourseData? data,
    int? total,
  }) {
    return ZxCourseEntity()
      ..code = code ?? this.code
      ..success = success ?? this.success
      ..message = message ?? this.message
      ..data = data ?? this.data
      ..total = total ?? this.total;
  }
}

ZxCourseData $ZxCourseDataFromJson(Map<String, dynamic> json) {
  final ZxCourseData zxCourseData = ZxCourseData();
  final String? currentPage = jsonConvert.convert<String>(json['currentPage']);
  if (currentPage != null) {
    zxCourseData.currentPage = currentPage;
  }
  final String? totalPage = jsonConvert.convert<String>(json['totalPage']);
  if (totalPage != null) {
    zxCourseData.totalPage = totalPage;
  }
  final String? size = jsonConvert.convert<String>(json['size']);
  if (size != null) {
    zxCourseData.size = size;
  }
  final List<ZxCourseDataData>? data = (json['data'] as List<dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<ZxCourseDataData>(e) as ZxCourseDataData)
      .toList();
  if (data != null) {
    zxCourseData.data = data;
  }
  final String? totalItems = jsonConvert.convert<String>(json['totalItems']);
  if (totalItems != null) {
    zxCourseData.totalItems = totalItems;
  }
  return zxCourseData;
}

Map<String, dynamic> $ZxCourseDataToJson(ZxCourseData entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['currentPage'] = entity.currentPage;
  data['totalPage'] = entity.totalPage;
  data['size'] = entity.size;
  data['data'] = entity.data.map((v) => v.toJson()).toList();
  data['totalItems'] = entity.totalItems;
  return data;
}

extension ZxCourseDataExtension on ZxCourseData {
  ZxCourseData copyWith({
    String? currentPage,
    String? totalPage,
    String? size,
    List<ZxCourseDataData>? data,
    String? totalItems,
  }) {
    return ZxCourseData()
      ..currentPage = currentPage ?? this.currentPage
      ..totalPage = totalPage ?? this.totalPage
      ..size = size ?? this.size
      ..data = data ?? this.data
      ..totalItems = totalItems ?? this.totalItems;
  }
}

ZxCourseDataData $ZxCourseDataDataFromJson(Map<String, dynamic> json) {
  final ZxCourseDataData zxCourseDataData = ZxCourseDataData();
  final String? goodsId = jsonConvert.convert<String>(json['goodsId']);
  if (goodsId != null) {
    zxCourseDataData.goodsId = goodsId;
  }
  final String? goodsName = jsonConvert.convert<String>(json['goodsName']);
  if (goodsName != null) {
    zxCourseDataData.goodsName = goodsName;
  }
  final String? goodsDesc = jsonConvert.convert<String>(json['goodsDesc']);
  if (goodsDesc != null) {
    zxCourseDataData.goodsDesc = goodsDesc;
  }
  final String? usageInstruction = jsonConvert.convert<String>(
      json['usageInstruction']);
  if (usageInstruction != null) {
    zxCourseDataData.usageInstruction = usageInstruction;
  }
  final String? goodsCategoryId = jsonConvert.convert<String>(
      json['goodsCategoryId']);
  if (goodsCategoryId != null) {
    zxCourseDataData.goodsCategoryId = goodsCategoryId;
  }
  final String? goodsCategoryName = jsonConvert.convert<String>(
      json['goodsCategoryName']);
  if (goodsCategoryName != null) {
    zxCourseDataData.goodsCategoryName = goodsCategoryName;
  }
  final int? goodsType = jsonConvert.convert<int>(json['goodsType']);
  if (goodsType != null) {
    zxCourseDataData.goodsType = goodsType;
  }
  final String? goodsPicUrl = jsonConvert.convert<String>(json['goodsPicUrl']);
  if (goodsPicUrl != null) {
    zxCourseDataData.goodsPicUrl = goodsPicUrl;
  }
  final String? goodsSpecialPicUrl = jsonConvert.convert<String>(json['goodsSpecialPicUrl']);
  if (goodsSpecialPicUrl != null) {
    zxCourseDataData.goodsSpecialPicUrl = goodsSpecialPicUrl;
  }
  final String? goodsVideoUrl = jsonConvert.convert<String>(
      json['goodsVideoUrl']);
  if (goodsVideoUrl != null) {
    zxCourseDataData.goodsVideoUrl = goodsVideoUrl;
  }
  final int? goodsStock = jsonConvert.convert<int>(json['goodsStock']);
  if (goodsStock != null) {
    zxCourseDataData.goodsStock = goodsStock;
  }
  final int? goodsSales = jsonConvert.convert<int>(json['goodsSales']);
  if (goodsSales != null) {
    zxCourseDataData.goodsSales = goodsSales;
  }
  final double? goodsCostPrice = jsonConvert.convert<double>(
      json['goodsCostPrice']);
  if (goodsCostPrice != null) {
    zxCourseDataData.goodsCostPrice = goodsCostPrice;
  }
  final double? goodsMaxCostPrice = jsonConvert.convert<double>(
      json['goodsMaxCostPrice']);
  if (goodsMaxCostPrice != null) {
    zxCourseDataData.goodsMaxCostPrice = goodsMaxCostPrice;
  }
  final double? goodsOriginalPrice = jsonConvert.convert<double>(
      json['goodsOriginalPrice']);
  if (goodsOriginalPrice != null) {
    zxCourseDataData.goodsOriginalPrice = goodsOriginalPrice;
  }
  final double? goodsVipPrice = jsonConvert.convert<double>(
      json['goodsVipPrice']);
  if (goodsVipPrice != null) {
    zxCourseDataData.goodsVipPrice = goodsVipPrice;
  }
  final double? goodsOriginalDiscountPrice = jsonConvert.convert<double>(
      json['goodsOriginalDiscountPrice']);
  if (goodsOriginalDiscountPrice != null) {
    zxCourseDataData.goodsOriginalDiscountPrice = goodsOriginalDiscountPrice;
  }
  final double? goodsVipDiscountPrice = jsonConvert.convert<double>(
      json['goodsVipDiscountPrice']);
  if (goodsVipDiscountPrice != null) {
    zxCourseDataData.goodsVipDiscountPrice = goodsVipDiscountPrice;
  }
  final String? goodsCurrencyUnit = jsonConvert.convert<String>(
      json['goodsCurrencyUnit']);
  if (goodsCurrencyUnit != null) {
    zxCourseDataData.goodsCurrencyUnit = goodsCurrencyUnit;
  }
  final String? goodsCurrencyDesc = jsonConvert.convert<String>(
      json['goodsCurrencyDesc']);
  if (goodsCurrencyDesc != null) {
    zxCourseDataData.goodsCurrencyDesc = goodsCurrencyDesc;
  }
  final int? goodsCommission = jsonConvert.convert<int>(
      json['goodsCommission']);
  if (goodsCommission != null) {
    zxCourseDataData.goodsCommission = goodsCommission;
  }
  final double? goodsMaxOriginalPrice = jsonConvert.convert<double>(
      json['goodsMaxOriginalPrice']);
  if (goodsMaxOriginalPrice != null) {
    zxCourseDataData.goodsMaxOriginalPrice = goodsMaxOriginalPrice;
  }
  final int? goodsMaxOriginalDiscountPrice = jsonConvert.convert<int>(
      json['goodsMaxOriginalDiscountPrice']);
  if (goodsMaxOriginalDiscountPrice != null) {
    zxCourseDataData.goodsMaxOriginalDiscountPrice =
        goodsMaxOriginalDiscountPrice;
  }
  final double? goodsMaxVipPrice = jsonConvert.convert<double>(
      json['goodsMaxVipPrice']);
  if (goodsMaxVipPrice != null) {
    zxCourseDataData.goodsMaxVipPrice = goodsMaxVipPrice;
  }
  final double? goodsShowPrice = jsonConvert.convert<double>(
      json['goodsShowPrice']);
  if (goodsShowPrice != null) {
    zxCourseDataData.goodsShowPrice = goodsShowPrice;
  }
  final int? goodsMaxVipDiscountPrice = jsonConvert.convert<int>(
      json['goodsMaxVipDiscountPrice']);
  if (goodsMaxVipDiscountPrice != null) {
    zxCourseDataData.goodsMaxVipDiscountPrice = goodsMaxVipDiscountPrice;
  }
  final int? goodsCollect = jsonConvert.convert<int>(json['goodsCollect']);
  if (goodsCollect != null) {
    zxCourseDataData.goodsCollect = goodsCollect;
  }
  final int? goodsWeight = jsonConvert.convert<int>(json['goodsWeight']);
  if (goodsWeight != null) {
    zxCourseDataData.goodsWeight = goodsWeight;
  }
  final String? goodsTagOne = jsonConvert.convert<String>(json['goodsTagOne']);
  if (goodsTagOne != null) {
    zxCourseDataData.goodsTagOne = goodsTagOne;
  }
  final String? goodsTagTwo = jsonConvert.convert<String>(json['goodsTagTwo']);
  if (goodsTagTwo != null) {
    zxCourseDataData.goodsTagTwo = goodsTagTwo;
  }
  final String? goodsTagThree = jsonConvert.convert<String>(
      json['goodsTagThree']);
  if (goodsTagThree != null) {
    zxCourseDataData.goodsTagThree = goodsTagThree;
  }
  final int? goodsStatus = jsonConvert.convert<int>(json['goodsStatus']);
  if (goodsStatus != null) {
    zxCourseDataData.goodsStatus = goodsStatus;
  }
  final String? onSaleTime = jsonConvert.convert<String>(json['onSaleTime']);
  if (onSaleTime != null) {
    zxCourseDataData.onSaleTime = onSaleTime;
  }
  final String? curriculumId = jsonConvert.convert<String>(
      json['curriculumId']);
  if (curriculumId != null) {
    zxCourseDataData.curriculumId = curriculumId;
  }
  final String? curriculumName = jsonConvert.convert<String>(
      json['curriculumName']);
  if (curriculumName != null) {
    zxCourseDataData.curriculumName = curriculumName;
  }
  final ZxCourseDataDataCoupon? coupon = jsonConvert.convert<
      ZxCourseDataDataCoupon>(json['coupon']);
  if (coupon != null) {
    zxCourseDataData.coupon = coupon;
  }
  final int? whetherCollect = jsonConvert.convert<int>(json['whetherCollect']);
  if (whetherCollect != null) {
    zxCourseDataData.whetherCollect = whetherCollect;
  }
  final String? goodsSharePoster = jsonConvert.convert<String>(
      json['goodsSharePoster']);
  if (goodsSharePoster != null) {
    zxCourseDataData.goodsSharePoster = goodsSharePoster;
  }
  final String? goodsSharePosterTwo = jsonConvert.convert<String>(
      json['goodsSharePosterTwo']);
  if (goodsSharePosterTwo != null) {
    zxCourseDataData.goodsSharePosterTwo = goodsSharePosterTwo;
  }
  final String? goodsSharePosterThree = jsonConvert.convert<String>(
      json['goodsSharePosterThree']);
  if (goodsSharePosterThree != null) {
    zxCourseDataData.goodsSharePosterThree = goodsSharePosterThree;
  }
  final int? goodsRecommendationWeight = jsonConvert.convert<int>(
      json['goodsRecommendationWeight']);
  if (goodsRecommendationWeight != null) {
    zxCourseDataData.goodsRecommendationWeight = goodsRecommendationWeight;
  }
  final int? goodsRecommendationWhetherTop = jsonConvert.convert<int>(
      json['goodsRecommendationWhetherTop']);
  if (goodsRecommendationWhetherTop != null) {
    zxCourseDataData.goodsRecommendationWhetherTop =
        goodsRecommendationWhetherTop;
  }
  final dynamic studentCourse = json['studentCourse'];
  if (studentCourse != null) {
    zxCourseDataData.studentCourse = studentCourse;
  }
  final List<dynamic>? goodsSpecList = (json['goodsSpecList'] as List<dynamic>?)
      ?.map(
          (e) => e)
      .toList();
  if (goodsSpecList != null) {
    zxCourseDataData.goodsSpecList = goodsSpecList;
  }
  final List<dynamic>? goodsSpecPriceList = (json['goodsSpecPriceList'] as List<
      dynamic>?)?.map(
          (e) => e).toList();
  if (goodsSpecPriceList != null) {
    zxCourseDataData.goodsSpecPriceList = goodsSpecPriceList;
  }
  final List<dynamic>? goodsCarouselList = (json['goodsCarouselList'] as List<
      dynamic>?)?.map(
          (e) => e).toList();
  if (goodsCarouselList != null) {
    zxCourseDataData.goodsCarouselList = goodsCarouselList;
  }
  final List<
      ZxCourseDataDataGoodsShareTextList>? goodsShareTextList = (json['goodsShareTextList'] as List<
      dynamic>?)?.map(
          (e) =>
      jsonConvert.convert<ZxCourseDataDataGoodsShareTextList>(
          e) as ZxCourseDataDataGoodsShareTextList).toList();
  if (goodsShareTextList != null) {
    zxCourseDataData.goodsShareTextList = goodsShareTextList;
  }
  final List<dynamic>? goodsCatalogueList = (json['goodsCatalogueList'] as List<
      dynamic>?)?.map(
          (e) => e).toList();
  if (goodsCatalogueList != null) {
    zxCourseDataData.goodsCatalogueList = goodsCatalogueList;
  }
  final List<
      dynamic>? piGoodsExchangeCardList = (json['piGoodsExchangeCardList'] as List<
      dynamic>?)?.map(
          (e) => e).toList();
  if (piGoodsExchangeCardList != null) {
    zxCourseDataData.piGoodsExchangeCardList = piGoodsExchangeCardList;
  }
  return zxCourseDataData;
}

Map<String, dynamic> $ZxCourseDataDataToJson(ZxCourseDataData entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['goodsId'] = entity.goodsId;
  data['goodsName'] = entity.goodsName;
  data['goodsDesc'] = entity.goodsDesc;
  data['usageInstruction'] = entity.usageInstruction;
  data['goodsCategoryId'] = entity.goodsCategoryId;
  data['goodsCategoryName'] = entity.goodsCategoryName;
  data['goodsType'] = entity.goodsType;
  data['goodsPicUrl'] = entity.goodsPicUrl;
  data['goodsSpecialPicUrl'] = entity.goodsSpecialPicUrl;
  data['goodsVideoUrl'] = entity.goodsVideoUrl;
  data['goodsStock'] = entity.goodsStock;
  data['goodsSales'] = entity.goodsSales;
  data['goodsCostPrice'] = entity.goodsCostPrice;
  data['goodsMaxCostPrice'] = entity.goodsMaxCostPrice;
  data['goodsOriginalPrice'] = entity.goodsOriginalPrice;
  data['goodsVipPrice'] = entity.goodsVipPrice;
  data['goodsOriginalDiscountPrice'] = entity.goodsOriginalDiscountPrice;
  data['goodsVipDiscountPrice'] = entity.goodsVipDiscountPrice;
  data['goodsCurrencyUnit'] = entity.goodsCurrencyUnit;
  data['goodsCurrencyDesc'] = entity.goodsCurrencyDesc;
  data['goodsCommission'] = entity.goodsCommission;
  data['goodsMaxOriginalPrice'] = entity.goodsMaxOriginalPrice;
  data['goodsMaxOriginalDiscountPrice'] = entity.goodsMaxOriginalDiscountPrice;
  data['goodsMaxVipPrice'] = entity.goodsMaxVipPrice;
  data['goodsShowPrice'] = entity.goodsShowPrice;
  data['goodsMaxVipDiscountPrice'] = entity.goodsMaxVipDiscountPrice;
  data['goodsCollect'] = entity.goodsCollect;
  data['goodsWeight'] = entity.goodsWeight;
  data['goodsTagOne'] = entity.goodsTagOne;
  data['goodsTagTwo'] = entity.goodsTagTwo;
  data['goodsTagThree'] = entity.goodsTagThree;
  data['goodsStatus'] = entity.goodsStatus;
  data['onSaleTime'] = entity.onSaleTime;
  data['curriculumId'] = entity.curriculumId;
  data['curriculumName'] = entity.curriculumName;
  data['coupon'] = entity.coupon.toJson();
  data['whetherCollect'] = entity.whetherCollect;
  data['goodsSharePoster'] = entity.goodsSharePoster;
  data['goodsSharePosterTwo'] = entity.goodsSharePosterTwo;
  data['goodsSharePosterThree'] = entity.goodsSharePosterThree;
  data['goodsRecommendationWeight'] = entity.goodsRecommendationWeight;
  data['goodsRecommendationWhetherTop'] = entity.goodsRecommendationWhetherTop;
  data['studentCourse'] = entity.studentCourse;
  data['goodsSpecList'] = entity.goodsSpecList;
  data['goodsSpecPriceList'] = entity.goodsSpecPriceList;
  data['goodsCarouselList'] = entity.goodsCarouselList;
  data['goodsShareTextList'] =
      entity.goodsShareTextList.map((v) => v.toJson()).toList();
  data['goodsCatalogueList'] = entity.goodsCatalogueList;
  data['piGoodsExchangeCardList'] = entity.piGoodsExchangeCardList;
  return data;
}

extension ZxCourseDataDataExtension on ZxCourseDataData {
  ZxCourseDataData copyWith({
    String? goodsId,
    String? goodsName,
    String? goodsDesc,
    String? usageInstruction,
    String? goodsCategoryId,
    String? goodsCategoryName,
    int? goodsType,
    String? goodsPicUrl,
    String?goodsSpecialPicUrl,
    String? goodsVideoUrl,
    int? goodsStock,
    int? goodsSales,
    double? goodsCostPrice,
    double? goodsMaxCostPrice,
    double? goodsOriginalPrice,
    double? goodsVipPrice,
    double? goodsOriginalDiscountPrice,
    double? goodsVipDiscountPrice,
    String? goodsCurrencyUnit,
    String? goodsCurrencyDesc,
    int? goodsCommission,
    double? goodsMaxOriginalPrice,
    int? goodsMaxOriginalDiscountPrice,
    double? goodsMaxVipPrice,
    double? goodsShowPrice,
    int? goodsMaxVipDiscountPrice,
    int? goodsCollect,
    int? goodsWeight,
    String? goodsTagOne,
    String? goodsTagTwo,
    String? goodsTagThree,
    int? goodsStatus,
    String? onSaleTime,
    String? curriculumId,
    String? curriculumName,
    ZxCourseDataDataCoupon? coupon,
    int? whetherCollect,
    String? goodsSharePoster,
    String? goodsSharePosterTwo,
    String? goodsSharePosterThree,
    int? goodsRecommendationWeight,
    int? goodsRecommendationWhetherTop,
    dynamic studentCourse,
    List<dynamic>? goodsSpecList,
    List<dynamic>? goodsSpecPriceList,
    List<dynamic>? goodsCarouselList,
    List<ZxCourseDataDataGoodsShareTextList>? goodsShareTextList,
    List<dynamic>? goodsCatalogueList,
    List<dynamic>? piGoodsExchangeCardList,
  }) {
    return ZxCourseDataData()
      ..goodsId = goodsId ?? this.goodsId
      ..goodsName = goodsName ?? this.goodsName
      ..goodsDesc = goodsDesc ?? this.goodsDesc
      ..usageInstruction = usageInstruction ?? this.usageInstruction
      ..goodsCategoryId = goodsCategoryId ?? this.goodsCategoryId
      ..goodsCategoryName = goodsCategoryName ?? this.goodsCategoryName
      ..goodsType = goodsType ?? this.goodsType
      ..goodsPicUrl = goodsPicUrl ?? this.goodsPicUrl
      ..goodsSpecialPicUrl = goodsSpecialPicUrl ?? this.goodsSpecialPicUrl
      ..goodsVideoUrl = goodsVideoUrl ?? this.goodsVideoUrl
      ..goodsStock = goodsStock ?? this.goodsStock
      ..goodsSales = goodsSales ?? this.goodsSales
      ..goodsCostPrice = goodsCostPrice ?? this.goodsCostPrice
      ..goodsMaxCostPrice = goodsMaxCostPrice ?? this.goodsMaxCostPrice
      ..goodsOriginalPrice = goodsOriginalPrice ?? this.goodsOriginalPrice
      ..goodsVipPrice = goodsVipPrice ?? this.goodsVipPrice
      ..goodsOriginalDiscountPrice = goodsOriginalDiscountPrice ??
          this.goodsOriginalDiscountPrice
      ..goodsVipDiscountPrice = goodsVipDiscountPrice ??
          this.goodsVipDiscountPrice
      ..goodsCurrencyUnit = goodsCurrencyUnit ?? this.goodsCurrencyUnit
      ..goodsCurrencyDesc = goodsCurrencyDesc ?? this.goodsCurrencyDesc
      ..goodsCommission = goodsCommission ?? this.goodsCommission
      ..goodsMaxOriginalPrice = goodsMaxOriginalPrice ??
          this.goodsMaxOriginalPrice
      ..goodsMaxOriginalDiscountPrice = goodsMaxOriginalDiscountPrice ??
          this.goodsMaxOriginalDiscountPrice
      ..goodsMaxVipPrice = goodsMaxVipPrice ?? this.goodsMaxVipPrice
      ..goodsShowPrice = goodsShowPrice ?? this.goodsShowPrice
      ..goodsMaxVipDiscountPrice = goodsMaxVipDiscountPrice ??
          this.goodsMaxVipDiscountPrice
      ..goodsCollect = goodsCollect ?? this.goodsCollect
      ..goodsWeight = goodsWeight ?? this.goodsWeight
      ..goodsTagOne = goodsTagOne ?? this.goodsTagOne
      ..goodsTagTwo = goodsTagTwo ?? this.goodsTagTwo
      ..goodsTagThree = goodsTagThree ?? this.goodsTagThree
      ..goodsStatus = goodsStatus ?? this.goodsStatus
      ..onSaleTime = onSaleTime ?? this.onSaleTime
      ..curriculumId = curriculumId ?? this.curriculumId
      ..curriculumName = curriculumName ?? this.curriculumName
      ..coupon = coupon ?? this.coupon
      ..whetherCollect = whetherCollect ?? this.whetherCollect
      ..goodsSharePoster = goodsSharePoster ?? this.goodsSharePoster
      ..goodsSharePosterTwo = goodsSharePosterTwo ?? this.goodsSharePosterTwo
      ..goodsSharePosterThree = goodsSharePosterThree ??
          this.goodsSharePosterThree
      ..goodsRecommendationWeight = goodsRecommendationWeight ??
          this.goodsRecommendationWeight
      ..goodsRecommendationWhetherTop = goodsRecommendationWhetherTop ??
          this.goodsRecommendationWhetherTop
      ..studentCourse = studentCourse ?? this.studentCourse
      ..goodsSpecList = goodsSpecList ?? this.goodsSpecList
      ..goodsSpecPriceList = goodsSpecPriceList ?? this.goodsSpecPriceList
      ..goodsCarouselList = goodsCarouselList ?? this.goodsCarouselList
      ..goodsShareTextList = goodsShareTextList ?? this.goodsShareTextList
      ..goodsCatalogueList = goodsCatalogueList ?? this.goodsCatalogueList
      ..piGoodsExchangeCardList = piGoodsExchangeCardList ??
          this.piGoodsExchangeCardList;
  }
}

ZxCourseDataDataCoupon $ZxCourseDataDataCouponFromJson(
    Map<String, dynamic> json) {
  final ZxCourseDataDataCoupon zxCourseDataDataCoupon = ZxCourseDataDataCoupon();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    zxCourseDataDataCoupon.id = id;
  }
  final String? couponName = jsonConvert.convert<String>(json['couponName']);
  if (couponName != null) {
    zxCourseDataDataCoupon.couponName = couponName;
  }
  final String? couponStartTime = jsonConvert.convert<String>(
      json['couponStartTime']);
  if (couponStartTime != null) {
    zxCourseDataDataCoupon.couponStartTime = couponStartTime;
  }
  final String? couponEndTime = jsonConvert.convert<String>(
      json['couponEndTime']);
  if (couponEndTime != null) {
    zxCourseDataDataCoupon.couponEndTime = couponEndTime;
  }
  final int? couponEffectiveDays = jsonConvert.convert<int>(
      json['couponEffectiveDays']);
  if (couponEffectiveDays != null) {
    zxCourseDataDataCoupon.couponEffectiveDays = couponEffectiveDays;
  }
  final int? couponHasCondition = jsonConvert.convert<int>(
      json['couponHasCondition']);
  if (couponHasCondition != null) {
    zxCourseDataDataCoupon.couponHasCondition = couponHasCondition;
  }
  final int? couponCondition = jsonConvert.convert<int>(
      json['couponCondition']);
  if (couponCondition != null) {
    zxCourseDataDataCoupon.couponCondition = couponCondition;
  }
  final double? couponDiscount = jsonConvert.convert<double>(
      json['couponDiscount']);
  if (couponDiscount != null) {
    zxCourseDataDataCoupon.couponDiscount = couponDiscount;
  }
  final int? couponLimitType = jsonConvert.convert<int>(
      json['couponLimitType']);
  if (couponLimitType != null) {
    zxCourseDataDataCoupon.couponLimitType = couponLimitType;
  }
  final int? couponLimitQuantity = jsonConvert.convert<int>(
      json['couponLimitQuantity']);
  if (couponLimitQuantity != null) {
    zxCourseDataDataCoupon.couponLimitQuantity = couponLimitQuantity;
  }
  final int? couponQuantity = jsonConvert.convert<int>(json['couponQuantity']);
  if (couponQuantity != null) {
    zxCourseDataDataCoupon.couponQuantity = couponQuantity;
  }
  final int? couponReceivedQuantity = jsonConvert.convert<int>(
      json['couponReceivedQuantity']);
  if (couponReceivedQuantity != null) {
    zxCourseDataDataCoupon.couponReceivedQuantity = couponReceivedQuantity;
  }
  final int? couponType = jsonConvert.convert<int>(json['couponType']);
  if (couponType != null) {
    zxCourseDataDataCoupon.couponType = couponType;
  }
  final int? couponStatus = jsonConvert.convert<int>(json['couponStatus']);
  if (couponStatus != null) {
    zxCourseDataDataCoupon.couponStatus = couponStatus;
  }
  final String? createdTime = jsonConvert.convert<String>(json['createdTime']);
  if (createdTime != null) {
    zxCourseDataDataCoupon.createdTime = createdTime;
  }
  final String? updatedTime = jsonConvert.convert<String>(json['updatedTime']);
  if (updatedTime != null) {
    zxCourseDataDataCoupon.updatedTime = updatedTime;
  }
  final int? personLimitType = jsonConvert.convert<int>(
      json['personLimitType']);
  if (personLimitType != null) {
    zxCourseDataDataCoupon.personLimitType = personLimitType;
  }
  final int? couponUserType = jsonConvert.convert<int>(json['couponUserType']);
  if (couponUserType != null) {
    zxCourseDataDataCoupon.couponUserType = couponUserType;
  }
  final int? couponReceivedType = jsonConvert.convert<int>(
      json['couponReceivedType']);
  if (couponReceivedType != null) {
    zxCourseDataDataCoupon.couponReceivedType = couponReceivedType;
  }
  final int? couponTaskType = jsonConvert.convert<int>(json['couponTaskType']);
  if (couponTaskType != null) {
    zxCourseDataDataCoupon.couponTaskType = couponTaskType;
  }
  final List<dynamic>? couponVoList = (json['couponVoList'] as List<dynamic>?)
      ?.map(
          (e) => e)
      .toList();
  if (couponVoList != null) {
    zxCourseDataDataCoupon.couponVoList = couponVoList;
  }
  final int? showApp = jsonConvert.convert<int>(json['showApp']);
  if (showApp != null) {
    zxCourseDataDataCoupon.showApp = showApp;
  }
  final String? gainWay = jsonConvert.convert<String>(json['gainWay']);
  if (gainWay != null) {
    zxCourseDataDataCoupon.gainWay = gainWay;
  }
  final int? gainPersonLimitType = jsonConvert.convert<int>(
      json['gainPersonLimitType']);
  if (gainPersonLimitType != null) {
    zxCourseDataDataCoupon.gainPersonLimitType = gainPersonLimitType;
  }
  final int? shareable = jsonConvert.convert<int>(json['shareable']);
  if (shareable != null) {
    zxCourseDataDataCoupon.shareable = shareable;
  }
  final int? createRedemptionCode = jsonConvert.convert<int>(
      json['createRedemptionCode']);
  if (createRedemptionCode != null) {
    zxCourseDataDataCoupon.createRedemptionCode = createRedemptionCode;
  }
  final int? useSharePersonLimitType = jsonConvert.convert<int>(
      json['useSharePersonLimitType']);
  if (useSharePersonLimitType != null) {
    zxCourseDataDataCoupon.useSharePersonLimitType = useSharePersonLimitType;
  }
  return zxCourseDataDataCoupon;
}

Map<String, dynamic> $ZxCourseDataDataCouponToJson(
    ZxCourseDataDataCoupon entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['couponName'] = entity.couponName;
  data['couponStartTime'] = entity.couponStartTime;
  data['couponEndTime'] = entity.couponEndTime;
  data['couponEffectiveDays'] = entity.couponEffectiveDays;
  data['couponHasCondition'] = entity.couponHasCondition;
  data['couponCondition'] = entity.couponCondition;
  data['couponDiscount'] = entity.couponDiscount;
  data['couponLimitType'] = entity.couponLimitType;
  data['couponLimitQuantity'] = entity.couponLimitQuantity;
  data['couponQuantity'] = entity.couponQuantity;
  data['couponReceivedQuantity'] = entity.couponReceivedQuantity;
  data['couponType'] = entity.couponType;
  data['couponStatus'] = entity.couponStatus;
  data['createdTime'] = entity.createdTime;
  data['updatedTime'] = entity.updatedTime;
  data['personLimitType'] = entity.personLimitType;
  data['couponUserType'] = entity.couponUserType;
  data['couponReceivedType'] = entity.couponReceivedType;
  data['couponTaskType'] = entity.couponTaskType;
  data['couponVoList'] = entity.couponVoList;
  data['showApp'] = entity.showApp;
  data['gainWay'] = entity.gainWay;
  data['gainPersonLimitType'] = entity.gainPersonLimitType;
  data['shareable'] = entity.shareable;
  data['createRedemptionCode'] = entity.createRedemptionCode;
  data['useSharePersonLimitType'] = entity.useSharePersonLimitType;
  return data;
}

extension ZxCourseDataDataCouponExtension on ZxCourseDataDataCoupon {
  ZxCourseDataDataCoupon copyWith({
    String? id,
    String? couponName,
    String? couponStartTime,
    String? couponEndTime,
    int? couponEffectiveDays,
    int? couponHasCondition,
    int? couponCondition,
    double? couponDiscount,
    int? couponLimitType,
    int? couponLimitQuantity,
    int? couponQuantity,
    int? couponReceivedQuantity,
    int? couponType,
    int? couponStatus,
    String? createdTime,
    String? updatedTime,
    int? personLimitType,
    int? couponUserType,
    int? couponReceivedType,
    int? couponTaskType,
    List<dynamic>? couponVoList,
    int? showApp,
    String? gainWay,
    int? gainPersonLimitType,
    int? shareable,
    int? createRedemptionCode,
    int? useSharePersonLimitType,
  }) {
    return ZxCourseDataDataCoupon()
      ..id = id ?? this.id
      ..couponName = couponName ?? this.couponName
      ..couponStartTime = couponStartTime ?? this.couponStartTime
      ..couponEndTime = couponEndTime ?? this.couponEndTime
      ..couponEffectiveDays = couponEffectiveDays ?? this.couponEffectiveDays
      ..couponHasCondition = couponHasCondition ?? this.couponHasCondition
      ..couponCondition = couponCondition ?? this.couponCondition
      ..couponDiscount = couponDiscount ?? this.couponDiscount
      ..couponLimitType = couponLimitType ?? this.couponLimitType
      ..couponLimitQuantity = couponLimitQuantity ?? this.couponLimitQuantity
      ..couponQuantity = couponQuantity ?? this.couponQuantity
      ..couponReceivedQuantity = couponReceivedQuantity ??
          this.couponReceivedQuantity
      ..couponType = couponType ?? this.couponType
      ..couponStatus = couponStatus ?? this.couponStatus
      ..createdTime = createdTime ?? this.createdTime
      ..updatedTime = updatedTime ?? this.updatedTime
      ..personLimitType = personLimitType ?? this.personLimitType
      ..couponUserType = couponUserType ?? this.couponUserType
      ..couponReceivedType = couponReceivedType ?? this.couponReceivedType
      ..couponTaskType = couponTaskType ?? this.couponTaskType
      ..couponVoList = couponVoList ?? this.couponVoList
      ..showApp = showApp ?? this.showApp
      ..gainWay = gainWay ?? this.gainWay
      ..gainPersonLimitType = gainPersonLimitType ?? this.gainPersonLimitType
      ..shareable = shareable ?? this.shareable
      ..createRedemptionCode = createRedemptionCode ?? this.createRedemptionCode
      ..useSharePersonLimitType = useSharePersonLimitType ??
          this.useSharePersonLimitType;
  }
}

ZxCourseDataDataGoodsShareTextList $ZxCourseDataDataGoodsShareTextListFromJson(
    Map<String, dynamic> json) {
  final ZxCourseDataDataGoodsShareTextList zxCourseDataDataGoodsShareTextList = ZxCourseDataDataGoodsShareTextList();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    zxCourseDataDataGoodsShareTextList.id = id;
  }
  final String? goodsId = jsonConvert.convert<String>(json['goodsId']);
  if (goodsId != null) {
    zxCourseDataDataGoodsShareTextList.goodsId = goodsId;
  }
  final String? shareText = jsonConvert.convert<String>(json['shareText']);
  if (shareText != null) {
    zxCourseDataDataGoodsShareTextList.shareText = shareText;
  }
  return zxCourseDataDataGoodsShareTextList;
}

Map<String, dynamic> $ZxCourseDataDataGoodsShareTextListToJson(
    ZxCourseDataDataGoodsShareTextList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['goodsId'] = entity.goodsId;
  data['shareText'] = entity.shareText;
  return data;
}

extension ZxCourseDataDataGoodsShareTextListExtension on ZxCourseDataDataGoodsShareTextList {
  ZxCourseDataDataGoodsShareTextList copyWith({
    String? id,
    String? goodsId,
    String? shareText,
  }) {
    return ZxCourseDataDataGoodsShareTextList()
      ..id = id ?? this.id
      ..goodsId = goodsId ?? this.goodsId
      ..shareText = shareText ?? this.shareText;
  }
}