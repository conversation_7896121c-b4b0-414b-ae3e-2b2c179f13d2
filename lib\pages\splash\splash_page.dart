import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_state_manager/src/simple/get_view.dart';
import 'package:student_end_flutter/pages/base/controller/base_controller.dart';
import 'package:student_end_flutter/res/colors.dart';
import 'package:tx_im/dx_utils/http_config.dart';
import 'package:tx_im/im_module_service.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../common/Config.dart';
import '../../common/sensors_analytics.dart';
import '../../common/user_option.dart';
import '../../dialog/dialog_sdk_description.dart';
import '../../main.dart';
import '../../utils/android_ios_plugin.dart';
import '../../utils/app_version_util.dart';
import '../../utils/config_service.dart';
import '../../utils/httpRequest.dart';
import '../../utils/sharedPreferences_util.dart';
import '../base/base_page.dart';
import '../base/check_notification_permissons_page.dart';
import '../login/webView_page.dart';
import '../advertisement/open_screen_advertisement.dart';
import '../../navigator/home_navigator.dart';
import 'package:student_end_flutter/common/user_option.dart';
import '../../common/CommonUtil.dart';
RxInt key=1.obs;
var openScreenInfo={};
RxInt agreeTipsNumber=0.obs;
class SplashPage extends GetView<SplashController> {
  @override
  Widget build(BuildContext context) {
    Get.put(SplashController());
    return Scaffold(
        backgroundColor: Colors.white,
        body:Obx(()=>Stack(
          children: [
            Container(
              child: key.value==1? Image.asset(
                "assets/launchImage.png",
                fit: BoxFit.fill,
                width: double.infinity,
                height: double.infinity,
              ):GestureDetector(
                onTap: () {
                  goPath();
                },
                child:Container(
                  alignment: Alignment.center,
                    decoration: BoxDecoration(
                      gradient:openScreenInfo['backGroundColor'].isNotEmpty? LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [HexColor(openScreenInfo['backGroundColor']),HexColor('#ffffff'),HexColor(openScreenInfo['backGroundColor'])]):LinearGradient(colors: [HexColor('#ffffff'),HexColor('#ffffff')]),
                    ),

                  child: CachedNetworkImage(
                    imageUrl:openScreenInfo['picUrl'],
                    fit: BoxFit.fill,
                    width: double.infinity,
                    // height: double.infinity,
                  ),
                )
              ),
            ),
            // 跳过按钮
            if (key.value != 1)  // 根据需要设置显示条件（示例为非启动图时显示）
              Positioned(
                top: MediaQuery.of(context).padding.top + 16,  // 考虑状态栏高度
                right: 16,
                child: GestureDetector(
                  onTap: () {
                    // 添加跳过逻辑
                    // Get.find<SplashController>().skipSplash();
                    SplashController.isOpenNotificationSettings(agreeTipsNumber.value);
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.4),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      '跳过',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ),
              ),
          ],
        )));
  }
  goPath(){
    if (UserOption.checkNoToken()) {
      return;
    }
    String path ='';
    if(openScreenInfo!=null){
      if(openScreenInfo['goodsType']==1){
         path =
            HomeNavigator.goodsDetails(goodsId: openScreenInfo['goodsId'])
                .path+'&options=OpenScreenPage';
        AndroidIosPlugin.openUniApp(path);

      }else if(openScreenInfo['goodsType']==2){
        path=  HomeNavigator.memberCenterDetails(id: openScreenInfo['goodsId'])
            .path;
        AndroidIosPlugin.openUniApp(path);
      }else if(openScreenInfo['goodsType']==3){
        if(openScreenInfo['jumpUrl'].isNotEmpty){
          if(openScreenInfo['jumpUrl'].contains('?')){
            path += openScreenInfo['jumpUrl'];
          }else{
            path +='${openScreenInfo['jumpUrl']}?activityId=${openScreenInfo['goodsId']}';
          }
        }else{
          path+='activity/index?activityId=${openScreenInfo['goodsId']}';
        }
        path+="&token=${UserOption.token}&app=2&options=HomePage&nickName=${UserOption.userInfoBeanEntity?.nickName}&userCode=${UserOption.userInfoBeanEntity?.userCode}"
            "&identityType=${UserOption.userInfoBeanEntity?.identityType}&userId=${UserOption.userId}&phone=${UserOption.userInfoBeanEntity?.mobile}&baseUrl=${Config.URL}";
        AndroidIosPlugin.openUniApp(path);
      }else if(openScreenInfo['goodsType']==4){
        path += path+='activity/groupBuying/index?groupActivityId=${openScreenInfo['goodsId']}';
        path+="&token=${UserOption.token}&app=2&options=HomePage&nickName=${UserOption.userInfoBeanEntity?.nickName}&userCode=${UserOption.userInfoBeanEntity?.userCode}"
            "&identityType=${UserOption.userInfoBeanEntity?.identityType}&userId=${UserOption.userId}&phone=${UserOption.userInfoBeanEntity?.mobile}&baseUrl=${Config.URL}";
        AndroidIosPlugin.openUniApp(path);
      }
    }
  }
}

class SplashController extends GetxController {
  @override
  onInit() {
    super.onInit();
    getopenScreen();
    /// 获取分享类型
    if(UserOption.hasAgreedToPrivacyPolicy){
      ConfigService.getZxShareConfig();

    }else {

    }

    getDynamicGateway();  // 测试环境注释
  }

  @override
  onReady() {
    super.onReady();
    // agreeTips();
  }

  agreeTips() async {
    int agreeTips = await SharedPreferencesUtil.getData<int>(
        SharedPreferencesUtil.agreeTips);
  int  hasShownGuide =  await SharedPreferencesUtil.getData('hasShownGuide');
    print(agreeTips);
    agreeTipsNumber.value=agreeTips;
    if (agreeTips != 1) {
      //未同意  每次都提示弹窗
      Get.dialog(
        SdkDescriptionDialog(
          agreeFunc: () async {
            if(openScreenInfo.isEmpty){
              isOpenNotificationSettings(hasShownGuide);
            }else{
              openScreenTimeDown(agreeTips);
              key.value=2;
            }
            MyApp.agreeTips();
            // Get.off(BasePage());
          },
          onClickXieYi: () {
            clickUserAgree();
          },
          onClickYinsi: () {
            clickPrivacy();
          },
        ),
        barrierDismissible: false,
      );
    } else {
      timeDown(agreeTips);
    }
  }
  getopenScreen(){
    var response = HttpUtil().get('${Config.openScreen}');
    response.then((value) async {
      agreeTips();
      if(value!=null){
        openScreenInfo=value;
      }else{
        openScreenInfo={};
      }
      print(value);
      print('vvvvvvvvvvvvvvvvvvv11111111111111111111111111111111111');
    });
  }
  getDynamicGateway() {
    var response = HttpUtil(url: Config.getDynamicGatewayFlag)
        .get("${Config.getDynamicGatewayFlag}");
    response.then((value) async {
      if (value != null) {
        if (value is bool) {
          if (value == true) {
            HttpConfig.URL = Config.auditURL;
            Config.URL = Config.auditURL;
          }
        }
      }
    });
  }

  timeDown(agreeTips) {
    Future.delayed(const Duration(milliseconds: 1500), () {
      print(openScreenInfo.isEmpty);
      if(openScreenInfo.isEmpty){
        key.value=1;
        SplashController.isOpenNotificationSettings(agreeTips);
      }else{
        print('1111111111111111111111111111111111111');
        openScreenTimeDown(agreeTips);
        key.value=2;
      }
    });
  }
  openScreenTimeDown(agreeTips){
    print('2222222222222222222222222222');
    Future.delayed(const Duration(seconds: 5), () {
      isOpenNotificationSettings(agreeTips);
    });
  }
  clickUserAgree() {
    Get.to(() => WebViewPage(), arguments: {
      "titleName": "用户服务协议",
      "url": "https://document.dxznjy.com/applet/agreeon/useragreement.html"
    });
  }

  clickPrivacy() {
    Get.to(() => WebViewPage(), arguments: {
      "titleName": "隐私政策",
      "url": "https://document.dxznjy.com/applet/agreeon/app_privacypolicy.html"
    });
  }

 static  isOpenNotificationSettings(int agreeTips) {
    try {
      AndroidIosPlugin.isOpenNotificationSettings("").then((value) {
        if (value == "true") {
          if(agreeTips!=1){
            Get.off(()=>GuidePage());
          }else{
            Get.off(()=>BasePage());
          }

        } else {
          if(agreeTips!=1){
            Get.off(()=>GuidePage());
          }else{
            Get.off(()=>CheckNotificationPermissonsPage());
          }

        }
      }).catchError((error) {
        if(agreeTips!=1){
          Get.off(()=>GuidePage());
        }else{
          Get.off(()=>BasePage());
        }
      });
    } catch (e) {
      if(agreeTips!=1){
        Get.off(()=>GuidePage());
      }else{
        Get.off(()=>BasePage());
      }
    }
    AndroidIosPlugin.isOpenNotificationSettings("").then((value) {
      if (value == "true") {
        if(agreeTips!=1){
          Get.off(()=>GuidePage());
        }else{
          Get.off(()=>BasePage());
        }
      } else {
        if(agreeTips!=1){
          Get.off(()=>GuidePage());
        }else{
          Get.off(()=>CheckNotificationPermissonsPage());
        }
      }
    });
  }
}
