import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:netease_meeting_kit/meeting_ui.dart';
import 'package:student_end_flutter/bean/course_list_bean_entity.dart';
import 'package:student_end_flutter/bean/course_type_bean_entity.dart';

import '../common/CommonUtil.dart';

class SelectLessonTypeDialog extends StatefulWidget {
  List<CourseTypeBeanEntity> courseTypeList = [];
  var onSelect;
  int selectedIndex = 0;
  SelectLessonTypeDialog(this.courseTypeList,this.onSelect,this.selectedIndex);

  @override
  State<SelectLessonTypeDialog> createState() => _SelectLessonTypeDialogState();
}

class _SelectLessonTypeDialogState extends State<SelectLessonTypeDialog> {
  @override
  Widget build(BuildContext context) {
    double screenHeight = MediaQuery.of(context).size.height;
    return  Align(
      alignment: Alignment.bottomCenter,
      child: SingleChildScrollView(
        child: Container(
          padding: EdgeInsets.only(left: 14.w, right: 14.w),
          alignment: Alignment.bottomCenter,
          height: screenHeight/3,
          decoration:  BoxDecoration(
            color:HexColor('ffffff') ,
            borderRadius: BorderRadius.only(topLeft: Radius.circular(15),topRight: Radius.circular(15)),
          ),
          child: Column(
            children: [
              SizedBox(
                height: 40.h,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '课程类型',
                      style: TextStyle(
                          decoration: TextDecoration.none,
                          fontSize: 16.sp,
                          fontWeight: FontWeight.bold,
                          color: HexColor("333333")),
                    ),
                    GestureDetector(
                      onTap: () {
                        Navigator.pop(context);
                      },
                      child: Image.asset(
                        "assets/learn/ic_close.png",
                        width: 20.w,
                        height: 20.h,
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: GridView.builder(
                  controller: ScrollController(keepScrollOffset: false),
                  gridDelegate:  const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 3,
                      crossAxisSpacing: 10,
                      childAspectRatio:2
                  ),
                  itemCount: widget.courseTypeList.length,
                  itemBuilder: (BuildContext context, int index){
                    return buildContentGridView(context,index);
                  },
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget buildContentGridView(BuildContext context, int index) {
    return GestureDetector(
      onTap: () {
        Navigator.pop(context);
        if (widget.onSelect != null) {
          widget.onSelect(widget.courseTypeList[index].id,index);
        }
      },
      child: Stack(
        children: [
          Container(
            width: double.infinity,
            alignment: Alignment.center,
            height: 40.h,
            decoration: widget.selectedIndex == index ?BoxDecoration(
              color: HexColor("#ECF9F4"),
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: HexColor("#ECF9F4"), width: 0.w),
            ) : BoxDecoration(
              color: HexColor("ffffff"),
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: HexColor("E1DEDE"), width: 1.w),
            ),
            child: Text(widget.courseTypeList[index].enName,
              textAlign: TextAlign.center,
              style: TextStyle(
                  decoration: TextDecoration.none,
                  fontWeight: widget.selectedIndex == index ? FontWeight.bold : FontWeight.normal,
                  fontSize: 13.sp,
                  color: widget.selectedIndex == index ? HexColor('428a6f'): HexColor("555555")),
            ),
          ),
          Visibility(
            visible: widget.selectedIndex == index,
            child: Positioned(
                right: 0,
                top: 0,
                child: Image.asset(
                  "assets/learn/icon_xuanzhong_d.png",
                  width: 20.w,
                  height: 18.h,
                  fit: BoxFit.fill,
                )),
          )
        ],
      ),
    );
  }
}
