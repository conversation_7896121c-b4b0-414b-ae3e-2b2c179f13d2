import 'package:student_end_flutter/generated/json/base/json_field.dart';
import 'package:student_end_flutter/generated/json/deliver_bean_entity.g.dart';
import 'dart:convert';
export 'package:student_end_flutter/generated/json/deliver_bean_entity.g.dart';

@JsonSerializable()
class DeliverBeanEntity {
	late String courseId = '';
	late String planId = '';
	late String courseName = '';
	late String courseType = '';
	late String courseTime = '';
	late int status = 0;
	late String teacherId = '';
	late String teacherName = '';
	late String teacherPhoto = '';
	late String meetingId = '';
	late String meetingNum = '';
	late String feedback = '';
	late String studentCode = '';
	late String studentName = '';
	late String oneToManyType = '';
	late String classPlanStudyId = '';
	int leaveStatus = 0;
	int lessonsFlag = 0;
	DeliverBeanEntity();

	factory DeliverBeanEntity.fromJson(Map<String, dynamic> json) => $DeliverBeanEntityFromJson(json);

	Map<String, dynamic> toJson() => $DeliverBeanEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}