import 'package:student_end_flutter/generated/json/base/json_convert_content.dart';
import 'package:student_end_flutter/bean/yunxin_userinfo_bean_entity.dart';

YunxinUserinfoBeanEntity $YunxinUserinfoBeanEntityFromJson(
    Map<String, dynamic> json) {
  final YunxinUserinfoBeanEntity yunxinUserinfoBeanEntity = YunxinUserinfoBeanEntity();
  final int? code = jsonConvert.convert<int>(json['code']);
  if (code != null) {
    yunxinUserinfoBeanEntity.code = code;
  }
  final String? msg = jsonConvert.convert<String>(json['msg']);
  if (msg != null) {
    yunxinUserinfoBeanEntity.msg = msg;
  }
  final int? ts = jsonConvert.convert<int>(json['ts']);
  if (ts != null) {
    yunxinUserinfoBeanEntity.ts = ts;
  }
  final String? cost = jsonConvert.convert<String>(json['cost']);
  if (cost != null) {
    yunxinUserinfoBeanEntity.cost = cost;
  }
  final String? requestId = jsonConvert.convert<String>(json['requestId']);
  if (requestId != null) {
    yunxinUserinfoBeanEntity.requestId = requestId;
  }
  final YunxinUserinfoBeanData? data = jsonConvert.convert<
      YunxinUserinfoBeanData>(json['data']);
  if (data != null) {
    yunxinUserinfoBeanEntity.data = data;
  }
  return yunxinUserinfoBeanEntity;
}

Map<String, dynamic> $YunxinUserinfoBeanEntityToJson(
    YunxinUserinfoBeanEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['code'] = entity.code;
  data['msg'] = entity.msg;
  data['ts'] = entity.ts;
  data['cost'] = entity.cost;
  data['requestId'] = entity.requestId;
  data['data'] = entity.data.toJson();
  return data;
}

extension YunxinUserinfoBeanEntityExtension on YunxinUserinfoBeanEntity {
  YunxinUserinfoBeanEntity copyWith({
    int? code,
    String? msg,
    int? ts,
    String? cost,
    String? requestId,
    YunxinUserinfoBeanData? data,
  }) {
    return YunxinUserinfoBeanEntity()
      ..code = code ?? this.code
      ..msg = msg ?? this.msg
      ..ts = ts ?? this.ts
      ..cost = cost ?? this.cost
      ..requestId = requestId ?? this.requestId
      ..data = data ?? this.data;
  }
}

YunxinUserinfoBeanData $YunxinUserinfoBeanDataFromJson(
    Map<String, dynamic> json) {
  final YunxinUserinfoBeanData yunxinUserinfoBeanData = YunxinUserinfoBeanData();
  final List<dynamic>? notFoundUserUuids = (json['notFoundUserUuids'] as List<
      dynamic>?)?.map(
          (e) => e).toList();
  if (notFoundUserUuids != null) {
    yunxinUserinfoBeanData.notFoundUserUuids = notFoundUserUuids;
  }
  final List<
      YunxinUserinfoBeanDataUserInfos>? userInfos = (json['userInfos'] as List<
      dynamic>?)?.map(
          (e) =>
      jsonConvert.convert<YunxinUserinfoBeanDataUserInfos>(
          e) as YunxinUserinfoBeanDataUserInfos).toList();
  if (userInfos != null) {
    yunxinUserinfoBeanData.userInfos = userInfos;
  }
  return yunxinUserinfoBeanData;
}

Map<String, dynamic> $YunxinUserinfoBeanDataToJson(
    YunxinUserinfoBeanData entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['notFoundUserUuids'] = entity.notFoundUserUuids;
  data['userInfos'] = entity.userInfos.map((v) => v.toJson()).toList();
  return data;
}

extension YunxinUserinfoBeanDataExtension on YunxinUserinfoBeanData {
  YunxinUserinfoBeanData copyWith({
    List<dynamic>? notFoundUserUuids,
    List<YunxinUserinfoBeanDataUserInfos>? userInfos,
  }) {
    return YunxinUserinfoBeanData()
      ..notFoundUserUuids = notFoundUserUuids ?? this.notFoundUserUuids
      ..userInfos = userInfos ?? this.userInfos;
  }
}

YunxinUserinfoBeanDataUserInfos $YunxinUserinfoBeanDataUserInfosFromJson(
    Map<String, dynamic> json) {
  final YunxinUserinfoBeanDataUserInfos yunxinUserinfoBeanDataUserInfos = YunxinUserinfoBeanDataUserInfos();
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    yunxinUserinfoBeanDataUserInfos.name = name;
  }
  final String? userUuid = jsonConvert.convert<String>(json['userUuid']);
  if (userUuid != null) {
    yunxinUserinfoBeanDataUserInfos.userUuid = userUuid;
  }
  final String? privateMeetingNum = jsonConvert.convert<String>(
      json['privateMeetingNum']);
  if (privateMeetingNum != null) {
    yunxinUserinfoBeanDataUserInfos.privateMeetingNum = privateMeetingNum;
  }
  final int? state = jsonConvert.convert<int>(json['state']);
  if (state != null) {
    yunxinUserinfoBeanDataUserInfos.state = state;
  }
  final String? userToken = jsonConvert.convert<String>(json['userToken']);
  if (userToken != null) {
    yunxinUserinfoBeanDataUserInfos.userToken = userToken;
  }
  final List<dynamic>? departments = (json['departments'] as List<dynamic>?)
      ?.map(
          (e) => e)
      .toList();
  if (departments != null) {
    yunxinUserinfoBeanDataUserInfos.departments = departments;
  }
  return yunxinUserinfoBeanDataUserInfos;
}

Map<String, dynamic> $YunxinUserinfoBeanDataUserInfosToJson(
    YunxinUserinfoBeanDataUserInfos entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['name'] = entity.name;
  data['userUuid'] = entity.userUuid;
  data['privateMeetingNum'] = entity.privateMeetingNum;
  data['state'] = entity.state;
  data['userToken'] = entity.userToken;
  data['departments'] = entity.departments;
  return data;
}

extension YunxinUserinfoBeanDataUserInfosExtension on YunxinUserinfoBeanDataUserInfos {
  YunxinUserinfoBeanDataUserInfos copyWith({
    String? name,
    String? userUuid,
    String? privateMeetingNum,
    int? state,
    String? userToken,
    List<dynamic>? departments,
  }) {
    return YunxinUserinfoBeanDataUserInfos()
      ..name = name ?? this.name
      ..userUuid = userUuid ?? this.userUuid
      ..privateMeetingNum = privateMeetingNum ?? this.privateMeetingNum
      ..state = state ?? this.state
      ..userToken = userToken ?? this.userToken
      ..departments = departments ?? this.departments;
  }
}