package com.dxznjy.alading.activity;

import androidx.appcompat.app.AppCompatActivity;

import android.net.Uri;
import android.os.Bundle;
import android.util.Log;

import com.blankj.rxbus.RxBus;
import com.dxznjy.alading.R;
import com.dxznjy.alading.event.PayCallBackEvent;

import io.reactivex.disposables.Disposable;

public class AliPayTempActivity extends AppCompatActivity {
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_ali_pay_temp);
        Uri uri = getIntent().getData();
        if (uri != null) {
            String query = uri.getQuery();
            PayCallBackEvent payCallBackEvent ;
            if(query != null && !query.isEmpty()){
                if(query.contains("取消")){
                    payCallBackEvent = new PayCallBackEvent("cancel");
                }else{
                    payCallBackEvent = new PayCallBackEvent("");
                }
                RxBus.getDefault().post(payCallBackEvent);
                finish();
            }
        }
    }



}