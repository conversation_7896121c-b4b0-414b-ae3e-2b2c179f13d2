class HttpConfig {
  static const int REQUEST_FIRST = 1;
  static const int REQUEST_SECOND = 2;
  static const int REQUEST_THIRD = 3;
  static const int REQUEST_FOURTH = 4;
  static const int REQUEST_FIFTH = 5;
  static const int REQUEST_SIXTH = 6;

  // static String formalURL = "http://192.168.40.17:8081";
  // static String formalURL = "http://192.168.40.114:8081";

  // static String formalURL = "https://test2-k8s.ngrok.dxznjy.com";
  // static String formalURL = "https://dxcs157.ngrok.dxznjy.com";
  // static String formalURL = "https://cdw.ngrok.dxznjy.com";
  // static String formalURL = "https://gateway.dxznjy.com";
  // static String formalURL = 'http://cqr.ngrok.dxznjy.com';
  // static String formalURL = "https://xj.ngrok.dxznjy.com";
  static String formalURL = "https://test179.ngrok.dxznjy.com";
  // static String formalURL = "https://linetest.dxznjy.com";
  // static String formalURL = "http://zxmeet01.ngrok.dxznjy.com";
  // static String formalURL = "https://gcj.ngrok.dxznjy.com";
  //  static String formalURL = "https://testpay.ngrok.dxznjy.com";
  // static String formalURL = "https://test221.ngrok.dxznjy.com";
  // static String formalURL = "https://gateway.dxznjy.com";
  // static String formalURL = "https://dxcs178.ngrok.dxznjy.com";
  // static String formalURL = "https://189test.ngrok.dxznjy.com";


  static String URL = formalURL;

  static String addChatMessage = "$URL/im/api/chat/add-message"; //聊天消息保存

  static String deleteChatMessage = "$URL/im/api/chat/delete-message"; //单聊/群聊消息删除

  static String leaveGroup = "$URL/im/api/group/leave-group"; //退出群聊

  static String addGroupMessage = "$URL/im/group/save-group"; //群聊信息保存

  static String desensitization = "$URL/im/api/chat/desensitization"; //消息脱敏

  static String disbandGroup = "$URL/im/api/group/update-group"; //解散群聊

  static String chatUserInfo = "$URL/im/api/user/get-user"; //获取用户信息

  static String chatUserInfoNew = "$URL/im/api/user/getUserNew"; //获取用户信息 - 新版接口，针对IM角色查缺补漏建账号，返默认角色

  static String getRole = "$URL/im/api/role/get-role"; //获取用户角色

  static String addGroupMember = "$URL/im/api/group/add-group-member"; // 拉人进群

  static String deleteGroupMember = "$URL/im/api/group/delete-group-member"; //踢人出群

  static String getGroupQrcode = "$URL/im/api/group/get-group-qrcode"; // 获取群聊二维码

  static String confirmCardMessage = "$URL/im/api/chat/save-card-user"; // 群消息卡片确认人保存

  static String updateGroupMessage = "$URL/im/api/chat/update-group-message"; // 修改历史群聊消息

  static String getGroupInfo = "$URL/im/api/group/get-group-data"; // 根据群id获取群详情

  static String complaintMessage = "$URL/im/complaint/information"; // 消息投诉

  static String queryQuickMessage = "$URL/im/api/chat/query-quick-message"; // 查询快捷消息

  static String sendQuickMessage = "$URL/im/api/chat/send-quick-message"; // 发送快捷消息

  static String addBlackUser = "$URL/im/api/friend/block-friend"; // 好友拉黑

  static String queryChatRoleList = "$URL/im/api/friend_role/get-chat-role"; // 获取当前角色可聊天角色列表

  static String saveMessageBody = "$URL/im/api/chat/save-message-body"; // 保存群聊消息体

  static String checkStudentInfo = "$URL/deliver/app/im/checkStudentInfo"; // 进群前确认学员信息

  static String setGroupNotice = "$URL/im/api/group/group-bulletin"; // 设置群公告

  static String queryVirtualNumber = "$URL/im/api/user/get-virtual-number"; // 获取虚拟号

  static String modifyGroupName = "$URL/im/api/group/change-group-name"; // 修改群聊名称

  static String timLoginByTencId = "$URL/im/api/user/getSignByTencentId"; // 根据 tencentId 登录 TIM -- 修复修改头像会话列表消失的问题

  static String isMeetingCanEnter = "$URL/media/v2/meeting/isCanEnterMeeting"; // 查询会议是否为可进状态
}
