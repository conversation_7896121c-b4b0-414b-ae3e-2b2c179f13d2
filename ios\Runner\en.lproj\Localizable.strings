/* 
  Localizable.strings
  Runner

  Created by 许江泉 on 2025/2/8.
  
*/
CFBundleDisplayName = "DingXiaozhenxuan";
LoginPassword = "loginPassword";
PhoneNumber = "PhoneNumber";
Login = "login";
VerificationCode = "VerificationCode";
VerificatCode = "verificatCode";
PleaseEnterPhoneNumber = "pleaseEnterPhoneNumber";
PleaseEnterLoginPassword = "pleaseEnterLoginPassword";
PleaseEnterVerificationCode = "pleaseEnterVerificationCode";
Sent = "Sent";
PleaseFilmobilePhonenumber = "pleaseFilmobilePhonenumber";
MobilePhoneNumberEnteredWrongPleasereenter = "mobilePhoneNumberEnteredWrongPleasereenter";
PasswordisemptyPleaseenter = "passwordisemptyPleaseenter";
PleaseObtaintheverificationCode = "pleaseObtaintheverificationCode";
VerificationCodeCannotbeempty = "verificationCodeCannotbeempty";
Cellphonenumberdoesntexist = "cellphonenumberdoesntexist";
ClickRefresh = "clickRefresh";
StartClassNow = "startClassNow";
Viewfeedback = "Viewfeedback";
Logout = "logout";
Opensoonpleasewait = "opensoonpleasewait";
School = "school";
Confirm = "Confirm";
ConfirmExitLogin = "Confirm exit Login?";
Cancel = "Cancel";
VolumeToast = "VolumeToast";
UserPasswordLogin = "userPasswordLogin";
PhoneNumberVerificatCodeLogin = "PhoneNumberVerificatCodeLogin";
