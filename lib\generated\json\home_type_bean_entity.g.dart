import 'package:student_end_flutter/generated/json/base/json_convert_content.dart';
import 'package:student_end_flutter/bean/home_type_bean_entity.dart';

HomeTypeBeanEntity $HomeTypeBeanEntityFromJson(Map<String, dynamic> json) {
  final HomeTypeBeanEntity homeTypeBeanEntity = HomeTypeBeanEntity();
  final int? code = jsonConvert.convert<int>(json['code']);
  if (code != null) {
    homeTypeBeanEntity.code = code;
  }
  final bool? success = jsonConvert.convert<bool>(json['success']);
  if (success != null) {
    homeTypeBeanEntity.success = success;
  }
  final String? message = jsonConvert.convert<String>(json['message']);
  if (message != null) {
    homeTypeBeanEntity.message = message;
  }
  final List<HomeTypeBeanData>? data = (json['data'] as List<dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<HomeTypeBeanData>(e) as HomeTypeBeanData)
      .toList();
  if (data != null) {
    homeTypeBeanEntity.data = data;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    homeTypeBeanEntity.total = total;
  }
  return homeTypeBeanEntity;
}

Map<String, dynamic> $HomeTypeBeanEntityToJson(HomeTypeBeanEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['code'] = entity.code;
  data['success'] = entity.success;
  data['message'] = entity.message;
  data['data'] = entity.data.map((v) => v.toJson()).toList();
  data['total'] = entity.total;
  return data;
}

extension HomeTypeBeanEntityExtension on HomeTypeBeanEntity {
  HomeTypeBeanEntity copyWith({
    int? code,
    bool? success,
    String? message,
    List<HomeTypeBeanData>? data,
    int? total,
  }) {
    return HomeTypeBeanEntity()
      ..code = code ?? this.code
      ..success = success ?? this.success
      ..message = message ?? this.message
      ..data = data ?? this.data
      ..total = total ?? this.total;
  }
}

HomeTypeBeanData $HomeTypeBeanDataFromJson(Map<String, dynamic> json) {
  final HomeTypeBeanData homeTypeBeanData = HomeTypeBeanData();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    homeTypeBeanData.id = id;
  }
  final String? categoryName = jsonConvert.convert<String>(
      json['categoryName']);
  if (categoryName != null) {
    homeTypeBeanData.categoryName = categoryName;
  }
  final int? weight = jsonConvert.convert<int>(json['weight']);
  if (weight != null) {
    homeTypeBeanData.weight = weight;
  }
  final int? goodsQuantity = jsonConvert.convert<int>(json['goodsQuantity']);
  if (goodsQuantity != null) {
    homeTypeBeanData.goodsQuantity = goodsQuantity;
  }
  return homeTypeBeanData;
}

Map<String, dynamic> $HomeTypeBeanDataToJson(HomeTypeBeanData entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['categoryName'] = entity.categoryName;
  data['weight'] = entity.weight;
  data['goodsQuantity'] = entity.goodsQuantity;
  return data;
}

extension HomeTypeBeanDataExtension on HomeTypeBeanData {
  HomeTypeBeanData copyWith({
    String? id,
    String? categoryName,
    int? weight,
    int? goodsQuantity,
  }) {
    return HomeTypeBeanData()
      ..id = id ?? this.id
      ..categoryName = categoryName ?? this.categoryName
      ..weight = weight ?? this.weight
      ..goodsQuantity = goodsQuantity ?? this.goodsQuantity;
  }
}