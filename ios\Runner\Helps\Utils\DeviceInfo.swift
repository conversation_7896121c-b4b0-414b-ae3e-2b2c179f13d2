//
//  DeviceInfo.swift
//  StuMetApp
//
//  Created by 许江泉 on 2025/1/22.
//

import UIKit

// MARK: - 设备数据
class DeviceInfo: NSObject {
    // App 显示名称
    static let appDisplayName = Bundle.main.infoDictionary!["CFBundleDisplayName"] ?? ""

    // 项目/app 的名字
    static let appName = Bundle.main.infoDictionary![kCFBundleNameKey as String] ?? ""

    // App BuildNumber , 获取app的 Build ID
    static let build = Bundle.main.object(forInfoDictionaryKey: kCFBundleVersionKey as String) ?? ""

    // Bundle Identifier ,App BundleID,获取app的 Bundle Identifier
    static let bundleIdentifier: String = Bundle.main.bundleIdentifier ?? ""

    // 设备udid
    static let identifierNumber = UIDevice.current.identifierForVendor?.uuidString ?? ""

    // App版本号
    static let appVersion: String = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? ""

    // iOS版本
    static let iOSVersion: String = UIDevice.current.systemVersion

    // App Language en
    static let aPPLanguage = NSLocale.preferredLanguages[0]

    // MARK: - Device Info

    /// app版本号
    public static var versionS: String {
        return Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? ""
    }

    // bundle id
    public static let identifier = Bundle.main.bundleIdentifier

    /// 设备名称
    public static var deviceName: String {
        return UIDevice.current.localizedModel
    }

    /// 当前系统版本
    public static var systemVersion: String {
        return UIDevice.current.systemVersion
    }

    /// APP汇总信息
    public static func deviceInfo() -> [String: String] {
        return [
            "OSVersion": self.appVersion,
//            "OSName": osName(),
            "OSIdentifier": self.identifierNumber,
            "APPName": self.getAppName(),
            "AppVersion": self.appVersion,
        ]
    }


    /// 获取APP名称
    public static func getAppName() -> String {
        if let name = Bundle.main.localizedInfoDictionary?["CFBundleDisplayName"] as? String {
            return name
        }

        if let name = Bundle.main.infoDictionary?["CFBundleDisplayName"] as? String {
            return name
        }

        if let name = Bundle.main.infoDictionary?["CFBundleName"] as? String {
            return name
        }
        return "App"
    }
    
    public static func isIPhone16Pro() -> Bool {
        let device = UIDevice.current
        let screenSize = UIScreen.main.bounds.size
        // iPhone 16 Pro 屏幕尺寸为 390x844pt（@3x 渲染）
        return device.userInterfaceIdiom == .phone &&
               abs(screenSize.width - 390) < 1 &&
               abs(screenSize.height - 844) < 1
    }

    // MARK: - UI

    public static var isSupportSafeArea: Bool {
        if #available(iOS 11, *) {
            return true
        }
        return false
    }
}
