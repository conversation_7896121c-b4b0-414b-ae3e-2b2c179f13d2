// base_state.dart
import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'smart_empty.dart';
import '../utils/network_utils.dart';

abstract class BaseState<T extends StatefulWidget> extends State<T> {
  ViewState _currentState = ViewState.loading;

  /// 执行安全网络请求
  Future<void> safeRequest(
      Future<dynamic> Function() request, {
        bool checkEmpty = true,
      }) async {
    setState(() => _currentState = ViewState.loading);

    try {
      // 前置网络检查
      if (!await NetworkUtils.isConnected) {
        return _updateState(ViewState.noNetwork);
      }
      final data = await request();

      // 空数据检测
      if (checkEmpty && _isEmpty(data)) {
        _updateState(ViewState.emptyData);
      } else {
        _updateState(ViewState.success);
      }

      return data;
    } catch (e) {
      _handleError(e);
    }
  }

  /// 错误处理
  void _handleError(dynamic error) {
    if (error is SocketException) {
      _updateState(ViewState.noNetwork);
    } else {
      _updateState(ViewState.requestError);
    }
  }

  /// 空数据检测逻辑
  bool _isEmpty(dynamic data) {
    if (data is List) return data.isEmpty;
    if (data is Map) return data.isEmpty;
    if (data is String) return data.isEmpty;
    return data == null;
  }

  void _updateState(ViewState newState) {
    if (mounted) setState(() => _currentState = newState);
  }

  /// 构建页面内容
  @override
  Widget build(BuildContext context) {
    if (_currentState == ViewState.success) {
      return buildContent();
    }

    return SmartEmptyView(
      state: _currentState,
      onRetry: () => safeRequest(fetchData),
      onOpenSettings: NetworkUtils.openSettings,
    );
  }

  /// 抽象方法：子类必须实现
  Widget buildContent();
  Future<dynamic> fetchData();
}