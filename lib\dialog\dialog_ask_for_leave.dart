import 'package:bot_toast/bot_toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:student_end_flutter/bean/course_list_bean_entity.dart';
import 'package:student_end_flutter/bean/leave_apply_info_entity.dart';
import 'package:student_end_flutter/bean/make_up_class_times_entity.dart';
import 'package:student_end_flutter/common/CommonUtil.dart';
import 'package:student_end_flutter/components/spaced_column.dart';
import 'package:student_end_flutter/components/custom_dropdown.dart';
import 'package:get/get.dart';
import 'package:student_end_flutter/utils/event_bus_utils.dart';
import '../common/Config.dart';
import '../common/user_option.dart';
import '../components/toast_utils.dart';
import '../utils/httpRequest.dart';
import 'dialog_select_date.dart';

/// 控制按钮类型
enum ControlBtnType {
  confirm,
  cancel
}

/// 请假弹窗
class AskForLeaveDialog extends StatefulWidget {
  /// 弹出请假弹框
  /// [courseData] - 课程数据
  /// [leaveApplyInfo] - 请假回显数据
  /// [makeupClassTimes] - 请假可供补课的事件列表
  /// [type] - 弹窗类型，1 为请假，2 为设置补课时间，
  /// [testClassLeave] - 是否是试课请假, 默认为 false
  /// [onConfirm] - 确认回调，可选
  /// [onCancel] - 取消回调，可选
  const AskForLeaveDialog({
    super.key,
    required this.courseData,
    this.type = 1,
    required this.leaveApplyInfo,
    required this.makeupClassTimes,
    this.testClassLeave = false,
    this.onCancel,
    this.onConfirm
  });

  final int type; // 类型，1 为请假，2 为设置补课时间

  final CourseListBeanData courseData; // 课程数据

  final LeaveApplyInfoEntity leaveApplyInfo; // 请假回显数据

  final List<MakeUpClassTimesEntity> makeupClassTimes; // 请假可供补课的事件列表

  final bool? testClassLeave; // 是否是试课请假

  final VoidCallback? onConfirm; //  确认回调

  final VoidCallback? onCancel; // 取消回调

  @override
  State<AskForLeaveDialog> createState() => _AskForLeaveDialogState();
}

class _AskForLeaveDialogState extends State<AskForLeaveDialog> {
  /// 期望补课时间列表
  late List<String> _makeUpClassTime;
  
  /// 选中的补课时间
  String? _selectedMakeUpTime;

  @override
  void initState() {
    super.initState();
    // 初始化补课时间列表和映射
    final weekdayMap = ['', '周一', '周二', '周三', '周四', '周五', '周六', '周日'];
    
    _makeUpClassTime = [];
    
    for (var elem in widget.makeupClassTimes) {
      final weekDayStr = elem.week.toString().split('').map((d) => '${weekdayMap[int.parse(d)]}').join('、');
      final timeStr = '${elem.result?[0].startTime}-${elem.result?[0].endTime}';
      final displayText = '$weekDayStr$timeStr'; // 展示的补课时间
      
      _makeUpClassTime.add(displayText);
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    return Align(
      alignment: Alignment.center,
      child: SizedBox(
        width: screenSize.width * 0.85,
        // height: screenSize.height * 0.65,
        child: ConstrainedBox(
          constraints: BoxConstraints(
            maxHeight: screenSize.height * 0.8
          ),
          child: SingleChildScrollView(
            child: Container(
              padding: EdgeInsets.symmetric(vertical: 20.w, horizontal: 14.w),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12.w)
              ),
              child: SpacedColumn(
                spacing: 14.h,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 标题
                  _buildTitle(),
                  // 请假表单
                  ..._buildForm(),
                  SizedBox(height: 10.h,),
                  /// 底部控制按钮
                  _buildControlBtnGroup()
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // 构建弹框标题
  Widget _buildTitle() {
    return Center(
      child: Text(
        widget.testClassLeave == true
            ? '试课请假'
            : widget.type == 1
            ? '请假' : '期望补课时间',
        style: TextStyle(
            fontFamily: 'R',
            decoration: TextDecoration.none,
            fontSize: 18.sp,
            fontWeight: FontWeight.normal,
            color: HexColor('333333')
        ),
      ),
    );
  }

  /// 构建表单
  List<Widget> _buildForm() {
    return [
      _buildFormText('课程：${widget.courseData.courseName}'),
      _buildFormText('班级名称：${widget.leaveApplyInfo.className ?? ""}'),
      _buildFormText('上课时间：'),
      _buildFormText(widget.leaveApplyInfo.courseTime ?? widget.courseData.courseTime),
      _buildFormText('学员：${widget.courseData.studentName}'),
      _buildFormText('该学员该课程剩余次数：${widget.leaveApplyInfo.surplusLeaveNum ?? 0}次'),
      // 试课请假不设置补课时间
      if(widget.testClassLeave == false)
        ...[
          _buildFormText('期望补课时间'),
          _buildSelectMakeUpTime(),
        ],
      // 试课提示家长请假后课程将会被删除
      if(widget.testClassLeave == true)
        _buildDeleteClassTip(),
    ];
  }

  /// 构建表单文本组件
  Widget _buildFormText(String text) {
    return Text(
      text,
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
      style: TextStyle(
        fontFamily: 'R',
        decoration: TextDecoration.none,
        fontSize: 15.sp,
        fontWeight: FontWeight.normal,
        color: HexColor('555555')
      ),
    );
  }

  /// 构建选择补课时间组件
  Widget _buildSelectMakeUpTime() {
    return Material(
      child: CustomDropdown<String>(
        hint: _makeUpClassTime.isEmpty ? '课程未设置补课时间' : '请选择期望补课时间',
        items: _makeUpClassTime,
        maxVisibleItems: 3,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8.w),
          border: Border.all(
            width: 1.w,
            color: HexColor('eeeeee')
          ),
        ),
        dropdownIcon: Image.asset(
          width: 12.w,
          height: 12.w,
          fit: BoxFit.fill,
          'assets/learn/ic_drop_black.png',
        ),
        itemBuilder: (item) {
          return Text(
            item,
            style: TextStyle(
              fontFamily: 'R',
              decoration: TextDecoration.none,
              fontSize: 15.sp,
              fontWeight: FontWeight.normal,
              color: HexColor('555555')
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          );
        },
        onChanged: (selectedItem) {
          if (selectedItem != null) {
            setState(() {
              _selectedMakeUpTime = selectedItem;
            });
          }
        },
      ),
    );
  }

  Widget _buildDeleteClassTip() {
    return Row(
      children: [
        Image.asset(
          'assets/learn/icon_warn.png',
          width: 25.w,
          height: 25.w,
        ),
        SizedBox(width: 4.w,),
        Expanded(
          child: Text(
            '试课请假后将退出班级重新成班，当前课程将被删除！',
            style: TextStyle(
              fontFamily: 'R',
              decoration: TextDecoration.none,
              fontSize: 15.sp,
              fontWeight: FontWeight.normal,
              color: HexColor('8A8A8A')
            ),
          ),
        ),
      ],
    );
  }

  /// 构建底部控制按钮组
  _buildControlBtnGroup() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildControlBtn(ControlBtnType.cancel),
        _buildVerticalDivider(),
        _buildControlBtn(ControlBtnType.confirm),
      ],
    );
  }

  /// 构建底部控制按钮
  Widget _buildControlBtn(ControlBtnType btnType) {
    switch(btnType) {
      case ControlBtnType.cancel:
        return GestureDetector(
          onTap: _onCancel,
          child: Text(
              '取消',
              style: TextStyle(
                  fontFamily: 'R',
                  decoration: TextDecoration.none,
                  fontSize: 16.sp,
                  fontWeight: FontWeight.normal,
                  color: HexColor('999999')
              )
          ),
        );
      case ControlBtnType.confirm:
        return GestureDetector(
          onTap: _onConfirm,
          child: Text(
              '确认',
              style: TextStyle(
                  fontFamily: 'R',
                  decoration: TextDecoration.none,
                  fontSize: 16.sp,
                  fontWeight: FontWeight.normal,
                  color: HexColor('3B9578')
              )
          ),
        );
    }
  }


  /// 构建底部垂直分隔符
  Widget _buildVerticalDivider() {
    return Text(
        '|',
        style: TextStyle(
            fontFamily: 'R',
            decoration: TextDecoration.none,
            fontSize: 16.sp,
            fontWeight: FontWeight.normal,
            color: HexColor('999999')
        )
    );
  }

  _onConfirm() async {
    // 当前是请假弹窗并且请假时间已用完
    if(widget.leaveApplyInfo.surplusLeaveNum == 0
      && widget.type == 1
    ) {
      BotToast.showText(text: '该学员该课程请假次数已用完');
      return Navigator.pop(context);
    }

    // 请假时间未用完或当前是补课时间设置弹窗
    ToastUtil.showLoadingDialog();
    try {
      // 根据类型执行请假或保存补课时间
      if (widget.type == 1) {
        // ====== 请假 ======
        Map<String, dynamic> params = {
          'courseId': widget.courseData.planStudyId,
          'phone': UserOption.mobile,
          'grade': widget.leaveApplyInfo.grade
        };
        // 补课时间
        if (_selectedMakeUpTime != null) {
          // 添加选中的补课时间ID到请求参数
          params['makeUpClassTime'] = _selectedMakeUpTime;
        }
        
        var response = await HttpUtil(url: Config.sendLeaveApply, sendLoginToken: true)
            .post(Config.sendLeaveApply, data: params);
        if (response != null && response.data['success'] == true) {
          BotToast.showText(text: '请假申请提交成功');
          // 有回调执行回调
          if(widget.onConfirm != null) {
            widget.onConfirm!();
          }
          // 通知学习页面刷新最近一节交付课
          EventBusUtils.sendMsg('refreshLastClass');
          Navigator.pop(context);
        } else {
          String errorMsg = response != null && response.data['message'] != null
              ? response.data['message']
              : '请假申请提交失败';
          BotToast.showText(text: errorMsg);
        }
      } else {
        // ====== 保存补课时间 ======
        if (_selectedMakeUpTime == null) {
          BotToast.showText(text: '请选择补课时间');
          return;
        }
        Map<String, dynamic> params = {
          'id': widget.leaveApplyInfo.studentLeaveId, // 请假记录 id
          'makeUpClassTime': _selectedMakeUpTime, // 补课时间ID
        };
        
        var response = await HttpUtil(url: Config.saveMakeupClassTime)
            .post(Config.saveMakeupClassTime, data: params);
        if (response != null && response.data['success'] == true) {
          BotToast.showText(text: '补课时间保存成功');
          // 有回调执行回调
          if(widget.onConfirm != null) {
            widget.onConfirm!();
          }
          // 通知学习页面刷新最近一节交付课
          EventBusUtils.sendMsg('refreshLastClass');
          Navigator.pop(context);
        } else {
          String errorMsg = response != null && response.data['message'] != null
              ? response.data['message']
              : '补课时间保存失败';
          BotToast.showText(text: errorMsg);
        }
      }
    } catch (e) {
      debugPrint('提交请假/补课时间出错: $e');
      BotToast.showText(text: '操作失败，请稍后重试');
    } finally {
      ToastUtil.closeLoadingDialog();
    }
  }

  _onCancel() {
    if(widget.onCancel != null) {
      widget.onCancel!();
    }
    Navigator.pop(context);
  }
}
