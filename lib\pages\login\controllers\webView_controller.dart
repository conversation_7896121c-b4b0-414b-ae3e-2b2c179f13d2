import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:webview_flutter/webview_flutter.dart';

class WebViewXController extends GetxController {
  late WebViewController webViewController;
  String titleName = "";

  @override
  void onInit() {
    super.onInit();
    titleName = Get.arguments["titleName"] ?? "";
    String url = Get.arguments["url"] ?? "";
    webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..loadRequest(Uri.parse(url));
  }
}
