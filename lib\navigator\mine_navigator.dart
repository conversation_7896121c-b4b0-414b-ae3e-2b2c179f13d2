import 'package:flutter/cupertino.dart';
import '../common/user_option.dart';
import 'mini_program_path.dart';
import 'path_generator.dart';
class MineNavigator {
  const MineNavigator._();
  /* **************** 具体业务路径方法 **************** */

  /// 个人信息中心
  /// 示例：Personalcenter/home/<USER>
  static MiniProgramPath mineInfo() {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
        pathComponents: ['Personalcenter', 'home', 'info'],
        queryParams: {}
    );
    return MiniProgramPath(path);
  }

  /// 积分中心
  /// 示例：Personal center/my/myIntegral?
  static MiniProgramPath mypoints() {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
        pathComponents: ['Personalcenter', 'my', 'myIntegral'],
        queryParams: {
          'userId': UserOption.userId,
        }
    );
    return MiniProgramPath(path);
  }
  // incomeDetails/index?type=0

  /// （立即开通）
  /// 示例：Personal center/my/parentVipEquity?
  static MiniProgramPath parentVipEquity() {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
        pathComponents: ['Personalcenter', 'my', 'parentVipEquity'],
        isNeedParent: false,
        isNeedUserId:false,
        queryParams: {},
    );

    return MiniProgramPath(path);
  }

  /// 鼎币商城
  /// 示例：Personal shoppingMall/index?
  static MiniProgramPath shoppingMall() {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
      pathComponents: ['shoppingMall', 'index'],
      queryParams: {},
    );
    return MiniProgramPath(path);
  }

  /// 会员续费
  /// 示例：Personal center/my/parentVipEquity?
  static MiniProgramPath renewVipEquity() {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
        pathComponents: ['Personalcenter', 'my', 'parentVipEquity'],
        isNeedUserId: false,
        queryParams: {
          'expire': 1,
        },
    );
    return MiniProgramPath(path);
  }

  /// 我的收藏
  /// 示例：Persona lcenter/my/myCollection?
  static MiniProgramPath myCollection() {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
        pathComponents: ['Personalcenter', 'my', 'myCollection'],
        queryParams: {
          'pageNum': 1,  // 固定参数
          'pageSize': 15,
          'userId': UserOption.userId,
        }
    );
    return MiniProgramPath(path);
  }

  /// 我的订单
  /// 示例：splitContent/order/order?pageNum=1&pageSize=20&orderGroup
  static MiniProgramPath myOrder() {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
        pathComponents: ['splitContent', 'order', 'order'],
        queryParams: {
          'orderGroup': '2,3',
          'pageNum': 1,  // 固定参数
          'pageSize': 20,
          'checkHistory': '0',
        }
    );
    return MiniProgramPath(path);
  }

  /// 信息中心
  /// 示例：supermanClub/supermanSign/news
  static MiniProgramPath informationCenter() {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
        pathComponents: ['splitContent', 'message', 'message'],
        queryParams: {}
    );
    return MiniProgramPath(path);
  }

  /// 收货地址
  /// 示例：splitContent/address/list/list
  static MiniProgramPath shippingAddress() {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
        pathComponents: ['splitContent', 'address', 'list','list'],
        queryParams: {}
    );
    return MiniProgramPath(path);
  }

  /// 优惠卷
  /// 示例：coupons/CouponsList
  static MiniProgramPath coupons() {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
        pathComponents: ['coupons', 'CouponsList'],
        queryParams: {
          'pageNum': 1,  // 固定参数
          'pageSize': 20,
          'userId': UserOption.userId,
          'useStatus': 1,
        }
    );
    return MiniProgramPath(path);
  }

  /// 会员分享
  /// 示例：splitContent/poster/index
  static MiniProgramPath splitContent({
    @required required String type,
    @required required String courseId,
  }) {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
        pathComponents: ['splitContent', 'poster','index'],
        queryParams: {
          'type': type,
          'courseId': courseId,
        }
    );
    return MiniProgramPath(path);
  }

  /// 收益明细
  /// 示例：incomeDetails/index
  static MiniProgramPath incomeDetails({int type = 1}) {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
        pathComponents: ['incomeDetails', 'index'],
        queryParams: {
          "type":type,
          'userId': UserOption.userId,
          'pageNum': 1,  // 固定参数
          'pageSize': 20,
        }
    );
    return MiniProgramPath(path);
  }

  /// 试课推荐
  /// 示例：Trial class/recom mendname=&mobile=&status=&page=1
  static MiniProgramPath recommend() {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
        pathComponents: ['Trialclass', 'recommend'],
        queryParams: {
          'page': 1
        }
    );
    return MiniProgramPath(path);
  }

  /// 充值时长
  /// 示例：Recharge/index
  static MiniProgramPath recharge({
    @required required String schoolType,
    @required required String merchantCode,
    @required required String paytoken,
  })  {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
        pathComponents: ['Recharge', 'index'],
        queryParams: {
          'schoolType': schoolType,
          'merchantCode': merchantCode,
          'paytoken': paytoken,
        }
    );
    return MiniProgramPath(path);
  }

  /// 上课信息对接表
  /// 示例：  Recharge/onlineJoinTable/onlineJoinTable
  static MiniProgramPath onlineJoinTable() {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
        pathComponents: ['Recharge', 'onlineJoinTable', 'onlineJoinTable'],
        queryParams: {
          'pageNum': 1,  // 固定参数
          'pageSize': 20,
        }
    );
    return MiniProgramPath(path);
  }

  /// 意见反馈
  /// 示例：  Personal center/suggest/index
  static MiniProgramPath suggest() {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
        pathComponents: ['Personalcenter', 'suggest', 'index'],
        queryParams: {}
    );
    return MiniProgramPath(path);
  }

   ///  邀请有礼
  /// 示例：InvitationGifts/index activityId=2，goodsTypeListStr=6&orderBy=
  static MiniProgramPath invitationGifts({
    @required required String activityId,
  }) {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
        pathComponents: ['InvitationGifts', 'index'],
        queryParams: {
          'goodsTypeListStr': 6,
          'orderBy': '',
          'activityid': activityId,
          'orderType': '',  // 固定参数
          'userId':  UserOption.userId,
          'pageNum': 1,  // 固定参数
          'pageSize': 20,
        }
    );
    return MiniProgramPath(path);
  }

  /// 合同管理
  static MiniProgramPath contract() {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
        pathComponents: ['signature', 'contract', 'cManagement'],
        queryParams: {}
    );
    return MiniProgramPath(path);
  }

  /// 营销小组
  static MiniProgramPath marketingGroup() {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
        pathComponents: ['middlePage', 'callPhone'],
        queryParams: {}
    );
    return MiniProgramPath(path);
  }
  /// 了解更多
  static MiniProgramPath aboutPage() {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
        pathComponents: ['pages', 'aboutPage', 'aboutPage'],
        queryParams: {}
    );
    return MiniProgramPath(path);
  }
  /// 我的拼团
  static MiniProgramPath myGroupPurchase() {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
        pathComponents: ['activity', 'groupBuying', 'myGroupBuying'],
        queryParams: {}
    );
    return MiniProgramPath(path);
  }

  /// 推荐会员
  static MiniProgramPath recommendVIP() {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
        pathComponents: ['signature', 'contract', 'cManagement'],
        queryParams: {}
    );
    return MiniProgramPath(path);
  }

  /// 查看权益 Coursedetails/my/parentEquity
  static MiniProgramPath parentEquity() {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
        pathComponents: ['Coursedetails', 'my', 'parentEquity'],
        queryParams: {
          'goodsTypeListStr': 6,
          'pageNum': 1,
          'pageSize': 2,
          'parentMobile': UserOption.userInfoBeanEntity?.mobile,
          'goodsIdsOrName': '清北学霸',
          'userId': UserOption.userId,
        }
    );
    return MiniProgramPath(path);
  }


  /// 会员消息 Personalcenter/Career/news
  static MiniProgramPath memberMessage() {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
        pathComponents: ['Personalcenter', 'Career', 'news'],
        queryParams: {
          'phone': UserOption.userInfoBeanEntity?.mobile,
        }
    );
    return MiniProgramPath(path);
  }

  /// 推荐会员（会员分享） Personalcenter/my/parentVipEquity
  static MiniProgramPath shareVIP() {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
        pathComponents: ['Personalcenter', 'my', 'parentVipEquity'],
        isAddUserToken: false,
        queryParams: {
          'scene': UserOption.userId,
        }
    );
    return MiniProgramPath(path);
  }

  /// **混合参数类型支持**：实例
  /// 支持复杂参数类型的订单页面
  static MiniProgramPath orderPage({
    @required required String orderId,
    DateTime? timestamp,
    List<String>? tags,
  }) {
    final params = <String, dynamic>{
      'orderId': orderId,
      if (timestamp != null) 'ts': timestamp.millisecondsSinceEpoch,
      if (tags != null && tags.isNotEmpty) 'tags': tags.join(','),
    };

    final path =  MiniProgramPathBuilder.buildMiniProgramPath(
      pathComponents: ['orders', 'detail'],
      queryParams: params,
    );
    return MiniProgramPath(path);
  }

  /// 协议及政策
  static MiniProgramPath protocolList() {
    final path = MiniProgramPathBuilder.buildMiniProgramPath(
        pathComponents: ['signature', 'protocol', 'ProtocolList'],
        queryParams: {}
    );
    return MiniProgramPath(path);
  }
}