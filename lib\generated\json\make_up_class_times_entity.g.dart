import 'package:student_end_flutter/generated/json/base/json_convert_content.dart';
import 'package:student_end_flutter/bean/make_up_class_times_entity.dart';

MakeUpClassTimesEntity $MakeUpClassTimesEntityFromJson(
    Map<String, dynamic> json) {
  final MakeUpClassTimesEntity makeUpClassTimesEntity = MakeUpClassTimesEntity();
  final int? week = jsonConvert.convert<int>(json['week']);
  if (week != null) {
    makeUpClassTimesEntity.week = week;
  }
  final List<MakeUpClassTimesResult>? result = (json['result'] as List<
      dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<MakeUpClassTimesResult>(e) as MakeUpClassTimesResult)
      .toList();
  if (result != null) {
    makeUpClassTimesEntity.result = result;
  }
  return makeUpClassTimesEntity;
}

Map<String, dynamic> $MakeUpClassTimesEntityToJson(
    MakeUpClassTimesEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['week'] = entity.week;
  data['result'] = entity.result?.map((v) => v.toJson()).toList();
  return data;
}

extension MakeUpClassTimesEntityExtension on MakeUpClassTimesEntity {
  MakeUpClassTimesEntity copyWith({
    int? week,
    List<MakeUpClassTimesResult>? result,
  }) {
    return MakeUpClassTimesEntity()
      ..week = week ?? this.week
      ..result = result ?? this.result;
  }
}

MakeUpClassTimesResult $MakeUpClassTimesResultFromJson(
    Map<String, dynamic> json) {
  final MakeUpClassTimesResult makeUpClassTimesResult = MakeUpClassTimesResult();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    makeUpClassTimesResult.id = id;
  }
  final int? week = jsonConvert.convert<int>(json['week']);
  if (week != null) {
    makeUpClassTimesResult.week = week;
  }
  final String? startTime = jsonConvert.convert<String>(json['startTime']);
  if (startTime != null) {
    makeUpClassTimesResult.startTime = startTime;
  }
  final String? endTime = jsonConvert.convert<String>(json['endTime']);
  if (endTime != null) {
    makeUpClassTimesResult.endTime = endTime;
  }
  return makeUpClassTimesResult;
}

Map<String, dynamic> $MakeUpClassTimesResultToJson(
    MakeUpClassTimesResult entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['week'] = entity.week;
  data['startTime'] = entity.startTime;
  data['endTime'] = entity.endTime;
  return data;
}

extension MakeUpClassTimesResultExtension on MakeUpClassTimesResult {
  MakeUpClassTimesResult copyWith({
    String? id,
    int? week,
    String? startTime,
    String? endTime,
  }) {
    return MakeUpClassTimesResult()
      ..id = id ?? this.id
      ..week = week ?? this.week
      ..startTime = startTime ?? this.startTime
      ..endTime = endTime ?? this.endTime;
  }
}