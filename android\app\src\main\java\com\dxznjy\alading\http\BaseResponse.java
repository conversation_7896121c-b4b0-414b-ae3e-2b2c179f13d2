package com.dxznjy.alading.http;

import java.io.Serializable;

/**
 * Created by ho<PERSON><PERSON><PERSON> on 2016/12/2.
 */

public class BaseResponse implements Serializable {

    /**
     * status : 200
     * message : 操作成功
     */

    private int status;
    private String message;
    private int code;

    private Integer total;

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    /**
     * API是否请求失败
     *
     * @return 失败返回true, 成功返回false
     */
    public boolean isCodeInvalid() {
        String codeStr=code+"";
        if (codeStr.substring(0,3).equals("200")){
            return false;
        }else{
            return true;
        }

    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
