import 'dart:convert';
import 'dart:io';
import 'dart:async';
import 'package:bot_toast/bot_toast.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:student_end_flutter/common/user_option.dart';
import 'package:student_end_flutter/dialog/dialog_home_activity.dart';
import 'package:student_end_flutter/navigator/home_navigator.dart';
import 'package:student_end_flutter/utils/android_ios_plugin.dart';
import '../../../bean/banner_bean_entity.dart';
import '../../../bean/home_type_bean_entity.dart';
import '../../../bean/home_info_bean_entity.dart';
import '../../../bean/popup_model_entity.dart';
import '../../../bean/zx_course_entity.dart';
import '../../../common/Config.dart';
import '../../../common/pop_option.dart';
import '../../../components/toast_utils.dart';
import '../../../dialog/dialog_activity_pop.dart';
import '../../../dialog/dialog_add_teacher_code.dart';
import '../../../generated/json/base/json_convert_content.dart';
import '../../../navigator/mine_navigator.dart';
import '../../../utils/event_bus_utils.dart';
import '../../../utils/httpRequest.dart';
import '../../../utils/app_version_util.dart';
import '../../../utils/sharedPreferences_util.dart';
import '../../../dialog/dialog_sdk_description.dart';
import "../../zx/zx_page.dart";
import '../../../common/sensors_analytics_option.dart';

class HomeController extends GetxController
    with GetSingleTickerProviderStateMixin, WidgetsBindingObserver {
  final TextEditingController editingSearchController = TextEditingController();
  final TextEditingController editingTeacherCodeController =
      TextEditingController();
  String path = '';
  final PopupController controller = Get.put(PopupController('HomePage'));
  RxList<BannerBeanData> bannerList = RxList<BannerBeanData>([]);
  RxList<BannerBeanData> bannerPtList = RxList<BannerBeanData>([]);
  //金刚区
  Rx<HomePageData> homeInfo = HomePageData().obs;
  Rx<BannerItem> bannerInfo = BannerItem().obs;
  RxList<BannerItem> bannerNewList = RxList<BannerItem>([]);
  RxList<SpecialItem> specialList = RxList<SpecialItem>([]);
  RxList<HomePageGoodsItem> categoryGoodsList = RxList<HomePageGoodsItem>([]);
  // 在控制器中添加防抖相关变量

  // categoryGoodsList
  final PageController pageController = PageController();
  final FocusNode searchFocusNode = FocusNode();
  RxInt currentPage = 0.obs;
  int bannerIndex = 0;
  bool _isManualScrolling = false;
  RxString backgroundColor = ''.obs;
  var bannerTopList = [
    {'image': 'assets/home/<USER>', 'price': '1'},
    {'image': 'assets/home/<USER>', 'price': '1'},
    {'image': 'assets/home/<USER>', 'price': '1'},
    {'image': 'assets/home/<USER>', 'price': '1'},
  ];
  var learnMore = [
    {'english': 'Learning with a coach', 'title': '教练伴学', 'subject': '一对一'},
    {'english': 'Intelligent education', 'title': '智能教育', 'subject': 'AI+'},
    {'english': 'Score-improving training', 'title': '提分训练', 'subject': '专业'}
  ];
  var goodsTitle = [
    {
      'title': '体验课',
      'list': [
        {
          'image': '',
          'title': '鼎学能课价格',
          'price': '23.4',
          'oldPrice': '999',
          'number': '20000'
        }
      ]
    },
    {
      'title': '正式课',
      'list': [
        {
          'image': '',
          'title': '鼎学能课价格',
          'price': '23.4',
          'oldPrice': '999',
          'number': '20000'
        }
      ]
    },
    {
      'title': '1体验课',
      'list': [
        {
          'image': '',
          'title': '鼎学能课价格',
          'price': '23.4',
          'oldPrice': '999',
          'number': '20000'
        }
      ]
    }
  ];
  var buttonList = [
    [
      {"img": "assets/home/<USER>", "name": "甄选好课"},
      {"img": "assets/home/<USER>", "name": "文化中心"},
      {"img": "assets/home/<USER>", "name": "鼎币商城"},
      {"img": "assets/home/<USER>", "name": "家长会员"},
    ]
  ];
  // var buttonList = [
  //   [
  //     {"img": "assets/home/<USER>", "name": "测试-老师"},
  //     {"img": "assets/home/<USER>", "name": "测试-学生"},
  //     {"img": "assets/home/<USER>", "name": "文化中心"},
  //     {"img": "assets/home/<USER>", "name": "家长会员"},
  //   ]
  // ];

  bool canShowPop = true; //
  var goodItemPositions = [];
  var tabItemPositions = [];
  //课程
  RxList<HomeTypeBeanData> homeTypeList = RxList<HomeTypeBeanData>([]);
  RxInt pageNum = 1.obs;
  int pageSize = 20;
  RxList<ZxCourseDataData> dataList = RxList<ZxCourseDataData>([]);
  ScrollController scrollController = ScrollController();
  // ScrollController goodsScrollController = ScrollController();
  ScrollController scrollHorizontalController = ScrollController();
  int totalPages = 1;
  List<GlobalKey> keys = [];
  List<GlobalKey> goodItemKeys = [];
  final List<GlobalKey> tabKeys = [GlobalKey()];
  var currentTabIndex = 0.obs;
  RxBool showTab = true.obs;
  double bottomPosW = 30.w;
  Timer? _scrollDebounce;
  Duration _debounceDuration = const Duration(milliseconds: 100);
  int currentTempTabIndex = -1;

  String wgt_app_id = '';

  String wgt_url = '';

  String wgt_version = '';

  @override
  Future<void> onInit() async {
    showTab.value = true;
    scrollController.addListener(scrollListener);
    // goodsScrollController.addListener(scrollListener);
    scrollHorizontalController.addListener(scrollClassification);
    WidgetsBinding.instance.addObserver(this);
    super.onInit();
    pageController.addListener(() {
      // currentPage.value = pageController.page?.round() ?? 0; // 更新当前页数
    });
    EventBusUtils.eventBus.on<EventUtils>().listen((event) {
      if (event.message == "showPop") {
        if (PopOption.alreadyShowPopList.isNotEmpty &&
            PopOption.alreadyShowPopList.contains(PopOption.currentTabIndex)) {
          //如果已经展示过弹窗了，就不再展示
          return;
        }
        currentTempTabIndex = PopOption.currentTabIndex;
        fetchData(PopOption.currentTabIndex);
      }
    });
    getBannerList(1);
    getPorcelainInfo();
    getSpecialList();
    getHomeInfo();
    getTypeList();
    if (UserOption.iSLogin) {
      PopOption.currentTabIndex = 1;
      EventBusUtils.sendMsg("showPop");
    }

    // await checkWgtVersion();
  }

  Future<void> onRefresh() async {
    // showTab.value=true;
    await getBannerList(1);
    await getHomeInfo();
    await getPorcelainInfo();
    await getSpecialList();
    pageNum.value = 1;
    dataList.value = [];
    await getTypeList();
  }

  void scrollListener() {
    if (_isManualScrolling) return;
    // 取消之前的防抖计时
    // _scrollDebounce?.cancel();
    if (scrollController.offset > 50) {
      closeKeyboard();
    }
      if (scrollController.position.pixels > 565.h) {
        showTab.value = false;
      } else {
        showTab.value = true;
      }
      if (goodItemPositions.length > 0) {
        for (var i = 0; i < goodItemPositions.length; i++) {
          if (scrollController.position.pixels >
              (goodItemPositions[i].dy - 40.h)) {
            currentTabIndex.value = i;
            if (!showTab.value) {
              scrollRightSection(i);
            }
          }
        }
      }
    // });
  }

  void scrollClassification() {
    if (scrollHorizontalController.position.pixels ==
        scrollHorizontalController.position.maxScrollExtent) {}
  }

  // 修改 initTabController 方法
  initTabController() {
    int requiredLength = categoryGoodsList.length;
    if (keys.length < requiredLength) {
      keys.addAll(
          List.generate(requiredLength - keys.length, (index) => GlobalKey()));
    }

    // 确保在下一帧渲染完成后执行
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 添加一个小延迟确保渲染完全完成
      Future.delayed(const Duration(milliseconds: 50), () {
        getTabPositions();
      });
    });
  }

// 修改 initGoodController 方法
  initGoodController() {
    int requiredLength = categoryGoodsList.length;
    if (goodItemKeys.length < requiredLength) {
      goodItemKeys.addAll(List.generate(
          requiredLength - goodItemKeys.length, (index) => GlobalKey()));
    }

    // 确保在下一帧渲染完成后执行
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 添加一个小延迟确保渲染完全完成
      Future.delayed(const Duration(milliseconds: 50), () {
        getGoodItemPositions();
      });
    });
  }

// 修改 getTabPositions 方法
  void getTabPositions() {
    tabItemPositions.clear();
    bool allFound = true;

    for (int i = 0; i < keys.length; i++) {
      final key = keys[i];
      final context = key.currentContext;
      if (context != null) {
        final box = context.findRenderObject() as RenderBox?;
        if (box != null && box.hasSize) {
          final position = box.localToGlobal(Offset.zero);
          tabItemPositions.add(position);
        } else {
          allFound = false;
        }
      } else {
        allFound = false;
      }
    }
  }

// 修改 getGoodItemPositions 方法
  void getGoodItemPositions() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      goodItemPositions.clear();

      for (int i = 0; i < goodItemKeys.length; i++) {
        final key = goodItemKeys[i];
        final context = key.currentContext;

        if (context != null) {
          final box = context.findRenderObject() as RenderBox?;
          if (box != null && box.hasSize) {
            final position = box.localToGlobal(Offset.zero);
            goodItemPositions.add(position);
          }
        }
      }
    });
    // goodItemPositions.clear();
    // bool allFound = true;
    // for (int i = 0; i < goodItemKeys.length; i++) {
    //   final key = goodItemKeys[i];
    //   final context = key.currentContext;
    //
    //   if (context != null) {
    //     final box = context.findRenderObject() as RenderBox?;
    //     if (box != null && box.hasSize) {
    //       final position = box.localToGlobal(Offset.zero);
    //       goodItemPositions.add(position);
    //     } else {
    //       allFound = false;
    //     }
    //   } else {
    //     allFound = false;
    //     print("警告: 商品项 Key $i 的 context 为 null");
    //   }
    // }
    //
    // print('商品项位置获取完成: ${allFound ? "成功" : "部分失败"}');
  }

  changeTabChose(index) {
    currentTabIndex.value = index;
    scrollToSection(index);
    // pageNum.value = 1;
    // dataList.value = [];
    // getGoodList();
  }

  void scrollRightSection(int index) {
    final scrollPosition = !(index < 0 || index >= tabItemPositions.length)
        ? tabItemPositions[index].dx
        : index * 70.w; // 减去标签栏高度
    print(scrollHorizontalController.position.maxScrollExtent);
    print(scrollHorizontalController.position.maxScrollExtent < scrollPosition
        ? scrollHorizontalController.position.maxScrollExtent
        : scrollPosition);
    print(
        'kkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkk');
    if (scrollHorizontalController.hasClients) {
      scrollHorizontalController.animateTo(
        scrollHorizontalController.position.maxScrollExtent < scrollPosition
            ? scrollHorizontalController.position.maxScrollExtent
            : scrollPosition,
        duration: Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
    // }
  }

  void scrollToSection(int index) {
    if (index < 0 || index >= goodItemPositions.length) {
      // 如果索引无效，回退到默认行为
      scrollController.animateTo(
        0,
        duration: Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
      return;
    }
    if (goodItemPositions[index] != null) {
      // 计算需要滚动的距离
      var scrollPosition = goodItemPositions[index].dy - 20.h; // 减去标签栏高度
      if (index == 0) {
        scrollPosition = goodItemPositions[index].dy - 10.h; //
      }
      _isManualScrolling = true;
      // 滚动到指定位置
      scrollController
          .animateTo(
        scrollController.position.maxScrollExtent<scrollPosition?scrollController.position.maxScrollExtent:scrollPosition,
        duration: Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      )
          .then((_) {
        Future.delayed(const Duration(milliseconds: 500), () {
          _isManualScrolling = false;
        });
      });
    }
  }

  @override
  void onClose() {
    print('vvvvvvvvvvvvvvvvvvvvvvvvvvvvvhome');
    scrollController.removeListener(scrollListener);
    scrollController.dispose();
    WidgetsBinding.instance.removeObserver(this);
    super.onClose();
  }

  onSearchClick() {
    print('vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv');
    closeKeyboard();
    TrackingUtils.trackEvent('IndexSearchHandleClick', {
      'banner_id': '',
      'common_fields': '',
      'avitcity_id': '',
      'goods_id': '',
      'url_path': 'HomePage'
    });
    if (UserOption.checkNoTokenJump()) {
      return;
    }
    String uniApp = HomeNavigator.homeSearch().path;
    AndroidIosPlugin.openUniApp(uniApp);
  }

  closeKeyboard() {
    if (FocusManager.instance.primaryFocus?.hasFocus ?? false) {
      FocusManager.instance.primaryFocus?.unfocus();
    }
  }

  onButtonListClick(Map<String, String> map) async {
    closeKeyboard();
    if (UserOption.checkNoTokenJump()) {
      return;
    }
    String? name = map['name'];
    print(name);
    path = '';
    print('gggggggggggggggg11111111111111111111111111111111111');
    switch (name) {
      case '鼎币商城':
        // Map params = {
        //   'schoolId': '3992451',
        //   'classId': '378200462',
        //   'userId': '31PCY06nfe2NrUjGYZ8Dr5WFiUm',
        //   'token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTYwMjExNjUsImlhdCI6MTc1NTQxNjM2NSwiaXNzIjoibVZxUjltUlptdDlLMlpsV2FaUzJwRFI2MWZRS1AxSk4iLCJzY2hvb2xfaWQiOjM5OTI0NTEsInVzZXJfaWQiOiIzMVBDWTA2bmZlMk5yVWpHWVo4RHI1V0ZpVW0ifQ.fZDnP3C6OHV_-TzQ57aO53ueFSHO1_3svEJzjiPKEdY',
        //   'role': '0',
        //   'studentCode':'123123',
        //   'token_key':'321321',
        // };
        // AndroidIosPlugin.openLessonsDetail(jsonEncode(params));
        path = HomeNavigator.shoppingMall().path;
        break;
      case '甄选好课':
        // Get.to(ZxPage());
        Get.to(() => ZxPage());
        break;
      case '文化中心':
        path = HomeNavigator.memberCenter().path;
        print('444444444444444444444444444444444444444444');
        break;
      case '家长会员':
        if (UserOption.parentMemberType == 5) {
          path = MineNavigator.parentEquity().path;
        } else {
          path = HomeNavigator.parentVipEquity().path;
        }
        break;
      // case '超级合伙人':
      //   path = HomeNavigator.partnerApplication().path;
      //   break;
      // case '超级俱乐部':
      //   path = HomeNavigator.supermanClub().path;
      //   break;
    }
    if (path.isNotEmpty) AndroidIosPlugin.openUniApp(path);
  }

  specialFeature(item) {
    TrackingUtils.trackEvent('SpecialBannerClick', {
      'common_fields': item.specialName,
      'avitcity_id': '',
      'goods_id': '',
      'banner_id': '',
      'url_path': 'HomePage'
    });
    if (UserOption.checkNoTokenJump()) {
      return;
    }
    String path = HomeNavigator.specialFeature(
            specialId: item.specialId, specialName: item.specialName)
        .path;
    AndroidIosPlugin.openUniApp(path);
  }

  specialMore(item) {
    if (UserOption.checkNoTokenJump()) {
      return;
    }
    String path = HomeNavigator.specialMore(categoryId: item.id).path;
    AndroidIosPlugin.openUniApp(path);
  }

  goGoods(item) {
    TrackingUtils.trackEvent('IndexSpecialClick', {
      'common_fields': '',
      'avitcity_id': '',
      'goods_id': item.goodsId,
      'banner_id': '',
      'url_path': 'HomePage'
    });
    if (UserOption.checkNoTokenJump()) {
      return;
    }
    String path = HomeNavigator.goodsDetailsHaveBannerId(
            goodsId: item.goodsId, bannerId: '', title: 'bannerId')
        .path;
    AndroidIosPlugin.openUniApp(path);
  }

  goAboutPage() {
    if (UserOption.checkNoTokenJump()) {
      return;
    }
    String path = MineNavigator.aboutPage().path;
    AndroidIosPlugin.openUniApp(path);
  }

  onBannerListClick(index) {
    closeKeyboard();
    if (UserOption.checkNoTokenJump()) {
      return;
    }
    int ind = index < 0 ? 0 : index;
    print(index);

    BannerItem bannerBeanData = bannerNewList[ind];
    if (index < 0) {
      //瓷片
      print('vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv');
      bannerBeanData = bannerInfo.value ?? BannerItem();
    } else {}
    if (bannerBeanData.goodsId.isNotEmpty) {
      String path = HomeNavigator.goodsDetailsHaveBannerId(
              goodsId: bannerBeanData.goodsId,
              bannerId: bannerBeanData.id,
              title: 'bannerId')
          .path;
      AndroidIosPlugin.openUniApp(path);
    } else {
      int? identityType = UserOption.userInfoBeanEntity?.identityType;
      if (bannerBeanData.bannerLinkUrl == 'Personalcenter/my/nomyEquity' &&
          identityType == 4) {
        String path = HomeNavigator.myEquity().path;
        AndroidIosPlugin.openUniApp(path);
      } else {
        if (bannerBeanData.bannerLinkUrl.isNotEmpty) {
          String path = "";
          // 含有指定字符串 不跳转
          if (bannerBeanData.bannerLinkUrl.contains('WWW')) {
            return;
          }
          if (bannerBeanData.bannerLinkUrl.startsWith("/")) {
            bannerBeanData.bannerLinkUrl =
                bannerBeanData.bannerLinkUrl.substring(1);
          }
          if (bannerBeanData.bannerLinkUrl.contains('?')) {
            path =
                '${bannerBeanData.bannerLinkUrl}&bannerId=${bannerBeanData.id}';
          } else {
            path =
                '${bannerBeanData.bannerLinkUrl}?bannerId=${bannerBeanData.id}';
          }
          if (bannerBeanData.bannerLinkUrl.contains('groupActivityId')) {
            path = bannerBeanData.bannerLinkUrl;
          }
          path +=
              "&token=${UserOption.token}&app=2&options=HomePage&nickName=${UserOption.userInfoBeanEntity?.nickName}&userCode=${UserOption.userInfoBeanEntity?.userCode}"
              "&identityType=${UserOption.userInfoBeanEntity?.identityType}&userId=${UserOption.userId}&phone=${UserOption.userInfoBeanEntity?.mobile}&baseUrl=${Config.URL}&appVersion=${AppVersionUtil.appVersion ?? ''}&distinctId=${AppVersionUtil.distinctId ?? ''}";
          if (path.isNotEmpty) {
            AndroidIosPlugin.openUniApp(path);
          }
        }
      }
    }
  }

  fetchData(index) {
    /// 登录类型参数
    ///
    /// [loginType] 登录类型：
    /// * 1 - 小程序
    /// * 2 - C端
    /// * 3 - B端
    var response = HttpUtil().get("${Config.activityList}?loginType=2");
    response.then((value) async {
      if (value != null) {
        List<PopupModelData> items =
            jsonConvert.convertListNotNull<PopupModelData>(value)!;
        if (items.isNotEmpty) {
          if (currentTempTabIndex == index) {
            print('展示弹窗');
            await PopOption.initPopShowData(items);
            PopOption.getCurrentPopData(index);
          }
        }
      }
    });
  }

  getHomeInfo() {
    var response = HttpUtil().get('${Config.categoryGoodsList}?placement=0');
    response.then((value) async {
      List<HomePageGoodsItem>? data =
          jsonConvert.convertListNotNull<HomePageGoodsItem>(value);
      categoryGoodsList.value = data!;
      initGoodController();
      initTabController();
    });
  }

  getBannerList(index) {
    // bannerNewList
    var response = HttpUtil().get('${Config.bannerNewList}');
    response.then((value) async {
      List<BannerItem>? data =
          jsonConvert.convertListNotNull<BannerItem>(value);
      bannerNewList.value = data!;
      // print(bannerNewList.isNotEmpty);
      // print('66666666666666666666666666666666666666666666');
    });
  }

  getPorcelainInfo() {
    //   bannerInfo
    var response = HttpUtil().get('${Config.porcelainInfo}');
    response.then((value) async {
      // bannerInfo.value = BannerItem().fromJson(value);
      bannerInfo.value = BannerItem.fromJson(value);
    });
  }

  getSpecialList() {
    //   specialList  specialList
    var response = HttpUtil().get('${Config.specialList}?placement=0');
    response.then((value) async {
      List<SpecialItem>? data =
          jsonConvert.convertListNotNull<SpecialItem>(value);
      specialList.value = data!;
      if (specialList[0].backgroundColor.isNotEmpty) {
        backgroundColor.value = specialList[0].backgroundColor;
      }
    });
  }

  // {
  // "studentName": "string",
  // "qrcode": "string" http
  // }
  addTeacherCodeRequest() {
    // Get.dialog(
    //   AddTeacherCodeDialog({"studentName":"1111","qrcode":"https://document.dxznjy.com/course/49fdbf70fab64b40a4be6366ab365326.jpg"}),
    //   barrierDismissible: false,
    // );
    closeKeyboard();
    if (editingTeacherCodeController.text.isEmpty) {
      ToastUtil.showShortErrorToast("请输入教练验证码");
      return;
    }
    var response = HttpUtil().get(
        '${Config.getQrcodeByCode}?code=${editingTeacherCodeController.text}');
    response.then((value) {
      if (value != null) {
        Get.dialog(
          AddTeacherCodeDialog(value),
          barrierDismissible: false,
        );
      } else {
        ToastUtil.showShortErrorToast("邀请码错误或者您已添加教练~");
      }
    });
  }

  getTypeList() {
    var response = HttpUtil().get(Config.homeTypeList);
    response.then((value) async {
      List<HomeTypeBeanData>? entity =
          jsonConvert.convertListNotNull<HomeTypeBeanData>(value);
      if (entity != null) {
        homeTypeList.value = entity;
        // initTabController();
        getGoodList();
      }
    });
  }

  getGoodList() {
    var response = HttpUtil().get(
        "${Config.goodsEnableList}?pageSize=$pageSize&pageNum=$pageNum&recommendationCategoryId=${homeTypeList[currentTabIndex.value].id}&userId=${UserOption.userId}");
    response.then((value) async {
      ZxCourseData? entity = JsonConvert.fromJsonAsT<ZxCourseData>(value);
      if (pageNum == 1) {
        dataList.value = entity!.data;
      } else {
        dataList.addAll(entity!.data);
      }
      try {
        totalPages = int.parse(entity.totalPage);
      } catch (e) {
        totalPages = 1;
      }
    });
  }

  checkWgtVersion() async {
    // var response =
    //     await HttpUtil().get("${Config.checkWgtVersion}?enCode=DXZX");
    // if (response != null) {
    //   if (response is Map) {
    //     if (response.containsKey('APP_ID')) {
    //       wgt_app_id = response['APP_ID'];
    //     }
    //     if (response.containsKey('WGT_URL')) {
    //       wgt_url = response['WGT_URL'];
    //     }
    //     if (response.containsKey('APP_VERSION')) {
    //       wgt_version = response['APP_VERSION'];
    //     }
    //   }
    //   String wgtVersion = await AndroidIosPlugin.getWgtVersion();
    //   if (wgt_version != wgtVersion) {
    //     BotToast.showLoading();
    //     await AndroidIosPlugin.updateWgt(
    //         appId: wgt_app_id, wgtUrl: wgt_url, version: wgt_version);
    //   }
    // }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        getTabPositions();
        getGoodItemPositions();
      });
      canShowPop = false;
      showTab.value = true;
      goodItemPositions=[];
      currentTabIndex.value=0;
      scrollController.animateTo(
        0,
        duration: Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      ).then((value) {
        initGoodController();
      });
    } else if (state == AppLifecycleState.paused) {
      currentTabIndex.value=0;
      canShowPop = false;
      showTab.value = true;
      goodItemPositions=[];
      scrollController.animateTo(
        0,
        duration: Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      ).then((value ) {
        initGoodController();
      });
    }
  }
}
