package com.dxznjy.alading.util;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.text.TextUtils;
import android.util.Log;
import android.widget.Toast;

import com.blankj.rxbus.RxBus;
import com.bumptech.glide.Glide;
import com.bumptech.glide.request.target.SimpleTarget;
import com.bumptech.glide.request.transition.Transition;
import com.dxznjy.alading.DXApplication;
import com.dxznjy.alading.R;
import com.dxznjy.alading.common.Constants;
import com.dxznjy.alading.entity.AppShareParams;
import com.dxznjy.alading.entity.DxnParams;
import com.dxznjy.alading.entity.MiniParams;
import com.dxznjy.alading.entity.PayData;
import com.google.gson.Gson;
import com.tencent.mm.opensdk.modelbiz.WXLaunchMiniProgram;
import com.tencent.mm.opensdk.modelmsg.SendMessageToWX;
import com.tencent.mm.opensdk.modelmsg.WXMediaMessage;
import com.tencent.mm.opensdk.modelmsg.WXMiniProgramObject;
import com.tencent.mm.opensdk.modelmsg.WXWebpageObject;
import com.tencent.mm.opensdk.openapi.WXAPIFactory;

import java.io.ByteArrayOutputStream;

import io.dcloud.feature.sdk.DCUniMPSDK;
import io.dcloud.feature.unimp.config.UniMPOpenConfiguration;
import io.dcloud.uniplugin.AppShare;
import io.dcloud.uniplugin.PayEvent;

public class WxUtils {
    /**
     * 打开鼎学能微信小程序
     *
     */
    public static void startWxUniapp(DxnParams params ) {
        String path="?code="+params.getCode()+
                "&studentName="+params.getStudentName()+
                "&token="+params.getToken()+
                "&grade="+params.getGrade();
        WXLaunchMiniProgram.Req req = new WXLaunchMiniProgram.Req();
        req.userName = Constants.MiniDxnId; //
        req.path = path;
        req.extData=new Gson().toJson(params);
        req.miniprogramType = Constants.miniType; // 正式版

        DXApplication.api.sendReq(req);
        Log.i("zgj",req.extData);
    }

    /**
     * 微信小程序支付
     *
     */
    public static void startWxUniapp(PayData payData ) {
        String path="cusid="+payData.getCusid()+
                "&appid="+payData.getAppid()+
                "&orgid="+payData.getOrgid()+
                "&version="+payData.getVersion()+
                "&trxamt="+payData.getTrxamt()+
                "&reqsn="+payData.getReqsn()+
                "&innerappid="+payData.getInnerappid()+
                "&notify_url="+payData.getNotify_url()+
                "&body="+payData.getBody()+
                "&signtype="+payData.getSigntype()+
                "&validtime="+payData.getValidtime()+
                "&randomstr="+payData.getRandomstr()+
                "&paytype="+payData.getPaytype()+
                "&sign="+payData.getSign();
        WXLaunchMiniProgram.Req req = new WXLaunchMiniProgram.Req();
        req.userName =Constants.MiniPayId; //
        req.path="pages/orderDetail/orderDetail?"+path;
        req.miniprogramType = WXLaunchMiniProgram.Req.MINIPTOGRAM_TYPE_RELEASE; // 正式版
        DXApplication.api.sendReq(req);
    }

    /**
     * flutter分享到微信
     * @param
     */
    public  static void shareWeb(Context mContext, AppShareParams appShareParams){
        if(!AppUtils.isWxInstall(mContext)){//未安装微信
            Log.i("fxiaol","unstall");
            Toast.makeText(mContext,"微信未安装",Toast.LENGTH_SHORT).show();
        }else{
            WXWebpageObject webpage = new WXWebpageObject();
            webpage.webpageUrl = Constants.SHARE_H5_URL+appShareParams.getPath();
            //用 WXWebpageObject 对象初始化一个 WXMediaMessage 对象
            WXMediaMessage msg = new WXMediaMessage(webpage);
            msg.description ="";
            String title=appShareParams.getTitle();
            if(title.length()>30){
                title=title.substring(0,30)+"...";
            }
            msg.title =title;
            if(TextUtils.isEmpty(appShareParams.getImageUrl())){
                Bitmap thumbBmp = BitmapFactory.decodeResource(mContext.getResources(), R.mipmap.ic_share_default);
                msg.thumbData =bitmapToByteArray(thumbBmp);
                SendMessageToWX.Req req = new SendMessageToWX.Req();
                req.transaction = mContext.getResources().getString(R.string.app_name);
                req.message = msg;
                req.scene = SendMessageToWX.Req.WXSceneSession;
                //调用api接口，发送数据到微信
                DXApplication.api.sendReq(req);
            }else{
                SimpleTarget<Bitmap> simpleTarget = new SimpleTarget<Bitmap>() {
                    @Override
                    public void onResourceReady(Bitmap resource, Transition<? super Bitmap> transition) {
                        msg.thumbData =bitmapToByteArray(resource);
                        //构造一个Req
                        SendMessageToWX.Req req = new SendMessageToWX.Req();
                        req.transaction = mContext.getResources().getString(R.string.app_name);
                        req.message = msg;
                        req.scene = SendMessageToWX.Req.WXSceneSession;
                        //调用api接口，发送数据到微信
                        DXApplication.api.sendReq(req);
                    }
                };
                Glide.with(mContext)
                        .asBitmap()
                        .load(appShareParams.getImageUrl())
                        .override(160,160)
                        .into(simpleTarget);
            }
        }
    }

    /**
     * 打开微信小程序页面
     */
    public static void toUniappPage(Context mContext,String data){
        Log.i("zgj",data);
        try {
            UniMPOpenConfiguration uniMPOpenConfiguration = new UniMPOpenConfiguration();
            uniMPOpenConfiguration.path = data;
            DXApplication.iUniMP= DCUniMPSDK.getInstance().openUniMP(mContext, Constants.XCX_PKG_NAME,uniMPOpenConfiguration);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }



    /**
     * 小程序点击分享到微信
     */
    public static void shareWeb(Context mContext,AppShare result){
        if(!AppUtils.isWxInstall(mContext)){//未安装微信
            Toast.makeText(mContext,"微信未安装",Toast.LENGTH_SHORT).show();
        }else{
            WXWebpageObject webpage = new WXWebpageObject();
            AppShareParams appShareParams=new Gson().fromJson(result.getParams(), AppShareParams.class);
            webpage.webpageUrl =Constants.SHARE_H5_URL+appShareParams.getPath();
            //用 WXWebpageObject 对象初始化一个 WXMediaMessage 对象
            WXMediaMessage msg = new WXMediaMessage(webpage);
            msg.description ="";
            String title=appShareParams.getTitle();
            if(title.length()>30){
                title=title.substring(0,30)+"...";
            }
            msg.title =title;
            if (TextUtils.isEmpty(appShareParams.getImageUrl())){
                Bitmap thumbBmp = BitmapFactory.decodeResource(mContext.getResources(), R.mipmap.ic_share_default);
                msg.thumbData =bitmapToByteArray(thumbBmp);
                SendMessageToWX.Req req = new SendMessageToWX.Req();
                req.transaction = mContext.getResources().getString(R.string.app_name);
                req.message =msg;
                req.scene =SendMessageToWX.Req.WXSceneSession;
                //调用api接口，发送数据到微信
                DXApplication.api.sendReq(req);
            }else{
                SimpleTarget<Bitmap> simpleTarget = new SimpleTarget<Bitmap>() {
                    @Override
                    public void onResourceReady(Bitmap resource, Transition<? super Bitmap> transition) {
                        msg.thumbData = bitmapToByteArray(resource);
                        //构造一个Req
                        SendMessageToWX.Req req = new SendMessageToWX.Req();
                        req.transaction = mContext.getResources().getString(R.string.app_name);
                        req.message =msg;
                        req.scene =SendMessageToWX.Req.WXSceneSession;
                        //调用api接口，发送数据到微信
                        DXApplication.api.sendReq(req);
                    }
                };
                Glide.with(mContext)
                        .asBitmap()
                        .load(appShareParams.getImageUrl())
                        .override(160,160)
                        .into(simpleTarget);
            }
        }
    }


    //小程序分享小程序
    public  static  void shareMiniProgram(Context mContext,AppShare result){
        if(!AppUtils.isWxInstall(mContext)){//未安装微信
            Toast.makeText(mContext,"微信未安装",Toast.LENGTH_SHORT).show();
        }else {
            AppShareParams appShareParams=new Gson().fromJson(result.getParams(), AppShareParams.class);
            WXMiniProgramObject miniProgramObj = new WXMiniProgramObject();
            miniProgramObj.webpageUrl = "http://www.qq.com"; // 兼容低版本的网页链接
            miniProgramObj.miniprogramType = Constants.miniType;// 正式版:0，测试版:1，体验版:2
            miniProgramObj.userName = appShareParams.getMiniProgramId();     // 小程序原始id
            miniProgramObj.path = appShareParams.getPath();            //小程序页面路径；
            WXMediaMessage msg = new WXMediaMessage(miniProgramObj);
            String title=appShareParams.getTitle();
            if(title.length()>30){
                title=title.substring(0,30)+"...";
            }
            msg.title =title;
            msg.description = mContext.getResources().getString(R.string.app_name);
            if(TextUtils.isEmpty(appShareParams.getImageUrl())){
                Bitmap thumbBmp = BitmapFactory.decodeResource(mContext.getResources(), R.mipmap.ic_share_default);
                msg.thumbData =bitmapToByteArray(thumbBmp);
                // 小程序消息desc
                SendMessageToWX.Req req = new SendMessageToWX.Req();
                req.transaction = mContext.getResources().getString(R.string.app_name);
                req.message = msg;
                req.scene = SendMessageToWX.Req.WXSceneSession;  // 目前只支持会话
                DXApplication.api.sendReq(req);
            }else{
                SimpleTarget<Bitmap> simpleTarget = new SimpleTarget<Bitmap>() {
                    @Override
                    public void onResourceReady(Bitmap resource, Transition<? super Bitmap> transition) {
                        msg.thumbData =bitmapToByteArray(resource);
                        //构造一个Req
                        // 小程序消息desc
                        SendMessageToWX.Req req = new SendMessageToWX.Req();
                        req.transaction = mContext.getResources().getString(R.string.app_name);
                        req.message = msg;
                        req.scene = SendMessageToWX.Req.WXSceneSession;  // 目前只支持会话
                        DXApplication.api.sendReq(req);
                    }
                };
                Glide.with(mContext)
                        .asBitmap()
                        .load(appShareParams.getImageUrl())
                        .override(160,160)
                        .into(simpleTarget);
            }
        }
    }


    //原生分享小程序
    public static void shareMiniProgram(Context mContext, MiniParams miniParams){
        WXMiniProgramObject miniProgramObj = new WXMiniProgramObject();
        miniProgramObj.webpageUrl = "http://www.qq.com"; // 兼容低版本的网页链接
        miniProgramObj.miniprogramType =Constants.miniType;// 正式版:0，测试版:1，体验版:2
        miniProgramObj.userName = miniParams.getMiniProgramId();     // 小程序原始id
        miniProgramObj.path = miniParams.getPath();            //小程序页面路径；
        WXMediaMessage msg = new WXMediaMessage(miniProgramObj);
        String title=miniParams.getTitle();
        if(title.length()>30){
            title=title.substring(0,30)+"...";
        }
        msg.title =title;
        msg.description =  mContext.getResources().getString(R.string.app_name);
        if(TextUtils.isEmpty(miniParams.getLogo())){
            Bitmap thumbBmp = BitmapFactory.decodeResource(mContext.getResources(), R.mipmap.ic_share_default);
            msg.thumbData =bitmapToByteArray(thumbBmp);
            // 小程序消息desc
            SendMessageToWX.Req req = new SendMessageToWX.Req();
            req.transaction = mContext.getResources().getString(R.string.app_name);
            req.message = msg;
            req.scene = SendMessageToWX.Req.WXSceneSession;  // 目前只支持会话
            DXApplication.api.sendReq(req);
        }else{
            SimpleTarget<Bitmap> simpleTarget = new SimpleTarget<Bitmap>() {
                @Override
                public void onResourceReady(Bitmap resource, Transition<? super Bitmap> transition) {
                    msg.thumbData =bitmapToByteArray(resource);
                    //构造一个Req
                    // 小程序消息desc
                    SendMessageToWX.Req req = new SendMessageToWX.Req();
                    req.transaction = mContext.getResources().getString(R.string.app_name);
                    req.message = msg;
                    req.scene = SendMessageToWX.Req.WXSceneSession;  // 目前只支持会话
                    DXApplication.api.sendReq(req);
                }
            };
            Glide.with(mContext)
                    .asBitmap()
                    .load(miniParams.getLogo())
                    .override(160,160)
                    .into(simpleTarget);
        }
    }
    public static byte[] bitmapToByteArray(Bitmap bitmap) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        bitmap.compress(Bitmap.CompressFormat.PNG, 70, outputStream);
        return outputStream.toByteArray();
    }
}
