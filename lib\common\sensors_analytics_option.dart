import 'package:sensors_analytics_flutter_plugin/sensors_analytics_flutter_plugin.dart';
import '../utils/sharedPreferences_util.dart';

class TrackingUtils {

  static void trackPageView(String title,
      [Map<String, dynamic>? params]) async {
    String code = await SharedPreferencesUtil.getData<String>(
        SharedPreferencesUtil.userCode);
    String userCode = '';
    if (code.isNotEmpty) {
      userCode = code;
    }
    SensorsAnalyticsFlutterPlugin.trackViewScreen(
      title,
      {
        'event_type': "\$AppViewScreen",
        'u_code': userCode,
        'event_name': title,
        'view_source': 'ZX_APP',
        'terminal_source': 'MOBILE',
        ...?params
      },
    );
  }

  static void trackEvent(String event, [Map<String, dynamic>? params]) async {
    String code = await SharedPreferencesUtil.getData<String>(
        SharedPreferencesUtil.userCode);
    String userCode = '';
    if (code != null && code != "") {
      userCode = code;
    }
    SensorsAnalyticsFlutterPlugin.track(
      event,
      {
        'event_type': '\$AppClick',
        'event_name': event,
        'u_code': userCode,
        'referrer_path': '',
        'view_source': 'ZX_APP',
        'terminal_source': 'MOBILE',
        ...?params
      },
    );
  }
}
