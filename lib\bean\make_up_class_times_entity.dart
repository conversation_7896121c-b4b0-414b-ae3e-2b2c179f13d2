import 'package:student_end_flutter/generated/json/base/json_field.dart';
import 'package:student_end_flutter/generated/json/make_up_class_times_entity.g.dart';
import 'dart:convert';
export 'package:student_end_flutter/generated/json/make_up_class_times_entity.g.dart';

@JsonSerializable()
class MakeUpClassTimesEntity {
	int? week;
	List<MakeUpClassTimesResult>? result = [];

	MakeUpClassTimesEntity();

	factory MakeUpClassTimesEntity.fromJson(Map<String, dynamic> json) => $MakeUpClassTimesEntityFromJson(json);

	Map<String, dynamic> toJson() => $MakeUpClassTimesEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class MakeUpClassTimesResult {
	String? id;
	int? week;
	String? startTime;
	String? endTime;

	MakeUpClassTimesResult();

	factory MakeUpClassTimesResult.fromJson(Map<String, dynamic> json) => $MakeUpClassTimesResultFromJson(json);

	Map<String, dynamic> toJson() => $MakeUpClassTimesResultToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}