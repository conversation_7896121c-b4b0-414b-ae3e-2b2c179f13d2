import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../bean/course_list_bean_entity.dart';
import '../../../common/CommonUtil.dart';
import '../../../utils/lesson_util.dart';
import '../controller/lessonlist_controller.dart';

class LessonLeaveButton extends StatelessWidget {
  const LessonLeaveButton({
    super.key,
    required this.courseData,
  });

  final CourseListBeanData courseData;

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<LessonListController>();
    return Visibility(
      visible: LessonUtil.shouldShowLeaveBtn(
        courseData,
        controller.currentTabIndex.value,
        controller.currentLessonStatusTabIndex.value
      ),
      child: InkWell(
        onTap: () {
          final operationType = LessonUtil.getLeaveOperationType(courseData);
          controller.showAskForLeaveDialog(
            courseData: courseData,
            type: operationType,
            isTestClassLeave: courseData.experience == true
          );
        },
        child: Container(
          alignment: Alignment.center,
          padding: EdgeInsets.symmetric(vertical: 4.h, horizontal: 3.w),
          decoration: BoxDecoration(
            border: Border.all(
              color: HexColor('339378'), width: 1.w),
            borderRadius: BorderRadius.circular(8.w),
          ),
          child: Text(
            LessonUtil.getLeaveBtnTitle(courseData),
            style: TextStyle(
              fontSize: 12.sp,
              color: HexColor('339378')
            ),
          ),
        ),
      ),
    );
  }
}