<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:background="@drawable/bg_radius20_white"
        android:orientation="vertical"
        android:paddingTop="20dp"
        android:layout_marginLeft="14dp"
        android:layout_marginRight="14dp"
        android:paddingBottom="20dp">

        <RelativeLayout
            android:id="@+id/rl_top"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="20dp"
            android:layout_marginBottom="20dp">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:text="拖动下方滑块,进行拼图验证"
                android:textColor="@color/_333333"
                android:textSize="16sp"
                android:textStyle="bold" />

            <LinearLayout
                android:id="@+id/ll_close"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:gravity="center"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginLeft="14dp"
                    android:src="@mipmap/ic_close" />
            </LinearLayout>
        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="#E6E6E6" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="20dp"
            >

            <RelativeLayout
                android:id="@+id/rl_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"></RelativeLayout>

            <ImageView
                android:id="@+id/img_refresh"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_alignParentTop="true"
                android:layout_alignParentRight="true"
                android:layout_marginTop="10dp"
                android:layout_marginRight="10dp"
                android:src="@drawable/refresh" />
        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <com.dxznjy.alading.widget.captcha.TextSeekbar
                android:id="@+id/seekbar"
                style="@style/MySeekbarSytle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:splitTrack="false"
                android:thumbOffset="0dp" />

            <TextView
                android:id="@+id/tv_seek_tips"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="向右滑动滑块填充拼图"
                android:textColor="@color/_333333"
                android:textSize="15sp" />
        </RelativeLayout>

        <com.dxznjy.alading.widget.captcha.Captcha
            android:id="@+id/captCha"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:max_fail_count="100"
            app:progressDrawable="@drawable/custom_seekbar_progress_default"
            app:src="@mipmap/pic_peitu"
            app:thumbDrawable="@drawable/ic_thumb_n" />

        <ImageView
            android:id="@+id/img_content"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginLeft="80dp"
            android:layout_marginTop="20dp"
            android:layout_marginRight="80dp"
            android:scaleType="fitXY"
            android:visibility="gone" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="14dp"
            android:orientation="horizontal"
            android:visibility="gone">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="看不清？"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_change"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="换一张"
                android:textColor="@color/_428a6f"
                android:textSize="14sp" />
        </LinearLayout>

        <EditText
            android:id="@+id/ed_piccode"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginLeft="80dp"
            android:layout_marginTop="14dp"
            android:layout_marginRight="80dp"
            android:background="@drawable/bg_radius10_edit"
            android:maxLength="6"
            android:paddingLeft="14dp"
            android:singleLine="true"
            android:textColor="@color/_333333"
            android:textCursorDrawable="@null"
            android:textSize="16sp"
            android:visibility="gone" />


        <LinearLayout
            android:id="@+id/ll_bottom"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:gravity="center"
            android:orientation="horizontal"
            android:visibility="gone">

            <TextView
                android:id="@+id/tv_sure"
                android:layout_width="140dp"
                android:layout_height="40dp"
                android:background="@drawable/bg_radius20_428a6f"
                android:gravity="center"
                android:text="确定"
                android:textColor="@color/white"
                android:textSize="14sp" />
        </LinearLayout>
    </LinearLayout>
</RelativeLayout>