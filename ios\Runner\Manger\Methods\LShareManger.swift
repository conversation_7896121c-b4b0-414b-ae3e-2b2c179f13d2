
//
//  LappShareManger.swift
//  LappTest
//
//  Created by yancr on 2021/1/8.
//

import UIKit

class LShareManger: NSObject {
    static let manager = LShareManger()

    /// 分享图片
    /// - Parameters:
    ///   - platform:
    ///   - image:
    func shareImage(to platform: SocialPlatformType, image: Any?) {
        let maxLen = 26214400 // 25M
        var dataResult: Data?
        if image is String { // imagurl
            guard let thumImageUrl = image as? String else { return }
            if let data = try? Data(contentsOf: URL(string: thumImageUrl)!) {
                let image = UIImage(data: data) ?? UIImage()
                //            转为data
                dataResult = self.compressWithMaxCount(origin: image, maxCount: maxLen)
            }
        } else if image is UIImage {
            guard let imageImg = image else { return }
            //            转为data
            dataResult = self.compressWithMaxCount(origin: imageImg as! UIImage, maxCount: maxLen)
        } else {
            return
        }
        let imageObject = WXImageObject()
        if let data = dataResult {
            imageObject.imageData = data
        }
        let message = WXMediaMessage()
        message.thumbData = nil
        message.mediaObject = imageObject

        let req = SendMessageToWXReq()
        req.bText = false
        req.message = message
        switch platform {
        case .wechatSession:
            req.scene = Int32(WXSceneSession.rawValue)
        case .wechatTimeLine: // 朋友圈
            req.scene = Int32(WXSceneTimeline.rawValue)
        }
        WXApi.send(req, completion: nil)
    }

    // 分享文本

    func shareText(to platform: SocialPlatformType, text: String) {
        let req = SendMessageToWXReq()
        req.bText = true
        req.text = text
        switch platform {
        case .wechatSession:
            req.scene = Int32(WXSceneSession.rawValue)
        case .wechatTimeLine: // 朋友圈
            req.scene = Int32(WXSceneTimeline.rawValue)
        }
        WXApi.send(req, completion: nil)
    }

    /// 分享网页
    func shareWebPage(to platform: SocialPlatformType, title: String, description: String, thumImage: Any?, webPageUrl: String) {
        let maxLen = 65536 // 128kb
         // 检查 thumImage 类型
        if let thumImageUrl = thumImage as? String {
            // 异步加载网络图片
            loadImageAsync(urlString: thumImageUrl) { [weak self] image in
                guard let self = self, let image = image else {
                    return
                }
                handleImageData(image: image, maxLen: maxLen, completion: {[weak self] data in
                    if let dataImage = data {
                        self?.sendWeChatRequest(platform: platform, data: dataImage, title: title, description: description, webPageUrl: webPageUrl)
                    }
                    
                })
            }
        } else if let thumImages = thumImage as? UIImage {
            let webpageObject = WXWebpageObject()
            webpageObject.webpageUrl = webPageUrl
            let message = WXMediaMessage()
            if title.count > 30 {
                let substr = title.prefix(30)
                message.title = String(substr)
            } else {
                message.title = title
            }
            message.description = description
            
            var dataResult: Data?
            if thumImages is UIImage {
                guard let image = thumImage else { return }
                //            转为data
                dataResult = self.compressWithMaxCount(origin: image as! UIImage, maxCount: maxLen)
            } else {
                return
            }
            if let data = dataResult {
                message.setThumbImage(UIImage(data: data) ?? UIImage())
            }
            message.mediaObject = webpageObject

            let req = SendMessageToWXReq()
            req.bText = false
            req.message = message

            switch platform {
            case .wechatSession:
                req.scene = Int32(WXSceneSession.rawValue)
            case .wechatTimeLine:
                req.scene = Int32(WXSceneTimeline.rawValue)
            default: break
            }
            WXApi.send(req) { _ in
            }
        }
    
    }

 
    func shareMiniProgram(to platform: SocialPlatformType, title: String, description: String, thumImage: Any?, webPageUrl: String, userName: String, path: String) {
       // 判断是base64的图还是imageurl
       let maxLen = 131072 // 128kb
        // 检查 thumImage 类型
       if let thumImageUrl = thumImage as? String {
           // 异步加载网络图片
           loadImageAsync(urlString: thumImageUrl) { [weak self] image in
               guard let self = self, let image = image else {
                   return
               }
               handleImageData(image: image, maxLen: maxLen, completion: {[weak self] data in
                   if let dataImage = data {
                       self?.sendWeChatMiniProgramRequest(data: dataImage, title: title, description: description, webPageUrl: webPageUrl, userName: userName, path: path)
                   }
                   
               })
           }
       } else if let image = thumImage as? UIImage {
           handleImageData(image: image, maxLen: maxLen, completion: {[weak self] data in
               if let dataImage = data {
                   self?.sendWeChatMiniProgramRequest(data: dataImage, title: title, description: description, webPageUrl: webPageUrl, userName: userName, path: path)
               }
           })
       } else {
           return
       }

    }
    
    private func sendWeChatRequest(platform: SocialPlatformType, data: Data?, title: String, description: String,  webPageUrl: String) {
        guard let data = data else {
            return
        }
        let webpageObject = WXWebpageObject()
        webpageObject.webpageUrl = webPageUrl
        let message = WXMediaMessage()
        if title.count > 30 {
            let substr = title.prefix(30)
            message.title = String(substr)
        } else {
            message.title = title
        }
        message.setThumbImage(UIImage(data: data) ?? UIImage())
        message.description = description
        message.mediaObject = webpageObject
        let req = SendMessageToWXReq()
        req.bText = false
        req.message = message

        switch platform {
        case .wechatSession:
            req.scene = Int32(WXSceneSession.rawValue)
        case .wechatTimeLine:
            req.scene = Int32(WXSceneTimeline.rawValue)
        }
        WXApi.send(req) { _ in
        }
    }
    
    
    private func sendWeChatMiniProgramRequest(data: Data?, title: String, description: String,  webPageUrl: String, userName: String, path: String) {
        guard let hdImageData = data else {
            return
        }
        
        let object = WXMiniProgramObject()
        object.webpageUrl = webPageUrl
        object.userName = userName
        object.path = path
        object.hdImageData = hdImageData
//        object.miniProgramType = WxShareProgramType
        switch WxShareProgramType {
        case 0:
            object.miniProgramType = .release
        case 1:
            object.miniProgramType = .test
        case 2:
            object.miniProgramType = .preview
        default:
            // 如果 WxShareProgramType 的值不是 0、1 或 2，可以设置一个默认值
            object.miniProgramType = .release // 或者其他默认值
        }
        
//        WXMiniProgramTypeRelease = 0,       //**< 正式版  */
//        WXMiniProgramTypeTest = 1,        //**< 开发版  */
//        WXMiniProgramTypePreview = 2,         //**< 体验版  */
        
        
        
        let message = WXMediaMessage()
        message.title = String(title.prefix(30)) // 直接截取前30字符
        message.description = description
        message.mediaObject = object
        
        let req = SendMessageToWXReq()
        req.bText = false
        req.message = message
        req.scene = Int32(WXSceneSession.rawValue)
        
        WXApi.send(req) { success in
            if !success {
            }
        }
    }
    
    // 异步加载图片
    private func loadImageAsync(urlString: String, completion: @escaping (UIImage?) -> Void) {
        guard let url = URL(string: urlString.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? "") else {
            completion(nil)
            return
        }
        
        let task = URLSession.shared.dataTask(with: url) { data, response, error in
            if let error = error {
                completion(nil)
                return
            }
            guard let data = data, let image = UIImage(data: data) else {
                completion(nil)
                return
            }
            completion(image)
        }
        task.resume()
    }
    
    private  func compressWithMaxCount(origin: UIImage, maxCount: Int) -> Data? {
        var compression: CGFloat = 1
        guard var data = origin.jpegData(compressionQuality: compression) else { return nil }
        if data.count <= maxCount {
            return data
        }
        var max: CGFloat = 1, min: CGFloat = 0.8 // 最小0.8
        for index in 0 ... 6 { // 最多压缩6次
            compression = (max + min)/2
            if let tmpdata = origin.jpegData(compressionQuality: compression) {
                data = tmpdata
            } else {
                return nil
            }
            if data.count <= maxCount {
                return data
            } else {
                max = compression
            }
        }
        // 压缩分辨率
        guard var resultImage = UIImage(data: data) else { return nil }
        var lastDataCount: Int = 0
        while data.count > maxCount, data.count != lastDataCount {
            lastDataCount = data.count
            let ratio = CGFloat(maxCount)/CGFloat(data.count)
            let size = CGSize(width: resultImage.size.width*sqrt(ratio), height: resultImage.size.height*sqrt(ratio))
            UIGraphicsBeginImageContextWithOptions(CGSize(width: CGFloat(Int(size.width)), height: CGFloat(Int(size.height))), true, 1) // 防止黑边
            resultImage.draw(in: CGRect(origin: .zero, size: size)) // 比转成Int清晰
            if let tmp = UIGraphicsGetImageFromCurrentImageContext() {
                resultImage = tmp
                UIGraphicsEndImageContext()
            } else {
                UIGraphicsEndImageContext()
                return nil
            }
            if let tmpdata = resultImage.jpegData(compressionQuality: compression) {
                data = tmpdata
            } else {
                return nil
            }
        }
        return data
    }

    
    private func handleImageData(image: UIImage, maxLen: Int, completion: @escaping (Data?) -> Void) {
        DispatchQueue.global(qos: .userInitiated).async {
            var compressedData: Data? = self.compressWithMaxCount(origin: image, maxCount: maxLen)
            if compressedData == nil || compressedData!.count > maxLen {
                compressedData = nil
            }
            DispatchQueue.main.async {
                completion(compressedData)
            }
        }
    }
}
