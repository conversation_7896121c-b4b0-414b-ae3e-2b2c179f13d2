import 'package:student_end_flutter/generated/json/base/json_convert_content.dart';
import 'package:student_end_flutter/bean/student_token_bean_entity.dart';

StudentTokenBeanEntity $StudentTokenBeanEntityFromJson(
    Map<String, dynamic> json) {
  final StudentTokenBeanEntity studentTokenBeanEntity = StudentTokenBeanEntity();
  final String? token = jsonConvert.convert<String>(json['token']);
  if (token != null) {
    studentTokenBeanEntity.token = token;
  }
  return studentTokenBeanEntity;
}

Map<String, dynamic> $StudentTokenBeanEntityToJson(
    StudentTokenBeanEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['token'] = entity.token;
  return data;
}

extension StudentTokenBeanEntityExtension on StudentTokenBeanEntity {
  StudentTokenBeanEntity copyWith({
    String? token,
  }) {
    return StudentTokenBeanEntity()
      ..token = token ?? this.token;
  }
}