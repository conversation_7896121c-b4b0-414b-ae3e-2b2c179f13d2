package com.dxznjy.alading.widget;

import android.app.Activity;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.PopupWindow;
import android.widget.TextView;

import com.dxznjy.alading.R;

public class PermissonTipsPopWindow {
    Context mContext;
    View mLocationView;
    OnCheckPermissonListener mOnCheckPermissonListener;
    public PermissonTipsPopWindow(Context context, View locationView, OnCheckPermissonListener onCheckPermissonListener) {
        this.mLocationView=locationView;
        this.mContext=context;
        this.mOnCheckPermissonListener=onCheckPermissonListener;
    }

    public void showQuanXianTips(String tips){
        View view =  LayoutInflater.from(mContext).inflate(R.layout.pop_permisson_tips, null);
        TextView tv_content=view.findViewById(R.id.tv_content);
        TextView tv_cancel=view.findViewById(R.id.tv_cancel);
        TextView tv_sure=view.findViewById(R.id.tv_sure);
        tv_content.setText(tips);
        PopupWindow popupWindow = new PopupWindow(view, ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT, true);
        popupWindow.setOutsideTouchable(false);
        // 设置PopupWindow的背景
        popupWindow.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
// 设置PopupWindow是否能响应点击事件
        popupWindow.setTouchable(true);
        popupWindow.setOnDismissListener(new PopupWindow.OnDismissListener() {
            @Override
            public void onDismiss() {
                backgroundAlpha(1.0f);
            }
        });
        tv_cancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mOnCheckPermissonListener.onCancle();
                popupWindow.dismiss();
            }
        });
        tv_sure.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mOnCheckPermissonListener.onSuccess();
                popupWindow.dismiss();
            }
        });
        backgroundAlpha(0.5f);
        popupWindow.showAtLocation(mLocationView, Gravity.CENTER, 0, 0);
    }
    public void backgroundAlpha(float f) {
        WindowManager.LayoutParams lp =((Activity)mContext).getWindow().getAttributes();
        lp.alpha = f;
        ((Activity)mContext).getWindow().setAttributes(lp);
    }

    public interface OnCheckPermissonListener {//选择任意项 回调所有数据和当前选中状态
        void onSuccess();
        void onCancle();

    }
}
