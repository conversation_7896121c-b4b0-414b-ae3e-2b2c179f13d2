import UIKit
import HandyJSON
import SwiftyJSON
class UrlProtocolResolver: NSObject {
    deinit {}
    
    /// 打开小程序
    /// - Parameter path: 路径
    static func openUniMP(extraData: [String: Any], appId: String)  {
        let configuration = DCUniMPConfiguration()
        configuration.openMode = .push
        configuration.enableGestureClose = false
        configuration.showAnimated = true
        configuration.hideAnimated = false
        configuration.extraData = extraData
        DCUniMPSDKEngine.setCapsuleButtonHidden(true)
        if appId.isEmpty {
            DCUniMPSDKEngine.openUniMP(APPID, configuration: configuration) { instance, error in }
            return
        }
        DCUniMPSDKEngine.openUniMP(appId, configuration: configuration) { instance, error in }
    }
    
    /// 打开小程序
    /// - Parameter path: 路径
    static func openUniMP(path: String,appId:String) {
        let configuration = DCUniMPConfiguration()
        configuration.openMode = .push
        configuration.enableGestureClose = false
        configuration.showAnimated = true
        configuration.hideAnimated = false
        configuration.path = path
        DCUniMPSDKEngine.setCapsuleButtonHidden(true)
        if appId.isEmpty {
            DCUniMPSDKEngine.openUniMP(APPID, configuration: configuration) { instance, error in }
            return
        }
        DCUniMPSDKEngine.openUniMP(appId, configuration: configuration) { instance, error in }
    }
    
    
    static func closeUniMp() {
        DCUniMPSDKEngine.closeUniMP()
    }
    
    // 打开设置页面
    static func openNetwork() {
        if let url = URL(string: UIApplication.openSettingsURLString), UIApplication.shared.canOpenURL(url) {
            if #available(iOS 10.0, *) {
                UIApplication.shared.open(url, options: [:], completionHandler: nil)
            } else {
                UIApplication.shared.openURL(url) // iOS 10 以下版本兼容
            }
        }
    }
    
    // 打开鼎学能微信小程序
    static func openWechatMiniProgram(path: String) {
        if let data = path.toDictionary(){
            let result = dictionaryToQueryString(data)
            let req = WXLaunchMiniProgramReq()
            req.userName = SDKConfig.dxnId
            req.path = "?" + result
            req.extMsg = "?" + result
            req.miniProgramType = .release
            WXApi.send(req)
        }
     
    }
    
    static func dictionaryToQueryString(_ dictionary: [String: Any]) -> String {
        var components: [String] = []
        for (key, value) in dictionary {
            let stringValue = convertValueToString(value)
            components.append("\(key)=\(stringValue)")
        }
        return components.joined(separator: "&")
    }

    static func convertValueToString(_ value: Any) -> String {
        switch value {
        case let string as String:
            return string
        case let int as Int:
            return String(int)
        case let bool as Bool:
            return bool ? "true" : "false"
        case let double as Double:
            return String(double)
        case let float as Float:
            return String(float)
        default:
            return "\(value)"
        }
    }
    
    /// 分享
    static func shareWechat(_ data: [String: Any], isMiniProgram: Bool = true)  {
        UrlProtocolResolver.closeUniMp()
        if let model = ShareModel.deserialize(from: data) {
            let webPageUrl = sharePath + model.path
            // 检查用户是否安装微信
            if WXApi.isWXAppInstalled() {
                if isMiniProgram {
                    LShareManger.manager.shareMiniProgram(to: .wechatSession,
                                                          title: model.title,
                                                          description: DeviceInfo.getAppName(),
                                                          thumImage: model.logo == "" ?  model.imageUrl : model.logo,
                                                          webPageUrl: webPageUrl,
                                                          userName: model.miniProgramId,
                                                          path: model.path);
                }else{
                    LShareManger.manager.shareWebPage(to: .wechatSession, title: model.title, description: DeviceInfo.getAppName(), thumImage:  model.imageUrl == "" ?  UIImage.x_imageNamed(named: "ic_share_default") : model.imageUrl, webPageUrl: webPageUrl);
                }
            }else{
                if isMiniProgram {
                    appw?.l_showToastMessage(with: "请先安装微信")
                }else {
                    appDelegate.flutterChannelManager?.callMethod(MethodNames.initWeChatUnInstall.rawValue, args: nil)
                }
                
            }
           
        }
    }
    
    static func contactCustomerService()  {
        let source = QYSource()
        source.title = "在线客服"
        if let sessionViewController = QYSDK.shared().sessionViewController() {
            sessionViewController.sessionTitle = "在线客服"
            sessionViewController.source = source
            sessionViewController.navigationItem.leftBarButtonItem = UIBarButtonItem(image: R.image.icon_back(), style: .plain, target: self, action: #selector(handleBackAction))
            currentViewController()?.presentTo(BaseNavViewController(rootViewController: sessionViewController))
        }
    
    }
    
    /// 检查运行目录是否存在应用资源，不存在将应用资源部署到运行目录
    static func checkUniMPResoutce(){
        let wgtPath = Bundle.main.path(forResource: APPID, ofType: "wgt") ?? ""
        if DCUniMPSDKEngine.isExistsUniMP(APPID) {
            let _ = DCUniMPSDKEngine.getUniMPVersionInfo(withAppid: APPID)!
        } else {
            do {
                try DCUniMPSDKEngine.installUniMPResource(withAppid: APPID, resourceFilePath: wgtPath, password: nil)
                let _ = DCUniMPSDKEngine.getUniMPVersionInfo(withAppid: APPID)!
            } catch _  as NSError {
            }
        }
    }
    
    
    // MARK: - 返回事件处理

    @objc static func handleBackAction() {
        currentViewController()?.dismiss()
    }

    
}
