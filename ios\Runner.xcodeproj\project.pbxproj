// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		1498D2341E8E89220040F4C2 /* GeneratedPluginRegistrant.m in Sources */ = {isa = PBXBuildFile; fileRef = 1498D2331E8E89220040F4C2 /* GeneratedPluginRegistrant.m */; };
		331C808B294A63AB00263BE5 /* RunnerTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 331C807B294A618700263BE5 /* RunnerTests.swift */; };
		3B3967161E833CAA004F5970 /* AppFrameworkInfo.plist in Resources */ = {isa = PBXBuildFile; fileRef = 3B3967151E833CAA004F5970 /* AppFrameworkInfo.plist */; };
		4BB527902E7412510074D096 /* __UNI__D7DA9FC.wgt in Resources */ = {isa = PBXBuildFile; fileRef = 4BB5278F2E7412510074D096 /* __UNI__D7DA9FC.wgt */; };
		4BC64E9D2E575E3E0030CCFB /* TencentMeetingSDK.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 4BC64E9C2E575E3E0030CCFB /* TencentMeetingSDK.framework */; };
		74858FAF1ED2DC5600515810 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 74858FAE1ED2DC5600515810 /* AppDelegate.swift */; };
		7CFBE58170A3584E6B99355C /* Pods_Runner.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0E4AB924309D0D35F02DDCEB /* Pods_Runner.framework */; };
		8E1DE7822DB7634000183452 /* AppDelegate + WXApi.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E1DE7812DB7634000183452 /* AppDelegate + WXApi.swift */; };
		8E1DE7832DB7634000183452 /* AppDelegate + UniMP .swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E1DE7802DB7634000183452 /* AppDelegate + UniMP .swift */; };
		8E1E16DC2D71CBBB001F416F /* LShareManger.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E1E16DB2D71CBBB001F416F /* LShareManger.swift */; };
		8E1E16E02D71E188001F416F /* ShareModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E1E16DF2D71E188001F416F /* ShareModel.swift */; };
		8E296BC22D59E29000AE45BE /* FlutterChannelManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E296BC12D59E29000AE45BE /* FlutterChannelManager.swift */; };
		8E3022FD2D56EE9800FF6319 /* liblibIO.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 8E3022F82D56EE9800FF6319 /* liblibIO.a */; };
		8E3023072D56EEB100FF6319 /* UIImage+Extension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E3023012D56EEB100FF6319 /* UIImage+Extension.swift */; };
		8E3023082D56EEB100FF6319 /* ViewController+Extension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E3023052D56EEB100FF6319 /* ViewController+Extension.swift */; };
		8E30230A2D56EEB100FF6319 /* Color+Extension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E3022FE2D56EEB100FF6319 /* Color+Extension.swift */; };
		8E30230B2D56EEB100FF6319 /* UIKit+Extension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E3023032D56EEB100FF6319 /* UIKit+Extension.swift */; };
		8E30230C2D56EEB100FF6319 /* UIView-Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E3023042D56EEB100FF6319 /* UIView-Extensions.swift */; };
		8E30230E2D56EEB100FF6319 /* UIImageView+Extension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E3023022D56EEB100FF6319 /* UIImageView+Extension.swift */; };
		8E3023482D56EEBC00FF6319 /* UrlProtocolResolver.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E3023292D56EEBC00FF6319 /* UrlProtocolResolver.swift */; };
		8E3023822D56EEEC00FF6319 /* SplashView.xib in Resources */ = {isa = PBXBuildFile; fileRef = 8E3023702D56EEEC00FF6319 /* SplashView.xib */; };
		8E3023842D56EEEC00FF6319 /* BaseTableView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E30236D2D56EEEC00FF6319 /* BaseTableView.swift */; };
		8E3023852D56EEEC00FF6319 /* Enum.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E3023782D56EEEC00FF6319 /* Enum.swift */; };
		8E3023872D56EEEC00FF6319 /* Constant.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E3023762D56EEEC00FF6319 /* Constant.swift */; };
		8E30238A2D56EEEC00FF6319 /* LSize.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E3023792D56EEEC00FF6319 /* LSize.swift */; };
		8E30238B2D56EEEC00FF6319 /* DeviceInfo.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E3023772D56EEEC00FF6319 /* DeviceInfo.swift */; };
		8E30238C2D56EEEC00FF6319 /* BaseViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E3023662D56EEEC00FF6319 /* BaseViewController.swift */; };
		8E30238E2D56EEEC00FF6319 /* BaseTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E30236E2D56EEEC00FF6319 /* BaseTableViewCell.swift */; };
		8E30238F2D56EEEC00FF6319 /* BaseNavViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E3023642D56EEEC00FF6319 /* BaseNavViewController.swift */; };
		8E3023902D56EEEC00FF6319 /* SDKConfig.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E30237B2D56EEEC00FF6319 /* SDKConfig.swift */; };
		8E3023912D56EEEC00FF6319 /* Config.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E3023752D56EEEC00FF6319 /* Config.swift */; };
		8E3023932D56EEEC00FF6319 /* BaseViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E3023722D56EEEC00FF6319 /* BaseViewModel.swift */; };
		8E3023942D56EEEC00FF6319 /* BaseCommonController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E3023622D56EEEC00FF6319 /* BaseCommonController.swift */; };
		8E3023962D56EEEC00FF6319 /* SplashView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E30236F2D56EEEC00FF6319 /* SplashView.swift */; };
		8E3023982D56EEEC00FF6319 /* BaseModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E3023682D56EEEC00FF6319 /* BaseModel.swift */; };
		8E3023992D56EEEC00FF6319 /* SMFont.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E30237C2D56EEEC00FF6319 /* SMFont.swift */; };
		8E30239B2D56EEEC00FF6319 /* BaseCollectionCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E30236A2D56EEEC00FF6319 /* BaseCollectionCell.swift */; };
		8E3023A12D56EF0300FF6319 /* R.generated.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E30239E2D56EF0300FF6319 /* R.generated.swift */; };
		8E3024532D56F13C00FF6319 /* Localizable.strings in Resources */ = {isa = PBXBuildFile; fileRef = 8E3024512D56F13C00FF6319 /* Localizable.strings */; };
		8E4B86812DE09525002809C6 /* AppDelegate + Push.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E4B86802DE09525002809C6 /* AppDelegate + Push.swift */; };
		8E50F6242D72DCCA0095D0C6 /* PayParam.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E50F6232D72DCCA0095D0C6 /* PayParam.swift */; };
		8E50F6282D72DECA0095D0C6 /* CurriculumParam.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E50F6272D72DECA0095D0C6 /* CurriculumParam.swift */; };
		8E50F62D2D72F4AD0095D0C6 /* MiniProgramBridge.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E50F62C2D72F4AD0095D0C6 /* MiniProgramBridge.swift */; };
		8E6171C62DB65E0A00CD19D8 /* EnterMeetingController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E6171A62DB65E0A00CD19D8 /* EnterMeetingController.swift */; };
		8E6171CD2DB65E0A00CD19D8 /* PermissionDetectionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E6171A82DB65E0A00CD19D8 /* PermissionDetectionView.swift */; };
		8E706CE02D76E7FD00284B85 /* QYParam.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E706CDF2D76E7FD00284B85 /* QYParam.swift */; };
		8E771E3C2D632B0D00389265 /* ZXUniModule.m in Sources */ = {isa = PBXBuildFile; fileRef = 8E771E3B2D632B0D00389265 /* ZXUniModule.m */; };
		8E771E432D63320000389265 /* PayModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E771E422D63320000389265 /* PayModel.swift */; };
		8E7E14652DA70BB2005A2922 /* liblibCamera.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 8E7E14632DA70BB2005A2922 /* liblibCamera.a */; };
		8E7E146C2DA70C0B005A2922 /* AssetsLibrary.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8E7E146B2DA70C0B005A2922 /* AssetsLibrary.framework */; };
		8E7E146E2DA70C1D005A2922 /* Photos.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8E7E146D2DA70C1D005A2922 /* Photos.framework */; };
		8E7E14702DA70C34005A2922 /* CoreMedia.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8E7E146F2DA70C34005A2922 /* CoreMedia.framework */; };
		8E7E14722DA70C3F005A2922 /* MetalKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8E7E14712DA70C3F005A2922 /* MetalKit.framework */; };
		8E7E14742DA70C48005A2922 /* GLKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8E7E14732DA70C48005A2922 /* GLKit.framework */; };
		8E7E14792DA72A8C005A2922 /* DCTZImagePickerController.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 8E7E14772DA72A8C005A2922 /* DCTZImagePickerController.bundle */; };
		8E7E147A2DA72A8C005A2922 /* DCMediaEditingController.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 8E7E14762DA72A8C005A2922 /* DCMediaEditingController.bundle */; };
		8EB94A0F2DE5ABD60056EFD9 /* SuperUIColorExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8EB94A0E2DE5ABD60056EFD9 /* SuperUIColorExtension.swift */; };
		930B1B6840DCBE4CEB1F3A39 /* Pods_RunnerTests.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FBE15092C9EA8C6B8398C1DD /* Pods_RunnerTests.framework */; };
		97C146FE1CF9000F007C117D /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FD1CF9000F007C117D /* Assets.xcassets */; };
		97C147011CF9000F007C117D /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FF1CF9000F007C117D /* LaunchScreen.storyboard */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		331C8085294A63A400263BE5 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 97C146E61CF9000F007C117D /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 97C146ED1CF9000F007C117D;
			remoteInfo = Runner;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		0E4AB924309D0D35F02DDCEB /* Pods_Runner.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_Runner.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		1498D2321E8E86230040F4C2 /* GeneratedPluginRegistrant.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = GeneratedPluginRegistrant.h; sourceTree = "<group>"; };
		1498D2331E8E89220040F4C2 /* GeneratedPluginRegistrant.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GeneratedPluginRegistrant.m; sourceTree = "<group>"; };
		1779F0D49B74432152F9AB98 /* Pods-RunnerTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.release.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.release.xcconfig"; sourceTree = "<group>"; };
		331C807B294A618700263BE5 /* RunnerTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RunnerTests.swift; sourceTree = "<group>"; };
		331C8081294A63A400263BE5 /* RunnerTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = RunnerTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		3B3967151E833CAA004F5970 /* AppFrameworkInfo.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = AppFrameworkInfo.plist; path = Flutter/AppFrameworkInfo.plist; sourceTree = "<group>"; };
		3F90DED03783C251669466C5 /* Pods-Runner.profile.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.profile.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.profile.xcconfig"; sourceTree = "<group>"; };
		4BB5278F2E7412510074D096 /* __UNI__D7DA9FC.wgt */ = {isa = PBXFileReference; lastKnownFileType = file; path = __UNI__D7DA9FC.wgt; sourceTree = "<group>"; };
		4BC64E9C2E575E3E0030CCFB /* TencentMeetingSDK.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = TencentMeetingSDK.framework; sourceTree = "<group>"; };
		74858FAD1ED2DC5600515810 /* Runner-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "Runner-Bridging-Header.h"; sourceTree = "<group>"; };
		74858FAE1ED2DC5600515810 /* AppDelegate.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		7AFA3C8E1D35360C0083082E /* Release.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; name = Release.xcconfig; path = Flutter/Release.xcconfig; sourceTree = "<group>"; };
		8A6C7888DF326F819BDB7C5A /* Pods-RunnerTests.profile.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.profile.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.profile.xcconfig"; sourceTree = "<group>"; };
		8E1DE7802DB7634000183452 /* AppDelegate + UniMP .swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "AppDelegate + UniMP .swift"; sourceTree = "<group>"; };
		8E1DE7812DB7634000183452 /* AppDelegate + WXApi.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "AppDelegate + WXApi.swift"; sourceTree = "<group>"; };
		8E1E16DB2D71CBBB001F416F /* LShareManger.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LShareManger.swift; sourceTree = "<group>"; };
		8E1E16DF2D71E188001F416F /* ShareModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ShareModel.swift; sourceTree = "<group>"; };
		8E296BC12D59E29000AE45BE /* FlutterChannelManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FlutterChannelManager.swift; sourceTree = "<group>"; };
		8E3022F82D56EE9800FF6319 /* liblibIO.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = liblibIO.a; sourceTree = "<group>"; };
		8E3022FE2D56EEB100FF6319 /* Color+Extension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Color+Extension.swift"; sourceTree = "<group>"; };
		8E3023012D56EEB100FF6319 /* UIImage+Extension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "UIImage+Extension.swift"; sourceTree = "<group>"; };
		8E3023022D56EEB100FF6319 /* UIImageView+Extension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "UIImageView+Extension.swift"; sourceTree = "<group>"; };
		8E3023032D56EEB100FF6319 /* UIKit+Extension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "UIKit+Extension.swift"; sourceTree = "<group>"; };
		8E3023042D56EEB100FF6319 /* UIView-Extensions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "UIView-Extensions.swift"; sourceTree = "<group>"; };
		8E3023052D56EEB100FF6319 /* ViewController+Extension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "ViewController+Extension.swift"; sourceTree = "<group>"; };
		8E3023292D56EEBC00FF6319 /* UrlProtocolResolver.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UrlProtocolResolver.swift; sourceTree = "<group>"; };
		8E3023622D56EEEC00FF6319 /* BaseCommonController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BaseCommonController.swift; sourceTree = "<group>"; };
		8E3023642D56EEEC00FF6319 /* BaseNavViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BaseNavViewController.swift; sourceTree = "<group>"; };
		8E3023662D56EEEC00FF6319 /* BaseViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BaseViewController.swift; sourceTree = "<group>"; };
		8E3023682D56EEEC00FF6319 /* BaseModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BaseModel.swift; sourceTree = "<group>"; };
		8E30236A2D56EEEC00FF6319 /* BaseCollectionCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BaseCollectionCell.swift; sourceTree = "<group>"; };
		8E30236D2D56EEEC00FF6319 /* BaseTableView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BaseTableView.swift; sourceTree = "<group>"; };
		8E30236E2D56EEEC00FF6319 /* BaseTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BaseTableViewCell.swift; sourceTree = "<group>"; };
		8E30236F2D56EEEC00FF6319 /* SplashView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SplashView.swift; sourceTree = "<group>"; };
		8E3023702D56EEEC00FF6319 /* SplashView.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = SplashView.xib; sourceTree = "<group>"; };
		8E3023722D56EEEC00FF6319 /* BaseViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BaseViewModel.swift; sourceTree = "<group>"; };
		8E3023752D56EEEC00FF6319 /* Config.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Config.swift; sourceTree = "<group>"; };
		8E3023762D56EEEC00FF6319 /* Constant.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Constant.swift; sourceTree = "<group>"; };
		8E3023772D56EEEC00FF6319 /* DeviceInfo.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DeviceInfo.swift; sourceTree = "<group>"; };
		8E3023782D56EEEC00FF6319 /* Enum.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Enum.swift; sourceTree = "<group>"; };
		8E3023792D56EEEC00FF6319 /* LSize.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LSize.swift; sourceTree = "<group>"; };
		8E30237B2D56EEEC00FF6319 /* SDKConfig.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SDKConfig.swift; sourceTree = "<group>"; };
		8E30237C2D56EEEC00FF6319 /* SMFont.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SMFont.swift; sourceTree = "<group>"; };
		8E30239E2D56EF0300FF6319 /* R.generated.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = R.generated.swift; sourceTree = "<group>"; };
		8E3024522D56F13C00FF6319 /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/Localizable.strings; sourceTree = "<group>"; };
		8E3024542D56F1E300FF6319 /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/Localizable.strings"; sourceTree = "<group>"; };
		8E3262682D6C59F9000DCC56 /* Runner.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = Runner.entitlements; sourceTree = "<group>"; };
		8E3F4AEF2D576BED00217B2D /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		8E4B866B2DE08B0E002809C6 /* __UNI__A6C281A.wgt */ = {isa = PBXFileReference; lastKnownFileType = file; path = __UNI__A6C281A.wgt; sourceTree = "<group>"; };
		8E4B86802DE09525002809C6 /* AppDelegate + Push.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "AppDelegate + Push.swift"; sourceTree = "<group>"; };
		8E50F6232D72DCCA0095D0C6 /* PayParam.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PayParam.swift; sourceTree = "<group>"; };
		8E50F6272D72DECA0095D0C6 /* CurriculumParam.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CurriculumParam.swift; sourceTree = "<group>"; };
		8E50F62C2D72F4AD0095D0C6 /* MiniProgramBridge.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MiniProgramBridge.swift; sourceTree = "<group>"; };
		8E6171A62DB65E0A00CD19D8 /* EnterMeetingController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EnterMeetingController.swift; sourceTree = "<group>"; };
		8E6171A82DB65E0A00CD19D8 /* PermissionDetectionView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PermissionDetectionView.swift; sourceTree = "<group>"; };
		8E6171D32DB6632500CD19D8 /* RunnerDebug.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = RunnerDebug.entitlements; sourceTree = "<group>"; };
		8E6565652D642EF800466B72 /* RunnerProfile.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = RunnerProfile.entitlements; sourceTree = "<group>"; };
		8E706CDF2D76E7FD00284B85 /* QYParam.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = QYParam.swift; sourceTree = "<group>"; };
		8E771E3A2D632B0D00389265 /* ZXUniModule.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ZXUniModule.h; sourceTree = "<group>"; };
		8E771E3B2D632B0D00389265 /* ZXUniModule.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ZXUniModule.m; sourceTree = "<group>"; };
		8E771E422D63320000389265 /* PayModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PayModel.swift; sourceTree = "<group>"; };
		8E7E14632DA70BB2005A2922 /* liblibCamera.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = liblibCamera.a; sourceTree = "<group>"; };
		8E7E146B2DA70C0B005A2922 /* AssetsLibrary.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AssetsLibrary.framework; path = System/Library/Frameworks/AssetsLibrary.framework; sourceTree = SDKROOT; };
		8E7E146D2DA70C1D005A2922 /* Photos.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Photos.framework; path = System/Library/Frameworks/Photos.framework; sourceTree = SDKROOT; };
		8E7E146F2DA70C34005A2922 /* CoreMedia.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreMedia.framework; path = System/Library/Frameworks/CoreMedia.framework; sourceTree = SDKROOT; };
		8E7E14712DA70C3F005A2922 /* MetalKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MetalKit.framework; path = System/Library/Frameworks/MetalKit.framework; sourceTree = SDKROOT; };
		8E7E14732DA70C48005A2922 /* GLKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = GLKit.framework; path = System/Library/Frameworks/GLKit.framework; sourceTree = SDKROOT; };
		8E7E14762DA72A8C005A2922 /* DCMediaEditingController.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = DCMediaEditingController.bundle; sourceTree = "<group>"; };
		8E7E14772DA72A8C005A2922 /* DCTZImagePickerController.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = DCTZImagePickerController.bundle; sourceTree = "<group>"; };
		8E9A1F1B2DDD808A006B5DC4 /* __UNI__A6C281A.wgt */ = {isa = PBXFileReference; lastKnownFileType = file; path = __UNI__A6C281A.wgt; sourceTree = "<group>"; };
		8EB94A0E2DE5ABD60056EFD9 /* SuperUIColorExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SuperUIColorExtension.swift; sourceTree = "<group>"; };
		9740EEB21CF90195004384FC /* Debug.xcconfig */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xcconfig; name = Debug.xcconfig; path = Flutter/Debug.xcconfig; sourceTree = "<group>"; };
		9740EEB31CF90195004384FC /* Generated.xcconfig */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xcconfig; name = Generated.xcconfig; path = Flutter/Generated.xcconfig; sourceTree = "<group>"; };
		97C146EE1CF9000F007C117D /* Runner.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Runner.app; sourceTree = BUILT_PRODUCTS_DIR; };
		97C146FD1CF9000F007C117D /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		97C147001CF9000F007C117D /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		E2F56FC164F0509A9648B4E1 /* Pods-Runner.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.release.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.release.xcconfig"; sourceTree = "<group>"; };
		EDE9B144DDB75AE42516D1C4 /* Pods-RunnerTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.debug.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.debug.xcconfig"; sourceTree = "<group>"; };
		F198A9410B31BC86FB2E4C2F /* Pods-Runner.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.debug.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.debug.xcconfig"; sourceTree = "<group>"; };
		FBE15092C9EA8C6B8398C1DD /* Pods_RunnerTests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_RunnerTests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		5AEC1C7F33D45DACF507C2E5 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				930B1B6840DCBE4CEB1F3A39 /* Pods_RunnerTests.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		97C146EB1CF9000F007C117D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8E7E14742DA70C48005A2922 /* GLKit.framework in Frameworks */,
				4BC64E9D2E575E3E0030CCFB /* TencentMeetingSDK.framework in Frameworks */,
				8E7E14722DA70C3F005A2922 /* MetalKit.framework in Frameworks */,
				8E7E14702DA70C34005A2922 /* CoreMedia.framework in Frameworks */,
				8E7E146E2DA70C1D005A2922 /* Photos.framework in Frameworks */,
				8E7E146C2DA70C0B005A2922 /* AssetsLibrary.framework in Frameworks */,
				8E3022FD2D56EE9800FF6319 /* liblibIO.a in Frameworks */,
				8E7E14652DA70BB2005A2922 /* liblibCamera.a in Frameworks */,
				7CFBE58170A3584E6B99355C /* Pods_Runner.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		331C8082294A63A400263BE5 /* RunnerTests */ = {
			isa = PBXGroup;
			children = (
				331C807B294A618700263BE5 /* RunnerTests.swift */,
			);
			path = RunnerTests;
			sourceTree = "<group>";
		};
		80F5943DC9F9E118851C5E3D /* Pods */ = {
			isa = PBXGroup;
			children = (
				F198A9410B31BC86FB2E4C2F /* Pods-Runner.debug.xcconfig */,
				E2F56FC164F0509A9648B4E1 /* Pods-Runner.release.xcconfig */,
				3F90DED03783C251669466C5 /* Pods-Runner.profile.xcconfig */,
				EDE9B144DDB75AE42516D1C4 /* Pods-RunnerTests.debug.xcconfig */,
				1779F0D49B74432152F9AB98 /* Pods-RunnerTests.release.xcconfig */,
				8A6C7888DF326F819BDB7C5A /* Pods-RunnerTests.profile.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		8E1DE77F2DB7630F00183452 /* AppDelegate */ = {
			isa = PBXGroup;
			children = (
				8E4B86802DE09525002809C6 /* AppDelegate + Push.swift */,
				8E1DE7812DB7634000183452 /* AppDelegate + WXApi.swift */,
				8E1DE7802DB7634000183452 /* AppDelegate + UniMP .swift */,
			);
			path = AppDelegate;
			sourceTree = "<group>";
		};
		8E296BC02D59E26D00AE45BE /* FlutterChannelManager */ = {
			isa = PBXGroup;
			children = (
				8E706CDE2D76E7D300284B85 /* QY */,
				8E50F6262D72DEA50095D0C6 /* Curriculum */,
				8E296BC12D59E29000AE45BE /* FlutterChannelManager.swift */,
			);
			path = FlutterChannelManager;
			sourceTree = "<group>";
		};
		8E3022F72D56EE9800FF6319 /* Apps */ = {
			isa = PBXGroup;
			children = (
				4BB5278F2E7412510074D096 /* __UNI__D7DA9FC.wgt */,
			);
			path = Apps;
			sourceTree = "<group>";
		};
		8E3022F92D56EE9800FF6319 /* Libs */ = {
			isa = PBXGroup;
			children = (
				8E3022F82D56EE9800FF6319 /* liblibIO.a */,
			);
			path = Libs;
			sourceTree = "<group>";
		};
		8E3022FA2D56EE9800FF6319 /* File */ = {
			isa = PBXGroup;
			children = (
				8E3022F92D56EE9800FF6319 /* Libs */,
			);
			path = File;
			sourceTree = "<group>";
		};
		8E3022FB2D56EE9800FF6319 /* UniMPSDK */ = {
			isa = PBXGroup;
			children = (
				8E7E14752DA72A64005A2922 /* Features */,
				8E7E14642DA70BB2005A2922 /* Lib */,
				8E3022F72D56EE9800FF6319 /* Apps */,
				8E3022FA2D56EE9800FF6319 /* File */,
			);
			path = UniMPSDK;
			sourceTree = "<group>";
		};
		8E3023062D56EEB100FF6319 /* Extension */ = {
			isa = PBXGroup;
			children = (
				8E3022FE2D56EEB100FF6319 /* Color+Extension.swift */,
				8E3023012D56EEB100FF6319 /* UIImage+Extension.swift */,
				8E3023022D56EEB100FF6319 /* UIImageView+Extension.swift */,
				8E3023032D56EEB100FF6319 /* UIKit+Extension.swift */,
				8E3023042D56EEB100FF6319 /* UIView-Extensions.swift */,
				8E3023052D56EEB100FF6319 /* ViewController+Extension.swift */,
			);
			path = Extension;
			sourceTree = "<group>";
		};
		8E30232A2D56EEBC00FF6319 /* Methods */ = {
			isa = PBXGroup;
			children = (
				8E1E16DB2D71CBBB001F416F /* LShareManger.swift */,
				8E3023292D56EEBC00FF6319 /* UrlProtocolResolver.swift */,
			);
			path = Methods;
			sourceTree = "<group>";
		};
		8E30233B2D56EEBC00FF6319 /* Manger */ = {
			isa = PBXGroup;
			children = (
				8E30232A2D56EEBC00FF6319 /* Methods */,
			);
			path = Manger;
			sourceTree = "<group>";
		};
		8E3023672D56EEEC00FF6319 /* BaseController */ = {
			isa = PBXGroup;
			children = (
				8E3023622D56EEEC00FF6319 /* BaseCommonController.swift */,
				8E3023642D56EEEC00FF6319 /* BaseNavViewController.swift */,
				8E3023662D56EEEC00FF6319 /* BaseViewController.swift */,
			);
			path = BaseController;
			sourceTree = "<group>";
		};
		8E3023692D56EEEC00FF6319 /* BaseModel */ = {
			isa = PBXGroup;
			children = (
				8E3023682D56EEEC00FF6319 /* BaseModel.swift */,
			);
			path = BaseModel;
			sourceTree = "<group>";
		};
		8E3023712D56EEEC00FF6319 /* BaseView */ = {
			isa = PBXGroup;
			children = (
				8E30236A2D56EEEC00FF6319 /* BaseCollectionCell.swift */,
				8E30236D2D56EEEC00FF6319 /* BaseTableView.swift */,
				8E30236E2D56EEEC00FF6319 /* BaseTableViewCell.swift */,
				8E30236F2D56EEEC00FF6319 /* SplashView.swift */,
				8E3023702D56EEEC00FF6319 /* SplashView.xib */,
			);
			path = BaseView;
			sourceTree = "<group>";
		};
		8E3023732D56EEEC00FF6319 /* BaseViewModel */ = {
			isa = PBXGroup;
			children = (
				8E3023722D56EEEC00FF6319 /* BaseViewModel.swift */,
			);
			path = BaseViewModel;
			sourceTree = "<group>";
		};
		8E3023742D56EEEC00FF6319 /* BaseClass */ = {
			isa = PBXGroup;
			children = (
				8E3023672D56EEEC00FF6319 /* BaseController */,
				8E3023692D56EEEC00FF6319 /* BaseModel */,
				8E3023712D56EEEC00FF6319 /* BaseView */,
				8E3023732D56EEEC00FF6319 /* BaseViewModel */,
			);
			path = BaseClass;
			sourceTree = "<group>";
		};
		8E3023802D56EEEC00FF6319 /* Utils */ = {
			isa = PBXGroup;
			children = (
				8E3023752D56EEEC00FF6319 /* Config.swift */,
				8E3023762D56EEEC00FF6319 /* Constant.swift */,
				8E3023772D56EEEC00FF6319 /* DeviceInfo.swift */,
				8E3023782D56EEEC00FF6319 /* Enum.swift */,
				8E3023792D56EEEC00FF6319 /* LSize.swift */,
				8E30237B2D56EEEC00FF6319 /* SDKConfig.swift */,
				8E30237C2D56EEEC00FF6319 /* SMFont.swift */,
				8EB94A0E2DE5ABD60056EFD9 /* SuperUIColorExtension.swift */,
			);
			path = Utils;
			sourceTree = "<group>";
		};
		8E3023812D56EEEC00FF6319 /* Helps */ = {
			isa = PBXGroup;
			children = (
				8E3023802D56EEEC00FF6319 /* Utils */,
				8E3023742D56EEEC00FF6319 /* BaseClass */,
			);
			path = Helps;
			sourceTree = "<group>";
		};
		8E30239F2D56EF0300FF6319 /* Resources */ = {
			isa = PBXGroup;
			children = (
				8E30239E2D56EF0300FF6319 /* R.generated.swift */,
			);
			path = Resources;
			sourceTree = "<group>";
		};
		8E44E2492D5EDA4E0066078C /* UniMPModule */ = {
			isa = PBXGroup;
			children = (
				4BC64E9C2E575E3E0030CCFB /* TencentMeetingSDK.framework */,
				8E50F62B2D72F48D0095D0C6 /* MiniProgramBridge */,
				8E50F6252D72DD3E0095D0C6 /* Share */,
				8E50F6222D72DA050095D0C6 /* Pay */,
				8E771E3A2D632B0D00389265 /* ZXUniModule.h */,
				8E771E3B2D632B0D00389265 /* ZXUniModule.m */,
			);
			path = UniMPModule;
			sourceTree = "<group>";
		};
		8E50F6222D72DA050095D0C6 /* Pay */ = {
			isa = PBXGroup;
			children = (
				8E771E422D63320000389265 /* PayModel.swift */,
				8E50F6232D72DCCA0095D0C6 /* PayParam.swift */,
			);
			path = Pay;
			sourceTree = "<group>";
		};
		8E50F6252D72DD3E0095D0C6 /* Share */ = {
			isa = PBXGroup;
			children = (
				8E1E16DF2D71E188001F416F /* ShareModel.swift */,
			);
			path = Share;
			sourceTree = "<group>";
		};
		8E50F6262D72DEA50095D0C6 /* Curriculum */ = {
			isa = PBXGroup;
			children = (
				8E50F6272D72DECA0095D0C6 /* CurriculumParam.swift */,
			);
			path = Curriculum;
			sourceTree = "<group>";
		};
		8E50F62B2D72F48D0095D0C6 /* MiniProgramBridge */ = {
			isa = PBXGroup;
			children = (
				8E50F62C2D72F4AD0095D0C6 /* MiniProgramBridge.swift */,
			);
			path = MiniProgramBridge;
			sourceTree = "<group>";
		};
		8E6171A72DB65E0A00CD19D8 /* Controller */ = {
			isa = PBXGroup;
			children = (
				8E6171A62DB65E0A00CD19D8 /* EnterMeetingController.swift */,
			);
			path = Controller;
			sourceTree = "<group>";
		};
		8E6171A92DB65E0A00CD19D8 /* View */ = {
			isa = PBXGroup;
			children = (
				8E6171A82DB65E0A00CD19D8 /* PermissionDetectionView.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		8E6171AC2DB65E0A00CD19D8 /* Metting */ = {
			isa = PBXGroup;
			children = (
				8E6171A72DB65E0A00CD19D8 /* Controller */,
				8E6171A92DB65E0A00CD19D8 /* View */,
			);
			path = Metting;
			sourceTree = "<group>";
		};
		8E6171AD2DB65E0A00CD19D8 /* MainClass */ = {
			isa = PBXGroup;
			children = (
				8E6171AC2DB65E0A00CD19D8 /* Metting */,
			);
			path = MainClass;
			sourceTree = "<group>";
		};
		8E706CDE2D76E7D300284B85 /* QY */ = {
			isa = PBXGroup;
			children = (
				8E706CDF2D76E7FD00284B85 /* QYParam.swift */,
			);
			path = QY;
			sourceTree = "<group>";
		};
		8E7E14642DA70BB2005A2922 /* Lib */ = {
			isa = PBXGroup;
			children = (
				8E7E14632DA70BB2005A2922 /* liblibCamera.a */,
			);
			path = Lib;
			sourceTree = "<group>";
		};
		8E7E14752DA72A64005A2922 /* Features */ = {
			isa = PBXGroup;
			children = (
				8E7E14782DA72A8C005A2922 /* Resources */,
			);
			path = Features;
			sourceTree = "<group>";
		};
		8E7E14782DA72A8C005A2922 /* Resources */ = {
			isa = PBXGroup;
			children = (
				8E7E14762DA72A8C005A2922 /* DCMediaEditingController.bundle */,
				8E7E14772DA72A8C005A2922 /* DCTZImagePickerController.bundle */,
			);
			path = Resources;
			sourceTree = "<group>";
		};
		8EC71BC12DE40BE40030B42A /* Recovered References */ = {
			isa = PBXGroup;
			children = (
				8E9A1F1B2DDD808A006B5DC4 /* __UNI__A6C281A.wgt */,
				8E4B866B2DE08B0E002809C6 /* __UNI__A6C281A.wgt */,
			);
			name = "Recovered References";
			sourceTree = "<group>";
		};
		9740EEB11CF90186004384FC /* Flutter */ = {
			isa = PBXGroup;
			children = (
				3B3967151E833CAA004F5970 /* AppFrameworkInfo.plist */,
				9740EEB21CF90195004384FC /* Debug.xcconfig */,
				7AFA3C8E1D35360C0083082E /* Release.xcconfig */,
				9740EEB31CF90195004384FC /* Generated.xcconfig */,
			);
			name = Flutter;
			sourceTree = "<group>";
		};
		97C146E51CF9000F007C117D = {
			isa = PBXGroup;
			children = (
				9740EEB11CF90186004384FC /* Flutter */,
				97C146F01CF9000F007C117D /* Runner */,
				97C146EF1CF9000F007C117D /* Products */,
				331C8082294A63A400263BE5 /* RunnerTests */,
				80F5943DC9F9E118851C5E3D /* Pods */,
				FF7AEE895563F6ACD2AEDEBE /* Frameworks */,
				8EC71BC12DE40BE40030B42A /* Recovered References */,
			);
			sourceTree = "<group>";
		};
		97C146EF1CF9000F007C117D /* Products */ = {
			isa = PBXGroup;
			children = (
				97C146EE1CF9000F007C117D /* Runner.app */,
				331C8081294A63A400263BE5 /* RunnerTests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		97C146F01CF9000F007C117D /* Runner */ = {
			isa = PBXGroup;
			children = (
				8E6171D32DB6632500CD19D8 /* RunnerDebug.entitlements */,
				8E3262682D6C59F9000DCC56 /* Runner.entitlements */,
				8E6565652D642EF800466B72 /* RunnerProfile.entitlements */,
				8E3023812D56EEEC00FF6319 /* Helps */,
				8E30233B2D56EEBC00FF6319 /* Manger */,
				8E6171AD2DB65E0A00CD19D8 /* MainClass */,
				8E3023062D56EEB100FF6319 /* Extension */,
				8E30239F2D56EF0300FF6319 /* Resources */,
				8E3022FB2D56EE9800FF6319 /* UniMPSDK */,
				8E44E2492D5EDA4E0066078C /* UniMPModule */,
				8E296BC02D59E26D00AE45BE /* FlutterChannelManager */,
				8E1DE77F2DB7630F00183452 /* AppDelegate */,
				97C146FD1CF9000F007C117D /* Assets.xcassets */,
				97C146FF1CF9000F007C117D /* LaunchScreen.storyboard */,
				1498D2321E8E86230040F4C2 /* GeneratedPluginRegistrant.h */,
				1498D2331E8E89220040F4C2 /* GeneratedPluginRegistrant.m */,
				74858FAE1ED2DC5600515810 /* AppDelegate.swift */,
				74858FAD1ED2DC5600515810 /* Runner-Bridging-Header.h */,
				8E3024512D56F13C00FF6319 /* Localizable.strings */,
				8E3F4AEF2D576BED00217B2D /* Info.plist */,
			);
			path = Runner;
			sourceTree = "<group>";
		};
		FF7AEE895563F6ACD2AEDEBE /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				8E7E14732DA70C48005A2922 /* GLKit.framework */,
				8E7E14712DA70C3F005A2922 /* MetalKit.framework */,
				8E7E146F2DA70C34005A2922 /* CoreMedia.framework */,
				8E7E146D2DA70C1D005A2922 /* Photos.framework */,
				8E7E146B2DA70C0B005A2922 /* AssetsLibrary.framework */,
				0E4AB924309D0D35F02DDCEB /* Pods_Runner.framework */,
				FBE15092C9EA8C6B8398C1DD /* Pods_RunnerTests.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		331C8080294A63A400263BE5 /* RunnerTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 331C8087294A63A400263BE5 /* Build configuration list for PBXNativeTarget "RunnerTests" */;
			buildPhases = (
				6E953BC2C9B144FB27CC39C5 /* [CP] Check Pods Manifest.lock */,
				331C807D294A63A400263BE5 /* Sources */,
				331C807F294A63A400263BE5 /* Resources */,
				5AEC1C7F33D45DACF507C2E5 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				331C8086294A63A400263BE5 /* PBXTargetDependency */,
			);
			name = RunnerTests;
			productName = RunnerTests;
			productReference = 331C8081294A63A400263BE5 /* RunnerTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		97C146ED1CF9000F007C117D /* Runner */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 97C147051CF9000F007C117D /* Build configuration list for PBXNativeTarget "Runner" */;
			buildPhases = (
				CE7F25202142A12E5EA8A18B /* [CP] Check Pods Manifest.lock */,
				9740EEB61CF901F6004384FC /* Run Script */,
				97C146EA1CF9000F007C117D /* Sources */,
				97C146EB1CF9000F007C117D /* Frameworks */,
				97C146EC1CF9000F007C117D /* Resources */,
				3B06AD1E1E4923F5004D2608 /* Thin Binary */,
				D866319D501BCB84B326C9FE /* [CP] Embed Pods Frameworks */,
				50E9A270CB00CFA15AC05BBD /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Runner;
			productName = Runner;
			productReference = 97C146EE1CF9000F007C117D /* Runner.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		97C146E61CF9000F007C117D /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastUpgradeCheck = 1510;
				ORGANIZATIONNAME = "";
				TargetAttributes = {
					331C8080294A63A400263BE5 = {
						CreatedOnToolsVersion = 14.0;
						TestTargetID = 97C146ED1CF9000F007C117D;
					};
					97C146ED1CF9000F007C117D = {
						CreatedOnToolsVersion = 7.3.1;
						LastSwiftMigration = 1100;
					};
				};
			};
			buildConfigurationList = 97C146E91CF9000F007C117D /* Build configuration list for PBXProject "Runner" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				"zh-Hans",
			);
			mainGroup = 97C146E51CF9000F007C117D;
			productRefGroup = 97C146EF1CF9000F007C117D /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				97C146ED1CF9000F007C117D /* Runner */,
				331C8080294A63A400263BE5 /* RunnerTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		331C807F294A63A400263BE5 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		97C146EC1CF9000F007C117D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				97C147011CF9000F007C117D /* LaunchScreen.storyboard in Resources */,
				8E3023822D56EEEC00FF6319 /* SplashView.xib in Resources */,
				3B3967161E833CAA004F5970 /* AppFrameworkInfo.plist in Resources */,
				97C146FE1CF9000F007C117D /* Assets.xcassets in Resources */,
				4BB527902E7412510074D096 /* __UNI__D7DA9FC.wgt in Resources */,
				8E3024532D56F13C00FF6319 /* Localizable.strings in Resources */,
				8E7E14792DA72A8C005A2922 /* DCTZImagePickerController.bundle in Resources */,
				8E7E147A2DA72A8C005A2922 /* DCMediaEditingController.bundle in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		3B06AD1E1E4923F5004D2608 /* Thin Binary */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${TARGET_BUILD_DIR}/${INFOPLIST_PATH}",
			);
			name = "Thin Binary";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "/bin/sh \"$FLUTTER_ROOT/packages/flutter_tools/bin/xcode_backend.sh\" embed_and_thin\n";
		};
		50E9A270CB00CFA15AC05BBD /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		6E953BC2C9B144FB27CC39C5 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-RunnerTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		9740EEB61CF901F6004384FC /* Run Script */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			name = "Run Script";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "/bin/sh \"$FLUTTER_ROOT/packages/flutter_tools/bin/xcode_backend.sh\" build\n";
		};
		CE7F25202142A12E5EA8A18B /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Runner-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		D866319D501BCB84B326C9FE /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		331C807D294A63A400263BE5 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				331C808B294A63AB00263BE5 /* RunnerTests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		97C146EA1CF9000F007C117D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8E3023072D56EEB100FF6319 /* UIImage+Extension.swift in Sources */,
				8E3023082D56EEB100FF6319 /* ViewController+Extension.swift in Sources */,
				8E3023842D56EEEC00FF6319 /* BaseTableView.swift in Sources */,
				8E3023852D56EEEC00FF6319 /* Enum.swift in Sources */,
				8E1DE7822DB7634000183452 /* AppDelegate + WXApi.swift in Sources */,
				8E1DE7832DB7634000183452 /* AppDelegate + UniMP .swift in Sources */,
				8E3023872D56EEEC00FF6319 /* Constant.swift in Sources */,
				8E30238A2D56EEEC00FF6319 /* LSize.swift in Sources */,
				8E30238B2D56EEEC00FF6319 /* DeviceInfo.swift in Sources */,
				8E3023A12D56EF0300FF6319 /* R.generated.swift in Sources */,
				8E30238C2D56EEEC00FF6319 /* BaseViewController.swift in Sources */,
				8E30238E2D56EEEC00FF6319 /* BaseTableViewCell.swift in Sources */,
				8E30238F2D56EEEC00FF6319 /* BaseNavViewController.swift in Sources */,
				8E3023902D56EEEC00FF6319 /* SDKConfig.swift in Sources */,
				8E3023912D56EEEC00FF6319 /* Config.swift in Sources */,
				8E3023932D56EEEC00FF6319 /* BaseViewModel.swift in Sources */,
				8E771E3C2D632B0D00389265 /* ZXUniModule.m in Sources */,
				8E3023942D56EEEC00FF6319 /* BaseCommonController.swift in Sources */,
				8E3023962D56EEEC00FF6319 /* SplashView.swift in Sources */,
				8E3023982D56EEEC00FF6319 /* BaseModel.swift in Sources */,
				8E3023992D56EEEC00FF6319 /* SMFont.swift in Sources */,
				8E30239B2D56EEEC00FF6319 /* BaseCollectionCell.swift in Sources */,
				8E30230A2D56EEB100FF6319 /* Color+Extension.swift in Sources */,
				8E30230B2D56EEB100FF6319 /* UIKit+Extension.swift in Sources */,
				8E30230C2D56EEB100FF6319 /* UIView-Extensions.swift in Sources */,
				8E30230E2D56EEB100FF6319 /* UIImageView+Extension.swift in Sources */,
				74858FAF1ED2DC5600515810 /* AppDelegate.swift in Sources */,
				1498D2341E8E89220040F4C2 /* GeneratedPluginRegistrant.m in Sources */,
				8E1E16E02D71E188001F416F /* ShareModel.swift in Sources */,
				8E771E432D63320000389265 /* PayModel.swift in Sources */,
				8E3023482D56EEBC00FF6319 /* UrlProtocolResolver.swift in Sources */,
				8E50F6242D72DCCA0095D0C6 /* PayParam.swift in Sources */,
				8E6171C62DB65E0A00CD19D8 /* EnterMeetingController.swift in Sources */,
				8E6171CD2DB65E0A00CD19D8 /* PermissionDetectionView.swift in Sources */,
				8E4B86812DE09525002809C6 /* AppDelegate + Push.swift in Sources */,
				8E1E16DC2D71CBBB001F416F /* LShareManger.swift in Sources */,
				8E296BC22D59E29000AE45BE /* FlutterChannelManager.swift in Sources */,
				8E706CE02D76E7FD00284B85 /* QYParam.swift in Sources */,
				8E50F62D2D72F4AD0095D0C6 /* MiniProgramBridge.swift in Sources */,
				8E50F6282D72DECA0095D0C6 /* CurriculumParam.swift in Sources */,
				8EB94A0F2DE5ABD60056EFD9 /* SuperUIColorExtension.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		331C8086294A63A400263BE5 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 97C146ED1CF9000F007C117D /* Runner */;
			targetProxy = 331C8085294A63A400263BE5 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		8E3024512D56F13C00FF6319 /* Localizable.strings */ = {
			isa = PBXVariantGroup;
			children = (
				8E3024522D56F13C00FF6319 /* en */,
				8E3024542D56F1E300FF6319 /* zh-Hans */,
			);
			name = Localizable.strings;
			sourceTree = "<group>";
		};
		97C146FF1CF9000F007C117D /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				97C147001CF9000F007C117D /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		249021D3217E4FDB00AE95B9 /* Profile */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Profile;
		};
		249021D4217E4FDB00AE95B9 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/RunnerProfile.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 3;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = JGLLG8H5YW;
				ENABLE_BITCODE = "$(inherited)";
				FLUTTER_BUILD_MODE = profile;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/flutter_inappwebview_ios",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/EagerLinkingTBDs/Release-iphoneos",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/Alamofire",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/SwiftyGif",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/FFPopup",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/file_picker",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/EmptyDataSet-Swift",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphonesimulator",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/OrderedSet",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/DKPhotoGallery",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/flutter_timezone",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/CryptoSwift",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/flutter_native_splash",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/DynamicColor",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/audioplayers_darwin",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/flutter_contacts",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/SDWebImage",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/device_info_plus",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/XCFrameworkIntermediates/AlipaySDK-iOS",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/connectivity_plus",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/DKImagePickerController",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/HandyJSON",
					"$(PROJECT_DIR)/Runner/UniMPModule",
					"$(PROJECT_DIR)/Runner/FlutterChannelManager",
				);
				GCC_OPTIMIZATION_LEVEL = 0;
				INFOPLIST_FILE = Runner/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "鼎校甄选";
				INFOPLIST_KEY_ITSAppUsesNonExemptEncryption = NO;
				INFOPLIST_KEY_NSAppleMusicUsageDescription = "我们需要访问您的媒体库以便于课程学习，为您提供更好的服务";
				INFOPLIST_KEY_NSCalendarsUsageDescription = "我们要访问你的日历，以便于添加课程提醒，精彩内容不会错过";
				INFOPLIST_KEY_NSCameraUsageDescription = "我们需要访问您的相机以拍摄图片，您就能快速设置个人头像、背景图，或者便捷地上传图片参与活动/创作内容。丰富您的个人空间，从这里开始";
				INFOPLIST_KEY_NSContactsUsageDescription = "我们需要添加联系人信息以便于添加好友，快速沟通";
				INFOPLIST_KEY_NSLocationAlwaysAndWhenInUseUsageDescription = "我们需要您的位置，以便于更新收货地址等信息，为您提供更便捷的服务";
				INFOPLIST_KEY_NSLocationWhenInUseUsageDescription = "我们需要您的位置，以便于更新收货地址等信息，为您提供更便捷的服务";
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "我们需要访问您的麦克风来录制音频，以便于更好的进行课程复习等";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "我们需要访问您的照片库，以便将编辑后的照片保存到您的设备中。";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "我们需要访问您的照片库，您就能快速设置个人头像、背景图，或者便捷地上传图片参与活动/创作内容。丰富您的个人空间，从这里开始";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen.storyboard;
				INFOPLIST_KEY_UIRequiresFullScreen = YES;
				INFOPLIST_KEY_UIStatusBarHidden = NO;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_UIUserInterfaceStyle = Light;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Runner/UniMPSDK/File/Libs",
					"$(PROJECT_DIR)/Runner/OpenSDK",
					"$(PROJECT_DIR)/Runner/Vender/OpenSDK",
					"$(PROJECT_DIR)/Runner/UniMPSDK/Lib",
				);
				MARKETING_VERSION = 2.2.12;
				PRODUCT_BUNDLE_IDENTIFIER = com.dingxiao.studentmetting;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Profile;
		};
		331C8088294A63A400263BE5 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = EDE9B144DDB75AE42516D1C4 /* Pods-RunnerTests.debug.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.dxznjy.alading.RunnerTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Runner.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Runner";
			};
			name = Debug;
		};
		331C8089294A63A400263BE5 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1779F0D49B74432152F9AB98 /* Pods-RunnerTests.release.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.dxznjy.alading.RunnerTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Runner.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Runner";
			};
			name = Release;
		};
		331C808A294A63A400263BE5 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 8A6C7888DF326F819BDB7C5A /* Pods-RunnerTests.profile.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.dxznjy.alading.RunnerTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Runner.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Runner";
			};
			name = Profile;
		};
		97C147031CF9000F007C117D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		97C147041CF9000F007C117D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		97C147061CF9000F007C117D /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9740EEB21CF90195004384FC /* Debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/RunnerDebug.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 3;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = JGLLG8H5YW;
				ENABLE_BITCODE = "$(inherited)";
				FLUTTER_BUILD_MODE = debug;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/flutter_inappwebview_ios",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/EagerLinkingTBDs/Release-iphoneos",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/Alamofire",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/SwiftyGif",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/FFPopup",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/file_picker",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/EmptyDataSet-Swift",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphonesimulator",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/OrderedSet",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/DKPhotoGallery",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/flutter_timezone",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/CryptoSwift",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/flutter_native_splash",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/DynamicColor",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/audioplayers_darwin",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/flutter_contacts",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/SDWebImage",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/device_info_plus",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/XCFrameworkIntermediates/AlipaySDK-iOS",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/connectivity_plus",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/DKImagePickerController",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/HandyJSON",
					"$(PROJECT_DIR)/Runner/UniMPModule",
					"$(PROJECT_DIR)/Runner/FlutterChannelManager",
				);
				INFOPLIST_FILE = Runner/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "鼎校甄选";
				INFOPLIST_KEY_ITSAppUsesNonExemptEncryption = NO;
				INFOPLIST_KEY_NSAppleMusicUsageDescription = "我们需要访问您的媒体库以便于课程学习，为您提供更好的服务";
				INFOPLIST_KEY_NSCalendarsUsageDescription = "我们要访问你的日历，以便于添加课程提醒，精彩内容不会错过";
				INFOPLIST_KEY_NSCameraUsageDescription = "我们需要访问您的相机以拍摄图片，您就能快速设置个人头像、背景图，或者便捷地上传图片参与活动/创作内容。丰富您的个人空间，从这里开始";
				INFOPLIST_KEY_NSContactsUsageDescription = "我们需要添加联系人信息以便于添加好友，快速沟通";
				INFOPLIST_KEY_NSLocationAlwaysAndWhenInUseUsageDescription = "我们需要您的位置，以便于更新收货地址等信息，为您提供更便捷的服务";
				INFOPLIST_KEY_NSLocationWhenInUseUsageDescription = "我们需要您的位置，以便于更新收货地址等信息，为您提供更便捷的服务";
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "我们需要访问您的麦克风来录制音频，以便于更好的进行课程复习等";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "我们需要访问您的照片库，以便将编辑后的照片保存到您的设备中。";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "我们需要访问您的照片库，您就能快速设置个人头像、背景图，或者便捷地上传图片参与活动/创作内容。丰富您的个人空间，从这里开始";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen.storyboard;
				INFOPLIST_KEY_UIRequiresFullScreen = YES;
				INFOPLIST_KEY_UIStatusBarHidden = NO;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_UIUserInterfaceStyle = Light;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Runner/UniMPSDK/File/Libs",
					"$(PROJECT_DIR)/Runner/OpenSDK",
					"$(PROJECT_DIR)/Runner/Vender/OpenSDK",
					"$(PROJECT_DIR)/Runner/UniMPSDK/Lib",
				);
				MARKETING_VERSION = 2.2.12;
				PRODUCT_BUNDLE_IDENTIFIER = com.dingxiao.studentmetting;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		97C147071CF9000F007C117D /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 3;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = JGLLG8H5YW;
				ENABLE_BITCODE = "$(inherited)";
				FLUTTER_BUILD_MODE = release;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/flutter_inappwebview_ios",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/EagerLinkingTBDs/Release-iphoneos",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/Alamofire",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/SwiftyGif",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/FFPopup",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/file_picker",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/EmptyDataSet-Swift",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphonesimulator",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/OrderedSet",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/DKPhotoGallery",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/flutter_timezone",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/CryptoSwift",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/flutter_native_splash",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/DynamicColor",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/audioplayers_darwin",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/flutter_contacts",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/SDWebImage",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/device_info_plus",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/XCFrameworkIntermediates/AlipaySDK-iOS",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/connectivity_plus",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/DKImagePickerController",
					"$(PROJECT_DIR)/Runner/xcframework/Release/iphoneos/Release-iphoneos/HandyJSON",
					"$(PROJECT_DIR)/Runner/UniMPModule",
					"$(PROJECT_DIR)/Runner/FlutterChannelManager",
				);
				GCC_OPTIMIZATION_LEVEL = 0;
				INFOPLIST_FILE = Runner/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "鼎校甄选";
				INFOPLIST_KEY_ITSAppUsesNonExemptEncryption = NO;
				INFOPLIST_KEY_NSAppleMusicUsageDescription = "我们需要访问您的媒体库以便于课程学习，为您提供更好的服务";
				INFOPLIST_KEY_NSCalendarsUsageDescription = "我们要访问你的日历，以便于添加课程提醒，精彩内容不会错过";
				INFOPLIST_KEY_NSCameraUsageDescription = "我们需要访问您的相机以拍摄图片，您就能快速设置个人头像、背景图，或者便捷地上传图片参与活动/创作内容。丰富您的个人空间，从这里开始";
				INFOPLIST_KEY_NSContactsUsageDescription = "我们需要添加联系人信息以便于添加好友，快速沟通";
				INFOPLIST_KEY_NSLocationAlwaysAndWhenInUseUsageDescription = "我们需要您的位置，以便于更新收货地址等信息，为您提供更便捷的服务";
				INFOPLIST_KEY_NSLocationWhenInUseUsageDescription = "我们需要您的位置，以便于更新收货地址等信息，为您提供更便捷的服务";
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "我们需要访问您的麦克风来录制音频，以便于更好的进行课程复习等";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "我们需要访问您的照片库，以便将编辑后的照片保存到您的设备中。";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "我们需要访问您的照片库，您就能快速设置个人头像、背景图，或者便捷地上传图片参与活动/创作内容。丰富您的个人空间，从这里开始";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen.storyboard;
				INFOPLIST_KEY_UIRequiresFullScreen = YES;
				INFOPLIST_KEY_UIStatusBarHidden = NO;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_UIUserInterfaceStyle = Light;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Runner/UniMPSDK/File/Libs",
					"$(PROJECT_DIR)/Runner/OpenSDK",
					"$(PROJECT_DIR)/Runner/Vender/OpenSDK",
					"$(PROJECT_DIR)/Runner/UniMPSDK/Lib",
				);
				MARKETING_VERSION = 2.2.12;
				PRODUCT_BUNDLE_IDENTIFIER = com.dingxiao.studentmetting;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_COMPILATION_MODE = singlefile;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		331C8087294A63A400263BE5 /* Build configuration list for PBXNativeTarget "RunnerTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				331C8088294A63A400263BE5 /* Debug */,
				331C8089294A63A400263BE5 /* Release */,
				331C808A294A63A400263BE5 /* Profile */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		97C146E91CF9000F007C117D /* Build configuration list for PBXProject "Runner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				97C147031CF9000F007C117D /* Debug */,
				97C147041CF9000F007C117D /* Release */,
				249021D3217E4FDB00AE95B9 /* Profile */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		97C147051CF9000F007C117D /* Build configuration list for PBXNativeTarget "Runner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				97C147061CF9000F007C117D /* Debug */,
				97C147071CF9000F007C117D /* Release */,
				249021D4217E4FDB00AE95B9 /* Profile */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 97C146E61CF9000F007C117D /* Project object */;
}
