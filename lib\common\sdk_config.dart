import 'package:student_end_flutter/common/Config.dart';

class KSDKConfig {
  /// 测试的key
  ///  /**
  //      * 云信app 正式数据
  //      */
  String YX_APP_KEY = "9f920ca06eaf42806f7635fdb4e5e7eb";
  String YX_APP_SECRET = "d92a29f86e6c";
  //    /**
  //      * 云信app 测试数据
  //      */
  // //    String YX_APP_KEY="e39eb7ff1a808cf969ea9f7f52aed878";
  // //    String YX_APP_SECRET="eb2583cf500e";
  // /**
  //      * 云信app 公测
  //      */
  // //    String YX_APP_KEY = "ee0fb7743cc7658f9d1305bb918b7e42";
  // //    String YX_APP_SECRET = "c0683ff72077";


  // static  String kNEMeetingkey = 'ee0fb7743cc7658f9d1305bb918b7e42';
  //
  // static  String kAppSecret = 'c0683ff72077';

  static  String kNEMeetingkey = _selectValue(debug: "9f920ca06eaf42806f7635fdb4e5e7eb", release: "9f920ca06eaf42806f7635fdb4e5e7eb", beta: '9f920ca06eaf42806f7635fdb4e5e7eb');
  static  String kAppSecret = _selectValue(debug: "d92a29f86e6c",  release: "d92a29f86e6c", beta: 'd92a29f86e6c');

  static  String _selectValue({
    required String debug,
    required String release,
    required String beta,
  }) {
    return release;
    if(!Config.URL.contains("gateway.dxznjy")){
      print('当前运行环境: debug');
      return debug;
    }
    const String env = String.fromEnvironment('ENV', defaultValue: 'release'); // 默认 release
    print('当前运行环境: $env');
    switch (env) {
      case 'debug':
        return debug;
      case 'beta':
        return beta;
      case 'release':
      default:
        return release;
    }
  }
}
