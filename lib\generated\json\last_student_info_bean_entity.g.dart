import 'package:student_end_flutter/generated/json/base/json_convert_content.dart';
import 'package:student_end_flutter/bean/last_student_info_bean_entity.dart';

LastStudentInfoBeanEntity $LastStudentInfoBeanEntityFromJson(
    Map<String, dynamic> json) {
  final LastStudentInfoBeanEntity lastStudentInfoBeanEntity = LastStudentInfoBeanEntity();
  final String? studentCode = jsonConvert.convert<String>(json['studentCode']);
  if (studentCode != null) {
    lastStudentInfoBeanEntity.studentCode = studentCode;
  }
  final String? studentName = jsonConvert.convert<String>(json['studentName']);
  if (studentName != null) {
    lastStudentInfoBeanEntity.studentName = studentName;
  }
  final List<
      LastStudentInfoBeanMerchantVos>? merchantVos = (json['merchantVos'] as List<
      dynamic>?)?.map(
          (e) =>
      jsonConvert.convert<LastStudentInfoBeanMerchantVos>(
          e) as LastStudentInfoBeanMerchantVos).toList();
  if (merchantVos != null) {
    lastStudentInfoBeanEntity.merchantVos = merchantVos;
  }
  return lastStudentInfoBeanEntity;
}

Map<String, dynamic> $LastStudentInfoBeanEntityToJson(
    LastStudentInfoBeanEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['studentCode'] = entity.studentCode;
  data['studentName'] = entity.studentName;
  data['merchantVos'] = entity.merchantVos.map((v) => v.toJson()).toList();
  return data;
}

extension LastStudentInfoBeanEntityExtension on LastStudentInfoBeanEntity {
  LastStudentInfoBeanEntity copyWith({
    String? studentCode,
    String? studentName,
    List<LastStudentInfoBeanMerchantVos>? merchantVos,
  }) {
    return LastStudentInfoBeanEntity()
      ..studentCode = studentCode ?? this.studentCode
      ..studentName = studentName ?? this.studentName
      ..merchantVos = merchantVos ?? this.merchantVos;
  }
}

LastStudentInfoBeanMerchantVos $LastStudentInfoBeanMerchantVosFromJson(
    Map<String, dynamic> json) {
  final LastStudentInfoBeanMerchantVos lastStudentInfoBeanMerchantVos = LastStudentInfoBeanMerchantVos();
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    lastStudentInfoBeanMerchantVos.name = name;
  }
  final String? realName = jsonConvert.convert<String>(json['realName']);
  if (realName != null) {
    lastStudentInfoBeanMerchantVos.realName = realName;
  }
  final String? mobile = jsonConvert.convert<String>(json['mobile']);
  if (mobile != null) {
    lastStudentInfoBeanMerchantVos.mobile = mobile;
  }
  final int? isEnable = jsonConvert.convert<int>(json['isEnable']);
  if (isEnable != null) {
    lastStudentInfoBeanMerchantVos.isEnable = isEnable;
  }
  final String? merchantCode = jsonConvert.convert<String>(
      json['merchantCode']);
  if (merchantCode != null) {
    lastStudentInfoBeanMerchantVos.merchantCode = merchantCode;
  }
  final String? province = jsonConvert.convert<String>(json['province']);
  if (province != null) {
    lastStudentInfoBeanMerchantVos.province = province;
  }
  final String? city = jsonConvert.convert<String>(json['city']);
  if (city != null) {
    lastStudentInfoBeanMerchantVos.city = city;
  }
  final String? area = jsonConvert.convert<String>(json['area']);
  if (area != null) {
    lastStudentInfoBeanMerchantVos.area = area;
  }
  final String? address = jsonConvert.convert<String>(json['address']);
  if (address != null) {
    lastStudentInfoBeanMerchantVos.address = address;
  }
  final dynamic regTime = json['regTime'];
  if (regTime != null) {
    lastStudentInfoBeanMerchantVos.regTime = regTime;
  }
  final String? merchantName = jsonConvert.convert<String>(
      json['merchantName']);
  if (merchantName != null) {
    lastStudentInfoBeanMerchantVos.merchantName = merchantName;
  }
  final int? isCheck = jsonConvert.convert<int>(json['isCheck']);
  if (isCheck != null) {
    lastStudentInfoBeanMerchantVos.isCheck = isCheck;
  }
  final String? longitude = jsonConvert.convert<String>(json['longitude']);
  if (longitude != null) {
    lastStudentInfoBeanMerchantVos.longitude = longitude;
  }
  final String? latitude = jsonConvert.convert<String>(json['latitude']);
  if (latitude != null) {
    lastStudentInfoBeanMerchantVos.latitude = latitude;
  }
  final dynamic expireDate = json['expireDate'];
  if (expireDate != null) {
    lastStudentInfoBeanMerchantVos.expireDate = expireDate;
  }
  final String? marketChargePerson = jsonConvert.convert<String>(
      json['marketChargePerson']);
  if (marketChargePerson != null) {
    lastStudentInfoBeanMerchantVos.marketChargePerson = marketChargePerson;
  }
  final String? consultingChargePerson = jsonConvert.convert<String>(
      json['consultingChargePerson']);
  if (consultingChargePerson != null) {
    lastStudentInfoBeanMerchantVos.consultingChargePerson =
        consultingChargePerson;
  }
  final String? teachingChargePerson = jsonConvert.convert<String>(
      json['teachingChargePerson']);
  if (teachingChargePerson != null) {
    lastStudentInfoBeanMerchantVos.teachingChargePerson = teachingChargePerson;
  }
  final String? marketChargePersonPhone = jsonConvert.convert<String>(
      json['marketChargePersonPhone']);
  if (marketChargePersonPhone != null) {
    lastStudentInfoBeanMerchantVos.marketChargePersonPhone =
        marketChargePersonPhone;
  }
  final String? consultingChargePersonPhone = jsonConvert.convert<String>(
      json['consultingChargePersonPhone']);
  if (consultingChargePersonPhone != null) {
    lastStudentInfoBeanMerchantVos.consultingChargePersonPhone =
        consultingChargePersonPhone;
  }
  final String? teachingChargePersonPhone = jsonConvert.convert<String>(
      json['teachingChargePersonPhone']);
  if (teachingChargePersonPhone != null) {
    lastStudentInfoBeanMerchantVos.teachingChargePersonPhone =
        teachingChargePersonPhone;
  }
  final String? roleTag = jsonConvert.convert<String>(json['roleTag']);
  if (roleTag != null) {
    lastStudentInfoBeanMerchantVos.roleTag = roleTag;
  }
  final String? channelManagerCode = jsonConvert.convert<String>(
      json['channelManagerCode']);
  if (channelManagerCode != null) {
    lastStudentInfoBeanMerchantVos.channelManagerCode = channelManagerCode;
  }
  final String? refereeCode = jsonConvert.convert<String>(json['refereeCode']);
  if (refereeCode != null) {
    lastStudentInfoBeanMerchantVos.refereeCode = refereeCode;
  }
  return lastStudentInfoBeanMerchantVos;
}

Map<String, dynamic> $LastStudentInfoBeanMerchantVosToJson(
    LastStudentInfoBeanMerchantVos entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['name'] = entity.name;
  data['realName'] = entity.realName;
  data['mobile'] = entity.mobile;
  data['isEnable'] = entity.isEnable;
  data['merchantCode'] = entity.merchantCode;
  data['province'] = entity.province;
  data['city'] = entity.city;
  data['area'] = entity.area;
  data['address'] = entity.address;
  data['regTime'] = entity.regTime;
  data['merchantName'] = entity.merchantName;
  data['isCheck'] = entity.isCheck;
  data['longitude'] = entity.longitude;
  data['latitude'] = entity.latitude;
  data['expireDate'] = entity.expireDate;
  data['marketChargePerson'] = entity.marketChargePerson;
  data['consultingChargePerson'] = entity.consultingChargePerson;
  data['teachingChargePerson'] = entity.teachingChargePerson;
  data['marketChargePersonPhone'] = entity.marketChargePersonPhone;
  data['consultingChargePersonPhone'] = entity.consultingChargePersonPhone;
  data['teachingChargePersonPhone'] = entity.teachingChargePersonPhone;
  data['roleTag'] = entity.roleTag;
  data['channelManagerCode'] = entity.channelManagerCode;
  data['refereeCode'] = entity.refereeCode;
  return data;
}

extension LastStudentInfoBeanMerchantVosExtension on LastStudentInfoBeanMerchantVos {
  LastStudentInfoBeanMerchantVos copyWith({
    String? name,
    String? realName,
    String? mobile,
    int? isEnable,
    String? merchantCode,
    String? province,
    String? city,
    String? area,
    String? address,
    dynamic regTime,
    String? merchantName,
    int? isCheck,
    String? longitude,
    String? latitude,
    dynamic expireDate,
    String? marketChargePerson,
    String? consultingChargePerson,
    String? teachingChargePerson,
    String? marketChargePersonPhone,
    String? consultingChargePersonPhone,
    String? teachingChargePersonPhone,
    String? roleTag,
    String? channelManagerCode,
    String? refereeCode,
  }) {
    return LastStudentInfoBeanMerchantVos()
      ..name = name ?? this.name
      ..realName = realName ?? this.realName
      ..mobile = mobile ?? this.mobile
      ..isEnable = isEnable ?? this.isEnable
      ..merchantCode = merchantCode ?? this.merchantCode
      ..province = province ?? this.province
      ..city = city ?? this.city
      ..area = area ?? this.area
      ..address = address ?? this.address
      ..regTime = regTime ?? this.regTime
      ..merchantName = merchantName ?? this.merchantName
      ..isCheck = isCheck ?? this.isCheck
      ..longitude = longitude ?? this.longitude
      ..latitude = latitude ?? this.latitude
      ..expireDate = expireDate ?? this.expireDate
      ..marketChargePerson = marketChargePerson ?? this.marketChargePerson
      ..consultingChargePerson = consultingChargePerson ??
          this.consultingChargePerson
      ..teachingChargePerson = teachingChargePerson ?? this.teachingChargePerson
      ..marketChargePersonPhone = marketChargePersonPhone ??
          this.marketChargePersonPhone
      ..consultingChargePersonPhone = consultingChargePersonPhone ??
          this.consultingChargePersonPhone
      ..teachingChargePersonPhone = teachingChargePersonPhone ??
          this.teachingChargePersonPhone
      ..roleTag = roleTag ?? this.roleTag
      ..channelManagerCode = channelManagerCode ?? this.channelManagerCode
      ..refereeCode = refereeCode ?? this.refereeCode;
  }
}