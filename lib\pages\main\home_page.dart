import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_carousel_widget/flutter_carousel_widget.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_state_manager/src/simple/get_view.dart';
// import 'package:netease_meeting_kit/meeting_ui.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:student_end_flutter/bean/home_type_bean_entity.dart';
import 'package:student_end_flutter/res/colors.dart';
import 'package:tx_im/utils/permissionUtils.dart';
import '../../common/CommonUtil.dart';
import '../../components/water_layout_list.dart';
import 'controller/home_controller.dart';
import 'package:tx_im/src/scanCode/scan_code.dart';
import '../../common/sensors_analytics_option.dart';
import '../../../bean/zx_course_entity.dart';
import 'package:flutter/material.dart';
import 'dart:ui' as ui; // 引入dart:ui并指定别名ui

class HomePage extends GetView<HomeController> {
  @override
  Widget build(BuildContext context) {
    // 构建完成后计算各区域位置
    Get.put(HomeController());

    return Scaffold(
      backgroundColor: HexColor('F3F8FC'),
      /* appBar: AppBarComp()
          .getNormalAppBar(name: "首页", backgroundColor: HexColor('F3F8FC')),*/
      // appBar: AppBar(
      //   backgroundColor: HexColor(controller.backgroundColor.isNotEmpty?controller.backgroundColor:'F3F8FC'),
      //   toolbarHeight: 0,
      // ),
      body: RefreshIndicator(
        color: HexColor('F3F8FC'),
        backgroundColor: Colors.black,
        notificationPredicate: (ScrollNotification notifation) {
          ScrollMetrics scrollMetrics = notifation.metrics;
          if (scrollMetrics.minScrollExtent == 0) {
            return true;
          } else {
            return false;
          }
        },
        onRefresh: controller.onRefresh,
        child: buildNestedScrollView(context),
      ),
    );
  }

  Widget buildNestedScrollView(BuildContext context) {
    return CustomScrollView(
      controller: controller.scrollController,
      physics: AlwaysScrollableScrollPhysics(), // 让它支持下拉
      // headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
      slivers: <Widget>[
        SliverToBoxAdapter(
            child: Column(children: [
          Obx(
            () => Stack(
              children: [
                FlutterCarousel(
                  items: _bannerUI(1),
                  options: FlutterCarouselOptions(
                    autoPlay: true,
                    enlargeCenterPage: false,
                    autoPlayInterval: const Duration(seconds: 3), // 自动播放间隔
                    autoPlayAnimationDuration:
                        const Duration(milliseconds: 800), // 动画持续时间
                    autoPlayCurve: Curves.linear,
                    enableInfiniteScroll: true,
                    showIndicator: false,
                    // aspectRatio: 2.0,
                    height: 350.h,
                    viewportFraction: 1,
                    onPageChanged: (index, reason) {
                      if (controller.specialList != null) {
                        controller.bannerIndex = index;
                        if (controller
                            .specialList[index].backgroundColor.isNotEmpty) {
                          controller.backgroundColor.value =
                              controller.specialList[index].backgroundColor;
                        } else {
                          controller.backgroundColor.value = '';
                        }
                      }
                    },
                  ),
                ),
                Positioned(
                    top: 22.h,
                    left: 10.w,
                    child: GestureDetector(
                      onTap: () {
                        controller.onSearchClick();
                      },
                      child: _searchUI(2),
                    )),
                Positioned(
                  bottom: 15.h,
                  right: 0.w,
                  child: GestureDetector(
                    onTap: () {
                      // ProductMoreClick
                      TrackingUtils.trackEvent('ProductMoreClick', {
                        'banner_id': '',
                        'common_fields': '',
                        'avitcity_id': '',
                        'goods_id': '',
                        'url_path': 'HomePage'
                      });
                      controller.specialFeature(
                          controller.specialList[controller.bannerIndex]);
                    },
                    child: Container(
                      padding: EdgeInsets.only(
                          left: 6.w, right: 6.w, top: 2.h, bottom: 2.h),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(4.h),
                              bottomLeft: Radius.circular(4.h)),
                          // border: Border.all(color: const Color(0xFF076E57), width: 1),
                          color: Colors.black.withOpacity(0.40)),
                      child: Wrap(
                        direction: Axis.vertical,
                        children: "查看更多"
                            .split('')
                            .map((char) => Text(char,
                                style: TextStyle(
                                    fontSize: 14.sp, color: Colors.white)))
                            .toList(),
                      ),
                    ),
                  ),
                )
              ],
            ),
          ),

          // Container(
          //   padding: EdgeInsets.only(left: 10.w, right: 10.w),
          //   alignment: Alignment.centerLeft,
          //   child: Text("欢迎来到鼎校👏🏻",
          //       style: TextStyle(
          //           color: AppColors.black,
          //           fontSize: 17.sp,
          //           fontFamily: "M")),
          // ),

          _middleContentUI(),
          //去除添加教练码
          // _addTeacherUI(),
          //瓷片
          Obx(
            () => Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                SizedBox(
                  width: 8.w,
                ),
                Container(
                  width: 167.w,
                  height: 160.w,
                  child: FlutterCarousel(
                    items: bannerLeft(),
                    options: FlutterCarouselOptions(
                      autoPlay: true,
                      enlargeCenterPage: true,
                      enableInfiniteScroll: true,
                      showIndicator: false,
                      // aspectRatio: 2.0,
                      viewportFraction: 1,
                      onPageChanged: (index, reason) {},
                    ),
                  ),
                ),
                SizedBox(
                  width: 7.w,
                ),
                GestureDetector(
                  onTap: () {
                    TrackingUtils.trackEvent('GrouppurChasingClick', {
                      'banner_id': controller.bannerInfo.value.id,
                      'common_fields': controller.bannerInfo.value.bannerName,
                      'avitcity_id':
                          controller.bannerInfo.value.activityId.isNotEmpty
                              ? controller.bannerInfo.value.activityId
                              : '',
                      'goods_id': controller.bannerInfo.value.goodsId.isNotEmpty
                          ? controller.bannerInfo.value.goodsId
                          : '',
                      'url_path': 'HomePage'
                    });
                    controller.onBannerListClick(-1);
                  },
                  child: Container(
                    width: 167.w,
                    height: 160.w,
                    child: controller.bannerInfo.value != null
                        ? ClipRRect(
                            borderRadius: BorderRadius.all(Radius.circular(10)),
                            child: CachedNetworkImage(
                              width: 160.w,
                              height: 160.h,
                              fit: BoxFit.fill,
                              imageUrl:
                                  controller.bannerInfo.value.bannerPicUrl,
                            ),
                          )
                        : Container(),
                  ),
                ),
              ],
            ),
          ),
        ])),
        // showTab.value
        Obx(() => SliverVisibility(
          visible: !controller.showTab.value,
          sliver: SliverOverlapAbsorber(
            key: controller.tabKeys[0],
            handle: SliverOverlapAbsorberHandle(),
            sliver: SliverAppBar(
              automaticallyImplyLeading: false,
              toolbarHeight: 95,
              pinned: true,
              expandedHeight: 95,
              collapsedHeight: 95,
              flexibleSpace: Container(
                color: Colors.white,
                child: _tabUI(),
              ),
            ),
          ),
        )),

        SliverToBoxAdapter(
          child: Column(
            children: [
              Obx(() => goodTitleCart(context)),
              Obx(
                () => SizedBox(
                  height: controller.categoryGoodsList.isEmpty ? 115.h : 15.h,
                ),
              ),
              Stack(
                children: [
                  GestureDetector(
                    onTap: () {
                      TrackingUtils.trackEvent('BottomDetailsClick', {
                        'common_fields': '了解更多',
                        'avitcity_id': '',
                        'goods_id': '',
                        'banner_id': '',
                        'url_path': 'HomePage'
                      });
                      controller.goAboutPage();
                    },
                    child: Container(
                      margin: EdgeInsets.only(left: 0.w),
                      width: 340.w,
                      height: 190.h,
                      //   pic_xuanzeiliyou
                      decoration: BoxDecoration(
                        image: DecorationImage(
                          image: AssetImage(
                              'assets/home/<USER>'), // 你的图片路径
                          fit: BoxFit.fitWidth, // 或者你需要的fit方式
                        ),
                      ),
                    ),
                  ),
                  Positioned(
                      bottom: 32.h,
                      child: GestureDetector(
                        onTap: () {
                          TrackingUtils.trackEvent('BottomDetailsClick', {
                            'common_fields': '了解更多',
                            'avitcity_id': '',
                            'goods_id': '',
                            'banner_id': '',
                            'url_path': 'HomePage'
                          });
                          controller.goAboutPage();
                        },
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            SizedBox(
                              width: 2.w,
                            ),
                            for (int i = 0;
                                i < controller.learnMore.length;
                                i++)
                              Container(
                                width: 105.w,
                                // color:Colors.blueAccent,
                                margin: EdgeInsets.only(right: 11.w),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(controller.learnMore[i]['english']!,
                                        style: TextStyle(
                                            color: AppColors.white,
                                            fontSize: 8.sp,
                                            fontFamily: "M")),
                                    SizedBox(
                                      height: 5.h,
                                    ),
                                    Text(controller.learnMore[i]['subject']!,
                                        style: TextStyle(
                                            color: AppColors.white,
                                            fontSize: 18.sp,
                                            fontFamily: "M")),
                                    Text(controller.learnMore[i]['title']!,
                                        style: TextStyle(
                                            color: AppColors.white,
                                            fontSize: 18.sp,
                                            fontFamily: "M"))
                                  ],
                                ),
                              )
                          ],
                        ),
                      ))
                ],
              )
            ],
          ),
        ),

        // Obx(() => goodTitleCart())
      ],
      // },
      // body:Container(height: 0,)
    );
  }

  bannerLeft() {
    List<Widget> bannerNode = [];
    if (controller.bannerNewList.isEmpty) {
      return [
        Container(
          height: 0,
        )
      ]; // 返回空容器占位
    }
    for (int i = 0; i < controller.bannerNewList.length; i++) {
      var item = controller.bannerNewList[i];
      var node = GestureDetector(
        onTap: () {
          TrackingUtils.trackEvent('OperationalActivitiesClick', {
            'banner_id': controller.bannerNewList[i].id,
            'common_fields': controller.bannerNewList[i].bannerName,
            'avitcity_id': controller.bannerNewList[i].activityId.isNotEmpty
                ? controller.bannerNewList[i].activityId
                : '',
            'goods_id': controller.bannerNewList[i].goodsId.isNotEmpty
                ? controller.bannerNewList[i].goodsId
                : '',
            'url_path': 'HomePage'
          });
          //
          controller.onBannerListClick(i);
        },
        child: ClipRRect(
          borderRadius: BorderRadius.all(Radius.circular(10)),
          child: CachedNetworkImage(
            width: 160.w,
            height: 160.w,
            fit: BoxFit.fill,
            imageUrl: item.bannerPicUrl,
          ),
        ),
      );
      bannerNode.add(node);
    }
    return bannerNode;
  }

  _bannerUI(bannerPostion) {
    if (controller.specialList.isEmpty) {
      return [Container()];
    }
    List<Widget> bannerNode = [];
    for (int i = 0; i < controller.specialList.length; i++) {
      var item = controller.specialList[i];
      var node = GestureDetector(
          onTap: () {
            // SpecialBannerClick

            controller.specialFeature(item);
          },
          child: Container(
            decoration: BoxDecoration(
              // image: DecorationImage(
              //   image: NetworkImage(item.picUrl), // 你的图片路径
              //   fit: BoxFit.cover, // 或者你需要的fit方式
              // ),
              gradient: controller.backgroundColor.isNotEmpty
                  ? LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                          HexColor(controller.backgroundColor.value),
                          HexColor('#ffffff'),
                          HexColor(controller.backgroundColor.value)
                        ])
                  : LinearGradient(
                      colors: [HexColor('#ffffff'), HexColor('#ffffff')]),
              borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(9.h),
                  bottomRight: Radius.circular(9.h)),
            ),
            child: Container(
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: AssetImage('assets/home/<USER>'), // 你的图片路径
                  fit: BoxFit.cover, // 或者你需要的fit方式
                ),
                borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(9.h),
                    bottomRight: Radius.circular(9.h)),
              ),
              width: 375.w,
              child: Column(
                children: [
                  SizedBox(
                    height: 65.h,
                  ),
                  Stack(
                    alignment: Alignment.center,
                    children: [
                      // 模糊背景层 - 增加尺寸
                      Opacity(
                        opacity: 0.1,
                        child: Container(
                          width: 360.w,
                          height: 200.h,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            // color: HexColor(controller.backgroundColor.value).withOpacity(0.5),
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(12),
                            child: Stack(
                              children: [
                                // 原始图片
                                Image.network(
                                  item.picUrl,
                                  width: 360.w,
                                  height: 220.h,
                                  fit: BoxFit.cover,
                                ),
                                // 模糊效果层
                                BackdropFilter(
                                  filter: ui.ImageFilter.blur(
                                    sigmaX: 20.h,
                                    sigmaY: 20.h,
                                  ),
                                  child: Container(
                                    color: Colors.white.withOpacity(0.2),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),

                      // 原始图片层
                      ClipRRect(
                        borderRadius: BorderRadius.circular(16.r),
                        child: CachedNetworkImage(
                          width: 330.w,
                          height: 170.h,
                          fit: BoxFit.fill,
                          imageUrl: item.picUrl,
                        ),
                      ),
                    ],
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // SizedBox(
                      //   width: 10.w,
                      // ),
                      GestureDetector(
                        onTap: () {
                          //
                          controller.goGoods(item.goodsList[0]);
                        },
                        child: item.goodsList.length > 0
                            ? Container(
                                alignment: Alignment.bottomLeft,
                                margin: EdgeInsets.only(
                                  left: 16.w,
                                ),
                                padding: EdgeInsets.only(left: 0, bottom: 0),
                                width: 151.w,
                                height: 79.w,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(8.r),
                                  // color: HexColor('CDEDC3'), // 背景色
                                  image: DecorationImage(
                                    image: NetworkImage(item
                                        .goodsList[0].goodsSpecialPicUrl), // 你的图片路径
                                    fit: BoxFit.cover, // 或者你需要的fit方式
                                  ),
                                ),
                                child: Container(
                                  width: 152.w,
                                  height: 39.w,
                                  alignment: Alignment.bottomLeft,
                                  padding: EdgeInsets.only(left: 8, bottom: 5),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(8.r),
                                    gradient: LinearGradient(
                                      begin: Alignment.topCenter,
                                      end: Alignment.bottomCenter,
                                      colors: [
                                        HexColor('ffffff').withOpacity(0),
                                        HexColor('000000').withOpacity(0.55)
                                      ],
                                    ),
                                  ),
                                  child: RichText(
                                    text: TextSpan(
                                      children: [
                                        TextSpan(
                                          text: getCoursePriceTitleType(
                                              item.goodsList[0].goodsType),
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontSize: 12.sp,
                                          ),
                                        ),
                                        TextSpan(
                                          text: ' ',
                                        ),
                                        TextSpan(
                                          text:
                                              '¥${item.goodsList[0].goodsVipPrice}',
                                          style: TextStyle(
                                              color: Colors.white,
                                              fontSize: 15.sp,
                                              fontWeight: FontWeight.w500),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              )
                            : Container(
                                child: Text(''),
                              ),
                      ),

                      SizedBox(
                        width: 8.w,
                      ),
                      GestureDetector(
                        onTap: () {
                          controller.goGoods(item.goodsList[1]);
                        },
                        child: item.goodsList.length > 1
                            ? Container(
                                alignment: Alignment.bottomLeft,
                                padding: EdgeInsets.only(left: 0, bottom: 0),
                                width: 150.w,
                                height: 79.w,
                                decoration: BoxDecoration(
                                  // color: HexColor('CDEDC3'), // 背景色
                                  borderRadius: BorderRadius.circular(8.r),
                                  image: DecorationImage(
                                    image: NetworkImage(item
                                        .goodsList[1].goodsSpecialPicUrl), // 你的图片路径
                                    fit: BoxFit.cover, // 或者你需要的fit方式
                                  ),
                                ),
                                child: Container(
                                  width: 152.w,
                                  height: 39.w,
                                  alignment: Alignment.bottomLeft,
                                  padding: EdgeInsets.only(left: 8, bottom: 5),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(8.r),
                                    gradient: LinearGradient(
                                      begin: Alignment.topCenter,
                                      end: Alignment.bottomCenter,
                                      colors: [
                                        HexColor('ffffff').withOpacity(0),
                                        HexColor('000000').withOpacity(0.55)
                                      ],
                                    ),
                                  ),
                                  child: RichText(
                                    text: TextSpan(
                                      children: [
                                        TextSpan(
                                          text: getCoursePriceTitleType(
                                              item.goodsList[1].goodsType),
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontSize: 12.sp,
                                          ),
                                        ),
                                        TextSpan(
                                          text: ' ',
                                        ),
                                        TextSpan(
                                          text:
                                              '¥${item.goodsList[1].goodsVipPrice}',
                                          style: TextStyle(
                                              color: Colors.white,
                                              fontSize: 15.sp,
                                              fontWeight: FontWeight.w500),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              )
                            : Container(),
                      ),

                      SizedBox(
                        width: 34.w,
                      ),
                    ],
                  )
                  // ClipRRect(
                  //   borderRadius: BorderRadius.all(Radius.circular(10)),
                  //   child: CachedNetworkImage(
                  //     width: double.infinity,
                  //     fit: BoxFit.fill,
                  //     imageUrl: 'assets/home/<USER>',
                  //     //   controller.bannerList[i].bannerPicUrl
                  //   ),
                  // ),
                ],
              ),
              //   #CDEDC3
            ),
          )
          // child: ClipRRect(
          //   borderRadius: BorderRadius.all(Radius.circular(10)),
          //   child: CachedNetworkImage(
          //     width: double.infinity,
          //     fit: BoxFit.fill,
          //     imageUrl: controller.bannerList[i].bannerPicUrl,
          //   ),
          // ),
          );
      bannerNode.add(node);
    }
    return bannerNode;
  }

  _tabUI() {
    if (controller.categoryGoodsList.isEmpty || controller.showTab.value) {
      return Container();
    }
    return Column(
      children: [
        SizedBox(
          height: 22.h,
        ),
        GestureDetector(
          onTap: () {
            controller.onSearchClick();
          },
          child: _searchUI(1),
        ),
        SizedBox(
          height: 5.h,
        ),
        Expanded(
          child: ListView.builder(
              scrollDirection: Axis.horizontal,
              controller: controller.scrollHorizontalController, // 绑定控制器
              itemCount: controller.categoryGoodsList.length,
              keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
              // itemExtent: 80.w, // 宽度
              itemBuilder: (BuildContext context, int index) {
                return _itemBuilder(index);
              }),
        ),
      ],
    );
  }

  _itemBuilder(int index) {
    var data = controller.categoryGoodsList[index];
    if (index >= controller.keys.length) {
      return Container();
    }
    return Obx(
      () => InkWell(
        onTap: () {
          // ProductNavigationClick
          TrackingUtils.trackEvent('ProductNavigationClick', {
            'banner_id': '',
            'common_fields': data.moduleName,
            'avitcity_id': '',
            'goods_id': '',
            'url_path': 'HomePage'
          });
          controller.changeTabChose(index);
        },
        child: Container(
            key: controller.keys[index],
            padding: EdgeInsets.only(left: 12.w, right: 16.w, top: 11.h),
            child: Column(
              children: [
                Text(
                  data.moduleName.length > 5
                      ? '${data.moduleName.substring(0, 5)}...'
                      : data.moduleName,
                  style: TextStyle(
                    fontSize: index == controller.currentTabIndex.value
                        ? 15.sp
                        : 14.sp,
                    fontWeight: index == controller.currentTabIndex.value
                        ? FontWeight.w500
                        : FontWeight.normal,
                    color: index == controller.currentTabIndex.value
                        ? HexColor('009B55')
                        : HexColor('555555'),
                  ),
                ),
                // Visibility(
                //     visible: controller.currentTabIndex.value == index,
                //     child: Image.asset(
                //       'assets/zx/icon_move.png',
                //       height: 20.w,
                //       width: 20.w,
                //     ))
              ],
            )),
      ),
    );
  }

  _contentUI(BuildContext context, item) {
    RxList<ZxCourseDataData> rxGoodsList = <ZxCourseDataData>[].obs;
    rxGoodsList.value = item.goodsList;

    if (item.horzVert == 1) {
      return Container(
        // padding: EdgeInsets.all(10.w),
        padding: EdgeInsets.only(left: 10.w, right: 10.w),
        child: WaterLayoutList().waterLayoutList(
            context, rxGoodsList, 1, item.moduleName,
            isScroll: false),
      );
    } else {
      if (rxGoodsList.value.length > 1) {
        if (rxGoodsList.length > 2) {
          return Container(
            padding: EdgeInsets.only(left: 10.w, right: 10.w),
            child: WaterLayoutList().waterLayoutList(
                context, rxGoodsList, 1, item.moduleName,
                isHorizontal: true),
          );
        } else {
          return Container(
            padding: EdgeInsets.only(left: 10.w, right: 10.w),
            child: WaterLayoutList().waterLayoutList(
                context, rxGoodsList, 1, item.moduleName,
                isScroll: false),
          );
        }
      } else {
        return Container(
          // alignment: Alignment.topCenter,
          margin: EdgeInsets.only(top: 1.h, bottom: 15.h),
          padding: EdgeInsets.only(left: 10.w, right: 10.w),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            color: Colors.white,
            // pic_shengyabeijing
            image: DecorationImage(
              image: AssetImage('assets/home/<USER>'), // 你的图片路径
              fit: BoxFit.cover, // 或者你需要的fit方式
            ),
          ),
          child: WaterLayoutList().waterLayoutList(
              context, rxGoodsList, 1, item.moduleName,
              isScroll: false, isShowOne: true),
        );
      }
    }
  }

  Widget goodTitleCart(BuildContext context) {
    if (controller.categoryGoodsList.isEmpty) {
      return Container();
    }
    // 移除 SingleChildScrollView，因为现在由 NestedScrollView 处理滚动
    return Column(
      children: [
        SizedBox(
          height: controller.showTab.value ? 1.h : 110.h,
        ),
        for (int i = 0; i < controller.categoryGoodsList.length; i++)
          Column(
            children: [
              SizedBox(
                height: 15.h,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Container(
                  //   child: Text('111111111111111111111111'),
                  // ),
                  Container(
                    key: controller.goodItemKeys[i],
                    padding: EdgeInsets.only(left: 16.w),
                    child: Text(
                      controller.categoryGoodsList[i].moduleName,
                      style: TextStyle(
                          fontSize: 16.sp, fontWeight: FontWeight.bold),
                    ),
                  ),

                  !controller.categoryGoodsList[i].allGoodsDisplayed
                      ? GestureDetector(
                          onTap: () {
                            // IndexLearnMoreClick
                            TrackingUtils.trackEvent('IndexLearnMoreClick', {
                              'banner_id': '',
                              'common_fields':
                                  controller.categoryGoodsList[i].moduleName,
                              'avitcity_id': '',
                              'goods_id': '',
                              'url_path': 'HomePage'
                            });
                            controller
                                .specialMore(controller.categoryGoodsList[i]);
                          },
                          child: Container(
                            padding: EdgeInsets.only(right: 16.w),
                            // #ABAAAA
                            child: Text(
                              '更多>',
                              style: TextStyle(
                                  fontFamily:
                                      "font-family: AlibabaPuHuiTi_3_55_Regular",
                                  fontSize: 14.sp,
                                  color: HexColor('#ABAAAA')),
                            ),
                          ),
                        )
                      : Container(),
                ],
              ),
              SizedBox(
                height: 15.h,
              ),
              Container(
                padding: EdgeInsets.only(top: 0.w),
                child: _contentUI(context, controller.categoryGoodsList[i]),
              ),
            ],
          ),
      ],
    );
  }

  _middleContentUI() {
    return Container(
      margin: EdgeInsets.only(top: 10.h),
      padding: EdgeInsets.only(left: 8.w, right: 8.w),
      width: double.infinity,
      height: 65.h,
      child: Column(
        children: <Widget>[
          Expanded(
            child: PageView(
              onPageChanged: (int page) {
                controller.currentPage.value = page; // 更新当前页数
              },
              controller: controller.pageController,
              children: _middleUI(),
            ),
          ),
          // SizedBox(
          //   height: 5.h,
          // ),
          // Obx(() => Center(
          //       child: Row(
          //         mainAxisAlignment: MainAxisAlignment.center,
          //         children: List.generate(
          //           controller.buttonList.length,
          //           (index) => _buildDot(index),
          //         ),
          //       ),
          //     )),
        ],
      ),
    );
  }

  _middleUI() {
    List<Widget> nodeList = [];
    for (int i = 0; i < controller.buttonList.length; i++) {
      nodeList.add(Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: List.generate(4, (j) => _childUI(i, j)),
      ));
    }
    return nodeList;
  }

  _childUI(i, j) {
    if (j >= controller.buttonList[i].length) {
      return Container();
    }
    var data = controller.buttonList[i][j];
    return GestureDetector(
        onTap: () {
          var title = '';
          if (data["name"] == '鼎币商城') {
            title = 'DingcoinMallClick';
          } else if (data["name"] == '甄选好课') {
            title = 'ZXGoodCoursesClick';
          } else if (data["name"] == '文化中心') {
            title = 'CultureCenterClick';
          } else if (data["name"] == '家长会员') {
            title = 'ParentMembershipClick';
          }
          TrackingUtils.trackEvent(title, {
            'common_fields': data["name"],
            'avitcity_id': '',
            'goods_id': '',
            'banner_id': '',
            'url_path': 'HomePage'
          });
          controller.onButtonListClick(data);
        },
        child: SizedBox(
          width: 68.h,
          child: Column(
            children: [
              Image(width: 32.w, height: 32.h, image: AssetImage(data["img"]!)),
              Container(
                margin: EdgeInsets.only(top: 6.h),
                child: Text(
                  data["name"]!,
                  style: TextStyle(fontFamily: "M", fontSize: 14.sp),
                ),
              )
            ],
          ),
        ));
  }

  // 小圆点生成函数
  Widget _buildDot(int index) {
    return AnimatedContainer(
      duration: Duration(milliseconds: 300),
      height: 3.0,
      width: 12.0,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(10)),
        color: controller.currentPage.value == index
            ? Color(0xFFFD9B2A)
            : Color(0xFFFAE1C5),
      ),
    );
  }

  /// 搜索
  _searchUI(key) {
    return Container(
      height: 34.h,
      width: 320.w,
      margin: EdgeInsets.only(top: 10.h, left: 10.w, right: 10.w),
      padding: EdgeInsets.only(left: 15.w, right: 10.w),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(40),
          // border: Border.all(color: const Color(0xFF076E57), width: 1),
          color:
              key == 2 ? Colors.white.withOpacity(0.47) : HexColor("F9FAFA")),
      child: Row(
        children: [
          Image(
            image: AssetImage(key == 2
                ? 'assets/home/<USER>'
                : 'assets/home/<USER>'),
            height: 20.w,
            width: 20.w,
          ),
          Container(
            width: 270.w,
            child: Container(
              width: double.infinity,
              // color: Colors.white,
              alignment: Alignment.centerLeft,
              margin: EdgeInsets.only(left: 10.w, right: 10.w),
              height: double.infinity,
              child: Text(
                "鼎拼读",
                style: TextStyle(
                  color: key == 2 ? AppColors.white : HexColor("BDBDBD"),
                  fontSize: 16.sp,
                ),
              ),
            ),
            // GestureDetector(
            //   onTap: () {
            //     print('ffffffffffffffff111fffffffffffffffffffffff');
            //     controller.onSearchClick();
            //   },
            //   child:
            // ),
          ),
          // InkWell(
          //   onTap: () async {
          //     var cameraStatus = await Permission.camera.request();
          //     if (cameraStatus.isGranted) {
          //       Navigator.push(context,
          //           MaterialPageRoute(builder: (context) => QRScannerScreen()));
          //     } else {
          //       permissionUtils.showSettingDialog(context,
          //           title: "权限请求", content: "我们需要访问您的相机来扫描图片。如果您愿意，请在设置中打开权限");
          //     }
          //   },
          //   child: Image(
          //       width: 22,
          //       height: 22,
          //       image: AssetImage("tx-im/assets/images/icon_scancode.png")),
          // ),
        ],
      ),
    );
  }

  /// 添加教练码
  _addTeacherUI() {
    return Container(
      height: 38.h,
      margin: EdgeInsets.only(top: 20.h, left: 10.w, right: 10.w),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.h),
          border:
              Border.all(color: const Color.fromRGBO(255, 0, 0, 1), width: 1),
          color: Colors.white),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Container(
              margin: EdgeInsets.only(left: 10.w, right: 10.w),
              height: double.infinity,
              child: TextField(
                controller: controller.editingTeacherCodeController,
                cursorColor: AppColors.f555555,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  hintText: '请输入添加教练验证码',
                  border: InputBorder.none,
                  fillColor: AppColors.f555555,
                  hintStyle: TextStyle(
                      color: AppColors.f555555, fontSize: 14.sp, height: 2.0),
                  labelStyle: TextStyle(
                    color: AppColors.f555555,
                    fontSize: 14.sp,
                  ),
                ),
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: GestureDetector(
              onTap: () {
                controller.addTeacherCodeRequest();
              },
              child: Container(
                alignment: Alignment.center,
                decoration: BoxDecoration(
                    color: const Color.fromRGBO(255, 0, 0, 1),
                    borderRadius: BorderRadius.only(
                        topRight: Radius.circular(9.h),
                        bottomRight: Radius.circular(9.h))),
                height: double.infinity,
                child: Text(
                  "确定",
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontFamily: "R",
                    color: AppColors.white,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Offset getItemOffset(int index) {
    print(
        'vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv11111111111111111111111111111111111');
    final RenderBox? renderBox =
        controller.keys[index].currentContext?.findRenderObject() as RenderBox?;
    if (renderBox != null) {
      final Offset position =
          renderBox.localToGlobal(Offset.zero); // 获取全局位置（相对于屏幕）
      controller.bottomPosW = position.dx + renderBox.size.width / 2 - 10.w;
      return Offset(controller.bottomPosW, position.dy);
    }
    return Offset(controller.bottomPosW, 0);
  }

  String getCoursePriceTitleType(type) {
    switch (type) {
      case 2:
        return '体验价';
      case 3:
        return '会员价';
      case 4:
        return '会员价';
      default:
        return '';
    }
  }
}
