import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_state_manager/src/simple/get_view.dart';
import 'package:student_end_flutter/pages/base/controller/base_controller.dart';
import 'package:student_end_flutter/res/colors.dart';
import 'package:tx_im/im_module_service.dart';
import '../../common/sensors_analytics_option.dart';
import '../../components/toast_utils.dart';

class BasePage extends GetView<BaseController> {
  DateTime? _lastPressedTime;

  @override
  Widget build(BuildContext context) {
    Get.put(BaseController());
    return WillPopScope(
        onWillPop: _onWillPop,
        child: Scaffold(
            body: TabBarView(
              physics: NeverScrollableScrollPhysics(),
              controller: controller.tabController,
              children: controller.pages,
            ),
            bottomNavigationBar: Obx(() {
              return BottomNavigationBar(
                currentIndex: controller.currentIndex.value,
                onTap: (index) {
                  controller.changeTab(index);
                },
                items: _barUI(),
                selectedItemColor: AppColors.f2E896F,
                unselectedItemColor: AppColors.f888896,
                type: BottomNavigationBarType.fixed,
                backgroundColor: Colors.white,
                selectedFontSize: 10.sp,
                unselectedFontSize: 10.sp,
              );
            })));
  }

  _barUI() {
    List<BottomNavigationBarItem> barItems = [];
    for (int i = 0; i < controller.pagesRes.length; i++) {
      barItems.add(
        i == controller.pages.length - 2
            ? BottomNavigationBarItem(
                icon: Stack(
                  children: <Widget>[
                    Image.asset(
                      controller.currentIndex.value == i
                          ? controller.pagesRes[i]["chose"]
                          : controller.pagesRes[i]["normal"],
                      width: 25.w,
                      height: 25.h,
                    ),
                    ValueListenableBuilder<int>(
                      valueListenable: IMModuleService().unreadMsgCountNotifier,
                      builder: (context, count, child) {
                        return Visibility(
                          visible:
                              IMModuleService().unreadMsgCountNotifier.value !=
                                  0,
                          child: Positioned(
                            right: 0,
                            top: 0,
                            child: Container(
                              width: 18,
                              alignment: Alignment.center,
                              height: 18,
                              padding: EdgeInsets.all(2.0),
                              // 红点周围的padding
                              decoration: BoxDecoration(
                                color: Colors.red, // 红点颜色
                                borderRadius:
                                    BorderRadius.circular(9), // 红点形状为圆形
                              ),
                              child: FittedBox(
                                fit: BoxFit.contain, // 保持宽高比
                                child: Text(
                                  IMModuleService()
                                              .unreadMsgCountNotifier
                                              .value >
                                          99
                                      ? "99+"
                                      : '${IMModuleService().unreadMsgCountNotifier.value}',
                                  style: TextStyle(
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ],
                ),
                label: controller.pagesRes[i]["lbl"],
              )
            : BottomNavigationBarItem(
                icon: Stack(
                  children: [
                    Image.asset(
                      controller.currentIndex.value == i
                          ? controller.pagesRes[i]["chose"]
                          : controller.pagesRes[i]["normal"],
                      width: 25.w,
                      height: 25.h,
                      gaplessPlayback: true,
                    ),
                    if (i == controller.pages.length - 1) // 我的页面
                      Obx(() {
                        return controller.showMessageRedDot.value
                            ? Positioned(
                                right: 0,
                                top: 0,
                                child: Container(
                                  width: 8,
                                  height: 8,
                                  decoration: BoxDecoration(
                                    color: Colors.red,
                                    shape: BoxShape.circle,
                                  ),
                                ),
                              )
                            : const SizedBox();
                      }),
                  ],
                ),
                label: controller.pagesRes[i]["lbl"],
              ),
      );
    }
    return barItems;
  }

  Future<bool> _onWillPop() async {
    if (Platform.isIOS) {
      return false;
    }
    final now = DateTime.now();
    if (_lastPressedTime == null ||
        now.difference(_lastPressedTime!) > Duration(seconds: 2)) {
      _lastPressedTime = now;
      ToastUtil.showToastText("再按一次退出应用");
      // // 记录退出操作埋点
      // TrackingUtils.trackEvent(
      //   'AppExitAttempt',
      //   {
      //     'current_screen': controller.currentIndex.value,
      //     'timestamp': DateTime.now().toIso8601String(),'avitcity_id':'','goods_id':'','banner_id':'','common_fields':'','url_path': controller.pagesRes[controller.currentIndex.value]['page']
      //   },
      // );

      return false;
    }
    // 记录实际退出应用埋点
    // TrackingUtils.trackEvent(
    //   'AppExit',
    //   {
    //     'current_screen': controller.currentIndex.value,
    //     'timestamp': DateTime.now().toIso8601String(),
    //     'avitcity_id': '',
    //     'goods_id': '',
    //     'banner_id': '',
    //     'common_fields': '',
    //     'url_path': controller.pagesRes[controller.currentIndex.value]['page']
    //   },
    // );

    SystemNavigator.pop();
    return true;
  }
}
