import 'dart:convert';

import 'package:cached_network_image/cached_network_image.dart';
import '../bean/utils/share_app_android_ios.dart';
import '../bean/zx_course_entity.dart';
import 'package:flutter/material.dart';
import '../common/CommonUtil.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';

import '../common/user_option.dart';
import '../dialog/dialog_share.dart';
import '../navigator/home_navigator.dart';
import '../utils/android_ios_plugin.dart';
import '../common/sensors_analytics_option.dart';

/// 商品item
class WaterLayoutList {
  bool isShow = false;
  Widget waterLayoutList(BuildContext context,
      RxList<ZxCourseDataData> dataList, type, commonFields,
      {isScroll = true, isHorizontal = false, isShowOne = false}) {
    isShow = isShowOne;
    if (dataList.isEmpty) {
      return SizedBox.shrink(); // 空列表返回空组件
    }

    if (isHorizontal) {
      return _buildHorizontalLayout(dataList, type, commonFields, isScroll);
    } else {
      return _buildVerticalLayout(
          dataList, type, commonFields, isScroll, context);
    }
  }

  Widget _buildHorizontalLayout(
      RxList<ZxCourseDataData> dataList, type, commonFields, isScroll) {
    return Container(
      height: 248.h, // 为水平列表提供固定高度
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: dataList.length,
        shrinkWrap: true,
        physics: isScroll ? null : const NeverScrollableScrollPhysics(),
        itemBuilder: (context, index) {
          return Container(
            width: 165.w, // 固定宽度
            margin: EdgeInsets.only(right: 10.w),
            child: waterLayoutItemBuilder(index, dataList, type, commonFields),
          );
        },
      ),
    );
  }

  Widget _buildVerticalLayout(RxList<ZxCourseDataData> dataList, type,
      commonFields, isScroll, BuildContext context) {
    return MediaQuery.removePadding(
      context: context, // 需要context，所以这个方法的参数可能需要加上BuildContext context
      removeTop: true, // 移除顶部padding
      child: MasonryGridView.count(
        cacheExtent: 1000,
        addAutomaticKeepAlives: true,
        crossAxisCount: isShow ? 1 : 2,
        mainAxisSpacing: 10,
        crossAxisSpacing: 10,
        itemCount: dataList.length,
        shrinkWrap: true,
        physics: isScroll ? null : const NeverScrollableScrollPhysics(),
        itemBuilder: (context, index) {
          return waterLayoutItemBuilder(index, dataList, type, commonFields);
        },
      ),
    );
  }

  Widget waterLayoutItemBuilder(
      index, RxList<ZxCourseDataData> dataList, type, commonFields) {
    ZxCourseDataData zxCourseDataData = dataList[index];
    return Container(
      height: isShow ? 280.h : 250.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: Colors.white,
      ),
      clipBehavior: Clip.hardEdge,
      child: Stack(
        children: [
          GestureDetector(
            onTap: () {
              closeKeyboard();
              if (UserOption.checkNoTokenJump()) {
                return;
              }
              var url_path = '';
              var title = '';
              var options = '';

              if (type == 1) {
                title = ' IndexProductDetailClick';
                options = '&options=HomePage';
                url_path = 'HomePage';
              } else if (type == 2) {
                title = 'SelectionProductDetailClick';
                options = '&options=ZxPage';
                url_path = 'ZxPage';
              }
              // commonFields
              TrackingUtils.trackEvent(title, {
                'goods_id': zxCourseDataData.goodsId,
                'common_fields': commonFields,
                'avitcity_id': '',
                'banner_id': '',
                'url_path': url_path
              });

              String path =
                  HomeNavigator.goodsDetails(goodsId: zxCourseDataData.goodsId)
                          .path +
                      options;
              AndroidIosPlugin.openUniApp(path);
            },
            child: layout(zxCourseDataData, dataList),
          ),
          // Visibility(
          //   visible: getCourseType(zxCourseDataData.goodsType) != "",
          //   child: Positioned(
          //     top: 10,
          //     right: 0,
          //     child: Container(
          //       decoration: BoxDecoration(
          //         color:
          //             HexColor(getCourseTypeColor(zxCourseDataData.goodsType)),
          //         borderRadius: BorderRadius.only(
          //           topLeft: Radius.circular(12),
          //           bottomLeft: Radius.circular(12),
          //         ),
          //         boxShadow: const [
          //           BoxShadow(
          //               offset: Offset(
          //                 3.0,
          //                 8.0,
          //               ),
          //               color: Color.fromRGBO(182, 200, 200, 1),
          //               blurRadius: 6.0,
          //               spreadRadius: -6.0)
          //         ], // 设置圆角
          //       ),
          //       padding:
          //           const EdgeInsets.symmetric(horizontal: 10, vertical: 3),
          //       child: Text(
          //         getCourseType(zxCourseDataData.goodsType),
          //         style: TextStyle(color: Colors.white, fontSize: 12),
          //       ),
          //     ),
          //   ),
          // )
        ],
      ),
    );
  }

  Widget layout(ZxCourseDataData zxCourseDataData, dataList) {
    return Column(
      children: [
        RepaintBoundary(
          child: CachedNetworkImage(
            width: double.infinity,
            height: !isShow ? 180.w : 220.w,
            fit: BoxFit.cover,
            imageUrl: !isShow && zxCourseDataData.goodsCarouselList.length > 0
                ? zxCourseDataData.goodsPicUrl
                : zxCourseDataData.goodsSpecialPicUrl,
            imageBuilder: (context, image) {
              image.resolve(ImageConfiguration()).addListener(
                  ImageStreamListener((ImageInfo info, bool _) {}));
              return Image(
                image: image,
                fit: BoxFit.cover,
                height: !isShow ? 180.w : 220.w,
              );
            },
          ),
        ),
        SizedBox(
          height: 2.w,
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                height: 38.h,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // 右侧文本 - 使用Expanded确保文本不会溢出
                    Expanded(
                      child: RichText(
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        text: TextSpan(
                          style: TextStyle(
                            fontSize: 14.sp,
                          ),
                          children: [
                            zxCourseDataData.goodsTagOne.isNotEmpty
                                ? WidgetSpan(
                                    child: Container(
                                      padding: EdgeInsets.symmetric(
                                          horizontal: 10.w, vertical: 0),
                                      margin: EdgeInsets.only(right: 5.w),
                                      decoration: BoxDecoration(
                                        // color: HexColor('#EFFFEE'), // 背景色
                                        border: Border.all(
                                            color: HexColor('#62C92D'),
                                            width: 1), // 边框
                                        borderRadius:
                                            BorderRadius.circular(4), // 圆角（可选）
                                      ),
                                      child: Text(
                                        zxCourseDataData.goodsTagOne,
                                        style: TextStyle(
                                            color: HexColor('#62C92D'),
                                            fontSize: 11.sp),
                                      ),
                                    ),
                                  )
                                : TextSpan(text: ''),
                            TextSpan(
                              text: zxCourseDataData.goodsName,
                              style: TextStyle(
                                fontSize: 14.sp,
                                height: 1.5,
                                fontWeight: FontWeight.w500,
                                color: Colors.black, // 根据需要设置颜色
                              ),
                            ),
                          ],
                        ),
                      ),
                      // child: Text(
                      //   zxCourseDataData.goodsName,
                      //   maxLines: 2,
                      //   overflow: TextOverflow.ellipsis,
                      //   style: TextStyle(
                      //     fontSize: 14.sp,
                      //     fontWeight: FontWeight.w500,
                      //   ),
                      // ),
                    ),
                  ],
                ),
              ),
              RichText(
                text: TextSpan(
                  children: [
                    WidgetSpan(
                      child: Container(
                        margin: EdgeInsets.only(right: 5.w),
                        child: Text(
                          zxCourseDataData.goodsTagTwo,
                          style: TextStyle(
                              color: HexColor('9595AE'), fontSize: 11.sp),
                        ),
                      ),
                    ),
                    TextSpan(
                      text: zxCourseDataData.goodsTagThree,
                      style: TextStyle(
                        color: HexColor('#9595AE'),
                        fontSize: 11.sp,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(
                height: 3.w,
              ),
              // !isShow
              //     ?
              //     : Container(),
              SizedBox(
                height: 3.w,
              ),
              Row(
                children: [
                  SizedBox(
                    width: 82.w,
                    child:RichText(
                            text: TextSpan(
                              children: [
                                TextSpan(
                                  text: getCoursePriceTitleType(
                                      zxCourseDataData.goodsType),
                                  style: TextStyle(
                                      color: HexColor('FA411A'), fontSize: 10.sp),
                                ),
                                TextSpan(
                                  text: '¥${zxCourseDataData.goodsVipPrice}',
                                  style: TextStyle(
                                      color: HexColor('FA411A'),
                                      fontSize: 11.sp,
                                      fontWeight: FontWeight.w500),
                                ),
                              ],
                            ),
                          ),
                  ),
                  SizedBox(
                    width: !isShow ? 0.w : 168.w,
                  ),
                  SizedBox(
                    width: 62.w,
                    child: Text(
                      formatPaymentCount(zxCourseDataData.goodsSales),
                      // '${zxCourseDataData.goodsSales}人付款',
                      style: TextStyle(color: HexColor('979797'), fontSize: 10.sp),
                      textAlign: TextAlign.right,
                    ),
                  ),
                  Spacer(),
                  // InkWell(
                  //   onTap: () {
                  //     String path = HomeNavigator.shareUrl(
                  //       goodsId: zxCourseDataData.goodsId,
                  //       type: "6",
                  //     ).path;
                  //     ShareAppAndroidIos shareAppAndroidIos =
                  //         ShareAppAndroidIos();
                  //     shareAppAndroidIos.path = path;
                  //     shareAppAndroidIos.imageUrl =
                  //         zxCourseDataData.goodsSharePoster;
                  //     shareAppAndroidIos.title =
                  //         zxCourseDataData.goodsShareTextList.isNotEmpty
                  //             ? zxCourseDataData.goodsShareTextList[0].shareText
                  //             : "";
                  //     String goodsId = zxCourseDataData.goodsId;
                  //     Get.bottomSheet(
                  //       ShareDialog(shareAppAndroidIos, goodsId),
                  //       backgroundColor: Colors.transparent, // 透明背景
                  //       isDismissible: true, // 点击外部关闭
                  //       shape: RoundedRectangleBorder(
                  //         borderRadius:
                  //             BorderRadius.vertical(top: Radius.circular(16)),
                  //       ),
                  //     );
                  //   },
                  //   child: Container(
                  //     width: 20.w,
                  //     height: 25.w,
                  //     alignment: Alignment.center,
                  //     child: Image.asset(
                  //       'assets/zx/icon_share.png',
                  //       width: 15,
                  //       height: 15,
                  //     ),
                  //   ),
                  // )
                ],
              ),
              SizedBox(
                height: 10.w,
              ),
            ],
          ),
        ),
      ],
    );
  }

  String getCourseType(type) {
    switch (type) {
      case 2:
        return '体验课';
      case 3:
        return '正式课';
      case 4:
        return '录播课';
      default:
        return '';
    }
  }

  String getCoursePriceTitleType(type) {
    switch (type) {
      case 2:
        return '体验价';
      case 3:
        return '会员价';
      case 4:
        return '会员价';
      default:
        return '';
    }
  }

  String getCourseTypeColor(type) {
    switch (type) {
      case 2:
        return 'F5A53A';
      case 3:
        return '2DC032';
      case 4:
        return '4095e5';
      default:
        return 'ffffff';
    }
  }

  String formatPaymentCount(int count) {
    if (count < 10000) {
      return '$count人付款'; // 低于1万显示具体数字
    } else {
      final tenThousands = count ~/ 10000; // 整除获取万的整数部分
      return '${tenThousands}万+人付款'; // 超过1万显示 "n万+"
    }
  }

  closeKeyboard() {
    // 滚动时关闭键盘
    if (FocusManager.instance.primaryFocus?.hasFocus ?? false) {
      FocusManager.instance.primaryFocus?.unfocus();
    }
  }
}
