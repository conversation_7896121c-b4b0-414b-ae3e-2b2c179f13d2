package com.dxznjy.alading.fragment;


import android.content.Intent;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.dxznjy.alading.AppManager;
import com.dxznjy.alading.R;
import com.dxznjy.alading.activity.LoginActivity;
import com.dxznjy.alading.api.ApiProvider;
import com.dxznjy.alading.common.Constants;
import com.dxznjy.alading.http.BaseSubscriber;
import com.dxznjy.alading.response.LoginResponse;
import com.dxznjy.alading.response.StudentMerchantResponse;
import com.dxznjy.alading.response.UserInfoResponse;
import com.dxznjy.alading.widget.MySplashView;
import com.dxznjy.alading.widget.SelectMerChantPopWindow;
import com.google.gson.Gson;
import com.vondear.rxtool.RxSPTool;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

import butterknife.BindView;
import butterknife.OnClick;
import io.dcloud.feature.sdk.DCUniMPSDK;
import io.dcloud.feature.sdk.Interface.IUniMP;
import io.dcloud.feature.unimp.config.UniMPOpenConfiguration;

public class FragmentMine extends BaseFragment implements View.OnClickListener {
    @BindView(R.id.ll_name)
    LinearLayout ll_name;
    @BindView(R.id.tv_fristname)
    TextView tv_fristname;
    @BindView(R.id.tv_name)
    TextView tv_name;
    @BindView(R.id.tv_gradename)
    TextView tv_gradename;
    private String pageRoute="";
    private String studentCode="";
    IUniMP unimp;
    @Override
    public void initViewAndData() {
        studentCode=RxSPTool.getString(getActivity(), Constants.STUDENT_CODE);
        int[] headBg=new int[]{R.drawable.bg_circle_12c287,R.drawable.bg_circle_25c36d,R.drawable.bg_circle_6882f0,R.drawable.bg_circle_eaf3dd
                ,R.drawable.bg_circle_d6eef4,R.drawable.bg_circle_f47307,R.drawable.bg_circle_f9eaa0};
        Random random=new Random();
        int index=random.nextInt(headBg.length);
        ll_name.setBackgroundResource(headBg[index]);
    }
    @Override
    public int setContent() {
        return R.layout.fragment_mine;
    }

    @OnClick({R.id.ll_stbg,R.id.ll_chljc,R.id.ll_ctb,R.id.ll_qwfx,R.id.ll_xxnr,R.id.ll_pyf,R.id.ll_kyw,R.id.ll_txjc,R.id.tv_exit})
    public void onClick(View view) {
        switch (view.getId()){
            case R.id.tv_exit:
                break;
            case R.id.ll_stbg:
                pageRoute=Constants.STBG_UNIAPP_PAGE+tv_name.getText().toString();
                toUniappPage();
                break;
            case R.id.ll_chljc:
                pageRoute= Constants.CHLJC_UNIAPP_PAGE+studentCode+"&studentName="+tv_name.getText().toString();
                toUniappPage();
                break;
            case R.id.ll_ctb:
                pageRoute=Constants.CTB_UNIAPP_PAGE+studentCode+"&studentName="+tv_name.getText().toString();
                toUniappPage();
                break;
            case R.id.ll_qwfx:
                getStudentMerchantList();
                break;
            case R.id.ll_xxnr:
                pageRoute=Constants.XXNR_UNIAPP_PAGE+studentCode+"&studentName="+tv_name.getText().toString();
                toUniappPage();
                break;
            case R.id.ll_pyf:
                pageRoute=Constants.PYF_UNIAPP_PAGE+studentCode;
                toUniappPage();
                break;
            case R.id.ll_kyw:
                pageRoute=Constants.KYW_UNIAPP_PAGE+"&Buttonclick="+studentCode+"&buttonclickName="+tv_name.getText().toString()  ;
                toUniappPage();
                break;
            case R.id.ll_txjc:
                pageRoute=Constants.TXJC_UNIAPP_PAGE+studentCode+"&studentName="+tv_name.getText().toString();
                toUniappPage();
                break;
        }
    }


    /**
     * 打开微信小程序页面
     */
    public void toUniappPage(){
        try {
            UniMPOpenConfiguration uniMPOpenConfiguration=new UniMPOpenConfiguration();
            if (pageRoute.contains(Constants.QWFX_UNIAPP_PAGE)){   //仅趣味复习  使用学生token
                String loginInfo=RxSPTool.getString(getActivity(), Constants.STUDENT_INFO);
                String studentToken="";
                if (!(TextUtils.isEmpty(loginInfo))){
                    LoginResponse.Login login=new Gson().fromJson(loginInfo, LoginResponse.Login.class);
                    studentToken=login.getToken();
                }
                String token=RxSPTool.getString(getActivity(), Constants.PARENT_TOKEN);
                uniMPOpenConfiguration.path=pageRoute+"&logintokenReview="+studentToken+"&token="+token;
            }else{
                String token=RxSPTool.getString(getActivity(), Constants.PARENT_TOKEN);
                uniMPOpenConfiguration.path=pageRoute+"&token="+token;
            }
//            uniMPOpenConfiguration.splashClass = MySplashView.class;
            Log.i("zgj",uniMPOpenConfiguration.path);
            DCUniMPSDK.getInstance().openUniMP(getActivity(), Constants.XCX_PKG_NAME,uniMPOpenConfiguration);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    /**
     * 关闭微信小程序页面
     */
    public void closeUniappPage(){
        try {
            unimp.closeUniMP();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @Override
    public void onResume() {
        super.onResume();
        getUserInfo();
    }

    /**
     * 获取用户信息
     */
    private void getUserInfo() {
        request(ApiProvider.getInstance(getActivity()).DFService.getUserInfo(), new BaseSubscriber<UserInfoResponse>(getActivity()){
            @Override
            public void onCompleted() {
                super.onCompleted();
            }

            @Override
            public void onError(Throwable e) {
                super.onError(e);
            }

            @Override
            public void onNext(UserInfoResponse response) {
                super.onNext(response);
                if (response.getData()!=null){
                    tv_fristname.setText(response.getData().getFirstName());
                    tv_gradename.setText(response.getData().getGradeName());
                    tv_name.setText(response.getData().getName());
                    RxSPTool.putString(getActivity(),Constants.STUDENT_NAME,response.getData().getName());
                }
            }
        });
    }
    /**
     * 学生门店接口列表
     */
    private void getStudentMerchantList() {
        showDialog();
        Map<String,String> map=new HashMap<>();
        map.put("studentCode",studentCode);
        request(ApiProvider.getInstance(getActivity()).DFService.getStudentMerchantList(map), new BaseSubscriber<StudentMerchantResponse>(getActivity()){
            @Override
            public void onCompleted() {
                super.onCompleted();
            }

            @Override
            public void onError(Throwable e) {
                super.onError(e);
                closeDialog();
            }

            @Override
            public void onNext(StudentMerchantResponse response) {
                closeDialog();
                super.onNext(response);
                showSelectMerChantPop(response.getData());
            }
        });
    }

    /**
     * 选择门店弹窗
     * @param data  门店数据
     */
    public void showSelectMerChantPop(List<StudentMerchantResponse.StudentMerchant> data){
        if (data!=null&&data.size()==1){
            pageRoute=Constants.QWFX_UNIAPP_PAGE+studentCode+"&merchantCode="+data.get(0).getMerchantCode();
            toUniappPage();
            return;
        }
        for (int i=0;i<data.size();i++){
            data.get(i).setType(1);
            data.get(i).setCheck(false);
        }
        SelectMerChantPopWindow selectMerChantPopWindow=new SelectMerChantPopWindow(
                getActivity(), "选择门店", "确定", mainView, data, Constants.VERTICAL,
                0, false, new SelectMerChantPopWindow.OnSelectListener() {
            @Override
            public void onSelect(StudentMerchantResponse.StudentMerchant studentMerchant) {
                pageRoute=Constants.QWFX_UNIAPP_PAGE+studentCode+"&merchantCod="+studentMerchant.getMerchantCode();
                toUniappPage();
            }
        });
        selectMerChantPopWindow.showPop();
    }

}
