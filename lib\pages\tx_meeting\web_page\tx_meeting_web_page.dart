import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';

class TxMeetingWebPage extends StatefulWidget {
  final String url;

  const TxMeetingWebPage({super.key, required this.url});

  @override
  State<TxMeetingWebPage> createState() => _TxMeetingWebPageState();
}

class _TxMeetingWebPageState extends State<TxMeetingWebPage> {
  late final WebViewController _controller;

  @override
  void initState() {
    super.initState();


    // 初始化WebViewController
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      // ..loadFlutterAsset('assets/test.html') // 本地HTML文件
    ..loadRequest(Uri.parse(widget.url))
      ..addJavaScriptChannel(
        'FlutterChannel', // JS调用Flutter时使用的通道名
        onMessageReceived: (JavaScriptMessage message) {
          // 处理从JS接收的消息
          _handleJsMessage(message.message);
        },
      );
  }

  // 处理JS发送的消息
  void _handleJsMessage(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('来自JS的消息'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  // Flutter调用JS方法
  Future<void> _callJsFunction() async {
    // 调用JS中的函数并获取返回值
    final result = await _controller.runJavaScriptReturningResult(
        'jsFunction("来自Flutter的消息")'
    );

    // 处理JS返回的结果
    if (result != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('JS返回: $result')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(child: Scaffold(
      body: ColoredBox(color: Colors.black,child: SafeArea(child: WebViewWidget(controller: _controller)),),
    ), onWillPop: () async {
      return true;
    });
  }
}
