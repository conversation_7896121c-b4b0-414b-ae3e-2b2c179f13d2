
import 'empty_widget.dart';
import 'package:flutter/material.dart';
enum ViewState {
  loading,     // 加载中
  success,     // 成功
  noNetwork,   // 无网络
  requestError,// 请求错误
  emptyData    // 空数据
}

class SmartEmptyView extends StatelessWidget {
  final ViewState state;
  final VoidCallback onRetry;
  final VoidCallback onOpenSettings;

  const SmartEmptyView({
    super.key,
    required this.state,
    required this.onRetry,
    required this.onOpenSettings,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyWidget(
      image: _getStateImage(),
      title: _getTitle(),
      subTitle: _getSubtitle(),
      buttonText: _getButtonText(),
      onButtonPressed: _getAction(),
    );
  }

  String _getStateImage() {
    switch (state) {
      case ViewState.noNetwork:
        return 'assets/no_network.png';
      case ViewState.requestError:
        return 'assets/server_error.png';
      case ViewState.emptyData:
        return 'assets/empty_data.png';
      default:
        return 'assets/general_error.png';
    }
  }

  String _getTitle() {
    switch (state) {
      case ViewState.noNetwork:
        return '网络离家出走了';
      case ViewState.requestError:
        return '服务器开小差了';
      case ViewState.emptyData:
        return '空空如也';
      default:
        return '发生了一些意外';
    }
  }

  String _getSubtitle() {
    switch (state) {
      case ViewState.noNetwork:
        return '请检查网络连接后重试';
      case ViewState.requestError:
        return '点击按钮重新加载';
      case ViewState.emptyData:
        return '暂时没有相关内容哦';
      default:
        return '请稍后再试';
    }
  }

  String? _getButtonText() {
    switch (state) {
      case ViewState.noNetwork:
        return '立即连接';
      case ViewState.requestError:
        return '点击重试';
      case ViewState.emptyData:
        return null; // 空数据不显示按钮
      default:
        return '重试';
    }
  }

  VoidCallback? _getAction() {
    switch (state) {
      case ViewState.noNetwork:
        return onOpenSettings;
      case ViewState.requestError:
        return onRetry;
      default:
        return null;
    }
  }
}