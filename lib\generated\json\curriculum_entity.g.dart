import 'package:student_end_flutter/generated/json/base/json_convert_content.dart';
import 'package:student_end_flutter/bean/curriculum_entity.dart';

CurriculumEntity $CurriculumEntityFromJson(Map<String, dynamic> json) {
  final CurriculumEntity curriculumEntity = CurriculumEntity();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    curriculumEntity.id = id;
  }
  final String? curriculumName = jsonConvert.convert<String>(
      json['curriculumName']);
  if (curriculumName != null) {
    curriculumEntity.curriculumName = curriculumName;
  }
  final String? curriculumTypeName = jsonConvert.convert<String>(
      json['curriculumTypeName']);
  if (curriculumTypeName != null) {
    curriculumEntity.curriculumTypeName = curriculumTypeName;
  }
  final String? curriculumType = jsonConvert.convert<String>(
      json['curriculumType']);
  if (curriculumType != null) {
    curriculumEntity.curriculumType = curriculumType;
  }
  final int? status = jsonConvert.convert<int>(json['status']);
  if (status != null) {
    curriculumEntity.status = status;
  }
  final int? sort = jsonConvert.convert<int>(json['sort']);
  if (sort != null) {
    curriculumEntity.sort = sort;
  }
  final String? curriculumToolName = jsonConvert.convert<String>(
      json['curriculumToolName']);
  if (curriculumToolName != null) {
    curriculumEntity.curriculumToolName = curriculumToolName;
  }
  final String? createTime = jsonConvert.convert<String>(json['createTime']);
  if (createTime != null) {
    curriculumEntity.createTime = createTime;
  }
  final String? updateTime = jsonConvert.convert<String>(json['updateTime']);
  if (updateTime != null) {
    curriculumEntity.updateTime = updateTime;
  }
  final List<
      CurriculumCurriculumTool>? curriculumTool = (json['curriculumTool'] as List<
      dynamic>?)?.map(
          (e) =>
      jsonConvert.convert<CurriculumCurriculumTool>(
          e) as CurriculumCurriculumTool).toList();
  if (curriculumTool != null) {
    curriculumEntity.curriculumTool = curriculumTool;
  }
  final bool? isPurchase = jsonConvert.convert<bool>(json['isPurchase']);
  if (isPurchase != null) {
    curriculumEntity.isPurchase = isPurchase;
  }
  final dynamic curriculumIdList = json['curriculumIdList'];
  if (curriculumIdList != null) {
    curriculumEntity.curriculumIdList = curriculumIdList;
  }
  return curriculumEntity;
}

Map<String, dynamic> $CurriculumEntityToJson(CurriculumEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['curriculumName'] = entity.curriculumName;
  data['curriculumTypeName'] = entity.curriculumTypeName;
  data['curriculumType'] = entity.curriculumType;
  data['status'] = entity.status;
  data['sort'] = entity.sort;
  data['curriculumToolName'] = entity.curriculumToolName;
  data['createTime'] = entity.createTime;
  data['updateTime'] = entity.updateTime;
  data['curriculumTool'] =
      entity.curriculumTool.map((v) => v.toJson()).toList();
  data['isPurchase'] = entity.isPurchase;
  data['curriculumIdList'] = entity.curriculumIdList;
  return data;
}

extension CurriculumEntityExtension on CurriculumEntity {
  CurriculumEntity copyWith({
    String? id,
    String? curriculumName,
    String? curriculumTypeName,
    String? curriculumType,
    int? status,
    int? sort,
    String? curriculumToolName,
    String? createTime,
    String? updateTime,
    List<CurriculumCurriculumTool>? curriculumTool,
    bool? isPurchase,
    dynamic curriculumIdList,
  }) {
    return CurriculumEntity()
      ..id = id ?? this.id
      ..curriculumName = curriculumName ?? this.curriculumName
      ..curriculumTypeName = curriculumTypeName ?? this.curriculumTypeName
      ..curriculumType = curriculumType ?? this.curriculumType
      ..status = status ?? this.status
      ..sort = sort ?? this.sort
      ..curriculumToolName = curriculumToolName ?? this.curriculumToolName
      ..createTime = createTime ?? this.createTime
      ..updateTime = updateTime ?? this.updateTime
      ..curriculumTool = curriculumTool ?? this.curriculumTool
      ..isPurchase = isPurchase ?? this.isPurchase
      ..curriculumIdList = curriculumIdList ?? this.curriculumIdList;
  }
}

CurriculumCurriculumTool $CurriculumCurriculumToolFromJson(
    Map<String, dynamic> json) {
  final CurriculumCurriculumTool curriculumCurriculumTool = CurriculumCurriculumTool();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    curriculumCurriculumTool.id = id;
  }
  final String? toolName = jsonConvert.convert<String>(json['toolName']);
  if (toolName != null) {
    curriculumCurriculumTool.toolName = toolName;
  }
  final int? status = jsonConvert.convert<int>(json['status']);
  if (status != null) {
    curriculumCurriculumTool.status = status;
  }
  final String? generalFlag = jsonConvert.convert<String>(json['generalFlag']);
  if (generalFlag != null) {
    curriculumCurriculumTool.generalFlag = generalFlag;
  }
  final int? sort = jsonConvert.convert<int>(json['sort']);
  if (sort != null) {
    curriculumCurriculumTool.sort = sort;
  }
  final String? iconAddress = jsonConvert.convert<String>(json['iconAddress']);
  if (iconAddress != null) {
    curriculumCurriculumTool.iconAddress = iconAddress;
  }
  final String? ashIconAddress = jsonConvert.convert<String>(
      json['ashIconAddress']);
  if (ashIconAddress != null) {
    curriculumCurriculumTool.ashIconAddress = ashIconAddress;
  }
  final String? jumpAddress = jsonConvert.convert<String>(json['jumpAddress']);
  if (jumpAddress != null) {
    curriculumCurriculumTool.jumpAddress = jumpAddress;
  }
  final String? appJumpAddress = jsonConvert.convert<String>(
      json['appJumpAddress']);
  if (appJumpAddress != null) {
    curriculumCurriculumTool.appJumpAddress = appJumpAddress;
  }
  final String? updateTime = jsonConvert.convert<String>(json['updateTime']);
  if (updateTime != null) {
    curriculumCurriculumTool.updateTime = updateTime;
  }
  final String? createTime = jsonConvert.convert<String>(json['createTime']);
  if (createTime != null) {
    curriculumCurriculumTool.createTime = createTime;
  }
  final String? bindId = jsonConvert.convert<String>(json['bindId']);
  if (bindId != null) {
    curriculumCurriculumTool.bindId = bindId;
  }
  final String? remark = jsonConvert.convert<String>(json['remark']);
  if (remark != null) {
    curriculumCurriculumTool.remark = remark;
  }
  final String? showChannel = jsonConvert.convert<String>(json['showChannel']);
  if (showChannel != null) {
    curriculumCurriculumTool.showChannel = showChannel;
  }
  final int? deleted = jsonConvert.convert<int>(json['deleted']);
  if (deleted != null) {
    curriculumCurriculumTool.deleted = deleted;
  }
  final int? showStatus = jsonConvert.convert<int>(json['showStatus']);
  if (showStatus != null) {
    curriculumCurriculumTool.showStatus = showStatus;
  }
  return curriculumCurriculumTool;
}

Map<String, dynamic> $CurriculumCurriculumToolToJson(
    CurriculumCurriculumTool entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['toolName'] = entity.toolName;
  data['status'] = entity.status;
  data['generalFlag'] = entity.generalFlag;
  data['sort'] = entity.sort;
  data['iconAddress'] = entity.iconAddress;
  data['ashIconAddress'] = entity.ashIconAddress;
  data['jumpAddress'] = entity.jumpAddress;
  data['appJumpAddress'] = entity.appJumpAddress;
  data['updateTime'] = entity.updateTime;
  data['createTime'] = entity.createTime;
  data['bindId'] = entity.bindId;
  data['remark'] = entity.remark;
  data['showChannel'] = entity.showChannel;
  data['deleted'] = entity.deleted;
  data['showStatus'] = entity.showStatus;
  return data;
}

extension CurriculumCurriculumToolExtension on CurriculumCurriculumTool {
  CurriculumCurriculumTool copyWith({
    String? id,
    String? toolName,
    int? status,
    String? generalFlag,
    int? sort,
    String? iconAddress,
    String? ashIconAddress,
    String? jumpAddress,
    String? appJumpAddress,
    String? updateTime,
    String? createTime,
    String? bindId,
    String? remark,
    String? showChannel,
    int? deleted,
    int? showStatus,
  }) {
    return CurriculumCurriculumTool()
      ..id = id ?? this.id
      ..toolName = toolName ?? this.toolName
      ..status = status ?? this.status
      ..generalFlag = generalFlag ?? this.generalFlag
      ..sort = sort ?? this.sort
      ..iconAddress = iconAddress ?? this.iconAddress
      ..ashIconAddress = ashIconAddress ?? this.ashIconAddress
      ..jumpAddress = jumpAddress ?? this.jumpAddress
      ..appJumpAddress = appJumpAddress ?? this.appJumpAddress
      ..updateTime = updateTime ?? this.updateTime
      ..createTime = createTime ?? this.createTime
      ..bindId = bindId ?? this.bindId
      ..remark = remark ?? this.remark
      ..showChannel = showChannel ?? this.showChannel
      ..deleted = deleted ?? this.deleted
      ..showStatus = showStatus ?? this.showStatus;
  }
}