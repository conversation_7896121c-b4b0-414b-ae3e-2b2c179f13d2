//
//  ViewController+Extension.swift
//  LaiZhuanPro
//
//  Created by HyBoard on 2018/10/31.
//  Copyright © 2018 Jason. All rights reserved.
//

import UIKit

extension UIViewController {
    func initAlert(extitle title: String, msg message: String, _ style: UIAlertController.Style = .alert, sureComplate complate: @escaping () -> Void) {
        let alert = UIAlertController.init(title: title, message: message, preferredStyle: style)
        let action1 = UIAlertAction(title: "取消", style: .cancel) { (_) in

        }
        alert.addAction(action1)
        let action2 = UIAlertAction(title: "确定", style: .default) { (_) in
            complate()
        }
        alert.addAction(action2)
        self.present(alert, animated: true, completion: nil)
    }
    /**
     *  push 到下一个控制器
     */
    func pushTo(_ viewController: UIViewController) {
        self.navigationController?.pushViewController(viewController, animated: true)
    }
   
    /**
     *  present 到下一个控制器
     */
    func presentTo(_ viewController: UIViewController) {
        viewController.modalPresentationStyle = .fullScreen
        self.present(viewController, animated: true) {
            // 跳转
        }
    }
    /**
     *  从当前控制器 pop
     */
    func popViewController(_ animated: Bool = true) {
        self.navigationController?.popViewController(animated: animated)
    }

    /**
     *  当前控制器 dismiss
     */
    func dismiss() {
        self.dismiss(animated: true) {

        }
    }
    
    func dismissAllPresentedControllers(completion: @escaping () -> Void) {
        // 关键点：始终从 RootVC 开始查找
        guard let rootVC = appDelegate.window?.rootViewController else {
            completion()
            return
        }
        
        // 查找当前最顶层的 presented 控制器
        var targetVC = rootVC
        while let nextVC = targetVC.presentedViewController {
            targetVC = nextVC
        }
        
        // 终止条件：没有更多 presented 控制器
        guard targetVC != rootVC else {
            completion()
            return
        }
        
        // 关闭当前顶层控制器后，重新从 Root 出发
        targetVC.dismiss(animated: true) { [weak self] in
            self?.dismissAllPresentedControllers(completion: completion)
        }
    }
    
    func l_pushViewController(viewController: UIViewController, animated: Bool) {
        self.navigationController?.pushViewController(viewController, animated: animated)
    }
}

// BaseNavViewController.swift
extension UIViewController {
    var canPop: Bool {
        return (navigationController?.viewControllers.count ?? 0) > 1
    }
    var isModalPresented: Bool {
        return presentingViewController != nil
    }
}

